"use strict";(()=>{var e={};e.id=4840,e.ids=[4840],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},5845:(e,t,r)=>{r.r(t),r.d(t,{config:()=>f,default:()=>_,routeModule:()=>h});var a={};r.r(a),r.d(a,{default:()=>c});var s=r(1802),o=r(7153),i=r(8781),n=r(8456),l=r(7474);async function c(e,t){try{let r=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!r)return t.status(401).json({error:"No authentication token"});let a=await (0,l.ZQ)(r);if(!a.valid||!a.user)return t.status(401).json({error:"Invalid authentication"});let s=a.user,o=Math.random().toString(36).substring(2,8);switch(console.log(`[${o}] Receipt templates API called by ${s.email}`),e.method){case"GET":return await d(e,t,s,o);case"POST":return await u(e,t,s,o);case"PUT":return await p(e,t,s,o);case"DELETE":return await m(e,t,s,o);default:return t.status(405).json({error:"Method not allowed"})}}catch(e){return console.error("Receipt templates API error:",e),t.status(500).json({error:"Internal server error"})}}async function d(e,t,r,a){try{let{data:e,error:r}=await n.pR.from("receipt_templates").select("*").eq("is_active",!0).order("is_default",{ascending:!1}).order("created_at",{ascending:!1});if(r){if(console.error(`[${a}] Error fetching receipt templates:`,r),"42P01"===r.code)return console.log(`[${a}] Receipt templates table doesn't exist, returning default templates`),t.status(200).json({templates:[{id:"default-standard",name:"Standard Receipt",description:"Default receipt template with all standard information",template_type:"standard",is_default:!0,is_active:!0,business_name:"Ocean Soul Sparkles",business_address:"Australia",business_phone:"+61 XXX XXX XXX",business_email:"<EMAIL>",business_website:"oceansoulsparkles.com.au",show_logo:!0,logo_position:"center",header_color:"#667eea",text_color:"#333333",font_family:"Arial",font_size:12,show_customer_details:!0,show_service_details:!0,show_artist_details:!0,show_payment_details:!0,show_booking_notes:!1,show_terms_conditions:!0,footer_message:"Thank you for choosing Ocean Soul Sparkles!",show_social_media:!1},{id:"default-compact",name:"Compact Receipt",description:"Minimal receipt template for quick transactions",template_type:"compact",is_default:!1,is_active:!0,business_name:"Ocean Soul Sparkles",business_address:"Australia",business_phone:"+61 XXX XXX XXX",business_email:"<EMAIL>",business_website:"oceansoulsparkles.com.au",show_logo:!1,logo_position:"left",header_color:"#667eea",text_color:"#333333",font_family:"Arial",font_size:10,show_customer_details:!0,show_service_details:!0,show_artist_details:!1,show_payment_details:!0,show_booking_notes:!1,show_terms_conditions:!1,footer_message:"Thank you!",show_social_media:!1}]});return t.status(500).json({error:"Failed to fetch receipt templates"})}return console.log(`[${a}] Retrieved ${e?.length||0} receipt templates`),t.status(200).json({templates:e||[]})}catch(e){return console.error(`[${a}] Error in handleGetTemplates:`,e),t.status(500).json({error:"Failed to fetch receipt templates"})}}async function u(e,t,r,a){try{let{name:s,description:o,template_type:i,is_default:l,business_name:c,business_address:d,business_phone:u,business_email:p,business_website:m,business_abn:_,show_logo:f,logo_position:h,header_color:w,text_color:b,font_family:g,font_size:y,show_customer_details:j,show_service_details:I,show_artist_details:k,show_payment_details:q,show_booking_notes:S,show_terms_conditions:v,footer_message:R,show_social_media:X,social_media_links:E,custom_fields:$}=e.body;if(!s||!i)return t.status(400).json({error:"Name and template type are required"});l&&await n.pR.from("receipt_templates").update({is_default:!1}).eq("is_default",!0);let{data:T,error:x}=await n.pR.from("receipt_templates").insert([{name:s,description:o,template_type:i,is_default:l||!1,is_active:!0,business_name:c,business_address:d,business_phone:u,business_email:p,business_website:m,business_abn:_,show_logo:f,logo_position:h,header_color:w,text_color:b,font_family:g,font_size:y,show_customer_details:j,show_service_details:I,show_artist_details:k,show_payment_details:q,show_booking_notes:S,show_terms_conditions:v,footer_message:R,show_social_media:X,social_media_links:E,custom_fields:$,created_by:r.id,updated_by:r.id}]).select().single();if(x)return console.error(`[${a}] Error creating receipt template:`,x),t.status(500).json({error:"Failed to create receipt template"});return console.log(`[${a}] Created receipt template: ${T.name}`),t.status(201).json({template:T})}catch(e){return console.error(`[${a}] Error in handleCreateTemplate:`,e),t.status(500).json({error:"Failed to create receipt template"})}}async function p(e,t,r,a){try{let{id:s}=e.query;if(!s)return t.status(400).json({error:"Template ID is required"});let o={...e.body};delete o.id,o.updated_by=r.id,o.updated_at=new Date().toISOString(),o.is_default&&await n.pR.from("receipt_templates").update({is_default:!1}).eq("is_default",!0).neq("id",s);let{data:i,error:l}=await n.pR.from("receipt_templates").update(o).eq("id",s).select().single();if(l)return console.error(`[${a}] Error updating receipt template:`,l),t.status(500).json({error:"Failed to update receipt template"});return console.log(`[${a}] Updated receipt template: ${i.name}`),t.status(200).json({template:i})}catch(e){return console.error(`[${a}] Error in handleUpdateTemplate:`,e),t.status(500).json({error:"Failed to update receipt template"})}}async function m(e,t,r,a){try{let{id:s}=e.query;if(!s)return t.status(400).json({error:"Template ID is required"});let{data:o}=await n.pR.from("receipt_templates").select("is_default, name").eq("id",s).single();if(o?.is_default)return t.status(400).json({error:"Cannot delete the default template"});let{error:i}=await n.pR.from("receipt_templates").update({is_active:!1,updated_by:r.id,updated_at:new Date().toISOString()}).eq("id",s);if(i)return console.error(`[${a}] Error deleting receipt template:`,i),t.status(500).json({error:"Failed to delete receipt template"});return console.log(`[${a}] Deleted receipt template: ${o?.name}`),t.status(200).json({success:!0})}catch(e){return console.error(`[${a}] Error in handleDeleteTemplate:`,e),t.status(500).json({error:"Failed to delete receipt template"})}}let _=(0,i.l)(a,"default"),f=(0,i.l)(a,"config"),h=new s.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/receipts",pathname:"/api/admin/receipts",bundlePath:"",filename:""},userland:a})},8456:(e,t,r)=>{r.d(t,{pR:()=>n});var a=r(2885);let s="https://ndlgbcsbidyhxbpqzgqp.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",i=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!s||!o)throw Error("Missing Supabase environment variables");(0,a.createClient)(s,o,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let n=(0,a.createClient)(s,i||o,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[2805],()=>r(5845));module.exports=a})();