"use strict";(()=>{var e={};e.id=1155,e.ids=[1155],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},3794:(e,s,t)=>{t.r(s),t.d(s,{config:()=>f,default:()=>m,routeModule:()=>c});var r={};t.r(r),t.d(r,{default:()=>g});var a=t(1802),i=t(7153),n=t(8781),o=t(7474),d=t(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,d.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function g(e,s){let t=Math.random().toString(36).substring(2,8);console.log(`[${t}] Staff training API called - ${e.method}`);try{let r=await (0,o.SA)(e);if(!r.success)return s.status(401).json({error:"Authentication required",message:r.message||"Authentication failed",requestId:t});let{user:a}=r;if(!a)return s.status(401).json({error:"User not found",requestId:t});if("GET"===e.method){let{staff_id:r,module_id:a}=e.query;if(r){let{data:e,error:a}=await l.from("staff_training_progress").select(`
            id,
            status,
            started_at,
            completed_at,
            score,
            attempts,
            notes,
            assigned_at,
            staff_training_modules!inner(
              id,
              name,
              description,
              category,
              is_required,
              duration_minutes,
              passing_score
            )
          `).eq("staff_id",r).order("assigned_at",{ascending:!1});if(a)return console.error(`[${t}] Database error:`,a),s.status(500).json({error:"Failed to fetch training progress",message:a.message,requestId:t});let i=e?.length||0,n=e?.filter(e=>"completed"===e.status).length||0,o=e?.filter(e=>e.staff_training_modules?.is_required).length||0,d=e?.filter(e=>e.staff_training_modules?.is_required&&"completed"===e.status).length||0;return s.status(200).json({progress:e||[],statistics:{total:i,completed:n,required:o,completedRequired:d,completionPercentage:i>0?Math.round(n/i*100):0,requiredCompletionPercentage:o>0?Math.round(d/o*100):0},requestId:t})}{let{data:e,error:r}=await l.from("staff_training_modules").select(`
            id,
            name,
            description,
            category,
            is_required,
            duration_minutes,
            content_url,
            passing_score,
            is_active,
            created_at
          `).eq("is_active",!0).order("category",{ascending:!0}).order("name",{ascending:!0});if(r)return console.error(`[${t}] Database error:`,r),s.status(500).json({error:"Failed to fetch training modules",message:r.message,requestId:t});return s.status(200).json({modules:e||[],requestId:t})}}if("POST"===e.method){let{action:r,staff_id:i,module_id:n,score:o,notes:d}=e.body;if(!r||!i)return s.status(400).json({error:"Missing required fields",message:"Action and staff ID are required",requestId:t});if("assign_module"===r){if(!n)return s.status(400).json({error:"Module ID required",message:"Module ID is required for assignment",requestId:t});let{data:e}=await l.from("staff_training_progress").select("id").eq("staff_id",i).eq("module_id",n).single();if(e)return s.status(409).json({error:"Module already assigned",message:"This training module is already assigned to the staff member",requestId:t});let{data:r,error:o}=await l.from("staff_training_progress").insert([{staff_id:i,module_id:n,status:"not_started",assigned_by:a.id,assigned_at:new Date().toISOString()}]).select(`
            id,
            status,
            assigned_at,
            staff_training_modules!inner(
              id,
              name,
              description,
              category,
              is_required,
              duration_minutes
            )
          `).single();if(o)return console.error(`[${t}] Error assigning module:`,o),s.status(500).json({error:"Failed to assign training module",message:o.message,requestId:t});return s.status(201).json({assignment:r,message:"Training module assigned successfully",requestId:t})}if("assign_all_required"===r){let{data:e}=await l.from("staff_training_modules").select("id").eq("is_required",!0).eq("is_active",!0);if(!e||0===e.length)return s.status(200).json({message:"No required modules to assign",requestId:t});let{data:r}=await l.from("staff_training_progress").select("module_id").eq("staff_id",i),n=r?.map(e=>e.module_id)||[],o=e.filter(e=>!n.includes(e.id));if(0===o.length)return s.status(200).json({message:"All required modules already assigned",requestId:t});let d=o.map(e=>({staff_id:i,module_id:e.id,status:"not_started",assigned_by:a.id,assigned_at:new Date().toISOString()})),{data:u,error:g}=await l.from("staff_training_progress").insert(d).select();if(g)return console.error(`[${t}] Error assigning modules:`,g),s.status(500).json({error:"Failed to assign required modules",message:g.message,requestId:t});return s.status(201).json({assignments:u,message:`${u?.length||0} required modules assigned successfully`,requestId:t})}if("start_training"===r){if(!n)return s.status(400).json({error:"Module ID required",message:"Module ID is required to start training",requestId:t});let{data:e,error:r}=await l.from("staff_training_progress").update({status:"in_progress",started_at:new Date().toISOString()}).eq("staff_id",i).eq("module_id",n).select().single();if(r)return console.error(`[${t}] Error starting training:`,r),s.status(500).json({error:"Failed to start training",message:r.message,requestId:t});return s.status(200).json({progress:e,message:"Training started successfully",requestId:t})}if("complete_training"===r){if(!n)return s.status(400).json({error:"Module ID required",message:"Module ID is required to complete training",requestId:t});let{data:e}=await l.from("staff_training_modules").select("passing_score").eq("id",n).single(),r=o&&e?.passing_score&&o>=e.passing_score?"completed":"failed",{data:a}=await l.from("staff_training_progress").select("attempts").eq("staff_id",i).eq("module_id",n).single(),{data:u,error:g}=await l.from("staff_training_progress").update({status:r,completed_at:new Date().toISOString(),score:o||null,notes:d||null,attempts:(a?.attempts||0)+1}).eq("staff_id",i).eq("module_id",n).select().single();if(g)return console.error(`[${t}] Error completing training:`,g),s.status(500).json({error:"Failed to complete training",message:g.message,requestId:t});return s.status(200).json({progress:u,message:`Training ${r} successfully`,requestId:t})}return s.status(400).json({error:"Invalid action",message:"Action must be assign_module, assign_all_required, start_training, or complete_training",requestId:t})}return s.status(405).json({error:"Method not allowed",requestId:t})}catch(e){return console.error(`[${t}] Unexpected error:`,e),s.status(500).json({error:"Internal server error",message:e instanceof Error?e.message:"Unknown error",requestId:t})}}let m=(0,n.l)(r,"default"),f=(0,n.l)(r,"config"),c=new a.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/staff/training",pathname:"/api/admin/staff/training",bundlePath:"",filename:""},userland:r})}};var s=require("../../../../webpack-api-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[2805],()=>t(3794));module.exports=r})();