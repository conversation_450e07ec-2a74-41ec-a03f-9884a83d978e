"use strict";(()=>{var e={};e.id=1036,e.ids=[1036],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},3478:(e,r,t)=>{t.r(r),t.d(r,{config:()=>p,default:()=>l,routeModule:()=>m});var s={};t.r(s),t.d(s,{default:()=>u});var a=t(1802),i=t(7153),n=t(8781),o=t(7474),d=t(6482);async function u(e,r){let t=`po-receive-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let{id:s}=e.query;if(console.log(`[${t}] Purchase order receiving API request:`,{method:e.method,purchaseOrderId:s,userAgent:e.headers["user-agent"]}),"POST"!==e.method)return r.status(405).json({error:"Method not allowed",requestId:t});if(!s||"string"!=typeof s)return r.status(400).json({error:"Invalid purchase order ID",requestId:t});let a=await (0,o.Wg)(e);if(!a.valid)return console.log(`[${t}] Authentication failed:`,a.error),r.status(401).json({error:"Unauthorized",requestId:t});if(!["DEV","Admin"].includes(a.user.role))return console.log(`[${t}] Insufficient permissions:`,a.user.role),r.status(403).json({error:"Insufficient permissions",requestId:t});return await c(e,r,t,s,a.user)}catch(e){return console.error(`[${t}] Purchase order receiving API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:t})}}async function c(e,r,t,s,a){try{let{receivedItems:i,actualDeliveryDate:n,notes:o}=e.body;if(!i||!Array.isArray(i)||0===i.length)return r.status(400).json({error:"Validation failed",message:"Received items array is required",requestId:t});let{data:u,error:c}=await d.supabaseAdmin.from("purchase_orders").select(`
        id,
        po_number,
        status,
        supplier_id,
        suppliers (name)
      `).eq("id",s).single();if(c){if("PGRST116"===c.code)return r.status(404).json({error:"Purchase order not found",requestId:t});throw c}if(!["confirmed","sent"].includes(u.status))return r.status(400).json({error:"Invalid purchase order status",message:"Purchase order must be confirmed or sent to receive items",requestId:t});let{data:l,error:p}=await d.supabaseAdmin.from("purchase_order_items").select("*").eq("purchase_order_id",s);if(p)throw p;let m=new Map(l.map(e=>[e.id,e])),h=[],v=0;for(let e of i){let{itemId:s,receivedQuantity:a}=e;if(!s||void 0===a||a<0)return r.status(400).json({error:"Validation failed",message:"Each received item must have itemId and non-negative receivedQuantity",requestId:t});let i=m.get(s);if(!i)return r.status(404).json({error:"Purchase order item not found",message:`Item ${s} not found in purchase order`,requestId:t});let n=i.received_quantity+parseInt(a);if(n>i.quantity)return r.status(400).json({error:"Validation failed",message:`Cannot receive more than ordered quantity for item ${s}`,requestId:t});h.push({...i,receivedQuantity:parseInt(a),newReceivedQuantity:n}),v+=parseInt(a)*i.unit_cost}let f={updatedItems:[],updatedInventory:[],updatedPurchaseOrder:null};try{for(let e of h)if(e.receivedQuantity>0){let{data:r,error:s}=await d.supabaseAdmin.from("purchase_order_items").update({received_quantity:e.newReceivedQuantity}).eq("id",e.id).select().single();if(s)throw s;if(f.updatedItems.push(r),e.inventory_id&&e.receivedQuantity>0){let{data:r,error:s}=await d.supabaseAdmin.from("inventory").update({quantity_on_hand:d.supabaseAdmin.sql`quantity_on_hand + ${e.receivedQuantity}`,updated_at:new Date().toISOString()}).eq("id",e.inventory_id).select().single();s?console.warn(`[${t}] Error updating inventory for item ${e.id}:`,s):f.updatedInventory.push(r)}}let{data:e,error:i}=await d.supabaseAdmin.from("purchase_order_items").select("quantity, received_quantity").eq("purchase_order_id",s);if(i)throw i;let c=e.every(e=>e.received_quantity>=e.quantity),l=c?"received":"confirmed",p={status:l,updated_at:new Date().toISOString()};n&&(p.actual_delivery_date=n),o&&(p.notes=o.trim());let{data:m,error:y}=await d.supabaseAdmin.from("purchase_orders").update(p).eq("id",s).select().single();if(y)throw y;return f.updatedPurchaseOrder=m,console.log(`[${t}] Purchase order receiving completed successfully:`,{id:u.id,poNumber:u.po_number,supplier:u.suppliers?.name,itemsReceived:f.updatedItems.length,inventoryUpdated:f.updatedInventory.length,totalValue:v.toFixed(2),newStatus:l,fullyReceived:c,receivedBy:a.email}),r.status(200).json({message:c?"Purchase order fully received":"Items received successfully",purchaseOrder:f.updatedPurchaseOrder,receivedItems:f.updatedItems,updatedInventory:f.updatedInventory,summary:{itemsReceived:f.updatedItems.length,inventoryUpdated:f.updatedInventory.length,totalValue:v,fullyReceived:c},requestId:t})}catch(e){throw console.error(`[${t}] Error during receiving transaction:`,e),e}}catch(e){throw console.error(`[${t}] Error receiving purchase order:`,e),e}}let l=(0,n.l)(s,"default"),p=(0,n.l)(s,"config"),m=new a.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/purchase-orders/[id]/receive",pathname:"/api/admin/purchase-orders/[id]/receive",bundlePath:"",filename:""},userland:s})},6482:(e,r,t)=>{t.r(r),t.d(r,{supabaseAdmin:()=>n});var s=t(2885);let a="https://ndlgbcsbidyhxbpqzgqp.supabase.co",i=process.env.SUPABASE_SERVICE_ROLE_KEY;a&&i||console.warn("Missing Supabase environment variables for admin client");let n=(0,s.createClient)(a,i||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",{auth:{autoRefreshToken:!1,persistSession:!1}})}};var r=require("../../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2805],()=>t(3478));module.exports=s})();