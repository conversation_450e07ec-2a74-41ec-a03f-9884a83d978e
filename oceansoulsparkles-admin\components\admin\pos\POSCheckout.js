import { useState } from 'react'
import PaymentMethodSelector from './PaymentMethodSelector'
import POSSquarePayment from './POSSquarePayment'
import POSSquareTerminal from './POSSquareTerminal'
import { generatePOSReceipt } from '@/lib/receipt-generator'
import styles from '@/styles/admin/POS.module.css'

/**
 * POSCheckout component - Complete checkout flow for POS bookings
 * Integrates customer info, payment methods, and booking creation
 */
export default function POSCheckout({ service, artist, tier, timeSlot, onBack, onComplete }) {
  const [checkoutStep, setCheckoutStep] = useState('customer') // customer, payment
  const [customerData, setCustomerData] = useState(null)
  const [paymentMethod, setPaymentMethod] = useState(null)
  const [paymentMethodData, setPaymentMethodData] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState(null)

  // Calculate total amount
  const totalAmount = tier?.price || 0

  const formatDuration = (minutes) => {
    if (!minutes) return ''
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`
    }
    return `${mins}m`
  }

  const handleCustomerData = (data) => {
    setCustomerData(data)
    setCheckoutStep('payment')
  }

  const handlePaymentMethodSelect = (method, methodData) => {
    setPaymentMethod(method)
    setPaymentMethodData(methodData)

    if (method === 'cash') {
      // For cash payments, process with the cash details from the form
      processCashPayment(methodData)
    }
    // For card and terminal payments, the respective Square components will handle the flow
  }

  const processCashPayment = async (methodData) => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create booking and payment record with cash details
      const result = await createBookingAndPayment('cash', {
        cashReceived: methodData?.cashReceived,
        changeAmount: methodData?.changeAmount,
        totalAmount: methodData?.totalAmount || totalAmount
      })

      if (result.success) {
        // Generate receipt for the transaction
        await generateTransactionReceipt(result, 'cash', methodData)
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to process cash payment')
      }
    } catch (err) {
      console.error('Cash payment error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentSuccess = async (paymentResult) => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create booking and payment record with Square transaction details
      const paymentMethodType = paymentResult.paymentDetails?.deviceId ? 'square_terminal' : 'card'
      const result = await createBookingAndPayment(paymentMethodType, paymentResult)

      if (result.success) {
        // Generate receipt for the transaction
        await generateTransactionReceipt(result, paymentMethodType, paymentResult)
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to record payment')
      }
    } catch (err) {
      console.error('Payment completion error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentError = (error) => {
    console.error('Square payment error:', error)
    setError(error.message || 'Card payment failed')
  }

  const generateTransactionReceipt = async (bookingResult, paymentMethod, paymentData) => {
    try {
      const receiptData = {
        receiptNumber: `POS-${Date.now()}`,
        customerName: customerData?.name || 'Walk-in Customer',
        customerEmail: customerData?.email,
        customerPhone: customerData?.phone,
        serviceName: service.name,
        tierName: tier.name,
        artistName: artist.name,
        startTime: timeSlot.time,
        duration: tier.duration,
        totalAmount: paymentData?.totalAmount || totalAmount,
        tipAmount: paymentData?.tipAmount || 0,
        paymentMethod: paymentMethod,
        notes: `POS Transaction - ${artist.name} - ${tier.name}`
      }

      const receiptResult = await generatePOSReceipt(receiptData)

      if (receiptResult.success) {
        console.log('Receipt generated successfully')
        // Store receipt HTML in booking result for potential display/printing
        bookingResult.receiptHtml = receiptResult.html
        bookingResult.receiptData = receiptData
      } else {
        console.warn('Failed to generate receipt:', receiptResult.error)
      }
    } catch (error) {
      console.error('Error generating receipt:', error)
      // Don't fail the transaction if receipt generation fails
    }
  }

  const createBookingAndPayment = async (paymentMethod, paymentDetails = null) => {
    try {
      console.log('🔄 Creating POS booking...')

      // Get authentication token from Supabase session (simple approach)
      const { supabase } = await import('@/lib/supabase')

      // Get current session without forcing refresh
      const { data: { session }, error } = await supabase.auth.getSession()

      console.log('Booking authentication check:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        hasToken: !!session?.access_token,
        userEmail: session?.user?.email,
        error: error?.message
      })

      if (!session?.access_token) {
        throw new Error('Authentication required. Please refresh the page and try again.')
      }

      const bookingData = {
        customer_name: customerData?.name || 'Walk-in Customer',
        customer_email: customerData?.email || '',
        customer_phone: customerData?.phone || '',
        service_id: service.id,
        service_name: service.name,
        artist_id: artist.id,
        artist_name: artist.name,
        tier_id: tier.id,
        tier_name: tier.name,
        duration: tier.duration,
        start_time: timeSlot.time,
        end_time: new Date(new Date(timeSlot.time).getTime() + (tier.duration * 60000)).toISOString(),
        total_amount: paymentMethodData?.totalAmount || totalAmount,
        status: 'completed',
        payment_method: paymentMethod,
        payment_status: 'completed',
        notes: `POS Booking - ${artist.name} - ${tier.name}${paymentMethodData?.tipAmount > 0 ? ` - Tip: $${paymentMethodData.tipAmount.toFixed(2)}` : ''}`,
        booking_type: 'pos',
        created_via: 'admin_pos',
        tip_amount: paymentMethodData?.tipAmount || 0,
        tip_method: paymentMethodData?.tipMethod || 'none',
        tip_percentage: paymentMethodData?.tipPercentage || 0
      }

      // Add payment-specific details
      if (paymentDetails) {
        if (paymentMethod === 'cash') {
          bookingData.cash_received = paymentDetails.cashReceived
          bookingData.change_amount = paymentDetails.changeAmount
        } else {
          bookingData.payment_transaction_id = paymentDetails.paymentId || paymentDetails.transactionId
          bookingData.processing_fee = paymentMethodData?.processingFee || 0
        }
      }

      console.log('Creating booking with data:', bookingData)

      const response = await fetch('/api/admin/pos/create-booking', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify(bookingData)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Booking creation failed:', response.status, errorText)
        throw new Error(`Failed to create booking: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ Booking created successfully:', result)

      return {
        success: true,
        booking: result.booking,
        payment: result.payment,
        message: 'Booking and payment processed successfully!'
      }

    } catch (error) {
      console.error('Error creating booking:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  return (
    <div className={styles.posCheckoutContainer}>
      <div className={styles.checkoutHeader}>
        <button onClick={onBack} className={styles.backButton}>
          ← Back
        </button>
        <h2>Complete Booking</h2>
      </div>

      {/* Booking Summary */}
      <div className={styles.bookingSummary}>
        <h3>Booking Details</h3>
        <div className={styles.summaryGrid}>
          <div className={styles.summaryItem}>
            <strong>Service:</strong> {service.name}
          </div>
          <div className={styles.summaryItem}>
            <strong>Artist:</strong> {artist.name}
          </div>
          <div className={styles.summaryItem}>
            <strong>Duration:</strong> {formatDuration(tier.duration)}
          </div>
          <div className={styles.summaryItem}>
            <strong>Price:</strong> ${tier.price?.toFixed(2)}
          </div>
          <div className={styles.summaryItem}>
            <strong>Date & Time:</strong> {new Date(timeSlot.time).toLocaleString()}
          </div>
        </div>
      </div>

      {error && (
        <div className={styles.errorAlert}>
          <span className={styles.errorIcon}>⚠️</span>
          <div className={styles.errorText}>{error}</div>
          <button onClick={() => setError(null)} className={styles.closeError}>×</button>
        </div>
      )}

      {/* Customer Information Step */}
      {checkoutStep === 'customer' && (
        <div className={styles.customerStep}>
          <h3>Customer Information</h3>
          <div className={styles.customerForm}>
            <CustomerInfoForm onSubmit={handleCustomerData} />
          </div>
        </div>
      )}

      {/* Payment Method Selection */}
      {checkoutStep === 'payment' && !paymentMethod && (
        <PaymentMethodSelector
          amount={totalAmount}
          customerName={customerData?.name || 'Walk-in Customer'}
          onMethodSelect={handlePaymentMethodSelect}
          onCancel={() => setCheckoutStep('customer')}
        />
      )}

      {/* Square Card Payment */}
      {checkoutStep === 'payment' && paymentMethod === 'square_payment' && (
        <POSSquarePayment
          amount={paymentMethodData?.totalAmount || totalAmount}
          currency="AUD"
          onSuccess={handleSquarePaymentSuccess}
          onError={handleSquarePaymentError}
          orderDetails={{
            service: service.name,
            tier: tier.name,
            customer: customerData?.name || 'Walk-in Customer',
            orderId: `pos_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
          }}
        />
      )}

      {/* Square Terminal Payment */}
      {checkoutStep === 'payment' && paymentMethod === 'square_terminal' && (
        <POSSquareTerminal
          amount={paymentMethodData?.totalAmount || totalAmount}
          currency="AUD"
          onSuccess={handleSquarePaymentSuccess}
          onError={handleSquarePaymentError}
          onCancel={() => setPaymentMethod(null)}
          orderDetails={{
            service: service.name,
            tier: tier.name,
            customer: customerData?.name || 'Walk-in Customer',
            orderId: `pos_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
          }}
        />
      )}

      {isProcessing && (
        <div className={styles.processingOverlay}>
          <div className={styles.processingContent}>
            <div className={styles.loadingSpinner}></div>
            <p>Processing booking and payment...</p>
          </div>
        </div>
      )}
    </div>
  )
}

// Customer Information Form Component
function CustomerInfoForm({ onSubmit }) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    notes: ''
  })

  const [isWalkIn, setIsWalkIn] = useState(false)

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (isWalkIn) {
      onSubmit({ name: 'Walk-in Customer', email: '', phone: '', notes: formData.notes })
    } else {
      if (!formData.name.trim()) {
        alert('Please enter customer name')
        return
      }
      onSubmit(formData)
    }
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <form onSubmit={handleSubmit} className={styles.customerForm}>
      <div className={styles.walkInToggle}>
        <label>
          <input
            type="checkbox"
            checked={isWalkIn}
            onChange={(e) => setIsWalkIn(e.target.checked)}
          />
          Walk-in Customer (no details required)
        </label>
      </div>

      {!isWalkIn && (
        <>
          <div className={styles.formGroup}>
            <label>Customer Name *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter customer name"
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label>Email</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <div className={styles.formGroup}>
            <label>Phone</label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="+61 XXX XXX XXX"
            />
          </div>
        </>
      )}

      <div className={styles.formGroup}>
        <label>Special Notes</label>
        <textarea
          value={formData.notes}
          onChange={(e) => handleInputChange('notes', e.target.value)}
          placeholder="Any special requirements or notes..."
          rows={3}
        />
      </div>

      <button type="submit" className={styles.continueButton}>
        Continue to Payment
      </button>
    </form>
  )
}
