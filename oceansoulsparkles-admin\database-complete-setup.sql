-- <PERSON> Soul Sparkles Complete Database Setup
-- This creates all necessary tables for the admin dashboard, POS, booking, and customer management

-- Ensure we can create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. ADMIN TABLES (Already created but ensuring they exist)
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHA<PERSON>(100) NOT NULL,
  role VARCHAR(20) NOT NULL CHECK (role IN ('DEV', 'Admin', 'Artist', 'Braider')),
  is_active BOOLEAN DEFAULT true,
  mfa_enabled BOOLEAN DEFAULT false,
  mfa_secret TEXT,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. CUSTOMERS TABLE
CREATE TABLE IF NOT EXISTS customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  name VARCHAR(200), -- Combined name field for POS compatibility
  email VARCHAR(255) UNIQUE,
  phone VARCHAR(20),
  date_of_birth DATE,
  address TEXT,
  emergency_contact_name VARCHAR(200),
  emergency_contact_phone VARCHAR(20),
  notes TEXT,
  created_via VARCHAR(50) DEFAULT 'admin', -- 'admin', 'pos', 'website'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. ARTIST PROFILES TABLE
CREATE TABLE IF NOT EXISTS artist_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_user_id UUID REFERENCES admin_users(id) ON DELETE CASCADE,
  name VARCHAR(200) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  specializations TEXT[], -- Array of specializations
  bio TEXT,
  hourly_rate DECIMAL(10,2),
  commission_rate DECIMAL(5,2) DEFAULT 30.00, -- Percentage
  is_active BOOLEAN DEFAULT true,
  is_available_today BOOLEAN DEFAULT true,
  max_daily_bookings INTEGER DEFAULT 8,
  rating DECIMAL(3,2) DEFAULT 5.00,
  total_bookings INTEGER DEFAULT 0,
  total_revenue DECIMAL(12,2) DEFAULT 0.00,
  joined_date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. SERVICES TABLE
CREATE TABLE IF NOT EXISTS services (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100), -- 'face_painting', 'hair_braiding', 'glitter_art', etc.
  base_price DECIMAL(10,2),
  duration INTEGER, -- minutes
  is_active BOOLEAN DEFAULT true,
  requires_consultation BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. SERVICE PRICING TIERS TABLE
CREATE TABLE IF NOT EXISTS service_pricing_tiers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL, -- 'Basic', 'Standard', 'Premium', etc.
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  duration INTEGER NOT NULL, -- minutes
  is_default BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. ARTIST AVAILABILITY TABLE
CREATE TABLE IF NOT EXISTS artist_availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  artist_id UUID REFERENCES artist_profiles(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL, -- 0 = Sunday, 1 = Monday, etc.
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  break_start_time TIME,
  break_end_time TIME,
  is_available BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. BOOKINGS TABLE
CREATE TABLE IF NOT EXISTS bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id),
  artist_id UUID REFERENCES artist_profiles(id),
  assigned_artist_id UUID REFERENCES artist_profiles(id),
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  booking_date DATE GENERATED ALWAYS AS (start_time::date) STORED,
  booking_time TIME GENERATED ALWAYS AS (start_time::time) STORED,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'completed', 'cancelled', 'no_show')),
  location VARCHAR(200) DEFAULT 'Studio',
  total_amount DECIMAL(10,2),
  notes TEXT,
  booking_source VARCHAR(50) DEFAULT 'admin', -- 'admin', 'pos', 'website', 'phone'
  pos_session_id VARCHAR(100), -- For POS bookings
  tier_name VARCHAR(100),
  tier_price DECIMAL(10,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. PAYMENTS TABLE
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'AUD',
  method VARCHAR(50) NOT NULL, -- 'cash', 'card', 'square_terminal', 'square_online'
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  transaction_id VARCHAR(255), -- Square or other payment processor ID
  processing_fee DECIMAL(10,2) DEFAULT 0.00,
  cash_received DECIMAL(10,2), -- For cash payments
  change_amount DECIMAL(10,2), -- For cash payments
  tip_amount DECIMAL(10,2) DEFAULT 0.00, -- Tip amount
  tip_method VARCHAR(20) DEFAULT 'none' CHECK (tip_method IN ('none', 'cash', 'card', 'split')), -- How tip was paid
  tip_percentage DECIMAL(5,2), -- Tip percentage if calculated
  receipt_data JSONB, -- Store receipt information
  payment_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. INVENTORY TABLE (For POS)
CREATE TABLE IF NOT EXISTS inventory (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  sku VARCHAR(100) UNIQUE,
  price DECIMAL(10,2),
  cost DECIMAL(10,2),
  quantity_on_hand INTEGER DEFAULT 0,
  min_stock_level INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  requires_artist BOOLEAN DEFAULT false, -- True for services that need artist assignment
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. USER PROFILES TABLE (For customer portal integration)
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200),
  email VARCHAR(255),
  phone VARCHAR(20),
  role VARCHAR(50) DEFAULT 'customer',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 11. AUDIT LOGS TABLE (already created but ensuring it exists)
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  action VARCHAR(100) NOT NULL,
  user_id UUID REFERENCES admin_users(id),
  user_role VARCHAR(20),
  email VARCHAR(255),
  ip_address INET,
  path TEXT,
  resource VARCHAR(100),
  resource_id TEXT,
  old_values JSONB,
  new_values JSONB,
  reason TEXT,
  error TEXT,
  metadata JSONB,
  severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 12. TIPS TABLE
CREATE TABLE IF NOT EXISTS tips (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID REFERENCES payments(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  artist_id UUID REFERENCES artist_profiles(id),
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'AUD',
  tip_method VARCHAR(20) NOT NULL CHECK (tip_method IN ('cash', 'card', 'split')),
  percentage DECIMAL(5,2), -- Tip percentage if calculated
  distribution_status VARCHAR(20) DEFAULT 'pending' CHECK (distribution_status IN ('pending', 'distributed', 'held')),
  distribution_date TIMESTAMP WITH TIME ZONE,
  distribution_method VARCHAR(20) CHECK (distribution_method IN ('cash', 'bank_transfer', 'payroll')),
  notes TEXT,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 13. SYSTEM SETTINGS TABLE
CREATE TABLE IF NOT EXISTS system_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category VARCHAR(50) NOT NULL,
  key VARCHAR(100) NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(category, key)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);

CREATE INDEX IF NOT EXISTS idx_artist_profiles_active ON artist_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_artist_profiles_available ON artist_profiles(is_available_today);

CREATE INDEX IF NOT EXISTS idx_services_category ON services(category);
CREATE INDEX IF NOT EXISTS idx_services_active ON services(is_active);

CREATE INDEX IF NOT EXISTS idx_bookings_customer_id ON bookings(customer_id);
CREATE INDEX IF NOT EXISTS idx_bookings_artist_id ON bookings(artist_id);
CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(booking_date);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_start_time ON bookings(start_time);

CREATE INDEX IF NOT EXISTS idx_payments_booking_id ON payments(booking_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_method ON payments(method);

CREATE INDEX IF NOT EXISTS idx_artist_availability_artist ON artist_availability(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_availability_day ON artist_availability(day_of_week);

-- Audit logs indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_severity ON audit_logs(severity);

-- Tips indexes for performance
CREATE INDEX IF NOT EXISTS idx_tips_payment_id ON tips(payment_id);
CREATE INDEX IF NOT EXISTS idx_tips_booking_id ON tips(booking_id);
CREATE INDEX IF NOT EXISTS idx_tips_artist_id ON tips(artist_id);
CREATE INDEX IF NOT EXISTS idx_tips_distribution_status ON tips(distribution_status);
CREATE INDEX IF NOT EXISTS idx_tips_created_at ON tips(created_at);

-- System settings indexes
CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(key);
CREATE INDEX IF NOT EXISTS idx_system_settings_category_key ON system_settings(category, key);

-- Enable Row Level Security
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE artist_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_pricing_tiers ENABLE ROW LEVEL SECURITY;
ALTER TABLE artist_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tips ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies (Allow admin access to everything)
CREATE POLICY IF NOT EXISTS "Admin full access customers" ON customers FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access artist_profiles" ON artist_profiles FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access services" ON services FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access service_pricing_tiers" ON service_pricing_tiers FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access artist_availability" ON artist_availability FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access bookings" ON bookings FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access payments" ON payments FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access inventory" ON inventory FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access user_profiles" ON user_profiles FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access tips" ON tips FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access system_settings" ON system_settings FOR ALL USING (true);

-- 16. EMAIL TEMPLATES TABLE (For customer communication)
CREATE TABLE IF NOT EXISTS email_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL UNIQUE,
  type VARCHAR(50) NOT NULL, -- 'booking_confirmation', 'booking_reminder', 'booking_cancellation', 'payment_receipt', 'staff_notification', 'custom'
  subject VARCHAR(200) NOT NULL,
  html_content TEXT NOT NULL,
  text_content TEXT,
  variables JSONB, -- Available template variables
  is_active BOOLEAN DEFAULT true,
  is_default BOOLEAN DEFAULT false,
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for email templates
CREATE INDEX IF NOT EXISTS idx_email_templates_type ON email_templates(type);
CREATE INDEX IF NOT EXISTS idx_email_templates_is_active ON email_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_email_templates_is_default ON email_templates(is_default);

-- 16B. SMS TEMPLATES TABLE (For SMS message templates)
CREATE TABLE IF NOT EXISTS sms_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template TEXT NOT NULL,
  variables JSONB DEFAULT '[]', -- Available template variables
  category VARCHAR(50) DEFAULT 'custom',
  is_active BOOLEAN DEFAULT true,
  is_default BOOLEAN DEFAULT false,
  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for SMS templates
CREATE INDEX IF NOT EXISTS idx_sms_templates_type ON sms_templates(type);
CREATE INDEX IF NOT EXISTS idx_sms_templates_category ON sms_templates(category);
CREATE INDEX IF NOT EXISTS idx_sms_templates_is_active ON sms_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_sms_templates_is_default ON sms_templates(is_default);

-- 17. CUSTOMER COMMUNICATIONS TABLE (For tracking sent communications)
CREATE TABLE IF NOT EXISTS customer_communications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
  template_id UUID REFERENCES email_templates(id) ON DELETE SET NULL,
  communication_type VARCHAR(20) NOT NULL, -- 'email', 'sms'
  recipient VARCHAR(255) NOT NULL, -- email address or phone number
  subject VARCHAR(200),
  content TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'delivered', 'opened'
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  opened_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  external_id VARCHAR(100), -- ID from email/SMS service provider
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for customer communications
CREATE INDEX IF NOT EXISTS idx_customer_communications_customer_id ON customer_communications(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_communications_booking_id ON customer_communications(booking_id);
CREATE INDEX IF NOT EXISTS idx_customer_communications_type ON customer_communications(communication_type);
CREATE INDEX IF NOT EXISTS idx_customer_communications_status ON customer_communications(status);
CREATE INDEX IF NOT EXISTS idx_customer_communications_created_at ON customer_communications(created_at);

-- 18. CUSTOMER FEEDBACK TABLE (For collecting customer reviews and feedback)
CREATE TABLE IF NOT EXISTS customer_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  artist_id UUID REFERENCES artist_profiles(id) ON DELETE SET NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  service_rating INTEGER CHECK (service_rating >= 1 AND service_rating <= 5),
  cleanliness_rating INTEGER CHECK (cleanliness_rating >= 1 AND cleanliness_rating <= 5),
  timeliness_rating INTEGER CHECK (timeliness_rating >= 1 AND timeliness_rating <= 5),
  overall_experience_rating INTEGER CHECK (overall_experience_rating >= 1 AND overall_experience_rating <= 5),
  feedback_text TEXT,
  would_recommend BOOLEAN,
  improvement_suggestions TEXT,
  is_public BOOLEAN DEFAULT false, -- Whether feedback can be displayed publicly
  response_text TEXT, -- Admin response to feedback
  responded_by UUID REFERENCES admin_users(id),
  responded_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for customer feedback
CREATE INDEX IF NOT EXISTS idx_customer_feedback_customer_id ON customer_feedback(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_feedback_booking_id ON customer_feedback(booking_id);
CREATE INDEX IF NOT EXISTS idx_customer_feedback_artist_id ON customer_feedback(artist_id);
CREATE INDEX IF NOT EXISTS idx_customer_feedback_rating ON customer_feedback(rating);
CREATE INDEX IF NOT EXISTS idx_customer_feedback_is_public ON customer_feedback(is_public);
CREATE INDEX IF NOT EXISTS idx_customer_feedback_created_at ON customer_feedback(created_at);

-- Enable RLS for new tables
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE sms_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_feedback ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies for new tables
CREATE POLICY IF NOT EXISTS "Admin full access email_templates" ON email_templates FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access sms_templates" ON sms_templates FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access customer_communications" ON customer_communications FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access customer_feedback" ON customer_feedback FOR ALL USING (true);

-- 19. STAFF TRAINING MODULES TABLE (For tracking training requirements)
CREATE TABLE IF NOT EXISTS staff_training_modules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL, -- 'safety', 'technical', 'customer_service', 'compliance'
  is_required BOOLEAN DEFAULT true,
  duration_minutes INTEGER, -- Estimated completion time
  content_url TEXT, -- Link to training materials
  quiz_questions JSONB, -- Optional quiz questions
  passing_score INTEGER DEFAULT 80, -- Minimum score to pass
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 20. STAFF TRAINING PROGRESS TABLE (For tracking individual progress)
CREATE TABLE IF NOT EXISTS staff_training_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  staff_id UUID REFERENCES admin_users(id) ON DELETE CASCADE,
  module_id UUID REFERENCES staff_training_modules(id) ON DELETE CASCADE,
  status VARCHAR(20) DEFAULT 'not_started', -- 'not_started', 'in_progress', 'completed', 'failed'
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  score INTEGER, -- Quiz score if applicable
  attempts INTEGER DEFAULT 0,
  notes TEXT,
  assigned_by UUID REFERENCES admin_users(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(staff_id, module_id)
);

-- 21. STAFF ONBOARDING CHECKLIST TABLE (For systematic onboarding)
CREATE TABLE IF NOT EXISTS staff_onboarding_checklist (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  staff_id UUID REFERENCES admin_users(id) ON DELETE CASCADE,
  checklist_item VARCHAR(200) NOT NULL,
  category VARCHAR(50) NOT NULL, -- 'documentation', 'training', 'equipment', 'access'
  description TEXT,
  is_required BOOLEAN DEFAULT true,
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMP WITH TIME ZONE,
  completed_by UUID REFERENCES admin_users(id),
  due_date DATE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 22. STAFF PERFORMANCE METRICS TABLE (For tracking performance)
CREATE TABLE IF NOT EXISTS staff_performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  staff_id UUID REFERENCES admin_users(id) ON DELETE CASCADE,
  metric_date DATE NOT NULL,
  total_bookings INTEGER DEFAULT 0,
  completed_bookings INTEGER DEFAULT 0,
  cancelled_bookings INTEGER DEFAULT 0,
  total_revenue DECIMAL(12,2) DEFAULT 0.00,
  total_tips DECIMAL(10,2) DEFAULT 0.00,
  average_rating DECIMAL(3,2),
  customer_feedback_count INTEGER DEFAULT 0,
  hours_worked DECIMAL(5,2) DEFAULT 0.00,
  punctuality_score DECIMAL(3,2), -- Based on on-time arrivals
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(staff_id, metric_date)
);

-- 23. STAFF CERTIFICATIONS TABLE (For professional certifications)
CREATE TABLE IF NOT EXISTS staff_certifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  staff_id UUID REFERENCES admin_users(id) ON DELETE CASCADE,
  certification_name VARCHAR(200) NOT NULL,
  issuing_organization VARCHAR(200),
  certification_number VARCHAR(100),
  issue_date DATE,
  expiry_date DATE,
  status VARCHAR(20) DEFAULT 'active', -- 'active', 'expired', 'suspended'
  document_url TEXT, -- Link to certificate document
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for new tables
CREATE INDEX IF NOT EXISTS idx_staff_training_modules_category ON staff_training_modules(category);
CREATE INDEX IF NOT EXISTS idx_staff_training_modules_is_active ON staff_training_modules(is_active);
CREATE INDEX IF NOT EXISTS idx_staff_training_progress_staff_id ON staff_training_progress(staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_training_progress_status ON staff_training_progress(status);
CREATE INDEX IF NOT EXISTS idx_staff_onboarding_checklist_staff_id ON staff_onboarding_checklist(staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_onboarding_checklist_category ON staff_onboarding_checklist(category);
CREATE INDEX IF NOT EXISTS idx_staff_performance_metrics_staff_id ON staff_performance_metrics(staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_performance_metrics_date ON staff_performance_metrics(metric_date);
CREATE INDEX IF NOT EXISTS idx_staff_certifications_staff_id ON staff_certifications(staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_certifications_expiry_date ON staff_certifications(expiry_date);

-- Enable RLS for new tables
ALTER TABLE staff_training_modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_training_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_onboarding_checklist ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_certifications ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies for new tables
CREATE POLICY IF NOT EXISTS "Admin full access staff_training_modules" ON staff_training_modules FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access staff_training_progress" ON staff_training_progress FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access staff_onboarding_checklist" ON staff_onboarding_checklist FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access staff_performance_metrics" ON staff_performance_metrics FOR ALL USING (true);
CREATE POLICY IF NOT EXISTS "Admin full access staff_certifications" ON staff_certifications FOR ALL USING (true);

-- Insert sample data for testing
-- Sample Services
INSERT INTO services (id, name, description, category, base_price, duration, is_active) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Basic Face Paint', 'Simple face painting designs', 'face_painting', 25.00, 30, true),
('550e8400-e29b-41d4-a716-446655440002', 'Festival Face Paint', 'Complex festival-style face painting', 'face_painting', 45.00, 60, true),
('550e8400-e29b-41d4-a716-446655440003', 'Hair Braiding - Simple', 'Basic braiding styles', 'hair_braiding', 35.00, 45, true),
('550e8400-e29b-41d4-a716-446655440004', 'Hair Braiding - Complex', 'Intricate braiding designs', 'hair_braiding', 65.00, 90, true),
('550e8400-e29b-41d4-a716-446655440005', 'Glitter Art Design', 'Glitter body art and designs', 'glitter_art', 30.00, 40, true)
ON CONFLICT (id) DO NOTHING;

-- Sample Service Pricing Tiers
INSERT INTO service_pricing_tiers (service_id, name, description, price, duration, is_default, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Quick Design', '15-minute simple design', 15.00, 15, false, 1),
('550e8400-e29b-41d4-a716-446655440001', 'Standard', '30-minute standard design', 25.00, 30, true, 2),
('550e8400-e29b-41d4-a716-446655440001', 'Detailed', '45-minute detailed design', 35.00, 45, false, 3),

('550e8400-e29b-41d4-a716-446655440002', 'Basic Festival', '45-minute festival design', 40.00, 45, false, 1),
('550e8400-e29b-41d4-a716-446655440002', 'Standard Festival', '60-minute festival design', 50.00, 60, true, 2),
('550e8400-e29b-41d4-a716-446655440002', 'Premium Festival', '90-minute elaborate design', 75.00, 90, false, 3),

('550e8400-e29b-41d4-a716-446655440003', 'Simple Style', '30-minute basic braids', 25.00, 30, false, 1),
('550e8400-e29b-41d4-a716-446655440003', 'Standard Style', '45-minute standard braids', 35.00, 45, true, 2),
('550e8400-e29b-41d4-a716-446655440003', 'Complex Style', '60-minute intricate braids', 50.00, 60, false, 3);

-- Sample Artist Profiles
INSERT INTO artist_profiles (id, name, email, phone, specializations, bio, hourly_rate, is_active, is_available_today, rating, total_bookings, total_revenue) VALUES
('650e8400-e29b-41d4-a716-446655440001', 'Emma Wilson', '<EMAIL>', '+61-400-111-222', ARRAY['face_painting', 'glitter_art'], 'Specialist in festival and event face painting', 45.00, true, true, 4.8, 156, 7800.00),
('650e8400-e29b-41d4-a716-446655440002', 'Lisa Brown', '<EMAIL>', '+61-400-333-444', ARRAY['hair_braiding', 'face_painting'], 'Expert in complex hair braiding and styling', 50.00, true, true, 4.9, 203, 12150.00),
('650e8400-e29b-41d4-a716-446655440003', 'Sophie Taylor', '<EMAIL>', '+61-400-555-666', ARRAY['glitter_art', 'body_art'], 'Creative glitter and body art specialist', 40.00, true, true, 4.7, 98, 4900.00),
('650e8400-e29b-41d4-a716-446655440004', 'Maya Chen', '<EMAIL>', '+61-400-777-888', ARRAY['face_painting', 'hair_braiding'], 'Versatile artist with multicultural techniques', 48.00, true, false, 4.6, 87, 4176.00)
ON CONFLICT (id) DO NOTHING;

-- Sample Artist Availability (Monday to Saturday, 9 AM to 5 PM)
INSERT INTO artist_availability (artist_id, day_of_week, start_time, end_time, break_start_time, break_end_time, is_available) VALUES
-- Emma Wilson (Mon-Sat)
('650e8400-e29b-41d4-a716-446655440001', 1, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 2, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 3, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 4, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 5, '09:00:00', '17:00:00', '12:00:00', '13:00:00', true),
('650e8400-e29b-41d4-a716-446655440001', 6, '10:00:00', '16:00:00', NULL, NULL, true),

-- Lisa Brown (Mon-Sat)
('650e8400-e29b-41d4-a716-446655440002', 1, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 2, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 3, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 4, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 5, '08:30:00', '16:30:00', '12:30:00', '13:30:00', true),
('650e8400-e29b-41d4-a716-446655440002', 6, '09:00:00', '15:00:00', NULL, NULL, true),

-- Sophie Taylor (Tue-Sat)
('650e8400-e29b-41d4-a716-446655440003', 2, '10:00:00', '18:00:00', '13:00:00', '14:00:00', true),
('650e8400-e29b-41d4-a716-446655440003', 3, '10:00:00', '18:00:00', '13:00:00', '14:00:00', true),
('650e8400-e29b-41d4-a716-446655440003', 4, '10:00:00', '18:00:00', '13:00:00', '14:00:00', true),
('650e8400-e29b-41d4-a716-446655440003', 5, '10:00:00', '18:00:00', '13:00:00', '14:00:00', true),
('650e8400-e29b-41d4-a716-446655440003', 6, '10:00:00', '16:00:00', NULL, NULL, true);

-- Sample Customers
INSERT INTO customers (id, first_name, last_name, name, email, phone, notes, created_via) VALUES
('750e8400-e29b-41d4-a716-446655440001', 'Sarah', 'Johnson', 'Sarah Johnson', '<EMAIL>', '+61-400-100-001', 'Regular customer, prefers Emma for face painting', 'admin'),
('750e8400-e29b-41d4-a716-446655440002', 'Mike', 'Chen', 'Mike Chen', '<EMAIL>', '+61-400-100-002', 'Event organizer, books multiple services', 'admin'),
('750e8400-e29b-41d4-a716-446655440003', 'Emma', 'Davis', 'Emma Davis', '<EMAIL>', '+61-400-100-003', 'Mother of three, regular family bookings', 'admin')
ON CONFLICT (id) DO NOTHING;

-- Sample Bookings (some in the future for testing)
INSERT INTO bookings (id, customer_id, service_id, artist_id, assigned_artist_id, start_time, end_time, status, total_amount, notes, booking_source) VALUES
('850e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001', '2025-01-16 14:00:00+00', '2025-01-16 15:00:00+00', 'confirmed', 50.00, 'Birthday party face painting', 'admin'),
('850e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440002', '2025-01-17 10:00:00+00', '2025-01-17 11:30:00+00', 'confirmed', 65.00, 'Wedding party hair braiding', 'admin'),
('850e8400-e29b-41d4-a716-446655440003', '750e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440005', '650e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440003', '2025-01-18 15:30:00+00', '2025-01-18 16:10:00+00', 'pending', 30.00, 'School event glitter art', 'admin')
ON CONFLICT (id) DO NOTHING;

-- Sample Payments
INSERT INTO payments (booking_id, amount, currency, method, status, transaction_id) VALUES
('850e8400-e29b-41d4-a716-446655440001', 50.00, 'AUD', 'card', 'completed', 'sq_txn_001'),
('850e8400-e29b-41d4-a716-446655440002', 65.00, 'AUD', 'cash', 'completed', NULL)
ON CONFLICT DO NOTHING;

-- Sample System Settings
INSERT INTO system_settings (category, key, value, description) VALUES
('general', 'businessName', 'Ocean Soul Sparkles', 'Business name displayed throughout the application'),
('general', 'businessEmail', '<EMAIL>', 'Primary business email address'),
('general', 'businessPhone', '+61 XXX XXX XXX', 'Primary business phone number'),
('general', 'businessAddress', 'Australia', 'Business address'),
('general', 'timezone', 'Australia/Sydney', 'Business timezone'),
('general', 'currency', 'AUD', 'Default currency for transactions'),

('booking', 'defaultBookingDuration', '60', 'Default booking duration in minutes'),
('booking', 'advanceBookingDays', '30', 'How many days in advance customers can book'),
('booking', 'cancellationHours', '24', 'Minimum hours before booking for cancellation'),
('booking', 'autoConfirmBookings', 'true', 'Automatically confirm new bookings'),
('booking', 'requireDeposit', 'false', 'Require deposit for bookings'),
('booking', 'depositPercentage', '20', 'Deposit percentage if required'),

('payment', 'squareEnabled', 'true', 'Enable Square payment processing'),
('payment', 'squareEnvironment', 'sandbox', 'Square environment (sandbox/production)'),
('payment', 'cashEnabled', 'true', 'Accept cash payments'),
('payment', 'cardEnabled', 'true', 'Accept card payments'),
('payment', 'allowPartialPayments', 'true', 'Allow partial payments'),
('payment', 'autoProcessRefunds', 'false', 'Automatically process refunds'),

-- Notification settings with comprehensive feature flags
('notifications', 'emailNotifications', 'true', 'Master toggle for email notifications'),
('notifications', 'smsNotifications', 'false', 'Master toggle for SMS notifications'),
('notifications', 'pushNotifications', 'false', 'Master toggle for push notifications'),

-- Email notification types
('notifications', 'emailBookingConfirmation', 'true', 'Send booking confirmation emails'),
('notifications', 'emailBookingReminder', 'true', 'Send booking reminder emails'),
('notifications', 'emailBookingCancellation', 'true', 'Send booking cancellation emails'),
('notifications', 'emailPaymentReceipt', 'true', 'Send payment receipt emails'),
('notifications', 'emailStaffNotification', 'true', 'Send staff notification emails'),
('notifications', 'emailLowInventoryAlert', 'true', 'Send low inventory alert emails'),
('notifications', 'emailPromotional', 'true', 'Send promotional emails'),

-- SMS notification types
('notifications', 'smsBookingConfirmation', 'false', 'Send booking confirmation SMS'),
('notifications', 'smsBookingReminder', 'false', 'Send booking reminder SMS'),
('notifications', 'smsBookingCancellation', 'false', 'Send booking cancellation SMS'),
('notifications', 'smsPaymentReceipt', 'false', 'Send payment receipt SMS'),
('notifications', 'smsStaffNotification', 'false', 'Send staff notification SMS'),
('notifications', 'smsPromotional', 'false', 'Send promotional SMS'),

-- Push notification types (for future)
('notifications', 'pushBookingConfirmation', 'false', 'Send booking confirmation push notifications'),
('notifications', 'pushBookingReminder', 'false', 'Send booking reminder push notifications'),
('notifications', 'pushBookingCancellation', 'false', 'Send booking cancellation push notifications'),
('notifications', 'pushStaffNotification', 'false', 'Send staff notification push notifications'),

-- General notification settings
('notifications', 'bookingReminders', 'true', 'Send booking reminders'),
('notifications', 'reminderHours', '24', 'Hours before booking to send reminder'),
('notifications', 'adminNotifications', 'true', 'Send notifications to admin'),
('notifications', 'customerNotifications', 'true', 'Send notifications to customers'),

-- Fallback behavior
('notifications', 'emailFallbackWhenSMSFails', 'true', 'Send email when SMS delivery fails'),
('notifications', 'smsFallbackWhenEmailFails', 'false', 'Send SMS when email delivery fails'),

('security', 'sessionTimeout', '1800', 'Session timeout in seconds'),
('security', 'adminSessionTimeout', '1800', 'Admin session timeout in seconds'),
('security', 'maxLoginAttempts', '5', 'Maximum login attempts before lockout'),
('security', 'lockoutDuration', '900', 'Lockout duration in seconds'),
('security', 'requireMFA', 'false', 'Require multi-factor authentication'),
('security', 'ipRestrictions', 'false', 'Enable IP address restrictions'),

('tips', 'enableTips', 'true', 'Enable tip functionality in POS'),
('tips', 'defaultTipPercentages', '15,18,20,25', 'Default tip percentage options'),
('tips', 'customTipAllowed', 'true', 'Allow custom tip amounts'),
('tips', 'tipOnCardPayments', 'true', 'Allow tips on card payments'),
('tips', 'tipOnCashPayments', 'true', 'Allow tips on cash payments'),
('tips', 'tipDistributionMethod', 'manual', 'How tips are distributed (manual/automatic)'),
('tips', 'tipDistributionFrequency', 'daily', 'How often tips are distributed (daily/weekly/monthly)'),
('tips', 'minimumTipAmount', '1.00', 'Minimum tip amount allowed'),
('tips', 'maximumTipPercentage', '50', 'Maximum tip percentage allowed')
ON CONFLICT (category, key) DO NOTHING;

-- Create development admin user if not exists
INSERT INTO admin_users (email, password_hash, first_name, last_name, role, is_active, mfa_enabled)
VALUES (
  '<EMAIL>',
  '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtGtJbPiOy', -- DevPassword123!
  'Development',
  'Admin',
  'DEV',
  true,
  false
)
ON CONFLICT (email) DO NOTHING;

-- Sample Email Templates
INSERT INTO email_templates (name, type, subject, html_content, variables, is_active, is_default) VALUES
(
  'Default Booking Confirmation',
  'booking_confirmation',
  'Booking Confirmation - {{serviceName}}',
  '<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Booking Confirmation</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
  <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
      <h1 style="margin: 0;">Ocean Soul Sparkles</h1>
      <p style="margin: 10px 0 0 0;">Booking Confirmation</p>
    </div>
    <div style="background: white; padding: 30px; border: 1px solid #e5e7eb;">
      <h2>Dear {{customerName}},</h2>
      <p>Your booking has been confirmed! Here are the details:</p>

      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="margin-top: 0;">Booking Details</h3>
        <p><strong>Service:</strong> {{serviceName}}</p>
        <p><strong>Artist:</strong> {{artistName}}</p>
        <p><strong>Date:</strong> {{date}}</p>
        <p><strong>Time:</strong> {{time}}</p>
        <p><strong>Duration:</strong> {{duration}} minutes</p>
        <p><strong>Location:</strong> {{location}}</p>
        <p><strong>Total Amount:</strong> ${{totalAmount}}</p>
      </div>

      <p>We look forward to seeing you! If you need to make any changes, please contact us at least 24 hours before your appointment.</p>

      <div style="text-align: center; margin: 30px 0;">
        <a href="{{websiteUrl}}" style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Visit Our Website</a>
      </div>

      <p style="color: #666; font-size: 14px;">Thank you for choosing Ocean Soul Sparkles!</p>
    </div>
  </div>
</body>
</html>',
  '["customerName", "serviceName", "artistName", "date", "time", "duration", "location", "totalAmount", "websiteUrl"]',
  true,
  true
),
(
  'Default Booking Reminder',
  'booking_reminder',
  'Appointment Reminder - Tomorrow at {{time}}',
  '<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Appointment Reminder</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
  <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
      <h1 style="margin: 0;">Ocean Soul Sparkles</h1>
      <p style="margin: 10px 0 0 0;">Appointment Reminder</p>
    </div>
    <div style="background: white; padding: 30px; border: 1px solid #e5e7eb;">
      <h2>Hi {{customerName}}!</h2>
      <p>This is a friendly reminder about your upcoming appointment:</p>

      <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
        <h3 style="margin-top: 0; color: #92400e;">Tomorrow''s Appointment</h3>
        <p><strong>Service:</strong> {{serviceName}}</p>
        <p><strong>Artist:</strong> {{artistName}}</p>
        <p><strong>Time:</strong> {{time}}</p>
        <p><strong>Duration:</strong> {{duration}} minutes</p>
        <p><strong>Location:</strong> {{location}}</p>
      </div>

      <p>Please arrive 10 minutes early to ensure we can start on time. If you need to reschedule or cancel, please let us know as soon as possible.</p>

      <p style="color: #666; font-size: 14px;">We can''t wait to see you tomorrow!</p>
    </div>
  </div>
</body>
</html>',
  '["customerName", "serviceName", "artistName", "time", "duration", "location"]',
  true,
  true
)
ON CONFLICT (name) DO NOTHING;

-- Sample SMS Templates
INSERT INTO sms_templates (type, name, description, template, variables, category, is_active, is_default) VALUES
(
  'booking_confirmation',
  'Booking Confirmation SMS',
  'SMS sent when a booking is confirmed',
  'Hi {{customerName}}! Your appointment for {{serviceName}} is confirmed for {{date}} at {{time}}. Location: Ocean Soul Sparkles. Questions? Reply to this message.',
  '["customerName", "serviceName", "date", "time"]',
  'booking',
  true,
  true
),
(
  'booking_reminder',
  'Booking Reminder SMS',
  'SMS sent before appointment (24h default)',
  'Reminder: Your appointment for {{serviceName}} is tomorrow at {{time}}. See you at Ocean Soul Sparkles! Reply CONFIRM to confirm or CANCEL to reschedule.',
  '["serviceName", "time"]',
  'booking',
  true,
  true
),
(
  'booking_cancellation',
  'Booking Cancellation SMS',
  'SMS sent when booking is cancelled',
  'Your appointment for {{serviceName}} on {{date}} at {{time}} has been cancelled. To reschedule, please contact Ocean Soul Sparkles. Thank you!',
  '["serviceName", "date", "time"]',
  'booking',
  true,
  true
),
(
  'payment_receipt',
  'Payment Receipt SMS',
  'SMS sent after successful payment',
  'Payment received! ${{amount}} for {{serviceName}}. Receipt #{{receiptNumber}}. Thank you for choosing Ocean Soul Sparkles!',
  '["amount", "serviceName", "receiptNumber"]',
  'payment',
  true,
  true
),
(
  'staff_notification',
  'Staff Notification SMS',
  'SMS sent to staff for notifications',
  'Staff notification: {{message}}. Check the admin portal for details. - Ocean Soul Sparkles',
  '["message"]',
  'staff',
  true,
  true
),
(
  'promotional',
  'Promotional SMS',
  'SMS sent for promotions and special offers',
  '{{title}} at Ocean Soul Sparkles! {{description}} Valid until {{expiryDate}}. Book now!',
  '["title", "description", "expiryDate"]',
  'marketing',
  true,
  true
)
ON CONFLICT (type) DO NOTHING;

-- Sample Staff Training Modules
INSERT INTO staff_training_modules (name, description, category, is_required, duration_minutes, passing_score, is_active) VALUES
('Health & Safety Basics', 'Basic health and safety protocols for salon environment', 'safety', true, 45, 80, true),
('Customer Service Excellence', 'Professional customer service standards and communication', 'customer_service', true, 60, 85, true),
('Hair Braiding Fundamentals', 'Basic techniques for hair braiding and styling', 'technical', true, 120, 90, true),
('Face Painting Techniques', 'Introduction to face painting and glitter application', 'technical', false, 90, 85, true),
('POS System Training', 'How to use the point-of-sale system for transactions', 'technical', true, 30, 80, true),
('Hygiene and Sanitation', 'Proper hygiene and sanitation procedures', 'safety', true, 40, 90, true),
('Booking System Management', 'Managing appointments and customer bookings', 'technical', true, 35, 80, true),
('Emergency Procedures', 'Emergency response and first aid basics', 'safety', true, 50, 85, true)
ON CONFLICT (name) DO NOTHING;

-- Sample Staff Certifications
INSERT INTO staff_certifications (staff_id, certification_name, issuing_organization, issue_date, expiry_date, status) VALUES
((SELECT id FROM admin_users WHERE email = '<EMAIL>' LIMIT 1), 'First Aid Certificate', 'Red Cross Australia', '2024-01-15', '2026-01-15', 'active'),
((SELECT id FROM admin_users WHERE email = '<EMAIL>' LIMIT 1), 'Hair Styling Certification', 'Australian Hair & Beauty Academy', '2023-06-01', '2025-06-01', 'active')
ON CONFLICT DO NOTHING;

-- 19. RECEIPT TEMPLATES TABLE
CREATE TABLE IF NOT EXISTS receipt_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template_type VARCHAR(50) NOT NULL DEFAULT 'standard', -- 'standard', 'compact', 'detailed'
  is_default BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,

  -- Business Information
  business_name VARCHAR(200) DEFAULT 'Ocean Soul Sparkles',
  business_address TEXT,
  business_phone VARCHAR(50),
  business_email VARCHAR(100),
  business_website VARCHAR(100),
  business_abn VARCHAR(50),

  -- Layout Settings
  show_logo BOOLEAN DEFAULT true,
  logo_position VARCHAR(20) DEFAULT 'center', -- 'left', 'center', 'right'
  header_color VARCHAR(7) DEFAULT '#667eea',
  text_color VARCHAR(7) DEFAULT '#333333',
  font_family VARCHAR(50) DEFAULT 'Arial',
  font_size INTEGER DEFAULT 12,

  -- Content Settings
  show_customer_details BOOLEAN DEFAULT true,
  show_service_details BOOLEAN DEFAULT true,
  show_artist_details BOOLEAN DEFAULT true,
  show_payment_details BOOLEAN DEFAULT true,
  show_booking_notes BOOLEAN DEFAULT false,
  show_terms_conditions BOOLEAN DEFAULT true,

  -- Footer Settings
  footer_message TEXT DEFAULT 'Thank you for choosing Ocean Soul Sparkles!',
  show_social_media BOOLEAN DEFAULT false,
  social_media_links JSONB,

  -- Custom Fields
  custom_fields JSONB DEFAULT '[]',

  created_by UUID REFERENCES admin_users(id),
  updated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default receipt templates
INSERT INTO receipt_templates (
  name, description, template_type, is_default, is_active,
  business_name, business_address, business_phone, business_email, business_website,
  show_logo, logo_position, header_color, text_color, font_family, font_size,
  show_customer_details, show_service_details, show_artist_details, show_payment_details,
  show_booking_notes, show_terms_conditions, footer_message, show_social_media
) VALUES
(
  'Standard Receipt',
  'Default receipt template with all standard information',
  'standard',
  true,
  true,
  'Ocean Soul Sparkles',
  'Australia',
  '+61 XXX XXX XXX',
  '<EMAIL>',
  'oceansoulsparkles.com.au',
  true,
  'center',
  '#667eea',
  '#333333',
  'Arial',
  12,
  true,
  true,
  true,
  true,
  false,
  true,
  'Thank you for choosing Ocean Soul Sparkles! We hope you love your new look!',
  false
),
(
  'Compact Receipt',
  'Minimal receipt template for quick transactions',
  'compact',
  false,
  true,
  'Ocean Soul Sparkles',
  'Australia',
  '+61 XXX XXX XXX',
  '<EMAIL>',
  'oceansoulsparkles.com.au',
  false,
  'left',
  '#667eea',
  '#333333',
  'Arial',
  10,
  true,
  true,
  false,
  true,
  false,
  false,
  'Thank you!',
  false
),
(
  'Detailed Receipt',
  'Comprehensive receipt template with all available information',
  'detailed',
  false,
  true,
  'Ocean Soul Sparkles',
  'Australia',
  '+61 XXX XXX XXX',
  '<EMAIL>',
  'oceansoulsparkles.com.au',
  true,
  'center',
  '#667eea',
  '#333333',
  'Arial',
  12,
  true,
  true,
  true,
  true,
  true,
  true,
  'Thank you for choosing Ocean Soul Sparkles! Follow us on social media for inspiration and updates.',
  true
)
ON CONFLICT (name) DO NOTHING;

-- Success message
SELECT 'Complete database setup completed successfully!' as message;
SELECT 'Sample data inserted for testing purposes.' as message;
