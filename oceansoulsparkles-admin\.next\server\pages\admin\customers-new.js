(()=>{var e={};e.id=7228,e.ids=[7228,660],e.modules={5273:e=>{e.exports={customersContainer:"Customers_customersContainer___hRzb",header:"Customers_header__Xsb8L",title:"Customers_title__kuVdg",headerContent:"Customers_headerContent__1x2Ki",headerActions:"Customers_headerActions__wW_iz",analyticsBtn:"Customers_analyticsBtn__Y6xGh",exportBtn:"Customers_exportBtn__6D97q",newCustomerBtn:"Customers_newCustomerBtn__ACkta",backButton:"Customers_backButton__mwCeO",controlsPanel:"Customers_controlsPanel__QNdpL",searchContainer:"Customers_searchContainer__uP_D_",filtersContainer:"Customers_filtersContainer__B81bf",filterSelect:"Customers_filterSelect__ANSKc",sortSelect:"Customers_sortSelect__oahlL",viewControls:"Customers_viewControls__VdvPJ",viewBtn:"Customers_viewBtn__TNPmH",active:"Customers_active__rk6Up",resultsInfo:"Customers_resultsInfo__cpO5L",searchSection:"Customers_searchSection__sgj9I",searchInput:"Customers_searchInput__BB_0d",sortSection:"Customers_sortSection__qb0FO",sortOrderBtn:"Customers_sortOrderBtn__t2r88",customersContent:"Customers_customersContent__z_zag",customersHeader:"Customers_customersHeader__einsi",statsCards:"Customers_statsCards__ph2BM",statCard:"Customers_statCard__Pua_g",statValue:"Customers_statValue__357cS",emptyState:"Customers_emptyState__umGfV",customersGrid:"Customers_customersGrid__nkG51",customerCard:"Customers_customerCard__oECGs",customerMeta:"Customers_customerMeta__JcUjA",customerDate:"Customers_customerDate__dVu6L",emptyIcon:"Customers_emptyIcon__xGfzC",emptyActionBtn:"Customers_emptyActionBtn__1HOvJ",customerHeader:"Customers_customerHeader__QfMYw",customerInfo:"Customers_customerInfo__sYPpi",statusBadge:"Customers_statusBadge__nVYbG",statusVip:"Customers_statusVip__GAam3",statusActive:"Customers_statusActive__N_jFf",statusNew:"Customers_statusNew__8r02c",statusInactive:"Customers_statusInactive__inD5l",statusDefault:"Customers_statusDefault__R5R8L",customerStats:"Customers_customerStats__OLqld",statItem:"Customers_statItem__YazVS",statLabel:"Customers_statLabel__pm6R5",customerNotes:"Customers_customerNotes__KM6Mg",customerActions:"Customers_customerActions__TS_QE",bookBtn:"Customers_bookBtn__333XR",customersTable:"Customers_customersTable__c6X31",customerName:"Customers_customerName__SARmR",bookingBadge:"Customers_bookingBadge__0JdCy",tableActions:"Customers_tableActions__fP8SO",editBtn:"Customers_editBtn__3Orgk",pagination:"Customers_pagination__QsqfM",paginationBtn:"Customers_paginationBtn__RUfWW",paginationInfo:"Customers_paginationInfo__7qsKa",loadingContainer:"Customers_loadingContainer__eyNOh",loadingSpinner:"Customers_loadingSpinner__2d9G_",spin:"Customers_spin__xe_Ia"}},9336:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{config:()=>h,default:()=>l,getServerSideProps:()=>p,getStaticPaths:()=>C,getStaticProps:()=>d,reportWebVitals:()=>g,routeModule:()=>N,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>x});var a=s(7093),o=s(5244),n=s(1323),_=s(2899),u=s.n(_),i=s(6814),c=s(6962),m=e([i,c]);[i,c]=m.then?(await m)():m;let l=(0,n.l)(c,"default"),d=(0,n.l)(c,"getStaticProps"),C=(0,n.l)(c,"getStaticPaths"),p=(0,n.l)(c,"getServerSideProps"),h=(0,n.l)(c,"config"),g=(0,n.l)(c,"reportWebVitals"),x=(0,n.l)(c,"unstable_getStaticProps"),S=(0,n.l)(c,"unstable_getStaticPaths"),v=(0,n.l)(c,"unstable_getStaticParams"),b=(0,n.l)(c,"unstable_getServerProps"),j=(0,n.l)(c,"unstable_getServerSideProps"),N=new a.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/admin/customers-new",pathname:"/admin/customers-new",bundlePath:"",filename:""},components:{App:i.default,Document:u()},userland:c});r()}catch(e){r(e)}})},6962:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{default:()=>d});var a=s(997),o=s(6689),n=s(968),_=s.n(n),u=s(4845),i=s(8568),c=s(5273),m=s.n(c),l=e([u]);function d(){let{user:e,loading:t}=(0,i.a)(),[s,r]=(0,o.useState)([]),[n,c]=(0,o.useState)([]),[l,d]=(0,o.useState)(!0),[C,p]=(0,o.useState)("");return t||l?a.jsx(u.Z,{children:(0,a.jsxs)("div",{className:m().loadingContainer,children:[a.jsx("div",{className:m().loadingSpinner}),a.jsx("p",{children:"Loading customers..."})]})}):(0,a.jsxs)(u.Z,{children:[a.jsx(_(),{children:a.jsx("title",{children:"Customers Management - Ocean Soul Sparkles Admin"})}),(0,a.jsxs)("div",{className:m().customersContainer,children:[(0,a.jsxs)("header",{className:m().header,children:[a.jsx("h1",{children:"Customers Management"}),a.jsx("div",{className:m().headerActions,children:a.jsx("button",{className:m().newCustomerBtn,children:"+ New Customer"})})]}),a.jsx("div",{className:m().controlsPanel,children:a.jsx("div",{className:m().searchContainer,children:a.jsx("input",{type:"text",placeholder:"Search customers...",value:C,onChange:e=>p(e.target.value),className:m().searchInput})})}),a.jsx("div",{className:m().customersGrid,children:0===n.length?a.jsx("div",{className:m().emptyState,children:a.jsx("p",{children:"No customers found."})}):n.map(e=>(0,a.jsxs)("div",{className:m().customerCard,children:[(0,a.jsxs)("div",{className:m().customerHeader,children:[a.jsx("h3",{children:e.name}),(0,a.jsxs)("span",{className:m().bookingCount,children:[e.total_bookings," bookings"]})]}),(0,a.jsxs)("div",{className:m().customerDetails,children:[a.jsx("p",{className:m().customerEmail,children:e.email}),a.jsx("p",{className:m().customerPhone,children:e.phone}),e.notes&&a.jsx("p",{className:m().customerNotes,children:e.notes})]}),(0,a.jsxs)("div",{className:m().customerActions,children:[a.jsx("button",{className:m().viewBtn,children:"View Details"}),a.jsx("button",{className:m().editBtn,children:"Edit"})]})]},e.id))})]})]})}u=(l.then?(await l)():l)[0],r()}catch(e){r(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[2899,6212,1664,7441],()=>s(9336));module.exports=r})();