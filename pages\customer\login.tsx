import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
import CustomerLayout from '../../components/customer/CustomerLayout';
import { useCustomerAuth } from '../../hooks/useCustomerAuth';
import styles from '../../styles/customer/Auth.module.css';

export default function CustomerLogin() {
  const router = useRouter();
  const { user, loading, login } = useCustomerAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Redirect if already logged in
  useEffect(() => {
    if (!loading && user) {
      router.push('/customer/dashboard');
    }
  }, [user, loading, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const result = await login(formData.email, formData.password);
      
      if (result.success) {
        router.push('/customer/dashboard');
      } else {
        if (result.requiresEmailVerification) {
          setError('Please verify your email address before logging in. Check your inbox for a verification link.');
        } else {
          setError(result.error || 'Login failed. Please try again.');
        }
      }
    } catch (error) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <CustomerLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Loading...</p>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <>
      <Head>
        <title>Customer Login - Ocean Soul Sparkles</title>
        <meta name="description" content="Login to your Ocean Soul Sparkles customer account" />
      </Head>

      <CustomerLayout>
        <div className={styles.authContainer}>
          <div className={styles.authCard}>
            <div className={styles.authHeader}>
              <h1>Welcome Back</h1>
              <p>Sign in to your Ocean Soul Sparkles account</p>
            </div>

            {error && (
              <div className={styles.errorMessage}>
                <span className={styles.errorIcon}>⚠️</span>
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className={styles.authForm}>
              <div className={styles.formGroup}>
                <label htmlFor="email">Email Address</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  placeholder="Enter your email"
                  className={styles.formInput}
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="password">Password</label>
                <div className={styles.passwordInput}>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    placeholder="Enter your password"
                    className={styles.formInput}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className={styles.passwordToggle}
                  >
                    {showPassword ? '👁️' : '👁️‍🗨️'}
                  </button>
                </div>
              </div>

              <div className={styles.formActions}>
                <Link href="/customer/forgot-password" className={styles.forgotPassword}>
                  Forgot your password?
                </Link>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={styles.submitButton}
              >
                {isSubmitting ? (
                  <>
                    <span className={styles.spinner}></span>
                    Signing In...
                  </>
                ) : (
                  'Sign In'
                )}
              </button>
            </form>

            <div className={styles.authFooter}>
              <p>
                Don't have an account?{' '}
                <Link href="/customer/register" className={styles.authLink}>
                  Create one here
                </Link>
              </p>
            </div>

            <div className={styles.divider}>
              <span>or</span>
            </div>

            <div className={styles.guestActions}>
              <Link href="/customer/services" className={styles.guestButton}>
                Browse Services as Guest
              </Link>
            </div>
          </div>
        </div>
      </CustomerLayout>
    </>
  );
}
