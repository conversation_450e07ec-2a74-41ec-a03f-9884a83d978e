"use strict";(()=>{var e={};e.id=7937,e.ids=[7937],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},6309:(e,t,r)=>{r.r(t),r.d(t,{config:()=>_,default:()=>c,routeModule:()=>g});var s={};r.r(s),r.d(s,{default:()=>m});var o=r(1802),i=r(7153),a=r(8781),n=r(7474),d=r(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,d.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function m(e,t){let{id:r}=e.query;if(!r||"string"!=typeof r)return t.status(400).json({error:"Booking ID is required"});try{let s=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!s)return t.status(401).json({error:"No authentication token"});let o=await (0,n.Wg)(s);if(!o.valid||!o.user)return t.status(401).json({error:"Invalid authentication"});let i=o.user;if("GET"===e.method){let{data:e,error:s}=await l.from("bookings").select(`
          id,
          booking_date,
          start_time,
          end_time,
          status,
          total_amount,
          notes,
          created_at,
          customer_id,
          service_id,
          assigned_artist_id,
          customers (
            id,
            first_name,
            last_name,
            email,
            phone
          ),
          services (
            id,
            name,
            duration,
            price
          ),
          artist_profiles!assigned_artist_id (
            id,
            artist_name,
            display_name
          )
        `).eq("id",r).single();if(s)return console.error("Booking query error:",s),t.status(500).json({error:"Failed to fetch booking"});if(!e)return t.status(404).json({error:"Booking not found"});if(("Artist"===i.role||"Braider"===i.role)&&e.assigned_artist_id!==i.id)return t.status(403).json({error:"Access denied to this booking"});let o=Array.isArray(e.customers)?e.customers[0]:e.customers,a=Array.isArray(e.services)?e.services[0]:e.services,n=Array.isArray(e.artist_profiles)?e.artist_profiles[0]:e.artist_profiles,d=new Date(e.start_time).toTimeString().slice(0,5),u={id:e.id,customer_name:o?`${o.first_name} ${o.last_name}`:"Unknown Customer",customer_email:o?.email||"",customer_phone:o?.phone||"",service_name:a?.name||"Unknown Service",service_duration:a?.duration||0,service_price:a?.price||0,artist_name:n?.artist_name||n?.display_name||"Unassigned",booking_date:e.booking_date,booking_time:d,start_time:e.start_time,end_time:e.end_time,status:e.status,total_amount:e.total_amount,notes:e.notes,created_at:e.created_at,customers:o,services:a,artist_profiles:n};return t.status(200).json({booking:u})}if("PUT"===e.method){let{booking_date:s,start_time:o,end_time:a,status:n,total_amount:d,notes:u,assigned_artist_id:m}=e.body;if("Artist"===i.role||"Braider"===i.role){let{data:e}=await l.from("bookings").select("assigned_artist_id").eq("id",r).single();if(!e||e.assigned_artist_id!==i.id)return t.status(403).json({error:"Access denied to this booking"});let{data:s,error:o}=await l.from("bookings").update({status:n,notes:u,updated_at:new Date().toISOString()}).eq("id",r).select().single();if(o)return console.error("Booking update error:",o),t.status(500).json({error:"Failed to update booking"});return t.status(200).json({booking:s})}let c={updated_at:new Date().toISOString()};s&&(c.booking_date=s),o&&(c.start_time=o),a&&(c.end_time=a),n&&(c.status=n),void 0!==d&&(c.total_amount=parseFloat(d)),void 0!==u&&(c.notes=u),m&&(c.assigned_artist_id=m);let{data:_,error:g}=await l.from("bookings").update(c).eq("id",r).select().single();if(g)return console.error("Booking update error:",g),t.status(500).json({error:"Failed to update booking"});return t.status(200).json({booking:_})}{if("DELETE"!==e.method)return t.status(405).json({error:"Method not allowed"});if("Admin"!==i.role&&"DEV"!==i.role)return t.status(403).json({error:"Only admins can delete bookings"});let{error:s}=await l.from("bookings").delete().eq("id",r);if(s)return console.error("Booking delete error:",s),t.status(500).json({error:"Failed to delete booking"});return t.status(200).json({message:"Booking deleted successfully"})}}catch(e){return console.error("Booking API error:",e),t.status(500).json({error:"Internal server error"})}}let c=(0,a.l)(s,"default"),_=(0,a.l)(s,"config"),g=new o.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/bookings/[id]",pathname:"/api/admin/bookings/[id]",bundlePath:"",filename:""},userland:s})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2805],()=>r(6309));module.exports=s})();