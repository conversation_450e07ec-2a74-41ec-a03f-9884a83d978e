(()=>{var e={};e.id=6283,e.ids=[6283,660],e.modules={2420:e=>{e.exports={trainingContainer:"StaffTraining_trainingContainer__aK4dZ",header:"StaffTraining_header__v6Eiz",headerLeft:"StaffTraining_headerLeft__3DA82",title:"StaffTraining_title__fF4c9",subtitle:"StaffTraining_subtitle__znx_O",headerActions:"StaffTraining_headerActions__aok8w",assignBtn:"StaffTraining_assignBtn__OEAqW",backBtn:"StaffTraining_backBtn__ioqCz",errorMessage:"StaffTraining_errorMessage__Sun1U",closeError:"StaffTraining_closeError__g_Bs3",progressSection:"StaffTraining_progressSection__nQxw6",progressCard:"StaffTraining_progressCard__Fzccp",progressBar:"StaffTraining_progressBar__CFhqi",progressFill:"StaffTraining_progressFill__YM0Pc",progressStats:"StaffTraining_progressStats__Ge9DG",filters:"StaffTraining_filters__h0KWy",filterGroup:"StaffTraining_filterGroup__qNz3z",filterSelect:"StaffTraining_filterSelect__NrFjw",emptyState:"StaffTraining_emptyState__UnM4B",emptyIcon:"StaffTraining_emptyIcon__8HntE",modulesList:"StaffTraining_modulesList__FlR2b",moduleCard:"StaffTraining_moduleCard__umB_G",cardHeader:"StaffTraining_cardHeader__uOpH0",moduleInfo:"StaffTraining_moduleInfo__TtgBi",moduleName:"StaffTraining_moduleName__T6Vhe",requiredBadge:"StaffTraining_requiredBadge__O7R__",moduleDescription:"StaffTraining_moduleDescription__Q7yGq",moduleDetails:"StaffTraining_moduleDetails__V9L2K",statusSection:"StaffTraining_statusSection__nuYau",statusBadge:"StaffTraining_statusBadge__0QFny",scoreDisplay:"StaffTraining_scoreDisplay__JP9Bc",cardBody:"StaffTraining_cardBody__Tndaa",progressInfo:"StaffTraining_progressInfo__asukL",cardActions:"StaffTraining_cardActions__DgMVa",startBtn:"StaffTraining_startBtn__xUDMg",completeBtn:"StaffTraining_completeBtn__AkIeB",moduleNotes:"StaffTraining_moduleNotes__W4KWr",loadingContainer:"StaffTraining_loadingContainer__hXS41",loadingSpinner:"StaffTraining_loadingSpinner__CeQE3",spin:"StaffTraining_spin__VyBIJ",modalOverlay:"StaffTraining_modalOverlay__vOQfg",modal:"StaffTraining_modal__m1XEa",modalHeader:"StaffTraining_modalHeader__W4_yT",closeModal:"StaffTraining_closeModal__Pzc88",modalBody:"StaffTraining_modalBody__2G5_l"}},8556:(e,a,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(a),s.d(a,{config:()=>p,default:()=>m,getServerSideProps:()=>f,getStaticPaths:()=>u,getStaticProps:()=>_,reportWebVitals:()=>h,routeModule:()=>y,unstable_getServerProps:()=>N,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>x,unstable_getStaticProps:()=>S});var i=s(7093),r=s(5244),n=s(1323),l=s(2899),o=s.n(l),d=s(6814),c=s(7931),g=e([d,c]);[d,c]=g.then?(await g)():g;let m=(0,n.l)(c,"default"),_=(0,n.l)(c,"getStaticProps"),u=(0,n.l)(c,"getStaticPaths"),f=(0,n.l)(c,"getServerSideProps"),p=(0,n.l)(c,"config"),h=(0,n.l)(c,"reportWebVitals"),S=(0,n.l)(c,"unstable_getStaticProps"),x=(0,n.l)(c,"unstable_getStaticPaths"),j=(0,n.l)(c,"unstable_getStaticParams"),N=(0,n.l)(c,"unstable_getServerProps"),v=(0,n.l)(c,"unstable_getServerSideProps"),y=new i.PagesRouteModule({definition:{kind:r.x.PAGES,page:"/admin/staff/training",pathname:"/admin/staff/training",bundlePath:"",filename:""},components:{App:d.default,Document:o()},userland:c});t()}catch(e){t(e)}})},7931:(e,a,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(a),s.d(a,{default:()=>u});var i=s(997),r=s(6689),n=s(968),l=s.n(n),o=s(1163),d=s(8568),c=s(4845),g=s(2420),m=s.n(g),_=e([c]);c=(_.then?(await _)():_)[0];let f={not_started:"#6b7280",in_progress:"#f59e0b",completed:"#10b981",failed:"#ef4444"},p={not_started:"Not Started",in_progress:"In Progress",completed:"Completed",failed:"Failed"},h={safety:"Health & Safety",technical:"Technical Skills",customer_service:"Customer Service",compliance:"Compliance"};function u(){let{user:e}=(0,d.a)(),a=(0,o.useRouter)(),{staff_id:s}=a.query,[t,n]=(0,r.useState)(!0),[g,_]=(0,r.useState)(null),[u,S]=(0,r.useState)([]),[x,j]=(0,r.useState)([]),[N,v]=(0,r.useState)({total:0,completed:0,required:0,completedRequired:0,completionPercentage:0,requiredCompletionPercentage:0}),[y,T]=(0,r.useState)(null),[b,C]=(0,r.useState)("all"),[B,q]=(0,r.useState)(!1),P=async()=>{try{n(!0);let e=await fetch(`/api/admin/staff/training?staff_id=${s}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}});if(e.ok){let a=await e.json();S(a.progress||[]),v(a.statistics||{})}else _("Failed to load training data")}catch(e){console.error("Error loading training data:",e),_("Failed to load training data")}finally{n(!1)}},k=async()=>{try{(await fetch("/api/admin/staff/training",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({action:"assign_all_required",staff_id:s})})).ok?await P():_("Failed to assign required modules")}catch(e){console.error("Error assigning required modules:",e),_("Failed to assign required modules")}},A=async e=>{try{(await fetch("/api/admin/staff/training",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({action:"assign_module",staff_id:s,module_id:e})})).ok?(await P(),q(!1)):_("Failed to assign module")}catch(e){console.error("Error assigning module:",e),_("Failed to assign module")}},F=async(e,a,t)=>{try{(await fetch("/api/admin/staff/training",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({action:a,staff_id:s,module_id:e,score:t})})).ok?await P():_(`Failed to ${a.replace("_"," ")}`)}catch(e){console.error(`Error ${a}:`,e),_(`Failed to ${a.replace("_"," ")}`)}},D=e=>new Date(e).toLocaleDateString("en-AU",{year:"numeric",month:"short",day:"numeric"}),O=e=>e>=90?"#10b981":e>=70?"#f59e0b":"#ef4444",M="all"===b?u:u.filter(e=>e.staff_training_modules.category===b),w=u.map(e=>e.staff_training_modules.id),I=x.filter(e=>!w.includes(e.id));return t?i.jsx(c.Z,{children:(0,i.jsxs)("div",{className:m().loadingContainer,children:[i.jsx("div",{className:m().loadingSpinner}),i.jsx("p",{children:"Loading training data..."})]})}):(0,i.jsxs)(c.Z,{children:[(0,i.jsxs)(l(),{children:[i.jsx("title",{children:"Staff Training | Ocean Soul Sparkles Admin"}),i.jsx("meta",{name:"description",content:"Manage staff training progress and modules"})]}),(0,i.jsxs)("div",{className:m().trainingContainer,children:[(0,i.jsxs)("header",{className:m().header,children:[(0,i.jsxs)("div",{className:m().headerLeft,children:[i.jsx("h1",{className:m().title,children:"Staff Training"}),y&&(0,i.jsxs)("p",{className:m().subtitle,children:[y.firstName," ",y.lastName," - ",y.role]})]}),(0,i.jsxs)("div",{className:m().headerActions,children:[i.jsx("button",{onClick:k,className:m().assignBtn,children:"Assign Required Modules"}),i.jsx("button",{onClick:()=>q(!0),className:m().assignBtn,children:"+ Assign Module"}),i.jsx("button",{onClick:()=>a.back(),className:m().backBtn,children:"← Back to Staff"})]})]}),g&&(0,i.jsxs)("div",{className:m().errorMessage,children:[g,i.jsx("button",{onClick:()=>_(null),className:m().closeError,children:"\xd7"})]}),(0,i.jsxs)("div",{className:m().progressSection,children:[(0,i.jsxs)("div",{className:m().progressCard,children:[i.jsx("h3",{children:"Overall Progress"}),i.jsx("div",{className:m().progressBar,children:i.jsx("div",{className:m().progressFill,style:{width:`${N.completionPercentage}%`,backgroundColor:O(N.completionPercentage)}})}),(0,i.jsxs)("div",{className:m().progressStats,children:[(0,i.jsxs)("span",{children:[N.completed," of ",N.total," completed"]}),(0,i.jsxs)("span",{children:[N.completionPercentage,"%"]})]})]}),(0,i.jsxs)("div",{className:m().progressCard,children:[i.jsx("h3",{children:"Required Modules"}),i.jsx("div",{className:m().progressBar,children:i.jsx("div",{className:m().progressFill,style:{width:`${N.requiredCompletionPercentage}%`,backgroundColor:O(N.requiredCompletionPercentage)}})}),(0,i.jsxs)("div",{className:m().progressStats,children:[(0,i.jsxs)("span",{children:[N.completedRequired," of ",N.required," completed"]}),(0,i.jsxs)("span",{children:[N.requiredCompletionPercentage,"%"]})]})]})]}),i.jsx("div",{className:m().filters,children:(0,i.jsxs)("div",{className:m().filterGroup,children:[i.jsx("label",{htmlFor:"categoryFilter",children:"Filter by Category:"}),(0,i.jsxs)("select",{id:"categoryFilter",value:b,onChange:e=>C(e.target.value),className:m().filterSelect,children:[i.jsx("option",{value:"all",children:"All Categories"}),Object.entries(h).map(([e,a])=>i.jsx("option",{value:e,children:a},e))]})]})}),i.jsx("div",{className:m().trainingContent,children:0===M.length?(0,i.jsxs)("div",{className:m().emptyState,children:[i.jsx("div",{className:m().emptyIcon,children:"\uD83C\uDF93"}),i.jsx("h3",{children:"No Training Modules Assigned"}),i.jsx("p",{children:"This staff member doesn't have any training modules assigned yet."}),i.jsx("button",{onClick:k,className:m().assignBtn,children:"Assign Required Modules"})]}):i.jsx("div",{className:m().modulesList,children:M.map(e=>(0,i.jsxs)("div",{className:m().moduleCard,children:[(0,i.jsxs)("div",{className:m().cardHeader,children:[(0,i.jsxs)("div",{className:m().moduleInfo,children:[(0,i.jsxs)("h3",{className:m().moduleName,children:[e.staff_training_modules.name,e.staff_training_modules.is_required&&i.jsx("span",{className:m().requiredBadge,children:"Required"})]}),i.jsx("p",{className:m().moduleDescription,children:e.staff_training_modules.description}),(0,i.jsxs)("div",{className:m().moduleDetails,children:[(0,i.jsxs)("span",{children:["Category: ",h[e.staff_training_modules.category]]}),(0,i.jsxs)("span",{children:["Duration: ",e.staff_training_modules.duration_minutes," minutes"]}),(0,i.jsxs)("span",{children:["Passing Score: ",e.staff_training_modules.passing_score,"%"]})]})]}),(0,i.jsxs)("div",{className:m().statusSection,children:[i.jsx("span",{className:m().statusBadge,style:{backgroundColor:f[e.status]},children:p[e.status]}),e.score&&(0,i.jsxs)("div",{className:m().scoreDisplay,children:["Score: ",e.score,"%"]})]})]}),(0,i.jsxs)("div",{className:m().cardBody,children:[(0,i.jsxs)("div",{className:m().progressInfo,children:[(0,i.jsxs)("p",{children:["Assigned: ",D(e.assigned_at)]}),e.started_at&&(0,i.jsxs)("p",{children:["Started: ",D(e.started_at)]}),e.completed_at&&(0,i.jsxs)("p",{children:["Completed: ",D(e.completed_at)]}),e.attempts>0&&(0,i.jsxs)("p",{children:["Attempts: ",e.attempts]})]}),(0,i.jsxs)("div",{className:m().cardActions,children:["not_started"===e.status&&i.jsx("button",{onClick:()=>F(e.staff_training_modules.id,"start_training"),className:m().startBtn,children:"Start Training"}),("in_progress"===e.status||"failed"===e.status)&&i.jsx("button",{onClick:()=>{let a=prompt("Enter completion score (0-100):");a&&!isNaN(Number(a))&&F(e.staff_training_modules.id,"complete_training",Number(a))},className:m().completeBtn,children:"Complete Training"})]})]}),e.notes&&(0,i.jsxs)("div",{className:m().moduleNotes,children:[i.jsx("strong",{children:"Notes:"})," ",e.notes]})]},e.id))})}),B&&i.jsx("div",{className:m().modalOverlay,children:(0,i.jsxs)("div",{className:m().modal,children:[(0,i.jsxs)("div",{className:m().modalHeader,children:[i.jsx("h3",{children:"Assign Training Module"}),i.jsx("button",{onClick:()=>q(!1),className:m().closeModal,children:"\xd7"})]}),i.jsx("div",{className:m().modalBody,children:0===I.length?i.jsx("p",{children:"All available modules are already assigned to this staff member."}):i.jsx("div",{className:m().moduleOptions,children:I.map(e=>(0,i.jsxs)("div",{className:m().moduleOption,children:[(0,i.jsxs)("div",{className:m().optionInfo,children:[i.jsx("h4",{children:e.name}),i.jsx("p",{children:e.description}),(0,i.jsxs)("div",{className:m().optionDetails,children:[i.jsx("span",{children:h[e.category]}),(0,i.jsxs)("span",{children:[e.duration_minutes," min"]}),e.is_required&&i.jsx("span",{className:m().requiredBadge,children:"Required"})]})]}),i.jsx("button",{onClick:()=>A(e.id),className:m().assignOptionBtn,children:"Assign"})]},e.id))})})]})})]})]})}t()}catch(e){t(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var a=require("../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[2899,6212,1664,7441],()=>s(8556));module.exports=t})();