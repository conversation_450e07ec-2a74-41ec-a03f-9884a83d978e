import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import CustomerLayout from '../../components/customer/CustomerLayout';
import { useCustomerAuth } from '../../hooks/useCustomerAuth';
import styles from '../../styles/customer/Dashboard.module.css';

interface DashboardData {
  profile: any;
  recentBookings: any[];
  upcomingBookings: any[];
  loyaltyInfo: any;
  notifications: any[];
}

export default function CustomerDashboard() {
  const router = useRouter();
  const { user, loading } = useCustomerAuth();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!loading && !user) {
      router.push('/customer/login');
      return;
    }

    if (user) {
      loadDashboardData();
    }
  }, [user, loading, router]);

  const loadDashboardData = async () => {
    try {
      const token = localStorage.getItem('customer-token');
      if (!token) return;

      // Load profile data
      const profileResponse = await fetch('/api/customer/profile', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!profileResponse.ok) {
        throw new Error('Failed to load profile');
      }

      const profileData = await profileResponse.json();

      // Load loyalty data
      const loyaltyResponse = await fetch('/api/customer/loyalty', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const loyaltyData = loyaltyResponse.ok ? await loyaltyResponse.json() : { loyalty: null };

      // Load booking requests
      const bookingRequestsResponse = await fetch('/api/customer/booking-requests', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const bookingRequestsData = bookingRequestsResponse.ok ? await bookingRequestsResponse.json() : { bookingRequests: [] };

      setDashboardData({
        profile: profileData.profile,
        recentBookings: profileData.profile.statistics.recent_bookings || [],
        upcomingBookings: bookingRequestsData.bookingRequests.filter((req: any) => req.status === 'approved') || [],
        loyaltyInfo: loyaltyData.loyalty,
        notifications: []
      });

    } catch (error) {
      console.error('Dashboard data loading error:', error);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  if (loading || isLoading) {
    return (
      <CustomerLayout user={user}>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Loading your dashboard...</p>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout user={user}>
        <div className={styles.errorContainer}>
          <h2>Oops! Something went wrong</h2>
          <p>{error}</p>
          <button onClick={loadDashboardData} className={styles.retryButton}>
            Try Again
          </button>
        </div>
      </CustomerLayout>
    );
  }

  if (!dashboardData) {
    return (
      <CustomerLayout user={user}>
        <div className={styles.errorContainer}>
          <h2>No data available</h2>
          <p>Unable to load dashboard information</p>
        </div>
      </CustomerLayout>
    );
  }

  const { profile, recentBookings, upcomingBookings, loyaltyInfo } = dashboardData;

  return (
    <>
      <Head>
        <title>Dashboard - Ocean Soul Sparkles</title>
        <meta name="description" content="Your Ocean Soul Sparkles customer dashboard" />
      </Head>

      <CustomerLayout user={user} title="Welcome Back!">
        <div className={styles.dashboard}>
          {/* Welcome Section */}
          <div className={styles.welcomeSection}>
            <div className={styles.welcomeCard}>
              <h2>Hello, {profile.first_name}! ✨</h2>
              <p>Welcome back to your Ocean Soul Sparkles dashboard</p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className={styles.statsGrid}>
            <div className={styles.statCard}>
              <div className={styles.statIcon}>📅</div>
              <div className={styles.statContent}>
                <h3>{profile.statistics.total_bookings}</h3>
                <p>Total Bookings</p>
              </div>
            </div>

            <div className={styles.statCard}>
              <div className={styles.statIcon}>⭐</div>
              <div className={styles.statContent}>
                <h3>{loyaltyInfo?.current_status.points_balance || 0}</h3>
                <p>Loyalty Points</p>
              </div>
            </div>

            <div className={styles.statCard}>
              <div className={styles.statIcon}>🏆</div>
              <div className={styles.statContent}>
                <h3>{loyaltyInfo?.current_status.tier_level || 'Bronze'}</h3>
                <p>Tier Level</p>
              </div>
            </div>

            <div className={styles.statCard}>
              <div className={styles.statIcon}>💰</div>
              <div className={styles.statContent}>
                <h3>${loyaltyInfo?.current_status.lifetime_spend || 0}</h3>
                <p>Lifetime Spend</p>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className={styles.contentGrid}>
            {/* Recent Bookings */}
            <div className={styles.contentCard}>
              <div className={styles.cardHeader}>
                <h3>Recent Bookings</h3>
                <Link href="/customer/bookings" className={styles.viewAllLink}>
                  View All
                </Link>
              </div>
              <div className={styles.cardContent}>
                {recentBookings.length > 0 ? (
                  <div className={styles.bookingsList}>
                    {recentBookings.slice(0, 3).map((booking) => (
                      <div key={booking.id} className={styles.bookingItem}>
                        <div className={styles.bookingInfo}>
                          <h4>{booking.service_name}</h4>
                          <p>{booking.artist_name}</p>
                          <span className={styles.bookingDate}>
                            {new Date(booking.date).toLocaleDateString()}
                          </span>
                        </div>
                        <div className={styles.bookingStatus}>
                          <span className={`${styles.statusBadge} ${styles[booking.status]}`}>
                            {booking.status}
                          </span>
                          <span className={styles.bookingAmount}>
                            ${booking.amount}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className={styles.emptyState}>
                    <p>No recent bookings</p>
                    <Link href="/customer/services" className={styles.ctaButton}>
                      Book Your First Service
                    </Link>
                  </div>
                )}
              </div>
            </div>

            {/* Loyalty Progress */}
            <div className={styles.contentCard}>
              <div className={styles.cardHeader}>
                <h3>Loyalty Progress</h3>
                <Link href="/customer/loyalty" className={styles.viewAllLink}>
                  View Details
                </Link>
              </div>
              <div className={styles.cardContent}>
                {loyaltyInfo ? (
                  <div className={styles.loyaltyProgress}>
                    <div className={styles.tierInfo}>
                      <div className={styles.currentTier}>
                        <span className={styles.tierBadge} style={{ 
                          backgroundColor: loyaltyInfo.tier_progression.current_tier.color 
                        }}>
                          {loyaltyInfo.tier_progression.current_tier.name}
                        </span>
                        <span className={styles.points}>
                          {loyaltyInfo.current_status.points_balance} points
                        </span>
                      </div>
                      
                      {loyaltyInfo.tier_progression.next_tier && (
                        <div className={styles.nextTier}>
                          <p>
                            {loyaltyInfo.tier_progression.points_to_next_tier} points to{' '}
                            {loyaltyInfo.tier_progression.next_tier.name}
                          </p>
                          <div className={styles.progressBar}>
                            <div 
                              className={styles.progressFill}
                              style={{ 
                                width: `${loyaltyInfo.tier_progression.progress_percentage}%` 
                              }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className={styles.referralInfo}>
                      <h4>Refer Friends & Earn</h4>
                      <p>Your referral code: <strong>{loyaltyInfo.referral_program.referral_code}</strong></p>
                      <p>{loyaltyInfo.referral_program.successful_referrals} successful referrals</p>
                    </div>
                  </div>
                ) : (
                  <div className={styles.emptyState}>
                    <p>Loyalty information unavailable</p>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className={styles.contentCard}>
              <div className={styles.cardHeader}>
                <h3>Quick Actions</h3>
              </div>
              <div className={styles.cardContent}>
                <div className={styles.actionGrid}>
                  <Link href="/customer/services" className={styles.actionButton}>
                    <span className={styles.actionIcon}>🎨</span>
                    <span>Browse Services</span>
                  </Link>
                  
                  <Link href="/customer/artists" className={styles.actionButton}>
                    <span className={styles.actionIcon}>👩‍🎨</span>
                    <span>Meet Artists</span>
                  </Link>
                  
                  <Link href="/customer/profile" className={styles.actionButton}>
                    <span className={styles.actionIcon}>⚙️</span>
                    <span>Edit Profile</span>
                  </Link>
                  
                  <Link href="/customer/loyalty" className={styles.actionButton}>
                    <span className={styles.actionIcon}>🎁</span>
                    <span>Loyalty Rewards</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    </>
  );
}
