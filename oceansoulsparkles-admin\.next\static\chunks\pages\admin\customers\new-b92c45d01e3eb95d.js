(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[198],{7595:function(n,r,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/customers/new",function(){return e(7685)}])},7685:function(n,r,e){"use strict";e.r(r),e.d(r,{default:function(){return O}}),function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}(),function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}();var t=e(9008),o=e.n(t),c=e(1163),i=e(1664),a=e.n(i),d=e(9678),u=e.n(d);function O(){let n=(0,c.useRouter)(),[r,e]=Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(!1),[t,i]=Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(!0);return(Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(()=>{(async()=>{try{let r="true"===localStorage.getItem("admin_logged_in");if(e(r),!r){n.push("/login");return}}catch(n){console.error("Error checking authentication:",n),e(!1)}finally{i(!1)}})()},[n]),t)?Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:u().loadingContainer,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:u().loadingSpinner}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("p",{children:"Loading..."})]}):r?Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}()),{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(o(),{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("title",{children:"New Customer | Ocean Soul Sparkles Admin"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{name:"description",content:"Add a new customer to the database"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:u().newCustomerContainer,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("header",{className:u().header,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h1",{className:u().title,children:"Add New Customer"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:u().headerActions,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(a(),{href:"/admin/customers",className:u().backButton,children:"← Back to Customers"})})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:u().content,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:u().comingSoon,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h2",{children:"New Customer Form"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("p",{children:"This page will contain a customer registration form with:"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("ul",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("li",{children:"Personal information (name, email, phone)"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("li",{children:"Address and contact details"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("li",{children:"Preferences and notes"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("li",{children:"Emergency contact information"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("li",{children:"Marketing preferences"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("li",{children:"Customer status and tags"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:u().actions,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(a(),{href:"/admin/customers",className:u().backBtn,children:"Back to Customers"})})]})})]})]}):null}},9678:function(n){n.exports={newCustomerContainer:"NewCustomer_newCustomerContainer__XnGc5",header:"NewCustomer_header__G25aq",title:"NewCustomer_title__rWf5W",headerActions:"NewCustomer_headerActions__rEnIB",backButton:"NewCustomer_backButton___2Z5y",content:"NewCustomer_content__KcWfG",comingSoon:"NewCustomer_comingSoon__r3Kn4",actions:"NewCustomer_actions__ayIfv",backBtn:"NewCustomer_backBtn__DDJUr",loadingContainer:"NewCustomer_loadingContainer__hcqi0",loadingSpinner:"NewCustomer_loadingSpinner__XBNDe",spin:"NewCustomer_spin__YA4lG"}}},function(n){n.O(0,[736,888,179],function(){return n(n.s=7595)}),_N_E=n.O()}]);