/**
 * Ocean Soul Sparkles Admin - Global Search Styles
 * Responsive search component with dropdown results
 */

.globalSearch {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.searchInput {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--admin-bg-secondary, #f8f9fa);
  border: 1px solid var(--admin-border-light, #e0e0e0);
  border-radius: var(--admin-radius-md, 8px);
  padding: 0;
  transition: all var(--admin-transition-normal, 0.2s ease);
}

.searchInput:focus-within {
  border-color: var(--admin-primary, #3788d8);
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
  background: var(--admin-bg-primary, #ffffff);
}

.searchIcon {
  padding: 0 12px;
  color: var(--admin-text-secondary, #666666);
  font-size: 1rem;
  pointer-events: none;
}

.input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 12px 8px 12px 0;
  font-size: 0.9rem;
  color: var(--admin-text-primary, #1a1a1a);
  outline: none;
}

.input::placeholder {
  color: var(--admin-text-secondary, #666666);
}

.loadingSpinner {
  padding: 0 12px;
  display: flex;
  align-items: center;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--admin-border-light, #e0e0e0);
  border-top: 2px solid var(--admin-primary, #3788d8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.clearButton {
  background: none;
  border: none;
  padding: 8px 12px;
  color: var(--admin-text-secondary, #666666);
  cursor: pointer;
  border-radius: 4px;
  transition: all var(--admin-transition-normal, 0.2s ease);
  font-size: 0.9rem;
}

.clearButton:hover {
  color: var(--admin-text-primary, #1a1a1a);
  background: rgba(0, 0, 0, 0.05);
}

.searchResults {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--admin-bg-primary, #ffffff);
  border: 1px solid var(--admin-border-light, #e0e0e0);
  border-radius: var(--admin-radius-lg, 12px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 10002;
  margin-top: 4px;
  max-height: 400px;
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.resultsHeader {
  padding: 12px 16px 8px;
  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);
  background: var(--admin-bg-secondary, #f8f9fa);
}

.resultsCount {
  font-size: 0.8rem;
  color: var(--admin-text-secondary, #666666);
  font-weight: 500;
}

.resultsList {
  max-height: 300px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.resultItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all var(--admin-transition-normal, 0.2s ease);
  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);
}

.resultItem:last-child {
  border-bottom: none;
}

.resultItem:hover,
.resultItem.selected {
  background: var(--admin-bg-secondary, #f8f9fa);
}

.resultItem.selected {
  background: rgba(55, 136, 216, 0.1);
  border-left: 3px solid var(--admin-primary, #3788d8);
}

.resultIcon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.resultContent {
  flex: 1;
  min-width: 0;
}

.resultTitle {
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  font-size: 0.9rem;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resultSubtitle {
  color: var(--admin-text-secondary, #666666);
  font-size: 0.8rem;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resultDescription {
  color: var(--admin-text-secondary, #666666);
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resultType {
  font-size: 0.7rem;
  color: var(--admin-text-secondary, #666666);
  background: var(--admin-bg-secondary, #f8f9fa);
  padding: 4px 8px;
  border-radius: var(--admin-radius-sm, 4px);
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.resultsFooter {
  padding: 8px 16px;
  border-top: 1px solid var(--admin-border-light, #e0e0e0);
  background: var(--admin-bg-secondary, #f8f9fa);
}

.moreResults {
  font-size: 0.75rem;
  color: var(--admin-text-secondary, #666666);
  font-style: italic;
}

.noResults {
  padding: 24px 16px;
  text-align: center;
}

.noResultsIcon {
  font-size: 2rem;
  margin-bottom: 8px;
  display: block;
  opacity: 0.5;
}

.noResultsText {
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  margin-bottom: 4px;
  font-size: 0.9rem;
}

.noResultsHint {
  color: var(--admin-text-secondary, #666666);
  font-size: 0.8rem;
}

.errorMessage {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--admin-danger, #dc3545);
  font-size: 0.9rem;
}

.errorIcon {
  font-size: 1.1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .globalSearch {
    max-width: 100%;
  }

  .searchResults {
    left: -10px;
    right: -10px;
    max-height: 350px;
  }

  .resultItem {
    padding: 16px;
  }

  .resultTitle {
    font-size: 1rem;
  }

  .resultSubtitle {
    font-size: 0.9rem;
  }

  .resultDescription {
    font-size: 0.8rem;
  }

  .resultIcon {
    font-size: 1.4rem;
    width: 28px;
  }
}

@media (max-width: 480px) {
  .searchInput {
    padding: 0;
  }

  .input {
    padding: 14px 8px 14px 0;
    font-size: 1rem;
  }

  .searchResults {
    left: -20px;
    right: -20px;
    border-radius: var(--admin-radius-md, 8px);
  }

  .resultContent {
    min-width: 0;
  }

  .resultTitle,
  .resultSubtitle,
  .resultDescription {
    white-space: normal;
    overflow: visible;
    text-overflow: initial;
  }

  .resultType {
    display: none; /* Hide on very small screens */
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .searchInput {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
  }

  .searchInput:focus-within {
    background: var(--admin-bg-primary-dark, #1a1a1a);
  }

  .input {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .input::placeholder {
    color: var(--admin-text-secondary-dark, #cccccc);
  }

  .searchResults {
    background: var(--admin-bg-primary-dark, #1a1a1a);
    border-color: var(--admin-border-dark, #404040);
  }

  .resultItem:hover,
  .resultItem.selected {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
  }

  .resultsHeader,
  .resultsFooter {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .searchInput {
    border-width: 2px;
  }

  .searchResults {
    border-width: 2px;
  }

  .resultItem.selected {
    border-left-width: 4px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .searchResults {
    animation: none;
  }

  .spinner {
    animation: none;
  }

  .resultItem,
  .searchInput,
  .clearButton {
    transition: none;
  }
}
