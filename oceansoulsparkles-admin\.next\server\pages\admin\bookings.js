(()=>{var e={};e.id=5092,e.ids=[5092,660],e.modules={8461:e=>{e.exports={analyticsContainer:"BookingAnalytics_analyticsContainer__9mwvM",analyticsHeader:"BookingAnalytics_analyticsHeader__xLMhj",timeRangeSelector:"BookingAnalytics_timeRangeSelector__PDmBu",active:"BookingAnalytics_active__TxOIw",metricsGrid:"BookingAnalytics_metricsGrid__RhMfg",metricCard:"BookingAnalytics_metricCard__Wulh9",metricValue:"BookingAnalytics_metricValue__y1B6e",metricLabel:"BookingAnalytics_metricLabel__8Zt27",chartsGrid:"BookingAnalytics_chartsGrid__j_qu3",chartCard:"BookingAnalytics_chartCard__MnasY",statusChart:"BookingAnalytics_statusChart__nf1N9",statusItem:"BookingAnalytics_statusItem__7JNm3",statusIndicator:"BookingAnalytics_statusIndicator__CYo8r",statusLabel:"BookingAnalytics_statusLabel__Hd5ye",statusCount:"BookingAnalytics_statusCount__uFsxj",topList:"BookingAnalytics_topList__AZWQO",topItem:"BookingAnalytics_topItem__zZAmz",topRank:"BookingAnalytics_topRank__P6LSm",topInfo:"BookingAnalytics_topInfo__cTaK_",topName:"BookingAnalytics_topName__Rp2A8",topStats:"BookingAnalytics_topStats__0sP2c",trendChart:"BookingAnalytics_trendChart__Y5IeY",trendItem:"BookingAnalytics_trendItem__2BYPl",trendMonth:"BookingAnalytics_trendMonth__yqawq",trendBookings:"BookingAnalytics_trendBookings__jtFHq",trendRevenue:"BookingAnalytics_trendRevenue__Haw3F",emptyState:"BookingAnalytics_emptyState__cVXz4"}},9113:e=>{e.exports={calendarContainer:"BookingCalendar_calendarContainer__gwvLi",calendarHeader:"BookingCalendar_calendarHeader__6KhH7",monthNavigation:"BookingCalendar_monthNavigation___DEjo",navButton:"BookingCalendar_navButton__g2PdZ",monthTitle:"BookingCalendar_monthTitle__DiK7E",todayButton:"BookingCalendar_todayButton__Lht_7",calendar:"BookingCalendar_calendar__9Rf9A",dayHeaders:"BookingCalendar_dayHeaders__gEDwd",dayHeader:"BookingCalendar_dayHeader__zSjjo",calendarGrid:"BookingCalendar_calendarGrid__umy5o",calendarDay:"BookingCalendar_calendarDay__fFkuf",today:"BookingCalendar_today__ItoLp",otherMonth:"BookingCalendar_otherMonth__qwT2U",dayNumber:"BookingCalendar_dayNumber__bJmAK",bookingsContainer:"BookingCalendar_bookingsContainer__c_ltx",bookingItem:"BookingCalendar_bookingItem__Ex83v",bookingTime:"BookingCalendar_bookingTime__XlFnR",bookingCustomer:"BookingCalendar_bookingCustomer__Gbocf",bookingService:"BookingCalendar_bookingService__by0T_",moreBookings:"BookingCalendar_moreBookings__RymFm"}},8704:e=>{e.exports={bookingsContainer:"Bookings_bookingsContainer__LxQns",header:"Bookings_header__LPpER",title:"Bookings_title__kkjQ_",headerActions:"Bookings_headerActions__oOFo9",newBookingBtn:"Bookings_newBookingBtn__DThjN",backButton:"Bookings_backButton__d7v8l",controlsPanel:"Bookings_controlsPanel__OqLxe",viewToggle:"Bookings_viewToggle__qHn5e",viewBtn:"Bookings_viewBtn__halWp",active:"Bookings_active__EPiR9",filters:"Bookings_filters__efSCE",searchInput:"Bookings_searchInput__WU9rK",statusFilter:"Bookings_statusFilter__cEsIO",dateFilter:"Bookings_dateFilter__91_jc",bookingsContent:"Bookings_bookingsContent__UNoyE",bookingsList:"Bookings_bookingsList__1shaa",emptyState:"Bookings_emptyState__vwGqK",bookingCard:"Bookings_bookingCard__rr_2I",bookingHeader:"Bookings_bookingHeader__kN4o8",customerInfo:"Bookings_customerInfo__sr86g",statusBadge:"Bookings_statusBadge__YyBWn",statusPending:"Bookings_statusPending__KxyNp",statusConfirmed:"Bookings_statusConfirmed__9YBLQ",statusCompleted:"Bookings_statusCompleted___ZbBM",statusCancelled:"Bookings_statusCancelled__L3mcN",statusDefault:"Bookings_statusDefault__P9Wkr",bookingDetails:"Bookings_bookingDetails__uGdps",serviceInfo:"Bookings_serviceInfo__aPhqp",timeInfo:"Bookings_timeInfo__fzguw",bookingNotes:"Bookings_bookingNotes__y0sB6",bookingActions:"Bookings_bookingActions__GQrrp",statusSelect:"Bookings_statusSelect__1MW5p",calendarView:"Bookings_calendarView__mzDJ0",comingSoon:"Bookings_comingSoon__Ssaev",loadingContainer:"Bookings_loadingContainer__aLt2y",loadingSpinner:"Bookings_loadingSpinner__V78_d",spin:"Bookings_spin__RJm_5"}},6806:e=>{e.exports={exportButton:"ExportButton_exportButton__utvly",exportBtn:"ExportButton_exportBtn__tIopM",exporting:"ExportButton_exporting__sXhBs",spinner:"ExportButton_spinner__9VMfk",spin:"ExportButton_spin__rvyvr",dropdownArrow:"ExportButton_dropdownArrow__ukQOV",exportDropdown:"ExportButton_exportDropdown__Rv4p1",slideDown:"ExportButton_slideDown__Cf7OP",dropdownHeader:"ExportButton_dropdownHeader__ZrlI_",dropdownItem:"ExportButton_dropdownItem__fkbTm",disabled:"ExportButton_disabled__D8pyR",formatIcon:"ExportButton_formatIcon__jrB0p",formatInfo:"ExportButton_formatInfo__CpXUd",formatName:"ExportButton_formatName__yKvvD",formatDesc:"ExportButton_formatDesc__vDwii",success:"ExportButton_success__suWHu",successPulse:"ExportButton_successPulse__zvjZh"}},2671:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{config:()=>p,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>g,getStaticProps:()=>_,reportWebVitals:()=>k,routeModule:()=>y,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>x});var n=a(7093),o=a(5244),i=a(1323),r=a(2899),l=a.n(r),c=a(6814),d=a(9562),m=e([c,d]);[c,d]=m.then?(await m)():m;let u=(0,i.l)(d,"default"),_=(0,i.l)(d,"getStaticProps"),g=(0,i.l)(d,"getStaticPaths"),h=(0,i.l)(d,"getServerSideProps"),p=(0,i.l)(d,"config"),k=(0,i.l)(d,"reportWebVitals"),x=(0,i.l)(d,"unstable_getStaticProps"),v=(0,i.l)(d,"unstable_getStaticPaths"),b=(0,i.l)(d,"unstable_getStaticParams"),j=(0,i.l)(d,"unstable_getServerProps"),N=(0,i.l)(d,"unstable_getServerSideProps"),y=new n.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/admin/bookings",pathname:"/admin/bookings",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});s()}catch(e){s(e)}})},892:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});var s=a(997),n=a(6689),o=a(8461),i=a.n(o);function r({bookings:e}){let[t,a]=(0,n.useState)(null),[o,r]=(0,n.useState)("30d"),l=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),c=e=>{switch(e?.toLowerCase()){case"confirmed":return"#28a745";case"pending":return"#ffc107";case"cancelled":return"#dc3545";case"completed":return"#17a2b8";default:return"#6c757d"}};return t?(0,s.jsxs)("div",{className:i().analyticsContainer,children:[(0,s.jsxs)("div",{className:i().analyticsHeader,children:[s.jsx("h3",{children:"Booking Analytics"}),(0,s.jsxs)("div",{className:i().timeRangeSelector,children:[s.jsx("button",{className:"7d"===o?i().active:"",onClick:()=>r("7d"),children:"7 Days"}),s.jsx("button",{className:"30d"===o?i().active:"",onClick:()=>r("30d"),children:"30 Days"}),s.jsx("button",{className:"90d"===o?i().active:"",onClick:()=>r("90d"),children:"90 Days"}),s.jsx("button",{className:"1y"===o?i().active:"",onClick:()=>r("1y"),children:"1 Year"})]})]}),(0,s.jsxs)("div",{className:i().metricsGrid,children:[(0,s.jsxs)("div",{className:i().metricCard,children:[s.jsx("div",{className:i().metricValue,children:t.totalBookings}),s.jsx("div",{className:i().metricLabel,children:"Total Bookings"})]}),(0,s.jsxs)("div",{className:i().metricCard,children:[s.jsx("div",{className:i().metricValue,children:l(t.totalRevenue)}),s.jsx("div",{className:i().metricLabel,children:"Total Revenue"})]}),(0,s.jsxs)("div",{className:i().metricCard,children:[s.jsx("div",{className:i().metricValue,children:l(t.averageBookingValue)}),s.jsx("div",{className:i().metricLabel,children:"Avg Booking Value"})]}),(0,s.jsxs)("div",{className:i().metricCard,children:[s.jsx("div",{className:i().metricValue,children:t.bookingsThisMonth}),s.jsx("div",{className:i().metricLabel,children:"This Month"})]})]}),(0,s.jsxs)("div",{className:i().chartsGrid,children:[(0,s.jsxs)("div",{className:i().chartCard,children:[s.jsx("h4",{children:"Booking Status"}),s.jsx("div",{className:i().statusChart,children:Object.entries(t.statusBreakdown).map(([e,t])=>(0,s.jsxs)("div",{className:i().statusItem,children:[s.jsx("div",{className:i().statusIndicator,style:{backgroundColor:c(e)}}),s.jsx("span",{className:i().statusLabel,children:e}),s.jsx("span",{className:i().statusCount,children:t})]},e))})]}),(0,s.jsxs)("div",{className:i().chartCard,children:[s.jsx("h4",{children:"Top Services"}),s.jsx("div",{className:i().topList,children:t.topServices.map((e,t)=>(0,s.jsxs)("div",{className:i().topItem,children:[(0,s.jsxs)("div",{className:i().topRank,children:["#",t+1]}),(0,s.jsxs)("div",{className:i().topInfo,children:[s.jsx("div",{className:i().topName,children:e.name}),(0,s.jsxs)("div",{className:i().topStats,children:[e.count," bookings • ",l(e.revenue)]})]})]},e.name))})]}),(0,s.jsxs)("div",{className:i().chartCard,children:[s.jsx("h4",{children:"Top Artists"}),s.jsx("div",{className:i().topList,children:t.topArtists.map((e,t)=>(0,s.jsxs)("div",{className:i().topItem,children:[(0,s.jsxs)("div",{className:i().topRank,children:["#",t+1]}),(0,s.jsxs)("div",{className:i().topInfo,children:[s.jsx("div",{className:i().topName,children:e.name}),(0,s.jsxs)("div",{className:i().topStats,children:[e.count," bookings • ",l(e.revenue)]})]})]},e.name))})]}),(0,s.jsxs)("div",{className:i().chartCard,children:[s.jsx("h4",{children:"Monthly Trend"}),s.jsx("div",{className:i().trendChart,children:t.monthlyTrend.map((e,t)=>(0,s.jsxs)("div",{className:i().trendItem,children:[s.jsx("div",{className:i().trendMonth,children:e.month}),(0,s.jsxs)("div",{className:i().trendBookings,children:[e.bookings," bookings"]}),s.jsx("div",{className:i().trendRevenue,children:l(e.revenue)})]},t))})]})]})]}):s.jsx("div",{className:i().analyticsContainer,children:s.jsx("div",{className:i().emptyState,children:s.jsx("p",{children:"No booking data available for analysis"})})})}},3337:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});var s=a(997),n=a(6689),o=a(9113),i=a.n(o);function r({bookings:e,onBookingClick:t,onDateClick:a}){let[o,r]=(0,n.useState)(new Date),[l,c]=(0,n.useState)([]),[d,m]=(0,n.useState)({}),u=e=>{let t=new Date(o);"prev"===e?t.setMonth(t.getMonth()-1):t.setMonth(t.getMonth()+1),r(t)},_=e=>{let t=new Date;return e.toDateString()===t.toDateString()},g=e=>e.getMonth()===o.getMonth(),h=e=>new Date(e).toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit",hour12:!1}),p=e=>{switch(e?.toLowerCase()){case"confirmed":return"#28a745";case"pending":return"#ffc107";case"cancelled":return"#dc3545";case"completed":return"#17a2b8";default:return"#6c757d"}};return(0,s.jsxs)("div",{className:i().calendarContainer,children:[(0,s.jsxs)("div",{className:i().calendarHeader,children:[(0,s.jsxs)("div",{className:i().monthNavigation,children:[s.jsx("button",{onClick:()=>u("prev"),className:i().navButton,children:"←"}),(0,s.jsxs)("h2",{className:i().monthTitle,children:[["January","February","March","April","May","June","July","August","September","October","November","December"][o.getMonth()]," ",o.getFullYear()]}),s.jsx("button",{onClick:()=>u("next"),className:i().navButton,children:"→"})]}),s.jsx("button",{onClick:()=>{r(new Date)},className:i().todayButton,children:"Today"})]}),(0,s.jsxs)("div",{className:i().calendar,children:[s.jsx("div",{className:i().dayHeaders,children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>s.jsx("div",{className:i().dayHeader,children:e},e))}),s.jsx("div",{className:i().calendarGrid,children:l.map((e,n)=>{let o=d[e.toISOString().split("T")[0]]||[];return(0,s.jsxs)("div",{className:`${i().calendarDay} ${_(e)?i().today:""} ${g(e)?"":i().otherMonth}`,onClick:()=>a?.(e),children:[s.jsx("div",{className:i().dayNumber,children:e.getDate()}),(0,s.jsxs)("div",{className:i().bookingsContainer,children:[o.slice(0,3).map(e=>(0,s.jsxs)("div",{className:i().bookingItem,style:{borderLeftColor:p(e.status)},onClick:a=>{a.stopPropagation(),t?.(e)},title:`${e.customer_name} - ${e.service_name} at ${h(e.start_time)}`,children:[s.jsx("div",{className:i().bookingTime,children:h(e.start_time)}),s.jsx("div",{className:i().bookingCustomer,children:e.customer_name}),s.jsx("div",{className:i().bookingService,children:e.service_name})]},e.id)),o.length>3&&(0,s.jsxs)("div",{className:i().moreBookings,children:["+",o.length-3," more"]})]})]},n)})})]})]})}},5179:(e,t,a)=>{"use strict";a.d(t,{F:()=>c});var s=a(997),n=a(6689),o=a(8370),i=a(6806),r=a.n(i);function l({data:e,type:t,className:i="",disabled:l=!1,options:c={},onExportStart:d,onExportComplete:m,onExportError:u}){let[_,g]=(0,n.useState)(!1),[h,p]=(0,n.useState)(!1),k=(0,n.useRef)(null),x=async(s="csv")=>{if(!e||0===e.length){u?.(Error("No data to export"));return}g(!0),p(!1),d?.();try{if("csv"===s)switch(t){case"bookings":(0,o.FC)(e,c);break;case"services":(0,o.r8)(e,c);break;case"customers":(0,o.Aq)(e,c);break;case"products":(0,o.yA)(e,c);break;case"inventory":(0,o.sg)(e,c);break;case"custom":let{exportToCSV:n}=await Promise.resolve().then(a.bind(a,8370));n(e,c);break;default:throw Error(`Unsupported export type: ${t}`)}else throw Error(`Export format ${s} not yet supported`);m?.()}catch(e){console.error("Export error:",e),u?.(e instanceof Error?e:Error("Export failed"))}finally{g(!1)}};return(0,s.jsxs)("div",{className:`${r().exportButton} ${i}`,ref:k,children:[(0,s.jsxs)("button",{onClick:()=>p(!h),disabled:l||_||0===e.length,className:`${r().exportBtn} ${_?r().exporting:""}`,title:l||0===e.length?"No data available to export":`Export ${e.length} ${t} records`,children:[_&&s.jsx("div",{className:r().spinner}),_?"Exporting...":0===e.length?"No Data":`📥 Export (${e.length})`,!_&&e.length>0&&s.jsx("span",{className:r().dropdownArrow,children:"▼"})]}),h&&e.length>0&&(0,s.jsxs)("div",{className:r().exportDropdown,children:[s.jsx("div",{className:r().dropdownHeader,children:s.jsx("span",{children:"Export Format"})}),(0,s.jsxs)("button",{onClick:()=>x("csv"),className:r().dropdownItem,disabled:_,children:[s.jsx("span",{className:r().formatIcon,children:"\uD83D\uDCC4"}),(0,s.jsxs)("div",{className:r().formatInfo,children:[s.jsx("div",{className:r().formatName,children:"CSV File"}),s.jsx("div",{className:r().formatDesc,children:"Comma-separated values"})]})]}),(0,s.jsxs)("button",{onClick:()=>x("excel"),className:`${r().dropdownItem} ${r().disabled}`,disabled:!0,title:"Excel export coming soon",children:[s.jsx("span",{className:r().formatIcon,children:"\uD83D\uDCCA"}),(0,s.jsxs)("div",{className:r().formatInfo,children:[s.jsx("div",{className:r().formatName,children:"Excel File"}),s.jsx("div",{className:r().formatDesc,children:"Coming soon"})]})]}),(0,s.jsxs)("button",{onClick:()=>x("pdf"),className:`${r().dropdownItem} ${r().disabled}`,disabled:!0,title:"PDF export coming soon",children:[s.jsx("span",{className:r().formatIcon,children:"\uD83D\uDCCB"}),(0,s.jsxs)("div",{className:r().formatInfo,children:[s.jsx("div",{className:r().formatName,children:"PDF Report"}),s.jsx("div",{className:r().formatDesc,children:"Coming soon"})]})]})]})]})}let c=({data:e,type:t,className:a="",...n})=>s.jsx(l,{data:e,type:t,className:a,onExportError:e=>{console.error("Export error:",e),alert(`Export failed: ${e.message}`)},...n})},8370:(e,t,a)=>{"use strict";function s(e,t={}){let{filename:a="export",columns:s,includeTimestamp:n=!0,dateFormat:o="short"}=t;if(!e||0===e.length)throw Error("No data to export");let i=s||Object.keys(e[0]).map(e=>({key:e,label:e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())})),r=[i.map(e=>e.label),...e.map(e=>i.map(t=>{let a=t.key.split(".").reduce((e,t)=>e?.[t],e);return t.formatter?t.formatter(a,e):null==a?"":"boolean"==typeof a?a?"Yes":"No":a instanceof Date||"string"==typeof a&&/^\d{4}-\d{2}-\d{2}/.test(a)&&!isNaN(Date.parse(a))?d(a):String(a)}))].map(e=>e.map(e=>`"${String(e||"").replace(/"/g,'""')}"`).join(",")).join("\n"),l=n?`-${new Date().toISOString().split("T")[0]}`:"";(function(e,t,a){let s=new Blob([e],{type:a}),n=window.URL.createObjectURL(s),o=document.createElement("a");o.href=n,o.download=t,document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(n),document.body.removeChild(o)})(r,`${a}${l}.csv`,"text/csv")}function n(e,t={}){s(e,{filename:"bookings",columns:[{key:"customer_name",label:"Customer Name"},{key:"customer_email",label:"Customer Email"},{key:"customer_phone",label:"Customer Phone"},{key:"service_name",label:"Service"},{key:"artist_name",label:"Artist"},{key:"booking_date",label:"Date",formatter:e=>c(e)},{key:"booking_time",label:"Time"},{key:"status",label:"Status",formatter:e=>e?.toUpperCase()||"UNKNOWN"},{key:"total_amount",label:"Amount",formatter:e=>m(e)},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function o(e,t={}){s(e,{filename:"services",columns:[{key:"name",label:"Service Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"base_price",label:"Base Price",formatter:e=>m(e)},{key:"duration",label:"Duration (min)"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"total_bookings",label:"Total Bookings"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function i(e,t={}){s(e,{filename:"customers",columns:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"email",label:"Email"},{key:"phone",label:"Phone"},{key:"total_bookings",label:"Total Bookings"},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function r(e,t={}){s(e,{filename:"products",columns:[{key:"name",label:"Product Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"price",label:"Price",formatter:e=>m(e)},{key:"stock_quantity",label:"Stock"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function l(e,t={}){s(e,{filename:"inventory",columns:[{key:"name",label:"Item Name"},{key:"category",label:"Category"},{key:"current_stock",label:"Current Stock"},{key:"minimum_stock",label:"Minimum Stock"},{key:"unit_cost",label:"Unit Cost",formatter:e=>m(e)},{key:"supplier_name",label:"Supplier"},{key:"last_restocked",label:"Last Restocked",formatter:e=>c(e)},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function c(e){return e?new Date(e).toLocaleDateString("en-AU"):""}function d(e){if(!e)return"";let t=new Date(e);return t.toLocaleDateString("en-AU")+" "+t.toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit"})}function m(e){return!e||isNaN(e)?"$0.00":new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(Number(e))}a.d(t,{Aq:()=>i,FC:()=>n,exportToCSV:()=>s,r8:()=>o,sg:()=>l,yA:()=>r})},9562:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{default:()=>b});var n=a(997),o=a(6689),i=a(968),r=a.n(i),l=a(1163),c=a(1664),d=a.n(c),m=a(4845),u=a(3337),_=a(892),g=a(5179),h=a(8568),p=a(3590),k=a(8704),x=a.n(k),v=e([m,p]);function b(){let e=(0,l.useRouter)(),{user:t,loading:a}=(0,h.a)(),[s,i]=(0,o.useState)([]),[c,k]=(0,o.useState)([]),[v,b]=(0,o.useState)(!0),[j,N]=(0,o.useState)(new Date().toISOString().split("T")[0]),[y,B]=(0,o.useState)("all"),[C,f]=(0,o.useState)(""),[S,w]=(0,o.useState)("list"),A=async(e,t)=>{try{let a=localStorage.getItem("admin-token");if(!(await fetch(`/api/admin/bookings/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify({status:t})})).ok)throw Error("Failed to update booking status");i(a=>a.map(a=>a.id===e?{...a,status:t}:a))}catch(e){console.error("Error updating booking status:",e)}},D=e=>{switch(e){case"confirmed":return x().statusConfirmed;case"pending":return x().statusPending;case"completed":return x().statusCompleted;case"cancelled":return x().statusCancelled;default:return x().statusDefault}},I=e=>{let t=new Date(e);return{date:t.toLocaleDateString(),time:t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}};return a||v?n.jsx(m.Z,{children:(0,n.jsxs)("div",{className:x().loadingContainer,children:[n.jsx("div",{className:x().loadingSpinner}),n.jsx("p",{children:"Loading bookings..."})]})}):(0,n.jsxs)(m.Z,{children:[(0,n.jsxs)(r(),{children:[n.jsx("title",{children:"Bookings Management | Ocean Soul Sparkles Admin"}),n.jsx("meta",{name:"description",content:"Manage customer bookings and appointments"})]}),(0,n.jsxs)("div",{className:x().bookingsContainer,children:[(0,n.jsxs)("header",{className:x().header,children:[n.jsx("h1",{className:x().title,children:"Bookings Management"}),(0,n.jsxs)("div",{className:x().headerActions,children:[n.jsx(g.F,{data:c,type:"bookings",className:x().exportBtn}),n.jsx(d(),{href:"/admin/bookings/new",className:x().newBookingBtn,children:"+ New Booking"}),n.jsx("button",{className:x().backButton,onClick:()=>e.push("/"),children:"← Back to Dashboard"})]})]}),n.jsx(_.Z,{bookings:s}),(0,n.jsxs)("div",{className:x().controlsPanel,children:[(0,n.jsxs)("div",{className:x().viewToggle,children:[n.jsx("button",{className:`${x().viewBtn} ${"list"===S?x().active:""}`,onClick:()=>w("list"),children:"List View"}),n.jsx("button",{className:`${x().viewBtn} ${"calendar"===S?x().active:""}`,onClick:()=>w("calendar"),children:"Calendar View"})]}),(0,n.jsxs)("div",{className:x().filters,children:[n.jsx("input",{type:"text",placeholder:"Search bookings...",value:C,onChange:e=>f(e.target.value),className:x().searchInput}),(0,n.jsxs)("select",{value:y,onChange:e=>B(e.target.value),className:x().statusFilter,children:[n.jsx("option",{value:"all",children:"All Status"}),n.jsx("option",{value:"pending",children:"Pending"}),n.jsx("option",{value:"confirmed",children:"Confirmed"}),n.jsx("option",{value:"completed",children:"Completed"}),n.jsx("option",{value:"cancelled",children:"Cancelled"})]}),"list"===S&&n.jsx("input",{type:"date",value:j,onChange:e=>N(e.target.value),className:x().dateFilter})]})]}),(0,n.jsxs)("div",{className:x().bookingsContent,children:["list"===S?n.jsx("div",{className:x().bookingsList,children:0===c.length?n.jsx("div",{className:x().emptyState,children:n.jsx("p",{children:"No bookings found for the selected criteria."})}):c.map(e=>{let{date:t,time:a}=I(e.start_time),s=I(e.end_time).time;return(0,n.jsxs)("div",{className:x().bookingCard,children:[(0,n.jsxs)("div",{className:x().bookingHeader,children:[(0,n.jsxs)("div",{className:x().customerInfo,children:[n.jsx("h3",{children:e.customer_name}),n.jsx("p",{children:e.customer_email})]}),n.jsx("div",{className:`${x().statusBadge} ${D(e.status)}`,children:e.status})]}),(0,n.jsxs)("div",{className:x().bookingDetails,children:[(0,n.jsxs)("div",{className:x().serviceInfo,children:[n.jsx("strong",{children:e.service_name}),(0,n.jsxs)("p",{children:["Artist: ",e.artist]}),(0,n.jsxs)("p",{children:["Price: $",e.price]})]}),(0,n.jsxs)("div",{className:x().timeInfo,children:[n.jsx("p",{children:n.jsx("strong",{children:t})}),(0,n.jsxs)("p",{children:[a," - ",s]})]})]}),e.notes&&(0,n.jsxs)("div",{className:x().bookingNotes,children:[n.jsx("strong",{children:"Notes:"})," ",e.notes]}),(0,n.jsxs)("div",{className:x().bookingActions,children:[n.jsx(d(),{href:`/admin/bookings/${e.id}`,className:x().viewBtn,children:"View Details"}),(0,n.jsxs)("select",{value:e.status,onChange:t=>A(e.id,t.target.value),className:x().statusSelect,children:[n.jsx("option",{value:"pending",children:"Pending"}),n.jsx("option",{value:"confirmed",children:"Confirmed"}),n.jsx("option",{value:"completed",children:"Completed"}),n.jsx("option",{value:"cancelled",children:"Cancelled"})]})]})]},e.id)})}):n.jsx("div",{className:x().calendarView,children:n.jsx(u.Z,{bookings:s,onBookingClick:t=>{e.push(`/admin/bookings/${t.id}`)},onDateClick:e=>{let t=e.toISOString().split("T")[0];N(t),w("list"),p.toast.info(`Switched to list view for ${e.toLocaleDateString()}`)}})}),"        "]})]})]})}[m,p]=v.then?(await v)():v,s()}catch(e){s(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[2899,6212,1664,7441],()=>a(2671));module.exports=s})();