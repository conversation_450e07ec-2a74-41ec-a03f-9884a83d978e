/**
 * SMS Service for Ocean Soul Sparkles Admin Dashboard
 * 
 * Provides SMS functionality with <PERSON>wilio integration and fallback logging.
 * Includes feature flag checking and settings-based control.
 */

const twilio = require('twilio');

/**
 * SMS service class with feature flag support
 */
class SMSService {
  constructor() {
    this.isConfigured = !!(process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN);
    this.client = this.isConfigured ? twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN) : null;
    this.fromNumber = process.env.TWILIO_PHONE_NUMBER || '+**********';
  }

  /**
   * Check if SMS notifications are enabled in system settings
   */
  async checkSMSEnabled(notificationType = null) {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/admin/settings`);
      const { settings } = await response.json();
      
      const notifications = settings?.notifications || {};
      
      // Check global SMS toggle
      if (!notifications.smsNotifications) {
        return { enabled: false, reason: 'SMS notifications disabled globally' };
      }
      
      // Check specific notification type if provided
      if (notificationType) {
        const typeKey = `sms${notificationType.charAt(0).toUpperCase() + notificationType.slice(1)}`;
        if (notifications[typeKey] === false) {
          return { enabled: false, reason: `SMS ${notificationType} notifications disabled` };
        }
      }
      
      return { enabled: true };
    } catch (error) {
      console.error('Error checking SMS settings:', error);
      // Default to disabled if settings check fails
      return { enabled: false, reason: 'Settings check failed' };
    }
  }

  /**
   * Send booking confirmation SMS
   */
  async sendBookingConfirmation(booking) {
    const settingsCheck = await this.checkSMSEnabled('bookingConfirmation');
    if (!settingsCheck.enabled) {
      console.log(`SMS booking confirmation skipped: ${settingsCheck.reason}`);
      return { success: false, skipped: true, reason: settingsCheck.reason };
    }

    if (!booking.customerPhone) {
      console.warn('No customer phone provided for booking confirmation SMS');
      return { success: false, error: 'No customer phone' };
    }

    const message = `Hi ${booking.customerName}! Your appointment for ${booking.serviceName} is confirmed for ${booking.date} at ${booking.time}. Location: Ocean Soul Sparkles. Questions? Reply to this message.`;
    
    return await this.sendSMS({
      to: booking.customerPhone,
      message,
      type: 'booking_confirmation',
      bookingId: booking.id
    });
  }

  /**
   * Send booking reminder SMS
   */
  async sendBookingReminder(booking) {
    const settingsCheck = await this.checkSMSEnabled('bookingReminder');
    if (!settingsCheck.enabled) {
      console.log(`SMS booking reminder skipped: ${settingsCheck.reason}`);
      return { success: false, skipped: true, reason: settingsCheck.reason };
    }

    if (!booking.customerPhone) {
      console.warn('No customer phone provided for booking reminder SMS');
      return { success: false, error: 'No customer phone' };
    }

    const message = `Reminder: Your appointment for ${booking.serviceName} is tomorrow at ${booking.time}. See you at Ocean Soul Sparkles! Reply CONFIRM to confirm or CANCEL to reschedule.`;
    
    return await this.sendSMS({
      to: booking.customerPhone,
      message,
      type: 'booking_reminder',
      bookingId: booking.id
    });
  }

  /**
   * Send booking cancellation SMS
   */
  async sendBookingCancellation(booking) {
    const settingsCheck = await this.checkSMSEnabled('bookingCancellation');
    if (!settingsCheck.enabled) {
      console.log(`SMS booking cancellation skipped: ${settingsCheck.reason}`);
      return { success: false, skipped: true, reason: settingsCheck.reason };
    }

    if (!booking.customerPhone) {
      console.warn('No customer phone provided for booking cancellation SMS');
      return { success: false, error: 'No customer phone' };
    }

    const message = `Your appointment for ${booking.serviceName} on ${booking.date} at ${booking.time} has been cancelled. To reschedule, please contact Ocean Soul Sparkles. Thank you!`;
    
    return await this.sendSMS({
      to: booking.customerPhone,
      message,
      type: 'booking_cancellation',
      bookingId: booking.id
    });
  }

  /**
   * Send staff notification SMS
   */
  async sendStaffNotification(notification) {
    const settingsCheck = await this.checkSMSEnabled('staffNotification');
    if (!settingsCheck.enabled) {
      console.log(`SMS staff notification skipped: ${settingsCheck.reason}`);
      return { success: false, skipped: true, reason: settingsCheck.reason };
    }

    if (!notification.staffPhone) {
      console.warn('No staff phone provided for notification SMS');
      return { success: false, error: 'No staff phone' };
    }

    const message = notification.message || `Staff notification: ${notification.subject}`;
    
    return await this.sendSMS({
      to: notification.staffPhone,
      message,
      type: 'staff_notification',
      staffId: notification.staffId
    });
  }

  /**
   * Send promotional SMS
   */
  async sendPromotionalSMS(customer, message) {
    const settingsCheck = await this.checkSMSEnabled('promotional');
    if (!settingsCheck.enabled) {
      console.log(`SMS promotional message skipped: ${settingsCheck.reason}`);
      return { success: false, skipped: true, reason: settingsCheck.reason };
    }

    if (!customer.phone) {
      console.warn('No customer phone provided for promotional SMS');
      return { success: false, error: 'No customer phone' };
    }

    return await this.sendSMS({
      to: customer.phone,
      message,
      type: 'promotional',
      customerId: customer.id
    });
  }

  /**
   * Core SMS sending function
   */
  async sendSMS({ to, message, type, bookingId, customerId, staffId }) {
    // Normalize phone number
    const phoneNumber = this.normalizePhoneNumber(to);
    
    if (!phoneNumber) {
      return { success: false, error: 'Invalid phone number format' };
    }

    // If Twilio not configured, log to console
    if (!this.client) {
      console.log('\n📱 SMS WOULD BE SENT:');
      console.log('To:', phoneNumber);
      console.log('Message:', message);
      console.log('Type:', type);
      console.log('---\n');
      return { 
        success: true, 
        messageId: 'console-log-' + Date.now(),
        fallback: true 
      };
    }

    try {
      const result = await this.client.messages.create({
        body: message,
        from: this.fromNumber,
        to: phoneNumber
      });

      console.log(`SMS sent successfully: ${result.sid}`);
      
      return {
        success: true,
        messageId: result.sid,
        status: result.status,
        to: phoneNumber,
        type
      };
    } catch (error) {
      console.error('SMS sending failed:', error);
      return {
        success: false,
        error: error.message,
        code: error.code,
        to: phoneNumber,
        type
      };
    }
  }

  /**
   * Send bulk SMS messages
   */
  async sendBulkSMS(recipients, message, type = 'bulk') {
    const settingsCheck = await this.checkSMSEnabled(type);
    if (!settingsCheck.enabled) {
      console.log(`Bulk SMS skipped: ${settingsCheck.reason}`);
      return { success: false, skipped: true, reason: settingsCheck.reason };
    }

    const results = [];
    
    for (const recipient of recipients) {
      try {
        const result = await this.sendSMS({
          to: recipient.phone,
          message: message.replace(/{{name}}/g, recipient.name || 'Valued Customer'),
          type,
          customerId: recipient.id
        });
        results.push({ phone: recipient.phone, ...result });
        
        // Add delay to avoid overwhelming SMS service
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        results.push({ 
          phone: recipient.phone, 
          success: false, 
          error: error.message 
        });
      }
    }
    
    return results;
  }

  /**
   * Normalize phone number to international format
   */
  normalizePhoneNumber(phone) {
    if (!phone) return null;
    
    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '');
    
    // Handle Australian numbers
    if (digits.startsWith('61')) {
      return '+' + digits;
    } else if (digits.startsWith('0') && digits.length === 10) {
      return '+61' + digits.substring(1);
    } else if (digits.length === 9) {
      return '+61' + digits;
    } else if (digits.startsWith('1') && digits.length === 11) {
      return '+' + digits;
    }
    
    // Default: assume it's already in correct format or add +61 for Australian
    return digits.length >= 10 ? '+' + digits : null;
  }

  /**
   * Verify SMS configuration
   */
  async verifyConfiguration() {
    if (!this.isConfigured) {
      return {
        configured: false,
        error: 'Twilio credentials not configured'
      };
    }

    try {
      // Test Twilio connection by fetching account info
      const account = await this.client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
      
      return {
        configured: true,
        accountSid: account.sid,
        status: account.status,
        fromNumber: this.fromNumber
      };
    } catch (error) {
      return {
        configured: false,
        error: error.message
      };
    }
  }

  /**
   * Get SMS service status
   */
  getStatus() {
    return {
      configured: this.isConfigured,
      provider: 'Twilio',
      fromNumber: this.fromNumber,
      environment: process.env.NODE_ENV || 'development'
    };
  }
}

module.exports = new SMSService();
