(()=>{var e={};e.id=9148,e.ids=[9148,660],e.modules={6806:e=>{e.exports={exportButton:"ExportButton_exportButton__utvly",exportBtn:"ExportButton_exportBtn__tIopM",exporting:"ExportButton_exporting__sXhBs",spinner:"ExportButton_spinner__9VMfk",spin:"ExportButton_spin__rvyvr",dropdownArrow:"ExportButton_dropdownArrow__ukQOV",exportDropdown:"ExportButton_exportDropdown__Rv4p1",slideDown:"ExportButton_slideDown__Cf7OP",dropdownHeader:"ExportButton_dropdownHeader__ZrlI_",dropdownItem:"ExportButton_dropdownItem__fkbTm",disabled:"ExportButton_disabled__D8pyR",formatIcon:"ExportButton_formatIcon__jrB0p",formatInfo:"ExportButton_formatInfo__CpXUd",formatName:"ExportButton_formatName__yKvvD",formatDesc:"ExportButton_formatDesc__vDwii",success:"ExportButton_success__suWHu",successPulse:"ExportButton_successPulse__zvjZh"}},9611:e=>{e.exports={servicesContainer:"Services_servicesContainer__8QxNu",header:"Services_header__IbB5h",title:"Services_title__EjFUH",headerActions:"Services_headerActions__szgs7",newServiceBtn:"Services_newServiceBtn__CY17p",backButton:"Services_backButton__7_3qx",controlsPanel:"Services_controlsPanel__aS7XE",searchSection:"Services_searchSection__F9zbf",searchInput:"Services_searchInput__hPj0n",filters:"Services_filters__fAGdk",categoryFilter:"Services_categoryFilter__Z4V0s",sortSelect:"Services_sortSelect__u7Aoe",servicesContent:"Services_servicesContent__w8Z_j",statsCards:"Services_statsCards__94oSk",statCard:"Services_statCard__oNCIx",statValue:"Services_statValue__7ZjYH",emptyState:"Services_emptyState__0i9rz",servicesGrid:"Services_servicesGrid__7m04a",serviceCard:"Services_serviceCard__E_hZS",serviceHeader:"Services_serviceHeader__cWEP9",serviceInfo:"Services_serviceInfo__M_x0w",category:"Services_category__bAB21",statusBadge:"Services_statusBadge__l2Nef",active:"Services_active__5C_7j",inactive:"Services_inactive__HhFHN",serviceDetails:"Services_serviceDetails__bhQDF",description:"Services_description__vsrev",serviceStats:"Services_serviceStats__lZFEQ",statItem:"Services_statItem__NtyhU",statLabel:"Services_statLabel__Hbj1F",serviceActions:"Services_serviceActions__gNBFO",editBtn:"Services_editBtn__W9iEM",toggleBtn:"Services_toggleBtn__Geg7f",activate:"Services_activate__zeoXn",deactivate:"Services_deactivate__aeEHN",loadingContainer:"Services_loadingContainer__HBsMm",loadingSpinner:"Services_loadingSpinner__bBh3i",spin:"Services_spin__M5QLZ"}},1289:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>x,default:()=>u,getServerSideProps:()=>v,getStaticPaths:()=>_,getStaticProps:()=>p,reportWebVitals:()=>h,routeModule:()=>N,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>g});var a=r(7093),i=r(5244),n=r(1323),o=r(2899),l=r.n(o),c=r(6814),d=r(6034),m=e([c,d]);[c,d]=m.then?(await m)():m;let u=(0,n.l)(d,"default"),p=(0,n.l)(d,"getStaticProps"),_=(0,n.l)(d,"getStaticPaths"),v=(0,n.l)(d,"getServerSideProps"),x=(0,n.l)(d,"config"),h=(0,n.l)(d,"reportWebVitals"),g=(0,n.l)(d,"unstable_getStaticProps"),b=(0,n.l)(d,"unstable_getStaticPaths"),S=(0,n.l)(d,"unstable_getStaticParams"),f=(0,n.l)(d,"unstable_getServerProps"),y=(0,n.l)(d,"unstable_getServerSideProps"),N=new a.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/admin/services",pathname:"/admin/services",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});s()}catch(e){s(e)}})},5179:(e,t,r)=>{"use strict";r.d(t,{F:()=>c});var s=r(997),a=r(6689),i=r(8370),n=r(6806),o=r.n(n);function l({data:e,type:t,className:n="",disabled:l=!1,options:c={},onExportStart:d,onExportComplete:m,onExportError:u}){let[p,_]=(0,a.useState)(!1),[v,x]=(0,a.useState)(!1),h=(0,a.useRef)(null),g=async(s="csv")=>{if(!e||0===e.length){u?.(Error("No data to export"));return}_(!0),x(!1),d?.();try{if("csv"===s)switch(t){case"bookings":(0,i.FC)(e,c);break;case"services":(0,i.r8)(e,c);break;case"customers":(0,i.Aq)(e,c);break;case"products":(0,i.yA)(e,c);break;case"inventory":(0,i.sg)(e,c);break;case"custom":let{exportToCSV:a}=await Promise.resolve().then(r.bind(r,8370));a(e,c);break;default:throw Error(`Unsupported export type: ${t}`)}else throw Error(`Export format ${s} not yet supported`);m?.()}catch(e){console.error("Export error:",e),u?.(e instanceof Error?e:Error("Export failed"))}finally{_(!1)}};return(0,s.jsxs)("div",{className:`${o().exportButton} ${n}`,ref:h,children:[(0,s.jsxs)("button",{onClick:()=>x(!v),disabled:l||p||0===e.length,className:`${o().exportBtn} ${p?o().exporting:""}`,title:l||0===e.length?"No data available to export":`Export ${e.length} ${t} records`,children:[p&&s.jsx("div",{className:o().spinner}),p?"Exporting...":0===e.length?"No Data":`📥 Export (${e.length})`,!p&&e.length>0&&s.jsx("span",{className:o().dropdownArrow,children:"▼"})]}),v&&e.length>0&&(0,s.jsxs)("div",{className:o().exportDropdown,children:[s.jsx("div",{className:o().dropdownHeader,children:s.jsx("span",{children:"Export Format"})}),(0,s.jsxs)("button",{onClick:()=>g("csv"),className:o().dropdownItem,disabled:p,children:[s.jsx("span",{className:o().formatIcon,children:"\uD83D\uDCC4"}),(0,s.jsxs)("div",{className:o().formatInfo,children:[s.jsx("div",{className:o().formatName,children:"CSV File"}),s.jsx("div",{className:o().formatDesc,children:"Comma-separated values"})]})]}),(0,s.jsxs)("button",{onClick:()=>g("excel"),className:`${o().dropdownItem} ${o().disabled}`,disabled:!0,title:"Excel export coming soon",children:[s.jsx("span",{className:o().formatIcon,children:"\uD83D\uDCCA"}),(0,s.jsxs)("div",{className:o().formatInfo,children:[s.jsx("div",{className:o().formatName,children:"Excel File"}),s.jsx("div",{className:o().formatDesc,children:"Coming soon"})]})]}),(0,s.jsxs)("button",{onClick:()=>g("pdf"),className:`${o().dropdownItem} ${o().disabled}`,disabled:!0,title:"PDF export coming soon",children:[s.jsx("span",{className:o().formatIcon,children:"\uD83D\uDCCB"}),(0,s.jsxs)("div",{className:o().formatInfo,children:[s.jsx("div",{className:o().formatName,children:"PDF Report"}),s.jsx("div",{className:o().formatDesc,children:"Coming soon"})]})]})]})]})}let c=({data:e,type:t,className:r="",...a})=>s.jsx(l,{data:e,type:t,className:r,onExportError:e=>{console.error("Export error:",e),alert(`Export failed: ${e.message}`)},...a})},8370:(e,t,r)=>{"use strict";function s(e,t={}){let{filename:r="export",columns:s,includeTimestamp:a=!0,dateFormat:i="short"}=t;if(!e||0===e.length)throw Error("No data to export");let n=s||Object.keys(e[0]).map(e=>({key:e,label:e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())})),o=[n.map(e=>e.label),...e.map(e=>n.map(t=>{let r=t.key.split(".").reduce((e,t)=>e?.[t],e);return t.formatter?t.formatter(r,e):null==r?"":"boolean"==typeof r?r?"Yes":"No":r instanceof Date||"string"==typeof r&&/^\d{4}-\d{2}-\d{2}/.test(r)&&!isNaN(Date.parse(r))?d(r):String(r)}))].map(e=>e.map(e=>`"${String(e||"").replace(/"/g,'""')}"`).join(",")).join("\n"),l=a?`-${new Date().toISOString().split("T")[0]}`:"";(function(e,t,r){let s=new Blob([e],{type:r}),a=window.URL.createObjectURL(s),i=document.createElement("a");i.href=a,i.download=t,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(a),document.body.removeChild(i)})(o,`${r}${l}.csv`,"text/csv")}function a(e,t={}){s(e,{filename:"bookings",columns:[{key:"customer_name",label:"Customer Name"},{key:"customer_email",label:"Customer Email"},{key:"customer_phone",label:"Customer Phone"},{key:"service_name",label:"Service"},{key:"artist_name",label:"Artist"},{key:"booking_date",label:"Date",formatter:e=>c(e)},{key:"booking_time",label:"Time"},{key:"status",label:"Status",formatter:e=>e?.toUpperCase()||"UNKNOWN"},{key:"total_amount",label:"Amount",formatter:e=>m(e)},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function i(e,t={}){s(e,{filename:"services",columns:[{key:"name",label:"Service Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"base_price",label:"Base Price",formatter:e=>m(e)},{key:"duration",label:"Duration (min)"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"total_bookings",label:"Total Bookings"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function n(e,t={}){s(e,{filename:"customers",columns:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"email",label:"Email"},{key:"phone",label:"Phone"},{key:"total_bookings",label:"Total Bookings"},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function o(e,t={}){s(e,{filename:"products",columns:[{key:"name",label:"Product Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"price",label:"Price",formatter:e=>m(e)},{key:"stock_quantity",label:"Stock"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function l(e,t={}){s(e,{filename:"inventory",columns:[{key:"name",label:"Item Name"},{key:"category",label:"Category"},{key:"current_stock",label:"Current Stock"},{key:"minimum_stock",label:"Minimum Stock"},{key:"unit_cost",label:"Unit Cost",formatter:e=>m(e)},{key:"supplier_name",label:"Supplier"},{key:"last_restocked",label:"Last Restocked",formatter:e=>c(e)},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function c(e){return e?new Date(e).toLocaleDateString("en-AU"):""}function d(e){if(!e)return"";let t=new Date(e);return t.toLocaleDateString("en-AU")+" "+t.toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit"})}function m(e){return!e||isNaN(e)?"$0.00":new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(Number(e))}r.d(t,{Aq:()=>n,FC:()=>a,exportToCSV:()=>s,r8:()=>i,sg:()=>l,yA:()=>o})},6034:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>x});var a=r(997),i=r(6689),n=r(968),o=r.n(n),l=r(1664),c=r.n(l),d=r(8568),m=r(4845),u=r(5179),p=r(9611),_=r.n(p),v=e([m]);function x(){let{user:e,loading:t}=(0,d.a)(),[r,s]=(0,i.useState)(!0),[n,l]=(0,i.useState)([]),[p,v]=(0,i.useState)([]),[x,h]=(0,i.useState)(""),[g,b]=(0,i.useState)("all"),[S,f]=(0,i.useState)("name"),y=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),N=e=>{if(e>=60){let t=Math.floor(e/60),r=e%60;return r>0?`${t}h ${r}m`:`${t}h`}return`${e}m`};return t||r?a.jsx(m.Z,{children:(0,a.jsxs)("div",{className:_().loadingContainer,children:[a.jsx("div",{className:_().loadingSpinner}),a.jsx("p",{children:"Loading services..."})]})}):e?(0,a.jsxs)(m.Z,{children:[(0,a.jsxs)(o(),{children:[a.jsx("title",{children:"Services Management | Ocean Soul Sparkles Admin"}),a.jsx("meta",{name:"description",content:"Manage service catalog and pricing"})]}),(0,a.jsxs)("div",{className:_().servicesContainer,children:[(0,a.jsxs)("header",{className:_().header,children:[a.jsx("h1",{className:_().title,children:"Services Management"}),(0,a.jsxs)("div",{className:_().headerActions,children:[a.jsx(u.F,{data:p,type:"services",className:_().exportBtn}),a.jsx(c(),{href:"/admin/services/new",className:_().newServiceBtn,children:"+ Add Service"})]})]}),(0,a.jsxs)("div",{className:_().controlsPanel,children:[a.jsx("div",{className:_().searchSection,children:a.jsx("input",{type:"text",placeholder:"Search services by name, category, or description...",value:x,onChange:e=>h(e.target.value),className:_().searchInput})}),(0,a.jsxs)("div",{className:_().filtersSection,children:[(0,a.jsxs)("div",{className:_().filterGroup,children:[a.jsx("label",{children:"Category:"}),(0,a.jsxs)("select",{value:g,onChange:e=>b(e.target.value),className:_().filterSelect,children:[a.jsx("option",{value:"all",children:"All Categories"}),a.jsx("option",{value:"Hair Braiding",children:"Hair Braiding"}),a.jsx("option",{value:"Protective Styles",children:"Protective Styles"}),a.jsx("option",{value:"Hair Care",children:"Hair Care"}),a.jsx("option",{value:"Styling",children:"Styling"}),a.jsx("option",{value:"Consultation",children:"Consultation"})]})]}),(0,a.jsxs)("div",{className:_().filterGroup,children:[a.jsx("label",{children:"Sort by:"}),(0,a.jsxs)("select",{value:S,onChange:e=>f(e.target.value),className:_().filterSelect,children:[a.jsx("option",{value:"name",children:"Name"}),a.jsx("option",{value:"category",children:"Category"}),a.jsx("option",{value:"price",children:"Price"}),a.jsx("option",{value:"duration",children:"Duration"})]})]})]})]}),a.jsx("div",{className:_().servicesContent,children:0===p.length?(0,a.jsxs)("div",{className:_().emptyState,children:[a.jsx("h3",{children:"No services found"}),a.jsx("p",{children:0===n.length?"Get started by adding your first service to the catalog.":"Try adjusting your search or filter criteria."}),a.jsx(c(),{href:"/admin/services/new",className:_().addFirstBtn,children:"Add First Service"})]}):a.jsx("div",{className:_().servicesGrid,children:p.map(e=>(0,a.jsxs)("div",{className:_().serviceCard,children:[(0,a.jsxs)("div",{className:_().cardHeader,children:[a.jsx("h3",{className:_().serviceName,children:e.name}),a.jsx("span",{className:_().categoryBadge,children:e.category})]}),(0,a.jsxs)("div",{className:_().cardBody,children:[e.description&&a.jsx("p",{className:_().description,children:e.description}),(0,a.jsxs)("div",{className:_().serviceDetails,children:[(0,a.jsxs)("div",{className:_().priceInfo,children:[a.jsx("span",{className:_().label,children:"Price:"}),a.jsx("span",{className:_().value,children:e.price?y(e.price):"Contact for pricing"})]}),e.duration&&(0,a.jsxs)("div",{className:_().durationInfo,children:[a.jsx("span",{className:_().label,children:"Duration:"}),a.jsx("span",{className:_().value,children:N(e.duration)})]}),e.requirements&&(0,a.jsxs)("div",{className:_().requirementsInfo,children:[a.jsx("span",{className:_().label,children:"Requirements:"}),a.jsx("span",{className:_().value,children:e.requirements})]})]}),e.images&&e.images.length>0&&a.jsx("div",{className:_().serviceImages,children:(0,a.jsxs)("div",{className:_().imageCount,children:[e.images.length," image",e.images.length>1?"s":""]})})]}),(0,a.jsxs)("div",{className:_().cardActions,children:[a.jsx(c(),{href:`/admin/services/${e.id}`,className:_().viewBtn,children:"View Details"}),a.jsx(c(),{href:`/admin/services/${e.id}/edit`,className:_().editBtn,children:"Edit"}),a.jsx("button",{className:_().toggleBtn,title:e.active?"Disable service":"Enable service",children:e.active?"Disable":"Enable"})]})]},e.id))})})]})]}):null}m=(v.then?(await v)():v)[0],s()}catch(e){s(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2899,6212,1664,7441],()=>r(1289));module.exports=s})();