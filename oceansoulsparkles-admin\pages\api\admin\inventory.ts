import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify admin authentication
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    if (req.method === 'GET') {
      // Try to get inventory from products or inventory table
      let data, error;
      
      // First try 'products' table
      ({ data, error } = await supabase
        .from('products')
        .select('*'));

      if (error && error.message.includes('relation "products" does not exist')) {
        // Try 'inventory' table
        ({ data, error } = await supabase
          .from('inventory')
          .select('*'));
      }

      if (error && error.message.includes('does not exist')) {
        // If no inventory tables, return structured mock data
        const mockInventory = [
          {
            id: 1,
            name: 'Kankelon Braiding Hair - Black',
            category: 'Braiding Hair',
            sku: 'KBH-001-BLK',
            stock_quantity: 45,
            min_stock_level: 10,
            cost_price: 8.50,
            sale_price: 15.00,
            supplier: 'Hair Plus Wholesale',
            last_restock: '2024-01-15T00:00:00Z',
            status: 'in_stock',
            description: 'High-quality synthetic braiding hair in jet black',
            weight: '100g',
            color: 'Black (#1B)',
            created_at: '2024-01-10T00:00:00Z'
          },
          {
            id: 2,
            name: 'Kankelon Braiding Hair - Brown',
            category: 'Braiding Hair',
            sku: 'KBH-002-BRN',
            stock_quantity: 8,
            min_stock_level: 10,
            cost_price: 8.50,
            sale_price: 15.00,
            supplier: 'Hair Plus Wholesale',
            last_restock: '2024-01-10T00:00:00Z',
            status: 'low_stock',
            description: 'High-quality synthetic braiding hair in dark brown',
            weight: '100g',
            color: 'Dark Brown (#4)',
            created_at: '2024-01-10T00:00:00Z'
          },
          {
            id: 3,
            name: 'Marley Hair - Natural',
            category: 'Marley Hair',
            sku: 'MH-001-NAT',
            stock_quantity: 22,
            min_stock_level: 5,
            cost_price: 12.00,
            sale_price: 22.00,
            supplier: 'Natural Hair Co',
            last_restock: '2024-01-20T00:00:00Z',
            status: 'in_stock',
            description: 'Premium Marley hair for protective styles',
            weight: '100g',
            color: 'Natural Black',
            created_at: '2024-01-15T00:00:00Z'
          },
          {
            id: 4,
            name: 'Edge Control - Strong Hold',
            category: 'Hair Products',
            sku: 'EC-001-STR',
            stock_quantity: 15,
            min_stock_level: 8,
            cost_price: 4.50,
            sale_price: 12.00,
            supplier: 'Beauty Supply Direct',
            last_restock: '2024-01-18T00:00:00Z',
            status: 'in_stock',
            description: 'Professional edge control for long-lasting hold',
            weight: '118ml',
            color: 'Clear',
            created_at: '2024-01-12T00:00:00Z'
          },
          {
            id: 5,
            name: 'Hair Serum - Moisturizing',
            category: 'Hair Products',
            sku: 'HS-001-MOIST',
            stock_quantity: 0,
            min_stock_level: 5,
            cost_price: 6.00,
            sale_price: 18.00,
            supplier: 'Natural Hair Co',
            last_restock: '2023-12-20T00:00:00Z',
            status: 'out_of_stock',
            description: 'Nourishing hair serum for dry and damaged hair',
            weight: '100ml',
            color: 'Amber',
            created_at: '2024-01-08T00:00:00Z'
          },
          {
            id: 6,
            name: 'Rubber Bands - Small',
            category: 'Accessories',
            sku: 'RB-001-SM',
            stock_quantity: 180,
            min_stock_level: 50,
            cost_price: 0.02,
            sale_price: 0.10,
            supplier: 'Accessories Direct',
            last_restock: '2024-01-22T00:00:00Z',
            status: 'in_stock',
            description: 'Small elastic bands for sectioning hair',
            weight: '1g',
            color: 'Clear',
            created_at: '2024-01-05T00:00:00Z'
          }
        ];

        return res.status(200).json({ inventory: mockInventory, source: 'mock' });
      }

      if (error) {
        throw error;
      }

      return res.status(200).json({ inventory: data || [], source: 'database' });
    }

    if (req.method === 'POST') {
      const { name, category, sku, stock_quantity, min_stock_level, cost_price, sale_price, supplier, description } = req.body;

      const { data, error } = await supabase
        .from('products')
        .insert([
          {
            name,
            category,
            sku,
            stock_quantity: parseInt(stock_quantity),
            min_stock_level: parseInt(min_stock_level),
            cost_price: parseFloat(cost_price),
            sale_price: parseFloat(sale_price),
            supplier,
            description,
            status: stock_quantity > min_stock_level ? 'in_stock' : 'low_stock',
            created_at: new Date().toISOString()
          }
        ])
        .select();

      if (error) {
        throw error;
      }

      return res.status(201).json({ product: data[0] });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Inventory API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
