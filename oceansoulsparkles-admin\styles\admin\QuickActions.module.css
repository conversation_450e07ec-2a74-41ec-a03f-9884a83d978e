/* Quick Actions Component Styles */
.quickActionsContainer {
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-xl);
  border: 1px solid var(--admin-border-light);
  box-shadow: 0 2px 4px var(--admin-shadow-light);
}

.header {
  margin-bottom: var(--admin-spacing-lg);
}

.sectionTitle {
  color: var(--admin-darker);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 var(--admin-spacing-xs) 0;
}

.sectionSubtitle {
  color: var(--admin-gray);
  font-size: 1rem;
  margin: 0;
}

.actionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--admin-spacing-md);
  margin-bottom: var(--admin-spacing-xl);
}

.actionCard {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
  padding: var(--admin-spacing-lg);
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  text-decoration: none;
  color: inherit;
  transition: all var(--admin-transition-normal);
  position: relative;
  overflow: hidden;
}

.actionCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  background: var(--action-color, var(--admin-primary));
  transform: scaleY(0);
  transition: transform var(--admin-transition-normal);
}

.actionCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px var(--admin-shadow-medium);
  border-color: var(--action-color, var(--admin-primary));
}

.actionCard:hover::before {
  transform: scaleY(1);
}

.actionIcon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--action-color, var(--admin-primary));
  color: white;
  border-radius: var(--admin-radius-lg);
  flex-shrink: 0;
  transition: all var(--admin-transition-normal);
}

.actionCard:hover .actionIcon {
  transform: scale(1.1);
}

.actionContent {
  flex: 1;
}

.actionTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--admin-darker);
  margin: 0 0 var(--admin-spacing-xs) 0;
}

.actionDescription {
  font-size: 0.9rem;
  color: var(--admin-gray);
  margin: 0;
  line-height: 1.4;
}

.actionArrow {
  font-size: 1.2rem;
  color: var(--admin-gray);
  transition: all var(--admin-transition-normal);
}

.actionCard:hover .actionArrow {
  color: var(--action-color, var(--admin-primary));
  transform: translateX(4px);
}

.roleSection {
  border-top: 1px solid var(--admin-border-light);
  padding-top: var(--admin-spacing-lg);
  margin-bottom: var(--admin-spacing-lg);
}

.roleSectionTitle {
  color: var(--admin-darker);
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 var(--admin-spacing-md) 0;
}

.roleActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--admin-spacing-sm);
}

.roleAction {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
  padding: var(--admin-spacing-md);
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  text-decoration: none;
  color: var(--admin-darker);
  font-weight: 500;
  transition: all var(--admin-transition-normal);
}

.roleAction:hover {
  background: var(--admin-primary);
  color: white;
  transform: translateY(-1px);
}

.roleActionIcon {
  font-size: 1.2rem;
}

.adminSection {
  border-top: 1px solid var(--admin-border-light);
  padding-top: var(--admin-spacing-lg);
}

.adminSectionTitle {
  color: var(--admin-darker);
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 var(--admin-spacing-md) 0;
}

.adminActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--admin-spacing-sm);
}

.adminAction {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--admin-spacing-xs);
  padding: var(--admin-spacing-md);
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  font-weight: 500;
  color: var(--admin-darker);
}

.adminAction:hover {
  background: var(--admin-primary);
  color: white;
  transform: translateY(-1px);
}

.adminActionIcon {
  font-size: 1.5rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .quickActionsContainer {
    padding: var(--admin-spacing-lg);
  }

  .actionsGrid {
    grid-template-columns: 1fr;
    gap: var(--admin-spacing-sm);
  }

  .actionCard {
    padding: var(--admin-spacing-md);
  }

  .actionIcon {
    width: 50px;
    height: 50px;
    font-size: 2rem;
  }

  .roleActions {
    grid-template-columns: 1fr;
  }

  .adminActions {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .quickActionsContainer {
    padding: var(--admin-spacing-md);
  }

  .sectionTitle {
    font-size: 1.25rem;
  }

  .actionCard {
    flex-direction: column;
    text-align: center;
    padding: var(--admin-spacing-md);
  }

  .actionIcon {
    width: 60px;
    height: 60px;
    font-size: 2.5rem;
  }

  .actionArrow {
    display: none;
  }

  .adminActions {
    grid-template-columns: 1fr;
  }
}
