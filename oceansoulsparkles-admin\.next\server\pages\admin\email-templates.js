(()=>{var e={};e.id=8152,e.ids=[8152,660],e.modules={4384:e=>{e.exports={templatesContainer:"EmailTemplates_templatesContainer__Dy9ae",header:"EmailTemplates_header__A1Hic",headerLeft:"EmailTemplates_headerLeft__iUCn_",title:"EmailTemplates_title__IjAyS",subtitle:"EmailTemplates_subtitle__MlCVx",headerActions:"EmailTemplates_headerActions__aIzMz",createBtn:"EmailTemplates_createBtn__gQLwH",errorMessage:"EmailTemplates_errorMessage__psel5",closeError:"EmailTemplates_closeError__Ff19A",filters:"EmailTemplates_filters__fh2xV",filterGroup:"EmailTemplates_filterGroup__xvqrm",filterSelect:"EmailTemplates_filterSelect__cnN3W",checkboxLabel:"EmailTemplates_checkboxLabel__pHOkL",templatesGrid:"EmailTemplates_templatesGrid__HxTMU",templateCard:"EmailTemplates_templateCard__5CIzM",cardHeader:"EmailTemplates_cardHeader__qWwb1",templateInfo:"EmailTemplates_templateInfo__vnn2X",templateName:"EmailTemplates_templateName__L7h8E",templateType:"EmailTemplates_templateType__FangX",templateSubject:"EmailTemplates_templateSubject__CL9JD",templateBadges:"EmailTemplates_templateBadges__oztO3",defaultBadge:"EmailTemplates_defaultBadge__KIYD3",statusBadge:"EmailTemplates_statusBadge__vXsS0",active:"EmailTemplates_active__FpG58",inactive:"EmailTemplates_inactive__LLRxn",templateVariables:"EmailTemplates_templateVariables__5zAeB",cardActions:"EmailTemplates_cardActions__CEDGT",previewBtn:"EmailTemplates_previewBtn__c8qr3",editBtn:"EmailTemplates_editBtn___7tet",defaultBtn:"EmailTemplates_defaultBtn__jrlCj",toggleBtn:"EmailTemplates_toggleBtn__32VWV",activate:"EmailTemplates_activate__6Mtp7",deactivate:"EmailTemplates_deactivate__HX2PX",deleteBtn:"EmailTemplates_deleteBtn__2w_pP",emptyState:"EmailTemplates_emptyState__q3okq",loadingContainer:"EmailTemplates_loadingContainer__Cd8Rs",loadingSpinner:"EmailTemplates_loadingSpinner__6lYmD",spin:"EmailTemplates_spin__srCDH"}},2553:(e,t,a)=>{"use strict";a.a(e,async(e,l)=>{try{a.r(t),a.d(t,{config:()=>g,default:()=>p,getServerSideProps:()=>h,getStaticPaths:()=>u,getStaticProps:()=>_,reportWebVitals:()=>v,routeModule:()=>T,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>x});var i=a(7093),s=a(5244),r=a(1323),n=a(2899),m=a.n(n),c=a(6814),o=a(3378),d=e([c,o]);[c,o]=d.then?(await d)():d;let p=(0,r.l)(o,"default"),_=(0,r.l)(o,"getStaticProps"),u=(0,r.l)(o,"getStaticPaths"),h=(0,r.l)(o,"getServerSideProps"),g=(0,r.l)(o,"config"),v=(0,r.l)(o,"reportWebVitals"),x=(0,r.l)(o,"unstable_getStaticProps"),f=(0,r.l)(o,"unstable_getStaticPaths"),j=(0,r.l)(o,"unstable_getStaticParams"),b=(0,r.l)(o,"unstable_getServerProps"),S=(0,r.l)(o,"unstable_getServerSideProps"),T=new i.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/admin/email-templates",pathname:"/admin/email-templates",bundlePath:"",filename:""},components:{App:c.default,Document:m()},userland:o});l()}catch(e){l(e)}})},3378:(e,t,a)=>{"use strict";a.a(e,async(e,l)=>{try{a.r(t),a.d(t,{default:()=>_});var i=a(997),s=a(6689),r=a(968),n=a.n(r),m=a(8568),c=a(4845),o=a(4384),d=a.n(o),p=e([c]);c=(p.then?(await p)():p)[0];let u=[{value:"booking_confirmation",label:"Booking Confirmation"},{value:"booking_reminder",label:"Booking Reminder"},{value:"booking_cancellation",label:"Booking Cancellation"},{value:"payment_receipt",label:"Payment Receipt"},{value:"staff_notification",label:"Staff Notification"},{value:"custom",label:"Custom"}];function _(){let{user:e}=(0,m.a)(),[t,a]=(0,s.useState)([]),[l,r]=(0,s.useState)(!0),[o,p]=(0,s.useState)(null),[_,h]=(0,s.useState)("all"),[g,v]=(0,s.useState)(!1),[x,f]=(0,s.useState)(null),[j,b]=(0,s.useState)(!1),[S,T]=(0,s.useState)(!1),E=async()=>{try{r(!0);let e=new URLSearchParams;"all"!==_&&e.append("type",_),g&&e.append("active_only","true");let t=await fetch(`/api/admin/email-templates?${e}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}});if(t.ok){let e=await t.json();a(e.templates||[])}else p("Failed to load email templates")}catch(e){console.error("Error loading templates:",e),p("Failed to load email templates")}finally{r(!1)}},y=async(e,a)=>{try{let l=t.find(t=>t.id===e);if(!l)return;(await fetch(`/api/admin/email-templates/${e}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({...l,is_active:!a})})).ok?await E():p("Failed to update template status")}catch(e){console.error("Error updating template:",e),p("Failed to update template status")}},N=async e=>{try{let a=t.find(t=>t.id===e);if(!a)return;(await fetch(`/api/admin/email-templates/${e}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({...a,is_default:!0})})).ok?await E():p("Failed to set default template")}catch(e){console.error("Error setting default:",e),p("Failed to set default template")}},B=async e=>{if(confirm("Are you sure you want to delete this template? This action cannot be undone."))try{let t=await fetch(`/api/admin/email-templates/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}});if(t.ok)await E();else{let e=await t.json();p(e.message||"Failed to delete template")}}catch(e){console.error("Error deleting template:",e),p("Failed to delete template")}},C=e=>{let t=u.find(t=>t.value===e);return t?t.label:e},k=t.filter(e=>("all"===_||e.type===_)&&(!g||!!e.is_active));return l?i.jsx(c.Z,{children:(0,i.jsxs)("div",{className:d().loadingContainer,children:[i.jsx("div",{className:d().loadingSpinner}),i.jsx("p",{children:"Loading email templates..."})]})}):(0,i.jsxs)(c.Z,{children:[(0,i.jsxs)(n(),{children:[i.jsx("title",{children:"Email Templates | Ocean Soul Sparkles Admin"}),i.jsx("meta",{name:"description",content:"Manage email templates for customer communications"})]}),(0,i.jsxs)("div",{className:d().templatesContainer,children:[(0,i.jsxs)("header",{className:d().header,children:[(0,i.jsxs)("div",{className:d().headerLeft,children:[i.jsx("h1",{className:d().title,children:"Email Templates"}),i.jsx("p",{className:d().subtitle,children:"Manage email templates for customer communications"})]}),i.jsx("div",{className:d().headerActions,children:i.jsx("button",{onClick:()=>b(!0),className:d().createBtn,children:"+ Create Template"})})]}),o&&(0,i.jsxs)("div",{className:d().errorMessage,children:[o,i.jsx("button",{onClick:()=>p(null),className:d().closeError,children:"\xd7"})]}),(0,i.jsxs)("div",{className:d().filters,children:[(0,i.jsxs)("div",{className:d().filterGroup,children:[i.jsx("label",{htmlFor:"typeFilter",children:"Filter by Type:"}),(0,i.jsxs)("select",{id:"typeFilter",value:_,onChange:e=>h(e.target.value),className:d().filterSelect,children:[i.jsx("option",{value:"all",children:"All Types"}),u.map(e=>i.jsx("option",{value:e.value,children:e.label},e.value))]})]}),i.jsx("div",{className:d().filterGroup,children:(0,i.jsxs)("label",{className:d().checkboxLabel,children:[i.jsx("input",{type:"checkbox",checked:g,onChange:e=>v(e.target.checked)}),"Show active only"]})})]}),i.jsx("div",{className:d().templatesGrid,children:0===k.length?(0,i.jsxs)("div",{className:d().emptyState,children:[i.jsx("h3",{children:"No email templates found"}),i.jsx("p",{children:"Create your first email template to get started with customer communications."}),i.jsx("button",{onClick:()=>b(!0),className:d().createBtn,children:"Create Template"})]}):k.map(e=>(0,i.jsxs)("div",{className:d().templateCard,children:[(0,i.jsxs)("div",{className:d().cardHeader,children:[(0,i.jsxs)("div",{className:d().templateInfo,children:[i.jsx("h3",{className:d().templateName,children:e.name}),i.jsx("p",{className:d().templateType,children:C(e.type)}),i.jsx("p",{className:d().templateSubject,children:e.subject})]}),(0,i.jsxs)("div",{className:d().templateBadges,children:[e.is_default&&i.jsx("span",{className:d().defaultBadge,children:"Default"}),i.jsx("span",{className:`${d().statusBadge} ${e.is_active?d().active:d().inactive}`,children:e.is_active?"Active":"Inactive"})]})]}),(0,i.jsxs)("div",{className:d().templateVariables,children:[i.jsx("strong",{children:"Variables:"})," ",e.variables.join(", ")||"None"]}),(0,i.jsxs)("div",{className:d().cardActions,children:[i.jsx("button",{onClick:()=>{f(e),T(!0)},className:d().previewBtn,children:"Preview"}),i.jsx("button",{onClick:()=>f(e),className:d().editBtn,children:"Edit"}),!e.is_default&&i.jsx("button",{onClick:()=>N(e.id),className:d().defaultBtn,children:"Set Default"}),i.jsx("button",{onClick:()=>y(e.id,e.is_active),className:`${d().toggleBtn} ${e.is_active?d().deactivate:d().activate}`,children:e.is_active?"Deactivate":"Activate"}),i.jsx("button",{onClick:()=>B(e.id),className:d().deleteBtn,children:"Delete"})]})]},e.id))})]})]})}l()}catch(e){l(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),l=t.X(0,[2899,6212,1664,7441],()=>a(2553));module.exports=l})();