/* Receipt Customizer Styles */
.receiptCustomizer {
  display: flex;
  gap: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  min-height: 600px;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.templatesSection {
  flex: 1;
  min-width: 350px;
  max-width: none;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.templatesSection h3 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.createButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.createButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.description {
  color: #64748b;
  margin: 0 0 20px 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.templateGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  width: 100%;
}

.templateCard {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.templateCard:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.templateCard.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea08 0%, #764ba208 100%);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.templateHeader {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.templateIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.templateInfo {
  flex: 1;
}

.templateName {
  margin: 0 0 4px 0;
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
}

.defaultBadge {
  background: #10b981;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.templateDescription {
  color: #64748b;
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.templateFeatures {
  margin-bottom: 16px;
}

.featureList {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.feature {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.templateMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
  font-size: 0.85rem;
}

.templateType {
  background: #667eea;
  color: white;
  padding: 3px 8px;
  border-radius: 6px;
  font-weight: 500;
  text-transform: capitalize;
}

.businessName {
  color: #64748b;
  font-weight: 500;
}

.templateActions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.templateCard:hover .templateActions {
  opacity: 1;
}

.actionButton {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 4px 6px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.actionButton:hover {
  background: #f8fafc;
  border-color: #667eea;
}

.deleteButton:hover {
  background: #fef2f2;
  border-color: #dc2626;
}

.previewSection {
  flex: 0 0 400px;
  min-width: 350px;
  max-width: 450px;
}

.previewSection h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.previewContainer {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.receiptPreview {
  height: 100%;
}

.previewHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.previewLabel {
  font-weight: 600;
  color: #475569;
  font-size: 0.9rem;
}

.refreshBtn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.refreshBtn:hover {
  background: #e2e8f0;
}

.previewContent {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
  font-family: Arial, sans-serif;
}

/* Loading States */
.loading, .previewLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #64748b;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error {
  text-align: center;
  padding: 40px;
  color: #dc2626;
}

.retryBtn {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 12px;
  transition: background-color 0.2s ease;
}

.retryBtn:hover {
  background: #5a67d8;
}

/* No Templates State */
.noTemplates {
  text-align: center;
  padding: 40px;
  color: #64748b;
  background: white;
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
}

.noTemplates p {
  margin: 0 0 8px 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .receiptCustomizer {
    flex-direction: column;
    gap: 20px;
  }

  .templatesSection,
  .previewSection {
    min-width: auto;
    max-width: none;
    flex: none;
  }

  .templateGrid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .receiptCustomizer {
    padding: 16px;
  }
  
  .templateGrid {
    grid-template-columns: 1fr;
  }
  
  .templateCard {
    padding: 16px;
  }
  
  .templateHeader {
    gap: 8px;
  }
  
  .featureList {
    gap: 4px;
  }
  
  .feature {
    font-size: 0.75rem;
    padding: 3px 6px;
  }
}

/* Print Styles for Receipt Preview */
@media print {
  .receiptCustomizer {
    display: block;
  }
  
  .templatesSection {
    display: none;
  }
  
  .previewSection {
    max-width: none;
  }
  
  .previewHeader {
    display: none;
  }
  
  .previewContent {
    padding: 0;
    max-height: none;
    overflow: visible;
  }
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.modalHeader h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.closeButton:hover {
  background: #f1f5f9;
}

.modalForm {
  padding: 24px;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.formSection h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup label {
  display: block;
  margin-bottom: 6px;
  color: #374151;
  font-weight: 500;
  font-size: 0.9rem;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkboxGroup {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.checkboxLabel {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  margin-bottom: 0 !important;
}

.checkboxLabel input[type="checkbox"] {
  width: auto !important;
  margin: 0;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.cancelButton {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.saveButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.saveButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}
