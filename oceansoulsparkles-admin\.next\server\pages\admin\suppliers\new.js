(()=>{var e={};e.id=1178,e.ids=[1178,660],e.modules={699:e=>{e.exports={supplierManagement:"Suppliers_supplierManagement__moQ9K",header:"Suppliers_header__e_GFD",headerContent:"Suppliers_headerContent__RlAv_",title:"Suppliers_title__mJ53N",subtitle:"Suppliers_subtitle__jKx8q",headerActions:"Suppliers_headerActions__mhBy0",addBtn:"Suppliers_addBtn__m7uLG",controls:"Suppliers_controls__u6XUL",searchSection:"Suppliers_searchSection___Yh_u",searchInput:"Suppliers_searchInput__ztdWK",filters:"Suppliers_filters__m2SbG",filterSelect:"Suppliers_filterSelect__NsTpf",sortSelect:"Suppliers_sortSelect__xah3_",suppliersContainer:"Suppliers_suppliersContainer__lLuBC",loading:"Suppliers_loading__SPL55",loadingSpinner:"Suppliers_loadingSpinner__8C00Y",spin:"Suppliers_spin__m4hm1",emptyState:"Suppliers_emptyState__2EIlp",emptyIcon:"Suppliers_emptyIcon__mVvSl",addFirstBtn:"Suppliers_addFirstBtn__5YL2d",suppliersGrid:"Suppliers_suppliersGrid__8K7Uc",supplierCard:"Suppliers_supplierCard__FpznX",cardHeader:"Suppliers_cardHeader__vUyV_",supplierInfo:"Suppliers_supplierInfo__LLH3s",supplierName:"Suppliers_supplierName__P3SYy",statusBadge:"Suppliers_statusBadge__q__Jt",active:"Suppliers_active__0rFtH",inactive:"Suppliers_inactive__VhmvH",cardBody:"Suppliers_cardBody__wjlXW",contactInfo:"Suppliers_contactInfo__r93AU",businessInfo:"Suppliers_businessInfo__gDnna",infoItem:"Suppliers_infoItem__NkSUU",label:"Suppliers_label__x1sKg",value:"Suppliers_value__Rvb2Z",notes:"Suppliers_notes__aLiX9",notesText:"Suppliers_notesText__Nlgcw",cardFooter:"Suppliers_cardFooter__Kh64U",cardActions:"Suppliers_cardActions__xZeWq",viewBtn:"Suppliers_viewBtn__W23ER",editBtn:"Suppliers_editBtn__VGxzN",deleteBtn:"Suppliers_deleteBtn__K6UPw",cardMeta:"Suppliers_cardMeta__30iaq",createdDate:"Suppliers_createdDate__Et97b",supplierForm:"Suppliers_supplierForm__Ile4t",formGrid:"Suppliers_formGrid__RoRp_",formGroup:"Suppliers_formGroup__exSuf",fullWidth:"Suppliers_fullWidth__DS0su",formLabel:"Suppliers_formLabel__zmgic",formInput:"Suppliers_formInput__GWKlp",formTextarea:"Suppliers_formTextarea__oPk6B",formSelect:"Suppliers_formSelect__nAwes",formActions:"Suppliers_formActions__buaHF",saveBtn:"Suppliers_saveBtn__CGIMf",cancelBtn:"Suppliers_cancelBtn__APjcB"}},5398:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>f,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>_,getStaticProps:()=>c,reportWebVitals:()=>x,routeModule:()=>y,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>g,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>S});var a=r(7093),i=r(5244),l=r(1323),n=r(2899),o=r.n(n),p=r(6814),m=r(3106),d=e([p,m]);[p,m]=d.then?(await d)():d;let u=(0,l.l)(m,"default"),c=(0,l.l)(m,"getStaticProps"),_=(0,l.l)(m,"getStaticPaths"),h=(0,l.l)(m,"getServerSideProps"),f=(0,l.l)(m,"config"),x=(0,l.l)(m,"reportWebVitals"),S=(0,l.l)(m,"unstable_getStaticProps"),b=(0,l.l)(m,"unstable_getStaticPaths"),g=(0,l.l)(m,"unstable_getStaticParams"),v=(0,l.l)(m,"unstable_getServerProps"),j=(0,l.l)(m,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/admin/suppliers/new",pathname:"/admin/suppliers/new",bundlePath:"",filename:""},components:{App:p.default,Document:o()},userland:m});s()}catch(e){s(e)}})},5352:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Z:()=>c});var a=r(997),i=r(6689),l=r(1163),n=r(1664),o=r.n(n),p=r(3590),m=r(699),d=r.n(m),u=e([p]);function c({supplier:e,isEditing:t=!1}){let r=(0,l.useRouter)(),[s,n]=(0,i.useState)(!1),[m,u]=(0,i.useState)({name:e?.name||"",contact_person:e?.contact_person||"",email:e?.email||"",phone:e?.phone||"",address:e?.address||"",website:e?.website||"",payment_terms:e?.payment_terms||"Net 30",lead_time_days:e?.lead_time_days||7,minimum_order_amount:e?.minimum_order_amount||0,is_active:e?.is_active===void 0||e.is_active,notes:e?.notes||""}),c=e=>{let{name:t,value:r,type:s}=e.target;u(a=>({...a,[t]:"number"===s?parseFloat(r)||0:"checkbox"===s?e.target.checked:r}))},_=()=>{let e=[];return m.name.trim()||e.push("Supplier name is required"),m.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(m.email)&&e.push("Invalid email format"),m.lead_time_days<0&&e.push("Lead time cannot be negative"),m.minimum_order_amount<0&&e.push("Minimum order amount cannot be negative"),m.website&&!m.website.startsWith("http")&&u(e=>({...e,website:`https://${m.website}`})),e},h=async s=>{s.preventDefault();let a=_();if(a.length>0){a.forEach(e=>p.toast.error(e));return}n(!0);try{let s=localStorage.getItem("adminToken");if(!s)throw Error("No authentication token found");let a=t?`/api/admin/suppliers/${e?.id}`:"/api/admin/suppliers",i=await fetch(a,{method:t?"PUT":"POST",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"},body:JSON.stringify({name:m.name.trim(),contactPerson:m.contact_person.trim(),email:m.email.trim()||null,phone:m.phone.trim()||null,address:m.address.trim()||null,website:m.website.trim()||null,paymentTerms:m.payment_terms,leadTimeDays:m.lead_time_days,minimumOrderAmount:m.minimum_order_amount,isActive:m.is_active,notes:m.notes.trim()||null})});if(!i.ok){let e=await i.json();throw Error(e.message||`Failed to ${t?"update":"create"} supplier`)}await i.json(),p.toast.success(`Supplier ${t?"updated":"created"} successfully`),t?r.push(`/admin/suppliers/${e?.id}`):r.push("/admin/suppliers")}catch(e){console.error("Error saving supplier:",e),p.toast.error(e instanceof Error?e.message:`Failed to ${t?"update":"create"} supplier`)}finally{n(!1)}};return(0,a.jsxs)("div",{className:d().supplierForm,children:[a.jsx("div",{className:d().header,children:(0,a.jsxs)("div",{className:d().headerContent,children:[a.jsx("h1",{className:d().title,children:t?"Edit Supplier":"Add New Supplier"}),a.jsx("p",{className:d().subtitle,children:t?"Update supplier information and business details":"Enter supplier information and business details"})]})}),(0,a.jsxs)("form",{onSubmit:h,children:[(0,a.jsxs)("div",{className:d().formGrid,children:[(0,a.jsxs)("div",{className:d().formGroup,children:[a.jsx("label",{htmlFor:"name",className:d().formLabel,children:"Supplier Name *"}),a.jsx("input",{type:"text",id:"name",name:"name",value:m.name,onChange:c,className:d().formInput,required:!0,placeholder:"Enter supplier name"})]}),(0,a.jsxs)("div",{className:d().formGroup,children:[a.jsx("label",{htmlFor:"contact_person",className:d().formLabel,children:"Contact Person"}),a.jsx("input",{type:"text",id:"contact_person",name:"contact_person",value:m.contact_person,onChange:c,className:d().formInput,placeholder:"Primary contact name"})]}),(0,a.jsxs)("div",{className:d().formGroup,children:[a.jsx("label",{htmlFor:"email",className:d().formLabel,children:"Email Address"}),a.jsx("input",{type:"email",id:"email",name:"email",value:m.email,onChange:c,className:d().formInput,placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:d().formGroup,children:[a.jsx("label",{htmlFor:"phone",className:d().formLabel,children:"Phone Number"}),a.jsx("input",{type:"tel",id:"phone",name:"phone",value:m.phone,onChange:c,className:d().formInput,placeholder:"+61 2 1234 5678"})]}),(0,a.jsxs)("div",{className:d().formGroup,children:[a.jsx("label",{htmlFor:"address",className:d().formLabel,children:"Address"}),a.jsx("input",{type:"text",id:"address",name:"address",value:m.address,onChange:c,className:d().formInput,placeholder:"Business address"})]}),(0,a.jsxs)("div",{className:d().formGroup,children:[a.jsx("label",{htmlFor:"website",className:d().formLabel,children:"Website"}),a.jsx("input",{type:"url",id:"website",name:"website",value:m.website,onChange:c,className:d().formInput,placeholder:"www.supplier.com"})]}),(0,a.jsxs)("div",{className:d().formGroup,children:[a.jsx("label",{htmlFor:"payment_terms",className:d().formLabel,children:"Payment Terms"}),(0,a.jsxs)("select",{id:"payment_terms",name:"payment_terms",value:m.payment_terms,onChange:c,className:d().formSelect,children:[a.jsx("option",{value:"Net 15",children:"Net 15"}),a.jsx("option",{value:"Net 30",children:"Net 30"}),a.jsx("option",{value:"Net 45",children:"Net 45"}),a.jsx("option",{value:"Net 60",children:"Net 60"}),a.jsx("option",{value:"COD",children:"Cash on Delivery"}),a.jsx("option",{value:"Prepaid",children:"Prepaid"})]})]}),(0,a.jsxs)("div",{className:d().formGroup,children:[a.jsx("label",{htmlFor:"lead_time_days",className:d().formLabel,children:"Lead Time (Days)"}),a.jsx("input",{type:"number",id:"lead_time_days",name:"lead_time_days",value:m.lead_time_days,onChange:c,className:d().formInput,min:"0",placeholder:"7"})]}),(0,a.jsxs)("div",{className:d().formGroup,children:[a.jsx("label",{htmlFor:"minimum_order_amount",className:d().formLabel,children:"Minimum Order Amount (AUD)"}),a.jsx("input",{type:"number",id:"minimum_order_amount",name:"minimum_order_amount",value:m.minimum_order_amount,onChange:c,className:d().formInput,min:"0",step:"0.01",placeholder:"0.00"})]}),a.jsx("div",{className:d().formGroup,children:(0,a.jsxs)("label",{className:d().formLabel,children:[a.jsx("input",{type:"checkbox",name:"is_active",checked:m.is_active,onChange:c,style:{marginRight:"0.5rem"}}),"Active Supplier"]})}),(0,a.jsxs)("div",{className:`${d().formGroup} ${d().fullWidth}`,children:[a.jsx("label",{htmlFor:"notes",className:d().formLabel,children:"Notes"}),a.jsx("textarea",{id:"notes",name:"notes",value:m.notes,onChange:c,className:d().formTextarea,placeholder:"Additional notes about this supplier...",rows:4})]})]}),(0,a.jsxs)("div",{className:d().formActions,children:[a.jsx(o(),{href:"/admin/suppliers",className:d().cancelBtn,children:"Cancel"}),a.jsx("button",{type:"submit",className:d().saveBtn,disabled:s,children:s?"Saving...":t?"Update Supplier":"Create Supplier"})]})]})]})}p=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},3106:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>c});var a=r(997),i=r(9816),l=r.n(i);r(6689);var n=r(968),o=r.n(n),p=r(8568),m=r(4845),d=r(5352),u=e([m,d]);function c(){let{user:e,loading:t}=(0,p.a)();return t?a.jsx(m.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("div",{style:{width:"32px",height:"32px",border:"3px solid #e2e8f0",borderTop:"3px solid #667eea",borderRadius:"50%",animation:"spin 1s linear infinite"}}),a.jsx("p",{style:{color:"#64748b"},children:"Authenticating..."})]})}):e?["DEV","Admin"].includes(e.role)?(0,a.jsxs)(m.Z,{children:[(0,a.jsxs)(o(),{children:[a.jsx("title",{className:"jsx-ff161281ed666c63",children:"Add New Supplier | Ocean Soul Sparkles Admin"}),a.jsx("meta",{name:"description",content:"Add a new supplier to the Ocean Soul Sparkles database",className:"jsx-ff161281ed666c63"})]}),a.jsx(d.Z,{}),a.jsx(l(),{id:"ff161281ed666c63",children:"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]}):a.jsx(m.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("h2",{style:{color:"#ef4444"},children:"Access Denied"}),a.jsx("p",{style:{color:"#64748b"},children:"You do not have permission to add suppliers."})]})}):a.jsx(m.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("h2",{style:{color:"#ef4444"},children:"Authentication Required"}),a.jsx("p",{style:{color:"#64748b"},children:"Please log in to add new suppliers."})]})})}[m,d]=u.then?(await u)():u,s()}catch(e){s(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},9816:e=>{"use strict";e.exports=require("styled-jsx/style")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2899,6212,1664,7441],()=>r(5398));module.exports=s})();