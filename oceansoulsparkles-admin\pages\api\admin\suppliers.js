import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { supabaseAdmin } from '../../../lib/supabase-admin';

/**
 * Suppliers API Endpoint
 * 
 * Handles CRUD operations for supplier management
 * Supports filtering, searching, and pagination
 */
export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `suppliers-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    console.log(`[${requestId}] Suppliers API request:`, {
      method: req.method,
      query: req.query,
      userAgent: req.headers['user-agent']
    });

    // Verify admin authentication
    const token = req.headers.authorization?.replace('Bearer ', '') ||
                 req.cookies['admin-token'];

    if (!token) {
      console.log(`[${requestId}] No authentication token provided`);
      return res.status(401).json({
        error: 'No authentication token',
        requestId
      });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({
        error: 'Unauthorized',
        requestId
      });
    }

    // Check admin permissions
    if (!['DEV', 'Admin'].includes(authResult.user.role)) {
      console.log(`[${requestId}] Insufficient permissions:`, authResult.user.role);
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        requestId 
      });
    }

    if (req.method === 'GET') {
      return await handleGetSuppliers(req, res, requestId);
    }

    if (req.method === 'POST') {
      return await handleCreateSupplier(req, res, requestId, authResult.user);
    }

    return res.status(405).json({ 
      error: 'Method not allowed',
      requestId 
    });

  } catch (error) {
    console.error(`[${requestId}] Suppliers API error:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message,
      requestId 
    });
  }
}

/**
 * Handle GET request - Fetch suppliers with filtering and search
 */
async function handleGetSuppliers(req, res, requestId) {
  try {
    const { 
      search = '', 
      active = 'all', 
      page = 1, 
      limit = 50,
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;

    let query = supabaseAdmin
      .from('suppliers')
      .select('*', { count: 'exact' });

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,contact_person.ilike.%${search}%,email.ilike.%${search}%`);
    }

    // Apply active filter
    if (active !== 'all') {
      query = query.eq('is_active', active === 'true');
    }

    // Apply sorting
    const validSortFields = ['name', 'contact_person', 'email', 'created_at', 'updated_at'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'name';
    const order = sortOrder === 'desc' ? false : true;
    query = query.order(sortField, { ascending: order });

    // Apply pagination
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit)));
    const offset = (pageNum - 1) * limitNum;
    
    query = query.range(offset, offset + limitNum - 1);

    const { data: suppliers, error, count } = await query;

    if (error) {
      console.error(`[${requestId}] Database error:`, error);
      throw error;
    }

    // Calculate pagination info
    const totalPages = Math.ceil(count / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    console.log(`[${requestId}] Suppliers fetched successfully:`, {
      count: suppliers?.length || 0,
      total: count,
      page: pageNum,
      totalPages
    });

    return res.status(200).json({
      suppliers: suppliers || [],
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: count,
        totalPages,
        hasNextPage,
        hasPrevPage
      },
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error fetching suppliers:`, error);
    throw error;
  }
}

/**
 * Handle POST request - Create new supplier
 */
async function handleCreateSupplier(req, res, requestId, user) {
  try {
    const {
      name,
      contactPerson,
      email,
      phone,
      address,
      website,
      paymentTerms,
      leadTimeDays,
      minimumOrderAmount,
      notes
    } = req.body;

    // Validate required fields
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Supplier name is required',
        requestId
      });
    }

    // Check for duplicate supplier name
    const { data: existingSupplier } = await supabaseAdmin
      .from('suppliers')
      .select('id')
      .eq('name', name.trim())
      .single();

    if (existingSupplier) {
      return res.status(409).json({
        error: 'Supplier already exists',
        message: 'A supplier with this name already exists',
        requestId
      });
    }

    // Validate email format if provided
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Invalid email format',
        requestId
      });
    }

    // Create supplier
    const supplierData = {
      name: name.trim(),
      contact_person: contactPerson?.trim() || null,
      email: email?.trim() || null,
      phone: phone?.trim() || null,
      address: address?.trim() || null,
      website: website?.trim() || null,
      payment_terms: paymentTerms?.trim() || 'Net 30',
      lead_time_days: leadTimeDays ? parseInt(leadTimeDays) : 7,
      minimum_order_amount: minimumOrderAmount ? parseFloat(minimumOrderAmount) : 0.00,
      notes: notes?.trim() || null,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: newSupplier, error } = await supabaseAdmin
      .from('suppliers')
      .insert([supplierData])
      .select()
      .single();

    if (error) {
      console.error(`[${requestId}] Database error creating supplier:`, error);
      throw error;
    }

    console.log(`[${requestId}] Supplier created successfully:`, {
      id: newSupplier.id,
      name: newSupplier.name,
      createdBy: user.id
    });

    return res.status(201).json({
      supplier: newSupplier,
      message: 'Supplier created successfully',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error creating supplier:`, error);
    throw error;
  }
}
