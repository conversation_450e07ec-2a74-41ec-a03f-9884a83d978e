/**
 * Safe Render Utils Module
 * Provides utilities for safe rendering and error handling in React components
 */

import { useState, useEffect, useCallback } from 'react';

/**
 * Safe state hook that handles errors gracefully
 */
export function useSafeState(initialState) {
  const [state, setState] = useState(initialState);
  const [error, setError] = useState(null);

  const safeSetState = useCallback((newState) => {
    try {
      if (typeof newState === 'function') {
        setState(prevState => {
          try {
            return newState(prevState);
          } catch (err) {
            setError(err);
            return prevState;
          }
        });
      } else {
        setState(newState);
        setError(null);
      }
    } catch (err) {
      setError(err);
      console.error('Safe state update error:', err);
    }
  }, []);

  return [state, safeSetState, error];
}

/**
 * Safe async operation hook
 */
export function useSafeAsync(asyncFunction, dependencies = []) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const execute = useCallback(async (...args) => {
    try {
      setLoading(true);
      setError(null);
      const result = await asyncFunction(...args);
      setData(result);
      return result;
    } catch (err) {
      setError(err);
      console.error('Safe async operation error:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, dependencies);

  return { data, loading, error, execute };
}

/**
 * Safe component wrapper that catches render errors
 */
export function SafeComponent({ children, fallback = null, onError = null }) {
  const [hasError, setHasError] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleError = (error, errorInfo) => {
      setHasError(true);
      setError(error);
      if (onError) {
        onError(error, errorInfo);
      }
      console.error('Safe component render error:', error);
    };

    // Reset error state when children change
    setHasError(false);
    setError(null);

    return () => {
      // Cleanup if needed
    };
  }, [children, onError]);

  if (hasError) {
    if (fallback) {
      return typeof fallback === 'function' ? fallback(error) : fallback;
    }
    return (
      <div style={{ 
        padding: '1rem', 
        border: '1px solid #ff6b6b', 
        borderRadius: '4px', 
        backgroundColor: '#ffe0e0',
        color: '#d63031'
      }}>
        <h4>Something went wrong</h4>
        <p>An error occurred while rendering this component.</p>
        {process.env.NODE_ENV === 'development' && (
          <details style={{ marginTop: '0.5rem' }}>
            <summary>Error Details</summary>
            <pre style={{ fontSize: '0.8rem', marginTop: '0.5rem' }}>
              {error?.toString()}
            </pre>
          </details>
        )}
      </div>
    );
  }

  try {
    return children;
  } catch (renderError) {
    setHasError(true);
    setError(renderError);
    return null;
  }
}

/**
 * Safely parse JSON with fallback
 */
export function safeJsonParse(jsonString, fallback = null) {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('Safe JSON parse error:', error);
    return fallback;
  }
}

/**
 * Safely stringify JSON with fallback
 */
export function safeJsonStringify(obj, fallback = '{}') {
  try {
    return JSON.stringify(obj);
  } catch (error) {
    console.warn('Safe JSON stringify error:', error);
    return fallback;
  }
}

/**
 * Safe property access with dot notation
 */
export function safeGet(obj, path, defaultValue = undefined) {
  try {
    if (!obj || typeof path !== 'string') {
      return defaultValue;
    }

    const keys = path.split('.');
    let result = obj;

    for (const key of keys) {
      if (result === null || result === undefined) {
        return defaultValue;
      }
      result = result[key];
    }

    return result !== undefined ? result : defaultValue;
  } catch (error) {
    console.warn('Safe get error:', error);
    return defaultValue;
  }
}

/**
 * Safe property setting with dot notation
 */
export function safeSet(obj, path, value) {
  try {
    if (!obj || typeof path !== 'string') {
      return false;
    }

    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = obj;

    for (const key of keys) {
      if (current[key] === null || current[key] === undefined) {
        current[key] = {};
      }
      current = current[key];
    }

    current[lastKey] = value;
    return true;
  } catch (error) {
    console.warn('Safe set error:', error);
    return false;
  }
}

/**
 * Safe array operation wrapper
 */
export function safeArrayOperation(array, operation, fallback = []) {
  try {
    if (!Array.isArray(array)) {
      return fallback;
    }
    return operation(array);
  } catch (error) {
    console.warn('Safe array operation error:', error);
    return fallback;
  }
}

/**
 * Safe number formatting
 */
export function safeFormatNumber(value, options = {}) {
  try {
    const num = parseFloat(value);
    if (isNaN(num)) {
      return options.fallback || '0';
    }
    return new Intl.NumberFormat('en-AU', options).format(num);
  } catch (error) {
    console.warn('Safe number format error:', error);
    return options.fallback || value?.toString() || '0';
  }
}

/**
 * Safe date formatting
 */
export function safeFormatDate(date, options = {}) {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) {
      return options.fallback || 'Invalid Date';
    }
    return new Intl.DateTimeFormat('en-AU', options).format(dateObj);
  } catch (error) {
    console.warn('Safe date format error:', error);
    return options.fallback || 'Invalid Date';
  }
}

/**
 * Safe string truncation
 */
export function safeTruncate(str, maxLength = 100, suffix = '...') {
  try {
    if (typeof str !== 'string') {
      return '';
    }
    if (str.length <= maxLength) {
      return str;
    }
    return str.substring(0, maxLength - suffix.length) + suffix;
  } catch (error) {
    console.warn('Safe truncate error:', error);
    return str?.toString() || '';
  }
}

/**
 * Safe HTML sanitization (basic)
 */
export function safeSanitizeHtml(html) {
  try {
    if (typeof html !== 'string') {
      return '';
    }
    // Basic HTML sanitization - remove script tags and dangerous attributes
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+="[^"]*"/gi, '')
      .replace(/javascript:/gi, '');
  } catch (error) {
    console.warn('Safe HTML sanitize error:', error);
    return '';
  }
}

/**
 * Safe local storage operations
 */
export const safeLocalStorage = {
  getItem: (key, fallback = null) => {
    try {
      if (typeof window === 'undefined') return fallback;
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : fallback;
    } catch (error) {
      console.warn('Safe localStorage get error:', error);
      return fallback;
    }
  },

  setItem: (key, value) => {
    try {
      if (typeof window === 'undefined') return false;
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.warn('Safe localStorage set error:', error);
      return false;
    }
  },

  removeItem: (key) => {
    try {
      if (typeof window === 'undefined') return false;
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn('Safe localStorage remove error:', error);
      return false;
    }
  }
};

/**
 * Safe render function for components
 */
export function safeRender(component, fallback = null) {
  try {
    if (typeof component === 'function') {
      return component();
    }
    return component;
  } catch (error) {
    console.warn('Safe render error:', error);
    return fallback;
  }
}

// Default export
export default {
  useSafeState,
  useSafeAsync,
  SafeComponent,
  safeJsonParse,
  safeJsonStringify,
  safeGet,
  safeSet,
  safeArrayOperation,
  safeFormatNumber,
  safeFormatDate,
  safeTruncate,
  safeSanitizeHtml,
  safeLocalStorage,
  safeRender
};
