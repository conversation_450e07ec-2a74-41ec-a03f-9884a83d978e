/* Ocean Soul Sparkles - Responsive Chart Styles */

.responsiveChart {
  width: 100%;
  background: var(--admin-bg-primary);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--admin-shadow-sm);
  border: 1px solid var(--admin-border-light);
  margin-bottom: 1.5rem;
}

.chartContainer {
  position: relative;
  width: 100%;
  height: auto;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--admin-bg-secondary);
  border-radius: 6px;
  padding: 1rem;
}

.chartContainer canvas {
  max-width: 100% !important;
  height: auto !important;
}

.chartLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  color: var(--admin-gray);
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--admin-border-light);
  border-top: 3px solid var(--admin-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.chartLoading p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--admin-gray);
}

/* Desktop-specific optimizations */
@media (min-width: 769px) {
  .responsiveChart {
    padding: 2rem;
  }

  .chartContainer {
    min-height: 400px;
    padding: 1.5rem;
  }

  .chartLoading {
    padding: 4rem 2rem;
  }

  .loadingSpinner {
    width: 50px;
    height: 50px;
    border-width: 4px;
  }

  .chartLoading p {
    font-size: 1.1rem;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .responsiveChart {
    display: none; /* Hide desktop chart on mobile */
  }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
  .responsiveChart {
    padding: 2.5rem;
  }

  .chartContainer {
    min-height: 450px;
    padding: 2rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .responsiveChart {
    border-width: 2px;
    border-color: var(--admin-dark);
  }

  .chartContainer {
    border: 2px solid var(--admin-border);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .loadingSpinner {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .responsiveChart {
    background: var(--admin-dark);
    border-color: #4a5568;
  }

  .chartContainer {
    background: #2d3748;
  }

  .chartLoading p {
    color: #a0aec0;
  }
}

/* Print styles */
@media print {
  .responsiveChart {
    background: white;
    border: 1px solid #000;
    box-shadow: none;
  }

  .chartContainer {
    background: white;
  }

  .chartLoading {
    display: none;
  }
}
