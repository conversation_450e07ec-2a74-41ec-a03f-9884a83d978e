import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import styles from '@/styles/admin/Artists.module.css';

/**
 * Artists Management Page
 * 
 * This page provides a comprehensive interface for managing artist/braider profiles,
 * including availability, specializations, and performance tracking.
 */
export default function ArtistsManagement() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [artists, setArtists] = useState([]);
  const [filteredArtists, setFilteredArtists] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [specializationFilter, setSpecializationFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  // Load artists from API
  const loadArtists = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/artists', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch artists');
      }

      const data = await response.json();
      setArtists(data.artists || []);
      setFilteredArtists(data.artists || []);
    } catch (error) {
      console.error('Error loading artists:', error);
      setArtists([]);
      setFilteredArtists([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && user) {
      loadArtists();
    }
  }, [authLoading, user]);
  // Filter and sort artists
  useEffect(() => {
    let filtered = artists;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(artist =>
        artist.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        artist.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (artist.specializations && artist.specializations.some && artist.specializations.some(spec => 
          spec.toLowerCase().includes(searchTerm.toLowerCase())
        ))
      );
    }

    // Filter by specialization
    if (specializationFilter !== 'all') {
      filtered = filtered.filter(artist => 
        artist.specializations.includes(specializationFilter)
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(artist => artist.status === statusFilter);
    }

    // Sort artists
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'rating':
          return b.rating - a.rating;
        case 'bookings':
          return b.total_bookings - a.total_bookings;
        case 'revenue':
          return b.total_revenue - a.total_revenue;
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredArtists(filtered);
  }, [artists, searchTerm, specializationFilter, statusFilter, sortBy]);

  // Get unique specializations
  const specializations = [...new Set(artists.flatMap(artist => artist.specializations))];

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'active': return styles.statusActive;
      case 'inactive': return styles.statusInactive;
      default: return styles.statusDefault;
    }
  };

  // Get availability badge class
  const getAvailabilityBadgeClass = (availability) => {
    switch (availability) {
      case 'available': return styles.availabilityAvailable;
      case 'busy': return styles.availabilityBusy;
      case 'unavailable': return styles.availabilityUnavailable;
      default: return styles.availabilityDefault;
    }
  };

  // Toggle artist status
  const toggleArtistStatus = async (artistId) => {
    try {
      setArtists(prevArtists =>
        prevArtists.map(artist =>
          artist.id === artistId
            ? { ...artist, status: artist.status === 'active' ? 'inactive' : 'active' }
            : artist
        )
      );
    } catch (error) {
      console.error('Error updating artist status:', error);
    }
  };

  // Calculate stats
  const stats = {
    totalArtists: artists.length,
    activeArtists: artists.filter(a => a.status === 'active').length,
    availableArtists: artists.filter(a => a.availability === 'available').length,
    totalRevenue: artists.reduce((sum, a) => sum + a.total_revenue, 0),
    avgRating: artists.reduce((sum, a) => sum + a.rating, 0) / artists.length
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading artists...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return null; // Will redirect to login via useAuth
  }

  return (
    <AdminLayout>
      <Head>
        <title>Artists Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage artist profiles and assignments" />
      </Head>

      <div className={styles.artistsContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Artists Management</h1>
          <div className={styles.headerActions}>
            <Link href="/admin/artists/new" className={styles.newArtistBtn}>
              + Add Artist
            </Link>
          </div>
        </header>

        <div className={styles.controlsPanel}>
          <div className={styles.searchSection}>
            <input
              type="text"
              placeholder="Search artists by name, email, or specialization..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filters}>
            <select
              value={specializationFilter}
              onChange={(e) => setSpecializationFilter(e.target.value)}
              className={styles.specializationFilter}
            >
              <option value="all">All Specializations</option>
              {specializations.map(spec => (
                <option key={spec} value={spec}>{spec}</option>
              ))}
            </select>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={styles.statusFilter}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className={styles.sortSelect}
            >
              <option value="name">Sort by Name</option>
              <option value="rating">Sort by Rating</option>
              <option value="bookings">Sort by Bookings</option>
              <option value="revenue">Sort by Revenue</option>
            </select>
          </div>
        </div>

        <div className={styles.artistsContent}>
          <div className={styles.statsCards}>
            <div className={styles.statCard}>
              <h3>Total Artists</h3>
              <div className={styles.statValue}>{stats.totalArtists}</div>
            </div>
            <div className={styles.statCard}>
              <h3>Active Artists</h3>
              <div className={styles.statValue}>{stats.activeArtists}</div>
            </div>
            <div className={styles.statCard}>
              <h3>Available Now</h3>
              <div className={styles.statValue}>{stats.availableArtists}</div>
            </div>
            <div className={styles.statCard}>
              <h3>Total Revenue</h3>
              <div className={styles.statValue}>${stats.totalRevenue.toLocaleString()}</div>
            </div>
            <div className={styles.statCard}>
              <h3>Avg Rating</h3>
              <div className={styles.statValue}>{stats.avgRating.toFixed(1)}★</div>
            </div>
          </div>

          {filteredArtists.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No artists found matching your criteria.</p>
            </div>
          ) : (
            <div className={styles.artistsGrid}>
              {filteredArtists.map(artist => (
                <div key={artist.id} className={styles.artistCard}>
                  <div className={styles.artistHeader}>
                    <div className={styles.artistInfo}>
                      <h3>{artist.name}</h3>
                      <p>{artist.email}</p>
                      <p>{artist.phone}</p>
                    </div>
                    <div className={styles.badges}>
                      <div className={`${styles.statusBadge} ${getStatusBadgeClass(artist.status)}`}>
                        {artist.status}
                      </div>
                      <div className={`${styles.availabilityBadge} ${getAvailabilityBadgeClass(artist.availability)}`}>
                        {artist.availability}
                      </div>
                    </div>
                  </div>

                  <div className={styles.specializations}>
                    <h4>Specializations:</h4>
                    <div className={styles.specializationTags}>
                      {artist.specializations.map(spec => (
                        <span key={spec} className={styles.specializationTag}>
                          {spec}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className={styles.artistStats}>
                    <div className={styles.statItem}>
                      <span className={styles.statLabel}>Rating:</span>
                      <span className={styles.statValue}>{artist.rating}★</span>
                    </div>
                    <div className={styles.statItem}>
                      <span className={styles.statLabel}>Bookings:</span>
                      <span className={styles.statValue}>{artist.total_bookings}</span>
                    </div>
                    <div className={styles.statItem}>
                      <span className={styles.statLabel}>Revenue:</span>
                      <span className={styles.statValue}>${artist.total_revenue.toLocaleString()}</span>
                    </div>
                    <div className={styles.statItem}>
                      <span className={styles.statLabel}>Joined:</span>
                      <span className={styles.statValue}>
                        {new Date(artist.joined_date).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <div className={styles.artistActions}>
                    <Link href={`/admin/artists/${artist.id}`} className={styles.viewBtn}>
                      View Profile
                    </Link>
                    <button
                      onClick={() => toggleArtistStatus(artist.id)}
                      className={`${styles.toggleBtn} ${artist.status === 'active' ? styles.deactivate : styles.activate}`}
                    >
                      {artist.status === 'active' ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
