(()=>{var e={};e.id=5540,e.ids=[5540,660],e.modules={6514:e=>{e.exports={container:"Tips_container__Mrzxx",header:"Tips_header__Z2O8N",loading:"Tips_loading__QeliO",spinner:"Tips_spinner__wb38N",spin:"Tips_spin__KXqhR",summaryCards:"Tips_summaryCards__Lj5Wn",summaryCard:"Tips_summaryCard__UdXNX",summaryValue:"Tips_summaryValue__tmy2Z",summaryCount:"Tips_summaryCount__KP_PU",controls:"Tips_controls__TslSl",filters:"Tips_filters__SWcaR",filterSelect:"Tips_filterSelect__5Sdin",dateFilter:"Tips_dateFilter__E3_Hm",bulkActions:"Tips_bulkActions__LKQpC",distributeButton:"Tips_distributeButton__Ydems",holdButton:"Tips_holdButton__9rKzA",tipsTable:"Tips_tipsTable__DI_E9",amount:"Tips_amount__aYp7v",statusBadge:"Tips_statusBadge__C58Oq",pending:"Tips_pending__oDfWc",distributed:"Tips_distributed__RwSRV",held:"Tips_held__LvSYC",actionButtons:"Tips_actionButtons__1ITql",distributeBtn:"Tips_distributeBtn__OLhg9",holdBtn:"Tips_holdBtn__xg6V2",releaseBtn:"Tips_releaseBtn__4__Lh",emptyState:"Tips_emptyState__fbuMq"}},2789:(e,t,s)=>{"use strict";s.a(e,async(e,i)=>{try{s.r(t),s.d(t,{config:()=>x,default:()=>p,getServerSideProps:()=>_,getStaticPaths:()=>m,getStaticProps:()=>h,reportWebVitals:()=>g,routeModule:()=>T,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>j});var a=s(7093),r=s(5244),n=s(1323),l=s(2899),d=s.n(l),c=s(6814),o=s(5154),u=e([c,o]);[c,o]=u.then?(await u)():u;let p=(0,n.l)(o,"default"),h=(0,n.l)(o,"getStaticProps"),m=(0,n.l)(o,"getStaticPaths"),_=(0,n.l)(o,"getServerSideProps"),x=(0,n.l)(o,"config"),g=(0,n.l)(o,"reportWebVitals"),j=(0,n.l)(o,"unstable_getStaticProps"),b=(0,n.l)(o,"unstable_getStaticPaths"),y=(0,n.l)(o,"unstable_getStaticParams"),S=(0,n.l)(o,"unstable_getServerProps"),v=(0,n.l)(o,"unstable_getServerSideProps"),T=new a.PagesRouteModule({definition:{kind:r.x.PAGES,page:"/admin/tips",pathname:"/admin/tips",bundlePath:"",filename:""},components:{App:c.default,Document:d()},userland:o});i()}catch(e){i(e)}})},5154:(e,t,s)=>{"use strict";s.a(e,async(e,i)=>{try{s.r(t),s.d(t,{default:()=>u});var a=s(997),r=s(6689),n=s(8568),l=s(4845),d=s(6514),c=s.n(d),o=e([l]);function u(){let{user:e,loading:t}=(0,n.a)(),[s,i]=(0,r.useState)(!0),[d,o]=(0,r.useState)([]),[u,p]=(0,r.useState)(null),[h,m]=(0,r.useState)([]),[_,x]=(0,r.useState)("all"),[g,j]=(0,r.useState)(""),[b,y]=(0,r.useState)(""),[S,v]=(0,r.useState)(!1),T=async()=>{try{i(!0);let e=new URLSearchParams({status:_,limit:"100"});g&&e.append("artist_id",g),b&&e.append("date_from",b);let t=await fetch(`/api/admin/tips?${e}`),s=await t.json();t.ok?(o(s.tips||[]),p(s.summary||{})):console.error("Failed to load tips:",s.error)}catch(e){console.error("Error loading tips:",e)}finally{i(!1)}},f=async(e,t,s="manual",i="")=>{try{v(!0);let a=await fetch("/api/admin/tips",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({tip_id:e,action:t,distribution_method:s,notes:i})}),r=await a.json();a.ok?(await T(),alert(`Tip ${t}d successfully!`)):alert(`Failed to ${t} tip: ${r.message}`)}catch(e){console.error(`Error ${t}ing tip:`,e),alert(`Error ${t}ing tip`)}finally{v(!1)}},N=async e=>{if(0===h.length){alert("Please select tips to process");return}let t=prompt("Distribution method (cash/bank_transfer/payroll):","cash");if(!t)return;let s=prompt("Notes (optional):");try{v(!0);let i=await fetch("/api/admin/tips",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:`bulk_${e}`,tip_ids:h,distribution_method:t,notes:s})}),a=await i.json();i.ok?(m([]),await T(),alert(`${a.updated_count} tips ${e}d successfully!`)):alert(`Failed to ${e} tips: ${a.message}`)}catch(t){console.error(`Error bulk ${e}ing tips:`,t),alert(`Error bulk ${e}ing tips`)}finally{v(!1)}},C=e=>{m(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},P=e=>`$${parseFloat(e).toFixed(2)}`,k=e=>new Date(e).toLocaleDateString("en-AU",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),B=e=>{let t={pending:{class:"pending",text:"Pending"},distributed:{class:"distributed",text:"Distributed"},held:{class:"held",text:"On Hold"}},s=t[e]||t.pending;return a.jsx("span",{className:`${c().statusBadge} ${c()[s.class]}`,children:s.text})};return t||s?a.jsx(l.Z,{children:(0,a.jsxs)("div",{className:c().loading,children:[a.jsx("div",{className:c().spinner}),a.jsx("p",{children:"Loading tip management..."})]})}):a.jsx(l.Z,{children:(0,a.jsxs)("div",{className:c().container,children:[(0,a.jsxs)("div",{className:c().header,children:[a.jsx("h1",{children:"\uD83D\uDCB0 Tip Management"}),a.jsx("p",{children:"Manage and distribute tips to artists and staff"})]}),u&&(0,a.jsxs)("div",{className:c().summaryCards,children:[(0,a.jsxs)("div",{className:c().summaryCard,children:[a.jsx("h3",{children:"Total Tips"}),a.jsx("div",{className:c().summaryValue,children:P(u.total_amount)}),(0,a.jsxs)("div",{className:c().summaryCount,children:[u.total_tips," tips"]})]}),(0,a.jsxs)("div",{className:c().summaryCard,children:[a.jsx("h3",{children:"Pending Distribution"}),a.jsx("div",{className:c().summaryValue,children:P(u.pending_amount)}),a.jsx("div",{className:c().summaryCount,children:"Awaiting distribution"})]}),(0,a.jsxs)("div",{className:c().summaryCard,children:[a.jsx("h3",{children:"Distributed"}),a.jsx("div",{className:c().summaryValue,children:P(u.distributed_amount)}),a.jsx("div",{className:c().summaryCount,children:"Already distributed"})]})]}),(0,a.jsxs)("div",{className:c().controls,children:[(0,a.jsxs)("div",{className:c().filters,children:[(0,a.jsxs)("select",{value:_,onChange:e=>x(e.target.value),className:c().filterSelect,children:[a.jsx("option",{value:"all",children:"All Tips"}),a.jsx("option",{value:"pending",children:"Pending"}),a.jsx("option",{value:"distributed",children:"Distributed"}),a.jsx("option",{value:"held",children:"On Hold"})]}),a.jsx("input",{type:"date",value:b,onChange:e=>y(e.target.value),className:c().dateFilter,placeholder:"Filter by date"})]}),a.jsx("div",{className:c().bulkActions,children:h.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:()=>N("distribute"),disabled:S,className:c().distributeButton,children:["Distribute Selected (",h.length,")"]}),a.jsx("button",{onClick:()=>N("hold"),disabled:S,className:c().holdButton,children:"Hold Selected"})]})})]}),a.jsx("div",{className:c().tipsTable,children:0===d.length?a.jsx("div",{className:c().emptyState,children:a.jsx("p",{children:"No tips found for the selected filters."})}):(0,a.jsxs)("table",{children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{children:[a.jsx("th",{children:a.jsx("input",{type:"checkbox",onChange:e=>{e.target.checked?m(d.map(e=>e.id)):m([])},checked:h.length===d.length&&d.length>0})}),a.jsx("th",{children:"Date"}),a.jsx("th",{children:"Artist"}),a.jsx("th",{children:"Service"}),a.jsx("th",{children:"Amount"}),a.jsx("th",{children:"Method"}),a.jsx("th",{children:"Status"}),a.jsx("th",{children:"Actions"})]})}),a.jsx("tbody",{children:d.map(e=>(0,a.jsxs)("tr",{children:[a.jsx("td",{children:a.jsx("input",{type:"checkbox",checked:h.includes(e.id),onChange:()=>C(e.id)})}),a.jsx("td",{children:k(e.created_at)}),a.jsx("td",{children:e.artist_profiles?.name||"Unknown"}),a.jsx("td",{children:e.bookings?.service_name||"N/A"}),a.jsx("td",{className:c().amount,children:P(e.amount)}),a.jsx("td",{children:e.tip_method}),a.jsx("td",{children:B(e.distribution_status)}),a.jsx("td",{children:(0,a.jsxs)("div",{className:c().actionButtons,children:["pending"===e.distribution_status&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:()=>f(e.id,"distribute"),disabled:S,className:c().distributeBtn,children:"Distribute"}),a.jsx("button",{onClick:()=>f(e.id,"hold"),disabled:S,className:c().holdBtn,children:"Hold"})]}),"held"===e.distribution_status&&a.jsx("button",{onClick:()=>f(e.id,"release"),disabled:S,className:c().releaseBtn,children:"Release"})]})})]},e.id))})]})})]})})}l=(o.then?(await o)():o)[0],i()}catch(e){i(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[2899,6212,1664,7441],()=>s(2789));module.exports=i})();