(()=>{var e={};e.id=9451,e.ids=[9451,660],e.modules={699:e=>{e.exports={supplierManagement:"Suppliers_supplierManagement__moQ9K",header:"Suppliers_header__e_GFD",headerContent:"Suppliers_headerContent__RlAv_",title:"Suppliers_title__mJ53N",subtitle:"Suppliers_subtitle__jKx8q",headerActions:"Suppliers_headerActions__mhBy0",addBtn:"Suppliers_addBtn__m7uLG",controls:"Suppliers_controls__u6XUL",searchSection:"Suppliers_searchSection___Yh_u",searchInput:"Suppliers_searchInput__ztdWK",filters:"Suppliers_filters__m2SbG",filterSelect:"Suppliers_filterSelect__NsTpf",sortSelect:"Suppliers_sortSelect__xah3_",suppliersContainer:"Suppliers_suppliersContainer__lLuBC",loading:"Suppliers_loading__SPL55",loadingSpinner:"Suppliers_loadingSpinner__8C00Y",spin:"Suppliers_spin__m4hm1",emptyState:"Suppliers_emptyState__2EIlp",emptyIcon:"Suppliers_emptyIcon__mVvSl",addFirstBtn:"Suppliers_addFirstBtn__5YL2d",suppliersGrid:"Suppliers_suppliersGrid__8K7Uc",supplierCard:"Suppliers_supplierCard__FpznX",cardHeader:"Suppliers_cardHeader__vUyV_",supplierInfo:"Suppliers_supplierInfo__LLH3s",supplierName:"Suppliers_supplierName__P3SYy",statusBadge:"Suppliers_statusBadge__q__Jt",active:"Suppliers_active__0rFtH",inactive:"Suppliers_inactive__VhmvH",cardBody:"Suppliers_cardBody__wjlXW",contactInfo:"Suppliers_contactInfo__r93AU",businessInfo:"Suppliers_businessInfo__gDnna",infoItem:"Suppliers_infoItem__NkSUU",label:"Suppliers_label__x1sKg",value:"Suppliers_value__Rvb2Z",notes:"Suppliers_notes__aLiX9",notesText:"Suppliers_notesText__Nlgcw",cardFooter:"Suppliers_cardFooter__Kh64U",cardActions:"Suppliers_cardActions__xZeWq",viewBtn:"Suppliers_viewBtn__W23ER",editBtn:"Suppliers_editBtn__VGxzN",deleteBtn:"Suppliers_deleteBtn__K6UPw",cardMeta:"Suppliers_cardMeta__30iaq",createdDate:"Suppliers_createdDate__Et97b",supplierForm:"Suppliers_supplierForm__Ile4t",formGrid:"Suppliers_formGrid__RoRp_",formGroup:"Suppliers_formGroup__exSuf",fullWidth:"Suppliers_fullWidth__DS0su",formLabel:"Suppliers_formLabel__zmgic",formInput:"Suppliers_formInput__GWKlp",formTextarea:"Suppliers_formTextarea__oPk6B",formSelect:"Suppliers_formSelect__nAwes",formActions:"Suppliers_formActions__buaHF",saveBtn:"Suppliers_saveBtn__CGIMf",cancelBtn:"Suppliers_cancelBtn__APjcB"}},466:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{config:()=>x,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>_,getStaticProps:()=>m,reportWebVitals:()=>f,routeModule:()=>N,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>S});var a=t(7093),i=t(5244),l=t(1323),n=t(2899),o=t.n(n),c=t(6814),p=t(3636),d=e([c,p]);[c,p]=d.then?(await d)():d;let u=(0,l.l)(p,"default"),m=(0,l.l)(p,"getStaticProps"),_=(0,l.l)(p,"getStaticPaths"),h=(0,l.l)(p,"getServerSideProps"),x=(0,l.l)(p,"config"),f=(0,l.l)(p,"reportWebVitals"),S=(0,l.l)(p,"unstable_getStaticProps"),g=(0,l.l)(p,"unstable_getStaticPaths"),j=(0,l.l)(p,"unstable_getStaticParams"),v=(0,l.l)(p,"unstable_getServerProps"),y=(0,l.l)(p,"unstable_getServerSideProps"),N=new a.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/admin/suppliers",pathname:"/admin/suppliers",bundlePath:"",filename:""},components:{App:c.default,Document:o()},userland:p});r()}catch(e){r(e)}})},3912:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.d(s,{Z:()=>u});var a=t(997),i=t(6689),l=t(1664),n=t.n(l),o=t(3590),c=t(699),p=t.n(c),d=e([o]);function u({initialSuppliers:e=[]}){let[s,t]=(0,i.useState)(e),[r,l]=(0,i.useState)(e),[c,d]=(0,i.useState)(!1),[u,m]=(0,i.useState)(""),[_,h]=(0,i.useState)("all"),[x,f]=(0,i.useState)("name"),[S,g]=(0,i.useState)("asc"),j=async()=>{d(!0);try{let e=localStorage.getItem("adminToken"),s=await fetch("/api/admin/suppliers",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!s.ok)throw Error("Failed to fetch suppliers");let r=await s.json();t(r.suppliers||[])}catch(e){console.error("Error fetching suppliers:",e),o.toast.error("Failed to load suppliers")}finally{d(!1)}},v=async(e,s)=>{if(confirm(`Are you sure you want to delete supplier "${s}"? This will mark them as inactive.`))try{let s=localStorage.getItem("adminToken"),t=await fetch(`/api/admin/suppliers/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(!t.ok){let e=await t.json();throw Error(e.message||"Failed to delete supplier")}o.toast.success("Supplier deleted successfully"),j()}catch(e){console.error("Error deleting supplier:",e),o.toast.error(e instanceof Error?e.message:"Failed to delete supplier")}},y=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),N=e=>new Date(e).toLocaleDateString("en-AU");return(0,a.jsxs)("div",{className:p().supplierManagement,children:[(0,a.jsxs)("div",{className:p().header,children:[(0,a.jsxs)("div",{className:p().headerContent,children:[a.jsx("h1",{className:p().title,children:"Supplier Management"}),a.jsx("p",{className:p().subtitle,children:"Manage your suppliers and vendor relationships"})]}),a.jsx("div",{className:p().headerActions,children:a.jsx(n(),{href:"/admin/suppliers/new",className:p().addBtn,children:"+ Add Supplier"})})]}),(0,a.jsxs)("div",{className:p().controls,children:[a.jsx("div",{className:p().searchSection,children:a.jsx("input",{type:"text",placeholder:"Search suppliers by name, contact, or email...",value:u,onChange:e=>m(e.target.value),className:p().searchInput})}),(0,a.jsxs)("div",{className:p().filters,children:[(0,a.jsxs)("select",{value:_,onChange:e=>h(e.target.value),className:p().filterSelect,children:[a.jsx("option",{value:"all",children:"All Suppliers"}),a.jsx("option",{value:"active",children:"Active Only"}),a.jsx("option",{value:"inactive",children:"Inactive Only"})]}),(0,a.jsxs)("select",{value:`${x}-${S}`,onChange:e=>{let[s,t]=e.target.value.split("-");f(s),g(t)},className:p().sortSelect,children:[a.jsx("option",{value:"name-asc",children:"Name (A-Z)"}),a.jsx("option",{value:"name-desc",children:"Name (Z-A)"}),a.jsx("option",{value:"created_at-desc",children:"Newest First"}),a.jsx("option",{value:"created_at-asc",children:"Oldest First"}),a.jsx("option",{value:"lead_time_days-asc",children:"Lead Time (Low-High)"}),a.jsx("option",{value:"lead_time_days-desc",children:"Lead Time (High-Low)"})]})]})]}),a.jsx("div",{className:p().suppliersContainer,children:c?(0,a.jsxs)("div",{className:p().loading,children:[a.jsx("div",{className:p().loadingSpinner}),a.jsx("p",{children:"Loading suppliers..."})]}):0===r.length?(0,a.jsxs)("div",{className:p().emptyState,children:[a.jsx("div",{className:p().emptyIcon,children:"\uD83D\uDCE6"}),a.jsx("h3",{children:"No suppliers found"}),a.jsx("p",{children:u||"all"!==_?"Try adjusting your search or filters":"Get started by adding your first supplier"}),!u&&"all"===_&&a.jsx(n(),{href:"/admin/suppliers/new",className:p().addFirstBtn,children:"Add First Supplier"})]}):a.jsx("div",{className:p().suppliersGrid,children:r.map(e=>(0,a.jsxs)("div",{className:p().supplierCard,children:[a.jsx("div",{className:p().cardHeader,children:(0,a.jsxs)("div",{className:p().supplierInfo,children:[a.jsx("h3",{className:p().supplierName,children:e.name}),a.jsx("span",{className:`${p().statusBadge} ${e.is_active?p().active:p().inactive}`,children:e.is_active?"Active":"Inactive"})]})}),(0,a.jsxs)("div",{className:p().cardBody,children:[e.contact_person&&(0,a.jsxs)("div",{className:p().contactInfo,children:[a.jsx("span",{className:p().label,children:"Contact:"}),a.jsx("span",{className:p().value,children:e.contact_person})]}),e.email&&(0,a.jsxs)("div",{className:p().contactInfo,children:[a.jsx("span",{className:p().label,children:"Email:"}),a.jsx("span",{className:p().value,children:e.email})]}),e.phone&&(0,a.jsxs)("div",{className:p().contactInfo,children:[a.jsx("span",{className:p().label,children:"Phone:"}),a.jsx("span",{className:p().value,children:e.phone})]}),(0,a.jsxs)("div",{className:p().businessInfo,children:[(0,a.jsxs)("div",{className:p().infoItem,children:[a.jsx("span",{className:p().label,children:"Payment Terms:"}),a.jsx("span",{className:p().value,children:e.payment_terms})]}),(0,a.jsxs)("div",{className:p().infoItem,children:[a.jsx("span",{className:p().label,children:"Lead Time:"}),(0,a.jsxs)("span",{className:p().value,children:[e.lead_time_days," days"]})]}),(0,a.jsxs)("div",{className:p().infoItem,children:[a.jsx("span",{className:p().label,children:"Min Order:"}),a.jsx("span",{className:p().value,children:y(e.minimum_order_amount)})]})]}),e.notes&&(0,a.jsxs)("div",{className:p().notes,children:[a.jsx("span",{className:p().label,children:"Notes:"}),a.jsx("p",{className:p().notesText,children:e.notes})]})]}),(0,a.jsxs)("div",{className:p().cardFooter,children:[(0,a.jsxs)("div",{className:p().cardActions,children:[a.jsx(n(),{href:`/admin/suppliers/${e.id}`,className:p().viewBtn,children:"View Details"}),a.jsx(n(),{href:`/admin/suppliers/${e.id}/edit`,className:p().editBtn,children:"Edit"}),a.jsx("button",{onClick:()=>v(e.id,e.name),className:p().deleteBtn,disabled:!e.is_active,children:"Delete"})]}),a.jsx("div",{className:p().cardMeta,children:(0,a.jsxs)("span",{className:p().createdDate,children:["Added ",N(e.created_at)]})})]})]},e.id))})})]})}o=(d.then?(await d)():d)[0],r()}catch(e){r(e)}})},3636:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{default:()=>_});var a=t(997),i=t(9816),l=t.n(i),n=t(6689),o=t(968),c=t.n(o),p=t(8568),d=t(4845),u=t(3912),m=e([d,u]);function _(){let{user:e,loading:s}=(0,p.a)(),[t,r]=(0,n.useState)(!0),[i,o]=(0,n.useState)([]),[m,_]=(0,n.useState)(null),h=async()=>{r(!0),_(null);try{let e=localStorage.getItem("adminToken");if(!e)throw Error("No authentication token found");let s=await fetch("/api/admin/suppliers",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!s.ok){if(401===s.status)throw Error("Authentication failed. Please log in again.");if(403===s.status)throw Error("You do not have permission to access suppliers.");throw Error(`Failed to fetch suppliers: ${s.status}`)}let t=await s.json();o(t.suppliers||[])}catch(e){console.error("Error fetching suppliers:",e),_(e.message)}finally{r(!1)}};return s?a.jsx(d.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("div",{style:{width:"32px",height:"32px",border:"3px solid #e2e8f0",borderTop:"3px solid #667eea",borderRadius:"50%",animation:"spin 1s linear infinite"}}),a.jsx("p",{style:{color:"#64748b"},children:"Authenticating..."})]})}):e?["DEV","Admin"].includes(e.role)?(0,a.jsxs)(d.Z,{children:[(0,a.jsxs)(c(),{children:[a.jsx("title",{className:"jsx-ff161281ed666c63",children:"Supplier Management | Ocean Soul Sparkles Admin"}),a.jsx("meta",{name:"description",content:"Manage suppliers and vendor relationships",className:"jsx-ff161281ed666c63"})]}),m?(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},className:"jsx-ff161281ed666c63",children:[a.jsx("h2",{style:{color:"#ef4444"},className:"jsx-ff161281ed666c63",children:"Error Loading Suppliers"}),a.jsx("p",{style:{color:"#64748b"},className:"jsx-ff161281ed666c63",children:m}),a.jsx("button",{onClick:h,style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",border:"none",padding:"0.75rem 1.5rem",borderRadius:"8px",fontWeight:"600",cursor:"pointer"},className:"jsx-ff161281ed666c63",children:"Try Again"})]}):a.jsx(u.Z,{initialSuppliers:i}),a.jsx(l(),{id:"ff161281ed666c63",children:"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]}):a.jsx(d.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("h2",{style:{color:"#ef4444"},children:"Access Denied"}),a.jsx("p",{style:{color:"#64748b"},children:"You do not have permission to access supplier management."})]})}):a.jsx(d.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("h2",{style:{color:"#ef4444"},children:"Authentication Required"}),a.jsx("p",{style:{color:"#64748b"},children:"Please log in to access the suppliers page."})]})})}[d,u]=m.then?(await m)():m,r()}catch(e){r(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},9816:e=>{"use strict";e.exports=require("styled-jsx/style")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[2899,6212,1664,7441],()=>t(466));module.exports=r})();