import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth/admin-auth'

/**
 * API endpoint for checking artist availability for POS bookings
 * Returns available time slots for a specific artist on a given date
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Artist availability API called`)

  try {
    // Authenticate admin request
    const { user, error: authError } = await authenticateAdminRequest(req)
    if (authError || !user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: authError?.message || 'Authentication failed',
        requestId
      })
    }

    const { artist_id, date, service_duration_minutes } = req.query

    // Validate required parameters
    if (!artist_id || !date || !service_duration_minutes) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'artist_id, date, and service_duration_minutes are required',
        requestId
      })
    }

    const serviceDuration = parseInt(service_duration_minutes)
    if (isNaN(serviceDuration) || serviceDuration <= 0) {
      return res.status(400).json({
        error: 'Invalid service duration',
        message: 'service_duration_minutes must be a positive number',
        requestId
      })
    }

    // Validate date format (YYYY-MM-DD)
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return res.status(400).json({
        error: 'Invalid date format',
        message: 'Date must be in YYYY-MM-DD format',
        requestId
      })
    }

    console.log(`[${requestId}] Checking availability for artist ${artist_id} on ${date} for ${serviceDuration} minutes`)

    // Get artist profile
    const { data: artistProfile, error: profileError } = await supabaseAdmin
      .from('artist_profiles')
      .select('*')
      .eq('id', artist_id)
      .single()

    if (profileError || !artistProfile) {
      return res.status(404).json({
        error: 'Artist not found',
        message: 'The specified artist does not exist',
        requestId
      })
    }

    // Check if artist is active and available
    if (!artistProfile.is_active) {
      return res.status(200).json({
        available_slots: [],
        message: 'Artist is currently inactive',
        requestId
      })
    }

    // Get artist's schedule for the day
    const dayOfWeek = new Date(date + 'T00:00:00Z').getDay()
    const { data: schedule, error: scheduleError } = await supabaseAdmin
      .from('artist_availability')
      .select('*')
      .eq('artist_id', artist_id)
      .eq('day_of_week', dayOfWeek)
      .eq('is_available', true)
      .single()

    if (scheduleError || !schedule) {
      return res.status(200).json({
        available_slots: [],
        message: 'Artist has no availability scheduled for this day',
        requestId
      })
    }

    // Get existing bookings for the date
    const dayStart = new Date(date + 'T00:00:00Z')
    const dayEnd = new Date(date + 'T23:59:59Z')

    const { data: existingBookings, error: bookingsError } = await supabaseAdmin
      .from('bookings')
      .select('start_time, end_time')
      .eq('assigned_artist_id', artist_id)
      .gte('start_time', dayStart.toISOString())
      .lte('start_time', dayEnd.toISOString())
      .not('status', 'in', ['canceled', 'no_show'])

    if (bookingsError) {
      console.error(`[${requestId}] Error fetching bookings:`, bookingsError)
      return res.status(500).json({
        error: 'Failed to fetch existing bookings',
        details: bookingsError.message,
        requestId
      })
    }

    // Check daily booking limit
    const maxDailyBookings = artistProfile.max_daily_bookings || 8
    if (existingBookings.length >= maxDailyBookings) {
      return res.status(200).json({
        available_slots: [],
        message: 'Artist has reached maximum daily bookings',
        requestId
      })
    }

    // Helper function to create Date from time string
    const createDateFromTime = (timeStr, dateObj) => {
      if (!timeStr) return null
      const [hours, minutes, seconds] = timeStr.split(':')
      const newDate = new Date(dateObj)
      newDate.setUTCHours(parseInt(hours, 10), parseInt(minutes, 10), parseInt(seconds || 0, 10), 0)
      return newDate
    }

    const inputDate = new Date(date + 'T00:00:00Z')
    
    // Get working period from schedule
    const workStart = createDateFromTime(schedule.start_time, inputDate)
    const workEnd = createDateFromTime(schedule.end_time, inputDate)

    if (!workStart || !workEnd) {
      return res.status(200).json({
        available_slots: [],
        message: 'Invalid schedule times',
        requestId
      })
    }

    // Generate available time slots
    const availableSlots = []
    const slotDurationMs = serviceDuration * 60 * 1000
    const slotIncrementMs = 15 * 60 * 1000 // 15-minute increments

    let currentTime = new Date(workStart)

    while (currentTime < workEnd) {
      const slotEnd = new Date(currentTime.getTime() + slotDurationMs)

      // Check if slot fits within working hours
      if (slotEnd > workEnd) {
        break
      }

      // Check if slot conflicts with existing bookings
      const hasConflict = existingBookings.some(booking => {
        const bookingStart = new Date(booking.start_time)
        const bookingEnd = new Date(booking.end_time)
        
        // Add buffer time (5 minutes before and after)
        const bufferMs = 5 * 60 * 1000
        const bufferedStart = new Date(bookingStart.getTime() - bufferMs)
        const bufferedEnd = new Date(bookingEnd.getTime() + bufferMs)

        return (
          (currentTime >= bufferedStart && currentTime < bufferedEnd) ||
          (slotEnd > bufferedStart && slotEnd <= bufferedEnd) ||
          (currentTime <= bufferedStart && slotEnd >= bufferedEnd)
        )
      })

      // Check break times if they exist
      let conflictsWithBreak = false
      if (schedule.break_start_time && schedule.break_end_time) {
        const breakStart = createDateFromTime(schedule.break_start_time, inputDate)
        const breakEnd = createDateFromTime(schedule.break_end_time, inputDate)

        if (breakStart && breakEnd) {
          conflictsWithBreak = (
            (currentTime >= breakStart && currentTime < breakEnd) ||
            (slotEnd > breakStart && slotEnd <= breakEnd) ||
            (currentTime <= breakStart && slotEnd >= breakEnd)
          )
        }
      }

      if (!hasConflict && !conflictsWithBreak) {
        availableSlots.push({
          time: currentTime.toISOString(),
          status: 'available'
        })
      }

      currentTime = new Date(currentTime.getTime() + slotIncrementMs)
    }

    console.log(`[${requestId}] Generated ${availableSlots.length} available slots`)

    return res.status(200).json({
      available_slots: availableSlots,
      message: availableSlots.length > 0 ? 'Availability fetched successfully' : 'No available slots found',
      requestId
    })

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred while checking availability',
      requestId
    })
  }
}
