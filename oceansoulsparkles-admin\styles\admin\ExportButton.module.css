/**
 * Ocean Soul Sparkles Admin - Export <PERSON><PERSON>
 * Responsive export button with dropdown functionality
 */

.exportButton {
  position: relative;
  display: inline-block;
}

.exportBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: var(--admin-primary, #3788d8);
  color: white;
  border: none;
  border-radius: var(--admin-radius-md, 8px);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--admin-transition-normal, 0.2s ease);
  white-space: nowrap;
  min-width: 120px;
  justify-content: center;
}

.exportBtn:hover:not(:disabled) {
  background: var(--admin-primary-dark, #2c6bb8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.3);
}

.exportBtn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(55, 136, 216, 0.2);
}

.exportBtn:disabled {
  background: var(--admin-text-secondary, #666666);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.exportBtn.exporting {
  background: var(--admin-warning, #ffc107);
  color: var(--admin-text-primary, #1a1a1a);
  cursor: wait;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dropdownArrow {
  font-size: 0.7rem;
  margin-left: 4px;
  transition: transform var(--admin-transition-normal, 0.2s ease);
}

.exportButton:hover .dropdownArrow {
  transform: translateY(1px);
}

.exportDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--admin-bg-primary, #ffffff);
  border: 1px solid var(--admin-border-light, #e0e0e0);
  border-radius: var(--admin-radius-lg, 12px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 10001;
  margin-top: 4px;
  min-width: 200px;
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdownHeader {
  padding: 12px 16px 8px;
  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);
  background: var(--admin-bg-secondary, #f8f9fa);
}

.dropdownHeader span {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--admin-text-secondary, #666666);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dropdownItem {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background var(--admin-transition-normal, 0.2s ease);
  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);
}

.dropdownItem:last-child {
  border-bottom: none;
}

.dropdownItem:hover:not(:disabled):not(.disabled) {
  background: var(--admin-bg-secondary, #f8f9fa);
}

.dropdownItem:disabled,
.dropdownItem.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.formatIcon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.formatInfo {
  flex: 1;
  min-width: 0;
}

.formatName {
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  font-size: 0.9rem;
  margin-bottom: 2px;
}

.formatDesc {
  font-size: 0.75rem;
  color: var(--admin-text-secondary, #666666);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .exportBtn {
    padding: 12px 16px;
    font-size: 1rem;
    min-width: 140px;
  }

  .exportDropdown {
    right: -10px;
    left: -10px;
    min-width: auto;
  }

  .dropdownItem {
    padding: 16px;
  }

  .formatIcon {
    font-size: 1.4rem;
    width: 28px;
  }

  .formatName {
    font-size: 1rem;
  }

  .formatDesc {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .exportBtn {
    padding: 14px 16px;
    min-width: 100%;
    justify-content: center;
  }

  .exportDropdown {
    right: 0;
    left: 0;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .exportDropdown {
    background: var(--admin-bg-primary-dark, #1a1a1a);
    border-color: var(--admin-border-dark, #404040);
  }

  .dropdownHeader {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
  }

  .dropdownItem:hover:not(:disabled):not(.disabled) {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
  }

  .formatName {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .formatDesc {
    color: var(--admin-text-secondary-dark, #cccccc);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .exportBtn {
    border: 2px solid currentColor;
  }

  .exportDropdown {
    border-width: 2px;
  }

  .dropdownItem {
    border-bottom-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .exportDropdown {
    animation: none;
  }

  .spinner {
    animation: none;
  }

  .exportBtn,
  .dropdownItem,
  .dropdownArrow {
    transition: none;
  }
}

/* Focus styles for accessibility */
.exportBtn:focus-visible {
  outline: 2px solid var(--admin-primary, #3788d8);
  outline-offset: 2px;
}

.dropdownItem:focus-visible {
  outline: 2px solid var(--admin-primary, #3788d8);
  outline-offset: -2px;
}

/* Loading state */
.exportBtn.exporting .dropdownArrow {
  display: none;
}

/* Success state (brief animation) */
.exportBtn.success {
  background: var(--admin-success, #28a745);
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}
