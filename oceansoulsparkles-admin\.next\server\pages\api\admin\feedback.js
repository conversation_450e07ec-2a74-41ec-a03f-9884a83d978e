"use strict";(()=>{var e={};e.id=700,e.ids=[700],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},2752:(e,r,t)=>{t.r(r),t.d(r,{config:()=>m,default:()=>g,routeModule:()=>_});var s={};t.r(s),t.d(s,{default:()=>u});var a=t(1802),i=t(7153),n=t(8781),o=t(7474),l=t(2885);let c=process.env.SUPABASE_SERVICE_ROLE_KEY,d=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",c);async function u(e,r){let t=Math.random().toString(36).substring(2,8);console.log(`[${t}] Customer feedback API called - ${e.method}`);try{let s=await (0,o.SA)(e);if(!s.success)return r.status(401).json({error:"Authentication required",message:s.message||"Authentication failed",requestId:t});let{user:a}=s;if("GET"===e.method){let{customer_id:s,artist_id:a,rating_min:i,rating_max:n,is_public:o,limit:l="50",offset:c="0"}=e.query,u=d.from("customer_feedback").select(`
          id,
          customer_id,
          booking_id,
          artist_id,
          rating,
          service_rating,
          cleanliness_rating,
          timeliness_rating,
          overall_experience_rating,
          feedback_text,
          would_recommend,
          improvement_suggestions,
          is_public,
          response_text,
          responded_at,
          created_at,
          customers!inner(
            id,
            first_name,
            last_name,
            email
          ),
          bookings(
            id,
            start_time,
            services(name)
          ),
          artist_profiles(
            id,
            name,
            artist_name
          )
        `).order("created_at",{ascending:!1}).range(parseInt(c),parseInt(c)+parseInt(l)-1);s&&(u=u.eq("customer_id",s)),a&&(u=u.eq("artist_id",a)),i&&(u=u.gte("rating",parseInt(i))),n&&(u=u.lte("rating",parseInt(n))),void 0!==o&&(u=u.eq("is_public","true"===o));let{data:g,error:m}=await u;if(m)return console.error(`[${t}] Database error:`,m),r.status(500).json({error:"Failed to fetch feedback",message:m.message,requestId:t});let _=d.from("customer_feedback").select("*",{count:"exact",head:!0});s&&(_=_.eq("customer_id",s)),a&&(_=_.eq("artist_id",a)),i&&(_=_.gte("rating",parseInt(i))),n&&(_=_.lte("rating",parseInt(n))),void 0!==o&&(_=_.eq("is_public","true"===o));let{count:p}=await _,{data:f}=await d.from("customer_feedback").select("rating, service_rating, cleanliness_rating, timeliness_rating, overall_experience_rating"),b={overall:0,service:0,cleanliness:0,timeliness:0,experience:0};return f&&f.length>0&&(b={overall:f.reduce((e,r)=>e+(r.rating||0),0)/f.length,service:f.reduce((e,r)=>e+(r.service_rating||0),0)/f.length,cleanliness:f.reduce((e,r)=>e+(r.cleanliness_rating||0),0)/f.length,timeliness:f.reduce((e,r)=>e+(r.timeliness_rating||0),0)/f.length,experience:f.reduce((e,r)=>e+(r.overall_experience_rating||0),0)/f.length}),r.status(200).json({feedback:g||[],total:p||0,averages:b,limit:parseInt(l),offset:parseInt(c),requestId:t})}if("POST"===e.method){let{customer_id:s,booking_id:a,artist_id:i,rating:n,service_rating:o,cleanliness_rating:l,timeliness_rating:c,overall_experience_rating:u,feedback_text:g,would_recommend:m,improvement_suggestions:_,is_public:p=!1}=e.body;if(!s||!a||!n)return r.status(400).json({error:"Missing required fields",message:"Customer ID, booking ID, and overall rating are required",requestId:t});for(let e of[n,o,l,c,u])if(null!=e&&(e<1||e>5))return r.status(400).json({error:"Invalid rating value",message:"All ratings must be between 1 and 5",requestId:t});let{data:f,error:b}=await d.from("customer_feedback").insert([{customer_id:s,booking_id:a,artist_id:i,rating:n,service_rating:o,cleanliness_rating:l,timeliness_rating:c,overall_experience_rating:u,feedback_text:g,would_recommend:m,improvement_suggestions:_,is_public:p}]).select(`
          id,
          customer_id,
          booking_id,
          artist_id,
          rating,
          service_rating,
          cleanliness_rating,
          timeliness_rating,
          overall_experience_rating,
          feedback_text,
          would_recommend,
          improvement_suggestions,
          is_public,
          created_at
        `).single();if(b)return console.error(`[${t}] Error creating feedback:`,b),r.status(500).json({error:"Failed to create feedback record",message:b.message,requestId:t});return r.status(201).json({feedback:f,message:"Feedback submitted successfully",requestId:t})}return r.status(405).json({error:"Method not allowed",requestId:t})}catch(e){return console.error(`[${t}] Unexpected error:`,e),r.status(500).json({error:"Internal server error",message:e instanceof Error?e.message:"Unknown error",requestId:t})}}let g=(0,n.l)(s,"default"),m=(0,n.l)(s,"config"),_=new a.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/feedback",pathname:"/api/admin/feedback",bundlePath:"",filename:""},userland:s})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2805],()=>t(2752));module.exports=s})();