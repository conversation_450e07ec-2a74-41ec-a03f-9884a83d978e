#!/usr/bin/env node

/**
 * Create Placeholder Product Images
 * Generates simple placeholder images for Ocean Soul Sparkles products
 */

const fs = require('fs');
const path = require('path');

// Product image data based on the 404 errors seen in testing
const productImages = [
  'splitcake-aurora-pak.jpg',
  'splitcake-cosmic-pak.jpg',
  'face-paint-basic-set.jpg',
  'glitter-gel-rainbow.jpg',
  'hair-braiding-kit.jpg',
  'festival-face-paint.jpg',
  'body-art-glitter.jpg',
  'temporary-tattoos.jpg',
  'face-gems-set.jpg',
  'makeup-brushes-set.jpg'
];

// SVG template for placeholder images
const createPlaceholderSVG = (productName, color) => `
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:${color};stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:${color};stop-opacity:0.4" />
    </linearGradient>
  </defs>
  <rect width="800" height="600" fill="url(#grad)"/>
  <rect x="50" y="50" width="700" height="500" fill="none" stroke="white" stroke-width="2" stroke-dasharray="10,5" opacity="0.6"/>
  <text x="400" y="280" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">
    Ocean Soul Sparkles
  </text>
  <text x="400" y="320" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="white" opacity="0.9">
    ${productName.replace(/-/g, ' ').replace(/\.(jpg|png|webp)$/i, '')}
  </text>
  <text x="400" y="360" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white" opacity="0.7">
    Placeholder Image
  </text>
  <circle cx="400" cy="450" r="40" fill="white" opacity="0.3"/>
  <text x="400" y="460" font-family="Arial, sans-serif" font-size="36" text-anchor="middle" fill="white">
    ✨
  </text>
</svg>`;

// Color palette for different product types
const getProductColor = (filename) => {
  if (filename.includes('aurora')) return '#9333ea'; // Purple
  if (filename.includes('cosmic')) return '#1e40af'; // Blue
  if (filename.includes('rainbow') || filename.includes('glitter')) return '#dc2626'; // Red
  if (filename.includes('face-paint')) return '#059669'; // Green
  if (filename.includes('hair')) return '#d97706'; // Orange
  if (filename.includes('festival')) return '#7c2d12'; // Brown
  if (filename.includes('body-art')) return '#be185d'; // Pink
  if (filename.includes('tattoo')) return '#374151'; // Gray
  if (filename.includes('gems')) return '#0891b2'; // Cyan
  return '#6366f1'; // Default indigo
};

async function createPlaceholderImages() {
  console.log('🎨 Creating placeholder product images...\n');

  const publicDir = path.join(__dirname, '..', 'public');
  const imagesDir = path.join(publicDir, 'images');
  const productsDir = path.join(imagesDir, 'products');

  // Ensure directories exist
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
  }
  if (!fs.existsSync(productsDir)) {
    fs.mkdirSync(productsDir, { recursive: true });
  }

  // Create placeholder images
  for (const filename of productImages) {
    const color = getProductColor(filename);
    const svgContent = createPlaceholderSVG(filename, color);
    const svgPath = path.join(productsDir, filename.replace(/\.(jpg|png|webp)$/i, '.svg'));
    
    try {
      fs.writeFileSync(svgPath, svgContent.trim());
      console.log(`✅ Created: ${filename.replace(/\.(jpg|png|webp)$/i, '.svg')}`);
    } catch (error) {
      console.error(`❌ Failed to create ${filename}:`, error.message);
    }
  }

  // Create a README for the images directory
  const readmeContent = `# Product Images

This directory contains placeholder images for Ocean Soul Sparkles products.

## Generated Images:
${productImages.map(img => `- ${img.replace(/\.(jpg|png|webp)$/i, '.svg')} (placeholder)`).join('\n')}

## To Replace with Real Images:
1. Replace the .svg files with actual product photos
2. Use the same filenames but with .jpg, .png, or .webp extensions
3. Recommended dimensions: 800x600px or larger
4. Optimize images for web display

## Image Guidelines:
- High quality product photos
- Good lighting and clear details
- Consistent background (white or transparent preferred)
- Show the product clearly and attractively
`;

  fs.writeFileSync(path.join(productsDir, 'README.md'), readmeContent);

  console.log('\n📋 Created README.md with image guidelines');
  console.log('\n🎉 Placeholder images created successfully!');
  console.log('\n📝 Next Steps:');
  console.log('1. Replace SVG placeholders with actual product photos');
  console.log('2. Update database image_url fields to match actual filenames');
  console.log('3. Test image display in the admin dashboard');
}

if (require.main === module) {
  createPlaceholderImages().catch(console.error);
}

module.exports = { createPlaceholderImages };
