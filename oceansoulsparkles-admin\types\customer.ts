/**
 * Ocean Soul Sparkles Admin Dashboard - Customer Types
 * Customer management and relationship types
 */

import { 
  ID, 
  Timestamp, 
  Email, 
  PhoneNumber, 
  Status,
  Address,
  ContactInfo,
  NotificationPreferences,
  CustomField,
  MediaFile,
  Money
} from './common';

// Core customer interface
export interface Customer {
  id: ID;
  firstName: string;
  lastName: string;
  email: Email;
  phone?: PhoneNumber;
  mobile?: PhoneNumber;
  dateOfBirth?: string; // ISO date
  gender?: 'male' | 'female' | 'non-binary' | 'prefer-not-to-say';
  
  // Contact and address
  address?: Address;
  contactInfo: ContactInfo;
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: PhoneNumber;
  };
  
  // Profile information
  avatar?: MediaFile;
  bio?: string;
  notes?: string;
  tags?: string[];
  
  // Preferences
  preferredArtist?: ID;
  preferredServices?: ID[];
  notifications: NotificationPreferences;
  marketingConsent: boolean;
  
  // Business data
  customerType: 'individual' | 'corporate';
  loyaltyTier?: 'bronze' | 'silver' | 'gold' | 'platinum';
  referralSource?: string;
  referredBy?: ID;
  
  // Financial
  totalSpent: Money;
  averageBookingValue: Money;
  outstandingBalance: Money;
  creditLimit?: Money;
  paymentTerms?: string;
  
  // Status and metadata
  status: Status;
  isVip: boolean;
  isBlacklisted: boolean;
  blacklistReason?: string;
  lastVisit?: Timestamp;
  totalVisits: number;
  
  // Custom fields
  customFields?: CustomField[];
  
  // System fields
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy?: ID;
  updatedBy?: ID;
}

// Customer creation and update types
export interface CreateCustomerData {
  firstName: string;
  lastName: string;
  email: Email;
  phone?: PhoneNumber;
  mobile?: PhoneNumber;
  dateOfBirth?: string;
  gender?: Customer['gender'];
  address?: Address;
  contactInfo?: Partial<ContactInfo>;
  emergencyContact?: Customer['emergencyContact'];
  bio?: string;
  notes?: string;
  tags?: string[];
  preferredArtist?: ID;
  preferredServices?: ID[];
  notifications?: Partial<NotificationPreferences>;
  marketingConsent?: boolean;
  customerType?: Customer['customerType'];
  referralSource?: string;
  referredBy?: ID;
  customFields?: CustomField[];
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {
  id: ID;
  status?: Status;
  isVip?: boolean;
  isBlacklisted?: boolean;
  blacklistReason?: string;
}

// Customer search and filtering
export interface CustomerSearchParams {
  query?: string;
  status?: Status[];
  customerType?: Customer['customerType'][];
  loyaltyTier?: Customer['loyaltyTier'][];
  isVip?: boolean;
  isBlacklisted?: boolean;
  tags?: string[];
  preferredArtist?: ID[];
  dateOfBirthFrom?: string;
  dateOfBirthTo?: string;
  totalSpentMin?: number;
  totalSpentMax?: number;
  lastVisitFrom?: string;
  lastVisitTo?: string;
  createdFrom?: string;
  createdTo?: string;
  referralSource?: string[];
}

export interface CustomerFilters {
  search: string;
  status: Status | 'all';
  customerType: Customer['customerType'] | 'all';
  loyaltyTier: Customer['loyaltyTier'] | 'all';
  isVip: boolean | null;
  tags: string[];
  dateRange: {
    field: 'createdAt' | 'lastVisit' | 'dateOfBirth';
    start?: string;
    end?: string;
  } | null;
}

// Customer analytics and insights
export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomersThisMonth: number;
  activeCustomers: number; // Visited in last 30 days
  vipCustomers: number;
  
  // Demographics
  demographics: {
    ageGroups: {
      '18-25': number;
      '26-35': number;
      '36-45': number;
      '46-55': number;
      '56+': number;
    };
    gender: {
      male: number;
      female: number;
      'non-binary': number;
      'prefer-not-to-say': number;
    };
    customerTypes: {
      individual: number;
      corporate: number;
    };
  };
  
  // Loyalty distribution
  loyaltyDistribution: {
    bronze: number;
    silver: number;
    gold: number;
    platinum: number;
    unassigned: number;
  };
  
  // Financial metrics
  financialMetrics: {
    totalRevenue: Money;
    averageCustomerValue: Money;
    averageBookingValue: Money;
    topSpenders: Array<{
      customerId: ID;
      customerName: string;
      totalSpent: Money;
    }>;
  };
  
  // Engagement metrics
  engagement: {
    averageVisitsPerCustomer: number;
    customerRetentionRate: number; // Percentage
    churnRate: number; // Percentage
    referralRate: number; // Percentage
  };
  
  // Geographic distribution
  geographic?: {
    suburbs: Array<{
      name: string;
      count: number;
      percentage: number;
    }>;
    states: Array<{
      name: string;
      count: number;
      percentage: number;
    }>;
  };
}

// Customer communication
export interface CustomerCommunication {
  id: ID;
  customerId: ID;
  type: 'email' | 'sms' | 'phone' | 'in-person' | 'note';
  subject?: string;
  message: string;
  direction: 'inbound' | 'outbound';
  status: 'sent' | 'delivered' | 'read' | 'failed';
  sentBy: ID;
  sentAt: Timestamp;
  readAt?: Timestamp;
  attachments?: MediaFile[];
  relatedBookingId?: ID;
  tags?: string[];
}

// Customer loyalty and rewards
export interface LoyaltyProgram {
  id: ID;
  customerId: ID;
  tier: Customer['loyaltyTier'];
  points: number;
  pointsLifetime: number;
  nextTierPoints?: number;
  benefits: string[];
  expiryDate?: string;
  isActive: boolean;
}

export interface LoyaltyTransaction {
  id: ID;
  customerId: ID;
  type: 'earned' | 'redeemed' | 'expired' | 'adjusted';
  points: number;
  description: string;
  relatedBookingId?: ID;
  relatedOrderId?: ID;
  createdAt: Timestamp;
  expiresAt?: Timestamp;
}

// Customer preferences and history
export interface CustomerPreferences {
  customerId: ID;
  preferredArtists: ID[];
  preferredServices: ID[];
  preferredTimeSlots: Array<{
    dayOfWeek: number; // 0-6 (Sunday-Saturday)
    timeSlot: string; // HH:MM
  }>;
  allergies?: string[];
  skinSensitivities?: string[];
  specialRequirements?: string[];
  communicationPreferences: {
    language: string;
    timezone: string;
    reminderTiming: number; // Hours before appointment
    marketingFrequency: 'daily' | 'weekly' | 'monthly' | 'never';
  };
}

export interface CustomerHistory {
  customerId: ID;
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  noShowBookings: number;
  totalSpent: Money;
  averageBookingValue: Money;
  favoriteServices: Array<{
    serviceId: ID;
    serviceName: string;
    bookingCount: number;
  }>;
  favoriteArtists: Array<{
    artistId: ID;
    artistName: string;
    bookingCount: number;
  }>;
  lastBooking?: {
    id: ID;
    date: string;
    serviceName: string;
    artistName: string;
    amount: Money;
  };
  upcomingBookings: Array<{
    id: ID;
    date: string;
    time: string;
    serviceName: string;
    artistName: string;
  }>;
}

// Customer import/export
export interface CustomerImportData {
  firstName: string;
  lastName: string;
  email: Email;
  phone?: PhoneNumber;
  mobile?: PhoneNumber;
  dateOfBirth?: string;
  gender?: string;
  address?: string; // Formatted address string
  notes?: string;
  tags?: string; // Comma-separated
  referralSource?: string;
  marketingConsent?: boolean;
}

export interface CustomerExportData extends Customer {
  // Flattened for CSV export
  fullName: string;
  addressFormatted?: string;
  totalBookings: number;
  lastBookingDate?: string;
  loyaltyPoints?: number;
  tagsFormatted?: string; // Comma-separated
}

// Customer validation
export interface CustomerValidationRules {
  firstName: {
    required: true;
    minLength: 1;
    maxLength: 50;
  };
  lastName: {
    required: true;
    minLength: 1;
    maxLength: 50;
  };
  email: {
    required: true;
    format: 'email';
    unique: true;
  };
  phone: {
    format: 'phone';
    unique: false;
  };
  dateOfBirth: {
    format: 'date';
    maxAge: 120;
    minAge: 13;
  };
}

// Customer relationship types
export interface CustomerRelationship {
  id: ID;
  primaryCustomerId: ID;
  relatedCustomerId: ID;
  relationship: 'family' | 'friend' | 'colleague' | 'referral' | 'emergency_contact';
  notes?: string;
  isActive: boolean;
  createdAt: Timestamp;
}
