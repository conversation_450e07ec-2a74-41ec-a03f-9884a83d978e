/**
 * Ocean Soul Sparkles Admin - Global Search Component
 * Provides search functionality across customers, bookings, and services
 */

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '../../styles/admin/GlobalSearch.module.css';

interface SearchResult {
  id: string;
  type: 'customer' | 'booking' | 'service';
  title: string;
  subtitle: string;
  description?: string;
  url: string;
  metadata?: Record<string, any>;
}

interface SearchResponse {
  query: string;
  results: SearchResult[];
  total: number;
  types: {
    customer: number;
    booking: number;
    service: number;
  };
}

interface GlobalSearchProps {
  placeholder?: string;
  className?: string;
}

export default function GlobalSearch({ 
  placeholder = "Search customers, bookings, services...",
  className = ""
}: GlobalSearchProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Debounced search
  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      setShowResults(false);
      return;
    }

    const timeoutId = setTimeout(() => {
      performSearch(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query]);

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!showResults || results.length === 0) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setSelectedIndex(prev => 
            prev < results.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
          break;
        case 'Enter':
          event.preventDefault();
          if (selectedIndex >= 0 && results[selectedIndex]) {
            handleResultClick(results[selectedIndex]);
          }
          break;
        case 'Escape':
          setShowResults(false);
          setSelectedIndex(-1);
          inputRef.current?.blur();
          break;
      }
    };

    if (showResults) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [showResults, results, selectedIndex]);

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/search?q=${encodeURIComponent(searchQuery)}&limit=10`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Search failed');
      }

      const data: SearchResponse = await response.json();
      setResults(data.results);
      setShowResults(true);
      setSelectedIndex(-1);

    } catch (error) {
      console.error('Search error:', error);
      setError('Search failed. Please try again.');
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handleResultClick = (result: SearchResult) => {
    setShowResults(false);
    setQuery('');
    setSelectedIndex(-1);
    router.push(result.url);
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'customer': return '👤';
      case 'booking': return '📅';
      case 'service': return '✨';
      default: return '📄';
    }
  };

  const getResultTypeLabel = (type: string) => {
    switch (type) {
      case 'customer': return 'Customer';
      case 'booking': return 'Booking';
      case 'service': return 'Service';
      default: return 'Result';
    }
  };

  return (
    <div className={`${styles.globalSearch} ${className}`} ref={searchRef}>
      <div className={styles.searchInput}>
        <div className={styles.searchIcon}>🔍</div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => {
            if (results.length > 0) {
              setShowResults(true);
            }
          }}
          placeholder={placeholder}
          className={styles.input}
          autoComplete="off"
          data-global-search="true"
        />
        {loading && (
          <div className={styles.loadingSpinner}>
            <div className={styles.spinner}></div>
          </div>
        )}
        {query && (
          <button
            className={styles.clearButton}
            onClick={() => {
              setQuery('');
              setResults([]);
              setShowResults(false);
              inputRef.current?.focus();
            }}
            title="Clear search"
          >
            ✕
          </button>
        )}
      </div>

      {showResults && (
        <div className={styles.searchResults}>
          {error ? (
            <div className={styles.errorMessage}>
              <span className={styles.errorIcon}>⚠️</span>
              {error}
            </div>
          ) : results.length > 0 ? (
            <>
              <div className={styles.resultsHeader}>
                <span className={styles.resultsCount}>
                  {results.length} result{results.length !== 1 ? 's' : ''} for "{query}"
                </span>
              </div>
              <div className={styles.resultsList}>
                {results.map((result, index) => (
                  <div
                    key={`${result.type}-${result.id}`}
                    className={`${styles.resultItem} ${
                      index === selectedIndex ? styles.selected : ''
                    }`}
                    onClick={() => handleResultClick(result)}
                    onMouseEnter={() => setSelectedIndex(index)}
                  >
                    <div className={styles.resultIcon}>
                      {getResultIcon(result.type)}
                    </div>
                    <div className={styles.resultContent}>
                      <div className={styles.resultTitle}>{result.title}</div>
                      <div className={styles.resultSubtitle}>{result.subtitle}</div>
                      {result.description && (
                        <div className={styles.resultDescription}>{result.description}</div>
                      )}
                    </div>
                    <div className={styles.resultType}>
                      {getResultTypeLabel(result.type)}
                    </div>
                  </div>
                ))}
              </div>
              {results.length >= 10 && (
                <div className={styles.resultsFooter}>
                  <span className={styles.moreResults}>
                    Showing first 10 results. Refine your search for more specific results.
                  </span>
                </div>
              )}
            </>
          ) : query.trim() && !loading ? (
            <div className={styles.noResults}>
              <span className={styles.noResultsIcon}>🔍</span>
              <div className={styles.noResultsText}>
                No results found for "{query}"
              </div>
              <div className={styles.noResultsHint}>
                Try searching for customer names, booking dates, or service names
              </div>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
