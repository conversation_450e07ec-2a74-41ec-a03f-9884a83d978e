import type { NextApiRequest, NextApiResponse } from 'next';
import { customerLogin } from '../../../../lib/auth/customer-auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    // Get client IP for audit logging
    const clientIP = req.headers['x-forwarded-for'] as string || 
                    req.headers['x-real-ip'] as string || 
                    req.connection.remoteAddress || 
                    'unknown';

    // Attempt login
    const loginResult = await customerLogin(email, password, clientIP);

    if (!loginResult.success) {
      return res.status(401).json({ 
        error: loginResult.error,
        requiresEmailVerification: loginResult.requiresEmailVerification
      });
    }

    return res.status(200).json({
      success: true,
      token: loginResult.token,
      user: loginResult.user
    });

  } catch (error) {
    console.error('Customer login API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
