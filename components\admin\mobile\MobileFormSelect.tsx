/**
 * Ocean Soul Sparkles - Mobile Form Select Component
 * Touch-optimized dropdown with native mobile behavior
 */

import React, { useState, useRef, useEffect } from 'react';
import styles from '../../../styles/admin/mobile/MobileFormSelect.module.css';

interface Option {
  value: string;
  label: string;
  icon?: string;
  disabled?: boolean;
}

interface MobileFormSelectProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  hint?: string;
  icon?: string;
  searchable?: boolean;
  multiple?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
}

export default function MobileFormSelect({
  label,
  value,
  onChange,
  options,
  placeholder = 'Select an option',
  required = false,
  disabled = false,
  error,
  hint,
  icon,
  searchable = false,
  multiple = false,
  onFocus,
  onBlur,
  className = ''
}: MobileFormSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const selectedOption = options.find(option => option.value === value);
  const hasValue = value.length > 0;

  // Filter options based on search term
  const filteredOptions = searchable
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setIsFocused(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  const handleToggle = () => {
    if (disabled) return;
    
    setIsOpen(!isOpen);
    setIsFocused(!isOpen);
    
    if (!isOpen) {
      onFocus?.();
    } else {
      onBlur?.();
      setSearchTerm('');
    }
  };

  const handleOptionSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setIsFocused(false);
    setSearchTerm('');
    onBlur?.();
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleLabelClick = () => {
    if (!disabled) {
      handleToggle();
    }
  };

  return (
    <div className={`${styles.mobileSelect} ${className} ${error ? styles.error : ''} ${disabled ? styles.disabled : ''}`} ref={selectRef}>
      {/* Select Trigger */}
      <div className={styles.selectContainer}>
        <div
          className={`${styles.selectTrigger} ${isOpen ? styles.open : ''} ${isFocused || hasValue ? styles.hasContent : ''}`}
          onClick={handleToggle}
        >
          <div className={styles.selectValue}>
            {selectedOption ? (
              <span className={styles.selectedOption}>
                {selectedOption.icon && <span className={styles.optionIcon}>{selectedOption.icon}</span>}
                {selectedOption.label}
              </span>
            ) : (
              <span className={styles.placeholder}>{placeholder}</span>
            )}
          </div>
          
          <div className={styles.selectArrow}>
            <span className={`${styles.arrow} ${isOpen ? styles.rotated : ''}`}>▼</span>
          </div>
        </div>

        <label
          onClick={handleLabelClick}
          className={`${styles.label} ${isFocused || hasValue ? styles.floating : ''} ${required ? styles.required : ''}`}
        >
          {icon && <span className={styles.labelIcon}>{icon}</span>}
          {label}
        </label>

        {/* Select Border Animation */}
        <div className={`${styles.selectBorder} ${isFocused ? styles.focused : ''}`}></div>
      </div>

      {/* Dropdown Options */}
      {isOpen && (
        <div className={styles.dropdown}>
          <div className={styles.dropdownContent}>
            {/* Search Input */}
            {searchable && (
              <div className={styles.searchContainer}>
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search options..."
                  className={styles.searchInput}
                />
                <span className={styles.searchIcon}>🔍</span>
              </div>
            )}

            {/* Options List */}
            <div className={styles.optionsList}>
              {filteredOptions.length === 0 ? (
                <div className={styles.noOptions}>
                  <span className={styles.noOptionsIcon}>📭</span>
                  <span>No options found</span>
                </div>
              ) : (
                filteredOptions.map((option) => (
                  <div
                    key={option.value}
                    className={`${styles.option} ${
                      option.value === value ? styles.selected : ''
                    } ${option.disabled ? styles.optionDisabled : ''}`}
                    onClick={() => !option.disabled && handleOptionSelect(option.value)}
                  >
                    {option.icon && <span className={styles.optionIcon}>{option.icon}</span>}
                    <span className={styles.optionLabel}>{option.label}</span>
                    {option.value === value && (
                      <span className={styles.selectedIndicator}>✓</span>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      )}

      {/* Helper Text */}
      {(hint || error) && (
        <div className={styles.helperText}>
          {error ? (
            <span className={styles.errorText}>
              <span className={styles.errorIcon}>⚠️</span>
              {error}
            </span>
          ) : (
            <span className={styles.hintText}>{hint}</span>
          )}
        </div>
      )}
    </div>
  );
}
