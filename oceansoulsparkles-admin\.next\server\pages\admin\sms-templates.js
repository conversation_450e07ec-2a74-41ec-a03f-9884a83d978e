(()=>{var e={};e.id=3465,e.ids=[3465,660],e.modules={2961:e=>{e.exports={container:"SMSTemplates_container__RqHVL",loading:"SMSTemplates_loading__DvT7h",header:"SMSTemplates_header___mL4G",title:"SMSTemplates_title__TBheM",headerActions:"SMSTemplates_headerActions__ypR7Z",testBtn:"SMSTemplates_testBtn__Fs0uw",createBtn:"SMSTemplates_createBtn__x0eLn",content:"SMSTemplates_content__BqgY0",templateList:"SMSTemplates_templateList__nGa4j",categoryFilter:"SMSTemplates_categoryFilter__cwAwR",categoryBtn:"SMSTemplates_categoryBtn__jB8b_",active:"SMSTemplates_active__5EmVV",templates:"SMSTemplates_templates__4ws1O",templateItem:"SMSTemplates_templateItem__3jU94",selected:"SMSTemplates_selected__eFdVH",templateHeader:"SMSTemplates_templateHeader__N3HIU",category:"SMSTemplates_category__iy5s_",booking:"SMSTemplates_booking__zZ_fi",payment:"SMSTemplates_payment__PfqxT",staff:"SMSTemplates_staff__Bpsaw",customer:"SMSTemplates_customer__ajPG7",marketing:"SMSTemplates_marketing__59cBO",admin:"SMSTemplates_admin__cdUcA",custom:"SMSTemplates_custom__Uh0OO",templateDescription:"SMSTemplates_templateDescription__Xb_ZI",templatePreview:"SMSTemplates_templatePreview__9rcRV",templateMeta:"SMSTemplates_templateMeta__H2SNI",status:"SMSTemplates_status__vAVZH",inactive:"SMSTemplates_inactive__wzhvf",defaultBadge:"SMSTemplates_defaultBadge__3ny8W",templateEditor:"SMSTemplates_templateEditor__uriyP",editorHeader:"SMSTemplates_editorHeader__aF3L_",editorActions:"SMSTemplates_editorActions__RZfiU",editBtn:"SMSTemplates_editBtn__xt86p",deleteBtn:"SMSTemplates_deleteBtn__BTn4f",cancelBtn:"SMSTemplates_cancelBtn__23gtp",saveBtn:"SMSTemplates_saveBtn__H0ZX1",noSelection:"SMSTemplates_noSelection__zozUA",editForm:"SMSTemplates_editForm__dZOTt",formGroup:"SMSTemplates_formGroup__tu_dn",templateView:"SMSTemplates_templateView__4UINh",templateDetails:"SMSTemplates_templateDetails__ITZmN",templateContent:"SMSTemplates_templateContent__WiYKE",templateText:"SMSTemplates_templateText__ubN_Y",variablesList:"SMSTemplates_variablesList__utF6k",variables:"SMSTemplates_variables__hP_Xy",variable:"SMSTemplates_variable__TbftC",preview:"SMSTemplates_preview__bcn7T",previewContent:"SMSTemplates_previewContent__Hx2gd",previewControls:"SMSTemplates_previewControls__NoN1z",previewInput:"SMSTemplates_previewInput__gjtwg"}},6692:e=>{e.exports={overlay:"SMSTestPanel_overlay__1ypWr",panel:"SMSTestPanel_panel__gOHpq",header:"SMSTestPanel_header__HD5Xs",closeBtn:"SMSTestPanel_closeBtn__pMZ0Y",content:"SMSTestPanel_content__wmIgZ",section:"SMSTestPanel_section__HHCDa",formGroup:"SMSTestPanel_formGroup__uJo5d",statusBtn:"SMSTestPanel_statusBtn__ilI9o",statusInfo:"SMSTestPanel_statusInfo__73ZdU",error:"SMSTestPanel_error__Y9Ot8",sendBtn:"SMSTestPanel_sendBtn__EGI1o",templateTests:"SMSTestPanel_templateTests__kIB_5",templateBtn:"SMSTestPanel_templateBtn___dURp",result:"SMSTestPanel_result__NR7_z",success:"SMSTestPanel_success__ghRvt",instructions:"SMSTestPanel_instructions__fXtHh"}},5373:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>u,default:()=>p,getServerSideProps:()=>_,getStaticPaths:()=>h,getStaticProps:()=>S,reportWebVitals:()=>x,routeModule:()=>f,unstable_getServerProps:()=>M,unstable_getServerSideProps:()=>T,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>v});var l=s(7093),i=s(5244),r=s(1323),n=s(2899),c=s.n(n),o=s(6814),d=s(6448),m=e([o,d]);[o,d]=m.then?(await m)():m;let p=(0,r.l)(d,"default"),S=(0,r.l)(d,"getStaticProps"),h=(0,r.l)(d,"getStaticPaths"),_=(0,r.l)(d,"getServerSideProps"),u=(0,r.l)(d,"config"),x=(0,r.l)(d,"reportWebVitals"),v=(0,r.l)(d,"unstable_getStaticProps"),g=(0,r.l)(d,"unstable_getStaticPaths"),j=(0,r.l)(d,"unstable_getStaticParams"),M=(0,r.l)(d,"unstable_getServerProps"),T=(0,r.l)(d,"unstable_getServerSideProps"),f=new l.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/admin/sms-templates",pathname:"/admin/sms-templates",bundlePath:"",filename:""},components:{App:o.default,Document:c()},userland:d});a()}catch(e){a(e)}})},6164:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var a=s(997),l=s(6689),i=s(6692),r=s.n(i);function n({onClose:e}){let[t,s]=(0,l.useState)({type:"test_sms",to:"",message:"Test message from Ocean Soul Sparkles admin dashboard. This is a test to verify SMS functionality is working correctly."}),[i,n]=(0,l.useState)(!1),[c,o]=(0,l.useState)(null),[d,m]=(0,l.useState)(null),p=async()=>{try{n(!0),o(null);let e=await fetch("/api/admin/notifications/sms",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),s=await e.json();o(s),s.success?alert("Test SMS sent successfully!"):alert(`SMS test failed: ${s.message}`)}catch(e){console.error("Error sending test SMS:",e),alert("Error sending test SMS. Please try again.")}finally{n(!1)}},S=async()=>{try{let e=await fetch("/api/admin/notifications/sms"),t=await e.json();m(t)}catch(e){console.error("Error checking SMS status:",e)}},h=[{type:"booking_confirmation",name:"Booking Confirmation",data:{customerName:"Sarah Johnson",serviceName:"Hair Braiding",date:"March 15, 2025",time:"2:30 PM",customerPhone:t.to}},{type:"booking_reminder",name:"Booking Reminder",data:{customerName:"Sarah Johnson",serviceName:"Hair Braiding",time:"2:30 PM",customerPhone:t.to}},{type:"booking_cancellation",name:"Booking Cancellation",data:{customerName:"Sarah Johnson",serviceName:"Hair Braiding",date:"March 15, 2025",time:"2:30 PM",customerPhone:t.to}}],_=async e=>{if(!t.to){alert("Please enter a phone number first");return}try{n(!0),o(null);let t=await fetch("/api/admin/notifications/sms",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:e.type,data:e.data})}),s=await t.json();o(s),s.success?alert(`${e.name} SMS sent successfully!`):alert(`${e.name} SMS failed: ${s.message}`)}catch(e){console.error("Error sending template SMS:",e),alert("Error sending template SMS. Please try again.")}finally{n(!1)}};return a.jsx("div",{className:r().overlay,children:(0,a.jsxs)("div",{className:r().panel,children:[(0,a.jsxs)("div",{className:r().header,children:[a.jsx("h2",{children:"SMS Test Panel"}),a.jsx("button",{onClick:e,className:r().closeBtn,children:"\xd7"})]}),(0,a.jsxs)("div",{className:r().content,children:[(0,a.jsxs)("div",{className:r().section,children:[a.jsx("h3",{children:"SMS Service Status"}),a.jsx("button",{onClick:S,className:r().statusBtn,children:"Check Status"}),d&&(0,a.jsxs)("div",{className:r().statusInfo,children:[(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Configured:"})," ",d.status.configured?"Yes":"No"]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Provider:"})," ",d.status.provider]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Environment:"})," ",d.status.environment]}),d.verification&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Verification:"})," ",d.verification.configured?"Valid":"Invalid"]}),d.verification.error&&(0,a.jsxs)("p",{className:r().error,children:[a.jsx("strong",{children:"Error:"})," ",d.verification.error]})]})]})]}),(0,a.jsxs)("div",{className:r().section,children:[a.jsx("h3",{children:"Custom SMS Test"}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Phone Number:"}),a.jsx("input",{type:"tel",value:t.to,onChange:e=>s(t=>({...t,to:e.target.value})),placeholder:"+61412345678"}),a.jsx("small",{children:"Enter phone number in international format"})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Message:"}),a.jsx("textarea",{value:t.message,onChange:e=>s(t=>({...t,message:e.target.value})),rows:3,placeholder:"Enter test message"})]}),a.jsx("button",{onClick:p,disabled:i||!t.to||!t.message,className:r().sendBtn,children:i?"Sending...":"Send Test SMS"})]}),(0,a.jsxs)("div",{className:r().section,children:[a.jsx("h3",{children:"Template Tests"}),a.jsx("p",{children:"Test SMS templates with sample data:"}),a.jsx("div",{className:r().templateTests,children:h.map(e=>(0,a.jsxs)("button",{onClick:()=>_(e),disabled:i||!t.to,className:r().templateBtn,children:["Test ",e.name]},e.type))})]}),c&&(0,a.jsxs)("div",{className:r().section,children:[a.jsx("h3",{children:"Last Result"}),(0,a.jsxs)("div",{className:`${r().result} ${c.success?r().success:r().error}`,children:[(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Status:"})," ",c.success?"Success":"Failed"]}),c.messageId&&(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Message ID:"})," ",c.messageId]}),c.message&&(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Message:"})," ",c.message]}),c.skipped&&(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Skipped:"})," ",c.reason]}),c.fallback&&(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Fallback:"})," Used console logging"]}),c.error&&(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Error:"})," ",c.error]})]})]}),(0,a.jsxs)("div",{className:r().section,children:[a.jsx("h3",{children:"Instructions"}),(0,a.jsxs)("ul",{className:r().instructions,children:[a.jsx("li",{children:"Enter a valid phone number in international format (e.g., +61412345678)"}),a.jsx("li",{children:"If Twilio is not configured, messages will be logged to console"}),a.jsx("li",{children:"Check SMS settings in Admin Settings to enable/disable SMS types"}),a.jsx("li",{children:"Template tests use sample data - actual bookings would use real data"}),a.jsx("li",{children:"Monitor the browser console for detailed SMS service logs"})]})]})]})]})})}},6448:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>p});var l=s(997),i=s(6689),r=s(8568),n=s(4845),c=s(6164),o=s(2961),d=s.n(o),m=e([n]);function p(){let{user:e,loading:t}=(0,r.a)(),[s,a]=(0,i.useState)(!0),[o,m]=(0,i.useState)([]),[p,S]=(0,i.useState)(null),[h,_]=(0,i.useState)(!1),[u,x]=(0,i.useState)(!1),[v,g]=(0,i.useState)(!1),[j,M]=(0,i.useState)({}),[T,f]=(0,i.useState)("all"),[b,y]=(0,i.useState)(!1),[N,P]=(0,i.useState)({type:"",name:"",description:"",template:"",variables:[],category:"custom",is_active:!0}),w=async()=>{try{a(!0);let e=await fetch("/api/admin/sms-templates"),t=await e.json();e.ok?m(t.templates||[]):console.error("Failed to fetch SMS templates:",t.message)}catch(e){console.error("Error fetching SMS templates:",e)}finally{a(!1)}},k=e=>{S(e),_(!1),x(!1);let t={};e.variables?.forEach(e=>{switch(e){case"customerName":case"name":t[e]="Sarah Johnson";break;case"serviceName":t[e]="Hair Braiding";break;case"date":t[e]="March 15, 2025";break;case"time":t[e]="2:30 PM";break;case"amount":t[e]="150.00";break;case"receiptNumber":t[e]="OSS-2025-001";break;default:t[e]=`[${e}]`}}),M(t)},B=async()=>{try{g(!0);let e=u?N:{...N,id:p?.id},t=await fetch("/api/admin/sms-templates",{method:u?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=await t.json();t.ok?(await w(),_(!1),x(!1),s.template&&S(s.template),alert(`SMS template ${u?"created":"updated"} successfully!`)):alert(`Failed to ${u?"create":"update"} SMS template: ${s.message}`)}catch(e){console.error("Error saving SMS template:",e),alert("Error saving SMS template. Please try again.")}finally{g(!1)}},C=async()=>{if(p&&!p.is_default&&confirm("Are you sure you want to delete this SMS template?"))try{let e=await fetch(`/api/admin/sms-templates?id=${p.id}`,{method:"DELETE"});if(e.ok)await w(),S(null),alert("SMS template deleted successfully!");else{let t=await e.json();alert(`Failed to delete SMS template: ${t.message}`)}}catch(e){console.error("Error deleting SMS template:",e),alert("Error deleting SMS template. Please try again.")}},E=e=>{let t=e.match(/{{(\w+)}}/g);if(!t)return[];let s=t.map(e=>e.replace(/[{}]/g,""));return Array.from(new Set(s))},I=e=>{let t=E(e);P(s=>({...s,template:e,variables:t}))},H="all"===T?o:o.filter(e=>e.category===T);return t||s?l.jsx(n.Z,{children:l.jsx("div",{className:d().loading,children:"Loading SMS templates..."})}):l.jsx(n.Z,{children:(0,l.jsxs)("div",{className:d().container,children:[(0,l.jsxs)("header",{className:d().header,children:[l.jsx("h1",{className:d().title,children:"SMS Templates"}),(0,l.jsxs)("div",{className:d().headerActions,children:[l.jsx("button",{onClick:()=>y(!0),className:d().testBtn,children:"Test SMS"}),l.jsx("button",{onClick:()=>{P({type:"",name:"",description:"",template:"",variables:[],category:"custom",is_active:!0}),x(!0),_(!1),S(null)},className:d().createBtn,children:"Create Template"})]})]}),(0,l.jsxs)("div",{className:d().content,children:[(0,l.jsxs)("div",{className:d().templateList,children:[l.jsx("div",{className:d().categoryFilter,children:["all","booking","payment","staff","customer","marketing","admin","custom"].map(e=>l.jsx("button",{className:`${d().categoryBtn} ${T===e?d().active:""}`,onClick:()=>f(e),children:e.charAt(0).toUpperCase()+e.slice(1)},e))}),l.jsx("div",{className:d().templates,children:H.map(e=>(0,l.jsxs)("div",{className:`${d().templateItem} ${p?.id===e.id?d().selected:""}`,onClick:()=>k(e),children:[(0,l.jsxs)("div",{className:d().templateHeader,children:[l.jsx("h3",{children:e.name}),l.jsx("span",{className:`${d().category} ${d()[e.category]}`,children:e.category})]}),l.jsx("p",{className:d().templateDescription,children:e.description}),(0,l.jsxs)("div",{className:d().templatePreview,children:[e.template.substring(0,100),"..."]}),(0,l.jsxs)("div",{className:d().templateMeta,children:[l.jsx("span",{className:`${d().status} ${e.is_active?d().active:d().inactive}`,children:e.is_active?"Active":"Inactive"}),e.is_default&&l.jsx("span",{className:d().defaultBadge,children:"Default"})]})]},e.id))})]}),l.jsx("div",{className:d().templateEditor,children:p||h||u?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:d().editorHeader,children:[l.jsx("h2",{children:u?"Create SMS Template":h?"Edit SMS Template":p?.name}),(0,l.jsxs)("div",{className:d().editorActions,children:[!h&&!u&&(0,l.jsxs)(l.Fragment,{children:[l.jsx("button",{onClick:()=>{p&&(P({type:p.type,name:p.name,description:p.description,template:p.template,variables:p.variables||[],category:p.category,is_active:p.is_active}),_(!0),x(!1))},className:d().editBtn,disabled:p?.is_default,children:"Edit"}),l.jsx("button",{onClick:C,className:d().deleteBtn,disabled:p?.is_default,children:"Delete"})]}),(h||u)&&(0,l.jsxs)(l.Fragment,{children:[l.jsx("button",{onClick:()=>{_(!1),x(!1)},className:d().cancelBtn,children:"Cancel"}),l.jsx("button",{onClick:B,className:d().saveBtn,disabled:v,children:v?"Saving...":"Save"})]})]})]}),h||u?(0,l.jsxs)("div",{className:d().editForm,children:[(0,l.jsxs)("div",{className:d().formGroup,children:[l.jsx("label",{children:"Template Type"}),l.jsx("input",{type:"text",value:N.type,onChange:e=>P(t=>({...t,type:e.target.value})),placeholder:"e.g., booking_confirmation"})]}),(0,l.jsxs)("div",{className:d().formGroup,children:[l.jsx("label",{children:"Name"}),l.jsx("input",{type:"text",value:N.name,onChange:e=>P(t=>({...t,name:e.target.value})),placeholder:"Template name"})]}),(0,l.jsxs)("div",{className:d().formGroup,children:[l.jsx("label",{children:"Description"}),l.jsx("input",{type:"text",value:N.description,onChange:e=>P(t=>({...t,description:e.target.value})),placeholder:"Template description"})]}),(0,l.jsxs)("div",{className:d().formGroup,children:[l.jsx("label",{children:"Category"}),(0,l.jsxs)("select",{value:N.category,onChange:e=>P(t=>({...t,category:e.target.value})),children:[l.jsx("option",{value:"booking",children:"Booking"}),l.jsx("option",{value:"payment",children:"Payment"}),l.jsx("option",{value:"staff",children:"Staff"}),l.jsx("option",{value:"customer",children:"Customer"}),l.jsx("option",{value:"marketing",children:"Marketing"}),l.jsx("option",{value:"admin",children:"Admin"}),l.jsx("option",{value:"custom",children:"Custom"})]})]}),(0,l.jsxs)("div",{className:d().formGroup,children:[l.jsx("label",{children:"Template Message"}),l.jsx("textarea",{value:N.template,onChange:e=>I(e.target.value),placeholder:"SMS message template with {{variables}}",rows:4}),(0,l.jsxs)("small",{children:["Use ","{{"," variableName ","}}","  for dynamic content"]})]}),l.jsx("div",{className:d().formGroup,children:(0,l.jsxs)("label",{children:[l.jsx("input",{type:"checkbox",checked:N.is_active,onChange:e=>P(t=>({...t,is_active:e.target.checked}))}),"Active"]})}),N.variables.length>0&&(0,l.jsxs)("div",{className:d().variablesList,children:[l.jsx("h4",{children:"Variables:"}),l.jsx("div",{className:d().variables,children:N.variables.map(e=>l.jsx("span",{className:d().variable,children:e},e))})]})]}):(0,l.jsxs)("div",{className:d().templateView,children:[(0,l.jsxs)("div",{className:d().templateDetails,children:[(0,l.jsxs)("p",{children:[l.jsx("strong",{children:"Type:"})," ",p?.type]}),(0,l.jsxs)("p",{children:[l.jsx("strong",{children:"Category:"})," ",p?.category]}),(0,l.jsxs)("p",{children:[l.jsx("strong",{children:"Description:"})," ",p?.description]})]}),(0,l.jsxs)("div",{className:d().templateContent,children:[l.jsx("h4",{children:"Template:"}),l.jsx("div",{className:d().templateText,children:p?.template})]}),p?.variables&&p.variables.length>0&&(0,l.jsxs)("div",{className:d().variablesList,children:[l.jsx("h4",{children:"Variables:"}),l.jsx("div",{className:d().variables,children:p.variables.map(e=>l.jsx("span",{className:d().variable,children:e},e))})]})]}),(0,l.jsxs)("div",{className:d().preview,children:[l.jsx("h4",{children:"Preview:"}),l.jsx("div",{className:d().previewContent,children:(()=>{if(!p&&!h&&!u)return"";let e=h||u?N.template:p?.template||"";return Object.keys(j).forEach(t=>{let s=RegExp(`{{${t}}}`,"g");e=e.replace(s,j[t])}),e})()}),(0,l.jsxs)("div",{className:d().previewControls,children:[l.jsx("h5",{children:"Preview Data:"}),(p?.variables||N.variables).map(e=>(0,l.jsxs)("div",{className:d().previewInput,children:[(0,l.jsxs)("label",{children:[e,":"]}),l.jsx("input",{type:"text",value:j[e]||"",onChange:t=>M(s=>({...s,[e]:t.target.value})),placeholder:`Enter ${e}`})]},e))]})]})]}):l.jsx("div",{className:d().noSelection,children:l.jsx("p",{children:"Select a template to view or edit, or create a new one."})})})]}),b&&l.jsx(c.Z,{onClose:()=>y(!1)})]})})}n=(m.then?(await m)():m)[0],a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2899,6212,1664,7441],()=>s(5373));module.exports=a})();