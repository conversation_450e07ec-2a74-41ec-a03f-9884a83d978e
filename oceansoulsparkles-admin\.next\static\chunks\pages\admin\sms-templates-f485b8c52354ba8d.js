(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[465],{4411:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/sms-templates",function(){return r(4357)}])},4357:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return s}});var n=r(6026),o=r(99),a=r(7253),c=r.n(a);function i(e){let{onClose:t}=e,[r,n]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({type:"test_sms",to:"",message:"Test message from Ocean Soul Sparkles admin dashboard. This is a test to verify SMS functionality is working correctly."}),[o,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[i,d]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[l,s]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),O=async()=>{try{a(!0),d(null);let e=await fetch("/api/admin/notifications/sms",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),t=await e.json();d(t),t.success?alert("Test SMS sent successfully!"):alert("SMS test failed: ".concat(t.message))}catch(e){console.error("Error sending test SMS:",e),alert("Error sending test SMS. Please try again.")}finally{a(!1)}},u=async()=>{try{let e=await fetch("/api/admin/notifications/sms"),t=await e.json();s(t)}catch(e){console.error("Error checking SMS status:",e)}},m=[{type:"booking_confirmation",name:"Booking Confirmation",data:{customerName:"Sarah Johnson",serviceName:"Hair Braiding",date:"March 15, 2025",time:"2:30 PM",customerPhone:r.to}},{type:"booking_reminder",name:"Booking Reminder",data:{customerName:"Sarah Johnson",serviceName:"Hair Braiding",time:"2:30 PM",customerPhone:r.to}},{type:"booking_cancellation",name:"Booking Cancellation",data:{customerName:"Sarah Johnson",serviceName:"Hair Braiding",date:"March 15, 2025",time:"2:30 PM",customerPhone:r.to}}],_=async e=>{if(!r.to){alert("Please enter a phone number first");return}try{a(!0),d(null);let t=await fetch("/api/admin/notifications/sms",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:e.type,data:e.data})}),r=await t.json();d(r),r.success?alert("".concat(e.name," SMS sent successfully!")):alert("".concat(e.name," SMS failed: ").concat(r.message))}catch(e){console.error("Error sending template SMS:",e),alert("Error sending template SMS. Please try again.")}finally{a(!1)}};return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().overlay,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().panel,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().header,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{children:"SMS Test Panel"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:t,className:c().closeBtn,children:"\xd7"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().content,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().section,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"SMS Service Status"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:u,className:c().statusBtn,children:"Check Status"}),l&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().statusInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Configured:"})," ",l.status.configured?"Yes":"No"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Provider:"})," ",l.status.provider]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Environment:"})," ",l.status.environment]}),l.verification&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Verification:"})," ",l.verification.configured?"Valid":"Invalid"]}),l.verification.error&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:c().error,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Error:"})," ",l.verification.error]})]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().section,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Custom SMS Test"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Phone Number:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"tel",value:r.to,onChange:e=>n(t=>({...t,to:e.target.value})),placeholder:"+61412345678"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("small",{children:"Enter phone number in international format"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Message:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("textarea",{value:r.message,onChange:e=>n(t=>({...t,message:e.target.value})),rows:3,placeholder:"Enter test message"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:O,disabled:o||!r.to||!r.message,className:c().sendBtn,children:o?"Sending...":"Send Test SMS"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().section,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Template Tests"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Test SMS templates with sample data:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().templateTests,children:m.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>_(e),disabled:o||!r.to,className:c().templateBtn,children:["Test ",e.name]},e.type))})]}),i&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().section,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Last Result"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"".concat(c().result," ").concat(i.success?c().success:c().error),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Status:"})," ",i.success?"Success":"Failed"]}),i.messageId&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Message ID:"})," ",i.messageId]}),i.message&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Message:"})," ",i.message]}),i.skipped&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Skipped:"})," ",i.reason]}),i.fallback&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Fallback:"})," Used console logging"]}),i.error&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Error:"})," ",i.error]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:c().section,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Instructions"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("ul",{className:c().instructions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{children:"Enter a valid phone number in international format (e.g., +61412345678)"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{children:"If Twilio is not configured, messages will be logged to console"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{children:"Check SMS settings in Admin Settings to enable/disable SMS types"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{children:"Template tests use sample data - actual bookings would use real data"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{children:"Monitor the browser console for detailed SMS service logs"})]})]})]})]})})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var d=r(2690),l=r.n(d);function s(){let{user:e,loading:t}=(0,n.a)(),[r,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[c,d]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[s,O]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[u,m]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[_,f]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[N,h]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[E,D]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({}),[U,v]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("all"),[j,p]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[M,T]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({type:"",name:"",description:"",template:"",variables:[],category:"custom",is_active:!0});Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{e&&b()},[e]);let b=async()=>{try{a(!0);let e=await fetch("/api/admin/sms-templates"),t=await e.json();e.ok?d(t.templates||[]):console.error("Failed to fetch SMS templates:",t.message)}catch(e){console.error("Error fetching SMS templates:",e)}finally{a(!1)}},S=e=>{var t;O(e),m(!1),f(!1);let r={};null===(t=e.variables)||void 0===t||t.forEach(e=>{switch(e){case"customerName":case"name":r[e]="Sarah Johnson";break;case"serviceName":r[e]="Hair Braiding";break;case"date":r[e]="March 15, 2025";break;case"time":r[e]="2:30 PM";break;case"amount":r[e]="150.00";break;case"receiptNumber":r[e]="OSS-2025-001";break;default:r[e]="[".concat(e,"]")}}),D(r)},w=async()=>{try{h(!0);let e=_?M:{...M,id:null==s?void 0:s.id},t=await fetch("/api/admin/sms-templates",{method:_?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.json();t.ok?(await b(),m(!1),f(!1),r.template&&O(r.template),alert("SMS template ".concat(_?"created":"updated"," successfully!"))):alert("Failed to ".concat(_?"create":"update"," SMS template: ").concat(r.message))}catch(e){console.error("Error saving SMS template:",e),alert("Error saving SMS template. Please try again.")}finally{h(!1)}},C=async()=>{if(s&&!s.is_default&&confirm("Are you sure you want to delete this SMS template?"))try{let e=await fetch("/api/admin/sms-templates?id=".concat(s.id),{method:"DELETE"});if(e.ok)await b(),O(null),alert("SMS template deleted successfully!");else{let t=await e.json();alert("Failed to delete SMS template: ".concat(t.message))}}catch(e){console.error("Error deleting SMS template:",e),alert("Error deleting SMS template. Please try again.")}},F=e=>{let t=e.match(/{{(\w+)}}/g);return t?Array.from(new Set(t.map(e=>e.replace(/[{}]/g,"")))):[]},L=e=>{let t=F(e);T(r=>({...r,template:e,variables:t}))},x="all"===U?c:c.filter(e=>e.category===U);return t||r?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(o.Z,{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().loading,children:"Loading SMS templates..."})}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(o.Z,{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().container,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("header",{className:l().header,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:l().title,children:"SMS Templates"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().headerActions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>p(!0),className:l().testBtn,children:"Test SMS"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>{T({type:"",name:"",description:"",template:"",variables:[],category:"custom",is_active:!0}),f(!0),m(!1),O(null)},className:l().createBtn,children:"Create Template"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().content,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateList,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().categoryFilter,children:["all","booking","payment","staff","customer","marketing","admin","custom"].map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:"".concat(l().categoryBtn," ").concat(U===e?l().active:""),onClick:()=>v(e),children:e.charAt(0).toUpperCase()+e.slice(1)},e))}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templates,children:x.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"".concat(l().templateItem," ").concat((null==s?void 0:s.id)===e.id?l().selected:""),onClick:()=>S(e),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:e.name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"".concat(l().category," ").concat(l()[e.category]),children:e.category})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:l().templateDescription,children:e.description}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templatePreview,children:[e.template.substring(0,100),"..."]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateMeta,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"".concat(l().status," ").concat(e.is_active?l().active:l().inactive),children:e.is_active?"Active":"Inactive"}),e.is_default&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().defaultBadge,children:"Default"})]})]},e.id))})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateEditor,children:s||u||_?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().editorHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{children:_?"Create SMS Template":u?"Edit SMS Template":null==s?void 0:s.name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().editorActions,children:[!u&&!_&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>{s&&(T({type:s.type,name:s.name,description:s.description,template:s.template,variables:s.variables||[],category:s.category,is_active:s.is_active}),m(!0),f(!1))},className:l().editBtn,disabled:null==s?void 0:s.is_default,children:"Edit"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:C,className:l().deleteBtn,disabled:null==s?void 0:s.is_default,children:"Delete"})]}),(u||_)&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>{m(!1),f(!1)},className:l().cancelBtn,children:"Cancel"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:w,className:l().saveBtn,disabled:N,children:N?"Saving...":"Save"})]})]})]}),u||_?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().editForm,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Template Type"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",value:M.type,onChange:e=>T(t=>({...t,type:e.target.value})),placeholder:"e.g., booking_confirmation"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Name"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",value:M.name,onChange:e=>T(t=>({...t,name:e.target.value})),placeholder:"Template name"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Description"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",value:M.description,onChange:e=>T(t=>({...t,description:e.target.value})),placeholder:"Template description"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Category"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:M.category,onChange:e=>T(t=>({...t,category:e.target.value})),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"booking",children:"Booking"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"payment",children:"Payment"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"staff",children:"Staff"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"customer",children:"Customer"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"marketing",children:"Marketing"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"admin",children:"Admin"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"custom",children:"Custom"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Template Message"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("textarea",{value:M.template,onChange:e=>L(e.target.value),placeholder:"SMS message template with {{variables}}",rows:4}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("small",{children:["Use ","{{"," variableName ","}}","  for dynamic content"]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().formGroup,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"checkbox",checked:M.is_active,onChange:e=>T(t=>({...t,is_active:e.target.checked}))}),"Active"]})}),M.variables.length>0&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().variablesList,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{children:"Variables:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().variables,children:M.variables.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().variable,children:e},e))})]})]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateView,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateDetails,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Type:"})," ",null==s?void 0:s.type]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Category:"})," ",null==s?void 0:s.category]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Description:"})," ",null==s?void 0:s.description]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateContent,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{children:"Template:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateText,children:null==s?void 0:s.template})]}),(null==s?void 0:s.variables)&&s.variables.length>0&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().variablesList,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{children:"Variables:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().variables,children:s.variables.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().variable,children:e},e))})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().preview,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{children:"Preview:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().previewContent,children:(()=>{if(!s&&!u&&!_)return"";let e=u||_?M.template:(null==s?void 0:s.template)||"";return Object.keys(E).forEach(t=>{let r=RegExp("{{".concat(t,"}}"),"g");e=e.replace(r,E[t])}),e})()}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().previewControls,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h5",{children:"Preview Data:"}),((null==s?void 0:s.variables)||M.variables).map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().previewInput,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:[e,":"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",value:E[e]||"",onChange:t=>D(r=>({...r,[e]:t.target.value})),placeholder:"Enter ".concat(e)})]},e))]})]})]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().noSelection,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Select a template to view or edit, or create a new one."})})})]}),j&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(i,{onClose:()=>p(!1)})]})})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}()},2690:function(e){e.exports={container:"SMSTemplates_container__RqHVL",loading:"SMSTemplates_loading__DvT7h",header:"SMSTemplates_header___mL4G",title:"SMSTemplates_title__TBheM",headerActions:"SMSTemplates_headerActions__ypR7Z",testBtn:"SMSTemplates_testBtn__Fs0uw",createBtn:"SMSTemplates_createBtn__x0eLn",content:"SMSTemplates_content__BqgY0",templateList:"SMSTemplates_templateList__nGa4j",categoryFilter:"SMSTemplates_categoryFilter__cwAwR",categoryBtn:"SMSTemplates_categoryBtn__jB8b_",active:"SMSTemplates_active__5EmVV",templates:"SMSTemplates_templates__4ws1O",templateItem:"SMSTemplates_templateItem__3jU94",selected:"SMSTemplates_selected__eFdVH",templateHeader:"SMSTemplates_templateHeader__N3HIU",category:"SMSTemplates_category__iy5s_",booking:"SMSTemplates_booking__zZ_fi",payment:"SMSTemplates_payment__PfqxT",staff:"SMSTemplates_staff__Bpsaw",customer:"SMSTemplates_customer__ajPG7",marketing:"SMSTemplates_marketing__59cBO",admin:"SMSTemplates_admin__cdUcA",custom:"SMSTemplates_custom__Uh0OO",templateDescription:"SMSTemplates_templateDescription__Xb_ZI",templatePreview:"SMSTemplates_templatePreview__9rcRV",templateMeta:"SMSTemplates_templateMeta__H2SNI",status:"SMSTemplates_status__vAVZH",inactive:"SMSTemplates_inactive__wzhvf",defaultBadge:"SMSTemplates_defaultBadge__3ny8W",templateEditor:"SMSTemplates_templateEditor__uriyP",editorHeader:"SMSTemplates_editorHeader__aF3L_",editorActions:"SMSTemplates_editorActions__RZfiU",editBtn:"SMSTemplates_editBtn__xt86p",deleteBtn:"SMSTemplates_deleteBtn__BTn4f",cancelBtn:"SMSTemplates_cancelBtn__23gtp",saveBtn:"SMSTemplates_saveBtn__H0ZX1",noSelection:"SMSTemplates_noSelection__zozUA",editForm:"SMSTemplates_editForm__dZOTt",formGroup:"SMSTemplates_formGroup__tu_dn",templateView:"SMSTemplates_templateView__4UINh",templateDetails:"SMSTemplates_templateDetails__ITZmN",templateContent:"SMSTemplates_templateContent__WiYKE",templateText:"SMSTemplates_templateText__ubN_Y",variablesList:"SMSTemplates_variablesList__utF6k",variables:"SMSTemplates_variables__hP_Xy",variable:"SMSTemplates_variable__TbftC",preview:"SMSTemplates_preview__bcn7T",previewContent:"SMSTemplates_previewContent__Hx2gd",previewControls:"SMSTemplates_previewControls__NoN1z",previewInput:"SMSTemplates_previewInput__gjtwg"}},7253:function(e){e.exports={overlay:"SMSTestPanel_overlay__1ypWr",panel:"SMSTestPanel_panel__gOHpq",header:"SMSTestPanel_header__HD5Xs",closeBtn:"SMSTestPanel_closeBtn__pMZ0Y",content:"SMSTestPanel_content__wmIgZ",section:"SMSTestPanel_section__HHCDa",formGroup:"SMSTestPanel_formGroup__uJo5d",statusBtn:"SMSTestPanel_statusBtn__ilI9o",statusInfo:"SMSTestPanel_statusInfo__73ZdU",error:"SMSTestPanel_error__Y9Ot8",sendBtn:"SMSTestPanel_sendBtn__EGI1o",templateTests:"SMSTestPanel_templateTests__kIB_5",templateBtn:"SMSTestPanel_templateBtn___dURp",result:"SMSTestPanel_result__NR7_z",success:"SMSTestPanel_success__ghRvt",instructions:"SMSTestPanel_instructions__fXtHh"}}},function(e){e.O(0,[736,592,888,179],function(){return e(e.s=4411)}),_N_E=e.O()}]);