/**
 * Ocean Soul Sparkles Admin - Mobile POS Component
 * Complete mobile point-of-sale interface optimized for touch devices
 */

import React, { useState, useEffect } from 'react';
import { HapticFeedback } from '../../../lib/gestures/swipe-handler';
import { useActionSheet } from './ActionSheet';
import { useMobileModal } from './MobileModal';
import PullToRefresh from '../PullToRefresh';
import styles from '../../../styles/admin/mobile/MobilePOS.module.css';

interface MobilePOSProps {
  onTransactionComplete: (transaction: any) => void;
  services: any[];
  products: any[];
  customers: any[];
}

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  type: 'service' | 'product';
  duration?: number;
}

export default function MobilePOS({
  onTransactionComplete,
  services,
  products,
  customers
}: MobilePOSProps) {
  const [currentView, setCurrentView] = useState<'services' | 'products' | 'cart' | 'checkout'>('services');
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [total, setTotal] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);

  const { show: showActionSheet, ActionSheet } = useActionSheet();
  const { show: showModal, MobileModal } = useMobileModal();

  // Calculate total when cart changes
  useEffect(() => {
    const newTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    setTotal(newTotal);
  }, [cart]);

  const handleRefresh = async () => {
    // Simulate data refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    HapticFeedback.success();
  };

  const addToCart = (item: any, type: 'service' | 'product') => {
    HapticFeedback.light();
    
    const existingItem = cart.find(cartItem => cartItem.id === item.id && cartItem.type === type);
    
    if (existingItem) {
      setCart(cart.map(cartItem =>
        cartItem.id === item.id && cartItem.type === type
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      ));
    } else {
      const newItem: CartItem = {
        id: item.id,
        name: item.name,
        price: item.price || 0,
        quantity: 1,
        type,
        duration: item.duration
      };
      setCart([...cart, newItem]);
    }
  };

  const removeFromCart = (itemId: string, type: 'service' | 'product') => {
    HapticFeedback.medium();
    setCart(cart.filter(item => !(item.id === itemId && item.type === type)));
  };

  const updateQuantity = (itemId: string, type: 'service' | 'product', newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId, type);
      return;
    }

    HapticFeedback.light();
    setCart(cart.map(item =>
      item.id === itemId && item.type === type
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  const handleItemLongPress = (item: CartItem) => {
    HapticFeedback.medium();
    
    showActionSheet({
      title: item.name,
      actions: [
        {
          id: 'edit-quantity',
          label: 'Edit Quantity',
          icon: '✏️',
          onClick: () => showQuantityModal(item)
        },
        {
          id: 'remove',
          label: 'Remove from Cart',
          icon: '🗑️',
          variant: 'destructive',
          onClick: () => removeFromCart(item.id, item.type)
        }
      ]
    });
  };

  const showQuantityModal = (item: CartItem) => {
    // Implementation for quantity modal would go here
    console.log('Show quantity modal for:', item);
  };

  const selectCustomer = (customer: any) => {
    HapticFeedback.light();
    setSelectedCustomer(customer);
  };

  const processTransaction = async () => {
    if (cart.length === 0) return;

    setIsProcessing(true);
    HapticFeedback.heavy();

    try {
      // Simulate transaction processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const transaction = {
        id: `txn_${Date.now()}`,
        items: cart,
        customer: selectedCustomer,
        total,
        timestamp: new Date().toISOString(),
        paymentMethod: 'card'
      };

      onTransactionComplete(transaction);
      
      // Reset state
      setCart([]);
      setSelectedCustomer(null);
      setCurrentView('services');
      
      HapticFeedback.success();
    } catch (error) {
      console.error('Transaction failed:', error);
      HapticFeedback.error();
    } finally {
      setIsProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return '';
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.category && product.category.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className={styles.mobilePOS}>
      {/* Header */}
      <div className={styles.header}>
        <h1 className={styles.title}>Mobile POS</h1>
        <div className={styles.cartBadge}>
          🛒 {cart.length}
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className={styles.tabNavigation}>
        <button
          className={`${styles.tab} ${currentView === 'services' ? styles.active : ''}`}
          onClick={() => setCurrentView('services')}
        >
          ✂️ Services
        </button>
        <button
          className={`${styles.tab} ${currentView === 'products' ? styles.active : ''}`}
          onClick={() => setCurrentView('products')}
        >
          🛍️ Products
        </button>
        <button
          className={`${styles.tab} ${currentView === 'cart' ? styles.active : ''}`}
          onClick={() => setCurrentView('cart')}
        >
          🛒 Cart ({cart.length})
        </button>
      </div>

      {/* Content */}
      <PullToRefresh onRefresh={handleRefresh} className={styles.content}>
        {/* Search */}
        {(currentView === 'services' || currentView === 'products') && (
          <div className={styles.searchContainer}>
            <input
              type="text"
              placeholder={`Search ${currentView}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>
        )}

        {/* Services View */}
        {currentView === 'services' && (
          <div className={styles.itemsGrid}>
            {filteredServices.map((service) => (
              <div key={service.id} className={styles.itemCard}>
                <div className={styles.itemInfo}>
                  <h3 className={styles.itemName}>{service.name}</h3>
                  <p className={styles.itemCategory}>{service.category}</p>
                  <div className={styles.itemDetails}>
                    <span className={styles.price}>
                      {formatCurrency(service.price || 50)}
                    </span>
                    {service.duration && (
                      <span className={styles.duration}>
                        {formatDuration(service.duration)}
                      </span>
                    )}
                  </div>
                </div>
                <button
                  className={styles.addButton}
                  onClick={() => addToCart(service, 'service')}
                >
                  +
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Products View */}
        {currentView === 'products' && (
          <div className={styles.itemsGrid}>
            {filteredProducts.map((product) => (
              <div key={product.id} className={styles.itemCard}>
                <div className={styles.itemInfo}>
                  <h3 className={styles.itemName}>{product.name}</h3>
                  <p className={styles.itemCategory}>{product.category || 'Product'}</p>
                  <div className={styles.itemDetails}>
                    <span className={styles.price}>
                      {formatCurrency(product.price || 25)}
                    </span>
                    {product.stock_quantity !== undefined && (
                      <span className={styles.stock}>
                        Stock: {product.stock_quantity}
                      </span>
                    )}
                  </div>
                </div>
                <button
                  className={styles.addButton}
                  onClick={() => addToCart(product, 'product')}
                  disabled={product.stock_quantity === 0}
                >
                  +
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Cart View */}
        {currentView === 'cart' && (
          <div className={styles.cartView}>
            {cart.length === 0 ? (
              <div className={styles.emptyCart}>
                <div className={styles.emptyIcon}>🛒</div>
                <h3>Cart is Empty</h3>
                <p>Add services or products to get started</p>
              </div>
            ) : (
              <>
                <div className={styles.cartItems}>
                  {cart.map((item) => (
                    <div
                      key={`${item.id}-${item.type}`}
                      className={styles.cartItem}
                      onTouchStart={() => handleItemLongPress(item)}
                    >
                      <div className={styles.itemInfo}>
                        <h4 className={styles.itemName}>{item.name}</h4>
                        <p className={styles.itemType}>
                          {item.type === 'service' ? '✂️ Service' : '🛍️ Product'}
                        </p>
                        <span className={styles.itemPrice}>
                          {formatCurrency(item.price)}
                        </span>
                      </div>
                      <div className={styles.quantityControls}>
                        <button
                          className={styles.quantityBtn}
                          onClick={() => updateQuantity(item.id, item.type, item.quantity - 1)}
                        >
                          -
                        </button>
                        <span className={styles.quantity}>{item.quantity}</span>
                        <button
                          className={styles.quantityBtn}
                          onClick={() => updateQuantity(item.id, item.type, item.quantity + 1)}
                        >
                          +
                        </button>
                      </div>
                      <div className={styles.itemTotal}>
                        {formatCurrency(item.price * item.quantity)}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Cart Summary */}
                <div className={styles.cartSummary}>
                  <div className={styles.totalRow}>
                    <span className={styles.totalLabel}>Total:</span>
                    <span className={styles.totalAmount}>{formatCurrency(total)}</span>
                  </div>
                  
                  {/* Customer Selection */}
                  <div className={styles.customerSection}>
                    <h4>Customer</h4>
                    {selectedCustomer ? (
                      <div className={styles.selectedCustomer}>
                        <span>{selectedCustomer.name}</span>
                        <button
                          className={styles.changeCustomerBtn}
                          onClick={() => setSelectedCustomer(null)}
                        >
                          Change
                        </button>
                      </div>
                    ) : (
                      <select
                        className={styles.customerSelect}
                        onChange={(e) => {
                          const customer = customers.find(c => c.id === e.target.value);
                          if (customer) selectCustomer(customer);
                        }}
                        value=""
                      >
                        <option value="">Select Customer (Optional)</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>

                  {/* Checkout Button */}
                  <button
                    className={styles.checkoutBtn}
                    onClick={processTransaction}
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processing...' : `Checkout ${formatCurrency(total)}`}
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </PullToRefresh>

      {/* Action Sheet and Modal Components */}
      <ActionSheet />
      <MobileModal>
        <div></div>
      </MobileModal>
    </div>
  );
}
