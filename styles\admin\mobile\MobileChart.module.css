/* Ocean Soul Sparkles - Mobile Chart Styles */

.mobileChart {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.chartContainer {
  position: relative;
  width: 100%;
  height: auto;
  min-height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc, #edf2f7);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.chartContainer canvas {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 8px;
}

.chartControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #edf2f7);
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

.chartInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.chartType {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
}

.chartSize {
  font-size: 0.75rem;
  color: #718096;
  font-family: 'Courier New', monospace;
}

.chartActions {
  display: flex;
  gap: 0.5rem;
}

.chartAction {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.chartAction:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.chartAction:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Mobile-specific chart optimizations */
@media (max-width: 768px) {
  .mobileChart {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 12px;
  }

  .chartContainer {
    min-height: 200px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .chartControls {
    padding: 0.75rem;
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .chartInfo {
    text-align: center;
  }

  .chartActions {
    justify-content: center;
    gap: 1rem;
  }

  .chartAction {
    width: 44px;
    height: 44px;
    font-size: 1.2rem;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .mobileChart {
    padding: 0.75rem;
    border-radius: 8px;
  }

  .chartContainer {
    min-height: 180px;
    padding: 0.5rem;
  }

  .chartControls {
    padding: 0.5rem;
  }

  .chartType {
    font-size: 0.85rem;
  }

  .chartSize {
    font-size: 0.7rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .mobileChart {
    border-width: 2px;
    border-color: #2d3748;
  }

  .chartContainer {
    border: 2px solid #e2e8f0;
  }

  .chartControls {
    border-width: 2px;
  }

  .chartAction {
    border: 2px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .chartAction {
    transition: none;
  }

  .chartAction:hover {
    transform: none;
  }

  .chartAction:active {
    transform: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .mobileChart {
    background: rgba(45, 55, 72, 0.95);
    border-color: #4a5568;
  }

  .chartContainer {
    background: linear-gradient(135deg, #2d3748, #4a5568);
  }

  .chartControls {
    background: linear-gradient(135deg, #2d3748, #4a5568);
    border-color: #4a5568;
  }

  .chartType {
    color: #f7fafc;
  }

  .chartSize {
    color: #a0aec0;
  }
}

/* Print styles */
@media print {
  .mobileChart {
    background: white;
    border: 1px solid #000;
    box-shadow: none;
    backdrop-filter: none;
  }

  .chartContainer {
    background: white;
  }

  .chartControls {
    display: none;
  }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .chartContainer {
    min-height: 150px;
  }

  .chartControls {
    flex-direction: row;
    align-items: center;
  }

  .chartInfo {
    text-align: left;
  }

  .chartActions {
    justify-content: flex-end;
  }
}

/* Touch-specific optimizations */
@media (pointer: coarse) {
  .chartAction {
    min-width: 44px;
    min-height: 44px;
  }

  .chartContainer {
    /* Improve touch scrolling */
    -webkit-overflow-scrolling: touch;
    overflow: auto;
  }
}

/* Hover-capable devices */
@media (hover: hover) {
  .chartAction:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  }
}

/* Hide on desktop when not needed */
@media (min-width: 769px) {
  .chartControls {
    display: none;
  }
}
