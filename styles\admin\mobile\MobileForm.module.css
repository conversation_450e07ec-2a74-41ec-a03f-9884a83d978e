/* Ocean Soul Sparkles - Mobile Form Styles */

.mobileForm {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.mobileForm.keyboardVisible {
  min-height: auto;
}

.formHeader {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 2rem 1.5rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.headerContent {
  text-align: center;
  margin-bottom: 1.5rem;
}

.formTitle {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.formSubtitle {
  margin: 0;
  font-size: 1rem;
  color: #718096;
  font-weight: 400;
  line-height: 1.4;
}

.progressContainer {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.progressBar {
  width: 100%;
  height: 6px;
  background: rgba(226, 232, 240, 0.5);
  border-radius: 3px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progressText {
  font-size: 0.85rem;
  color: #4a5568;
  font-weight: 500;
}

.form {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  margin: 1rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.formContent {
  flex: 1;
  padding: 2rem 1.5rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.formActions {
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc, #edf2f7);
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.cancelButton {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  color: #4a5568;
  border: 1px solid #e2e8f0;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancelButton:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.cancelButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.submitButton {
  flex: 2;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.loadingSpinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.keyboardSpacer {
  height: 300px; /* Space for virtual keyboard */
  background: transparent;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .formHeader {
    padding: 1.5rem 1rem 1rem;
  }

  .formTitle {
    font-size: 1.5rem;
  }

  .formSubtitle {
    font-size: 0.9rem;
  }

  .form {
    margin: 0.75rem;
    border-radius: 12px;
  }

  .formContent {
    padding: 1.5rem 1rem;
  }

  .formActions {
    padding: 1rem;
    flex-direction: column;
    gap: 0.75rem;
  }

  .cancelButton,
  .submitButton {
    width: 100%;
    flex: none;
    padding: 1rem;
    min-height: 48px;
  }

  .submitButton {
    order: -1; /* Submit button first on mobile */
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .formHeader {
    padding: 1rem 0.75rem 0.75rem;
  }

  .formTitle {
    font-size: 1.3rem;
  }

  .form {
    margin: 0.5rem;
    border-radius: 8px;
  }

  .formContent {
    padding: 1rem 0.75rem;
  }

  .formActions {
    padding: 0.75rem;
  }

  .cancelButton,
  .submitButton {
    padding: 0.875rem;
    min-height: 44px;
    font-size: 0.95rem;
  }
}

/* Landscape orientation */
@media (max-width: 768px) and (orientation: landscape) {
  .mobileForm {
    min-height: auto;
  }

  .formHeader {
    padding: 1rem;
  }

  .formTitle {
    font-size: 1.3rem;
  }

  .formContent {
    padding: 1rem;
  }

  .keyboardSpacer {
    height: 200px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .form {
    border-width: 2px;
    border-color: #2d3748;
  }

  .formActions {
    border-top-width: 2px;
  }

  .cancelButton {
    border-width: 2px;
  }

  .submitButton {
    border: 2px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .mobileForm,
  .progressFill,
  .cancelButton,
  .submitButton,
  .loadingSpinner {
    transition: none;
    animation: none;
  }

  .cancelButton:hover,
  .submitButton:hover {
    transform: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .formHeader {
    background: rgba(45, 55, 72, 0.95);
  }

  .formSubtitle {
    color: #a0aec0;
  }

  .progressText {
    color: #a0aec0;
  }

  .form {
    background: rgba(45, 55, 72, 0.95);
    border-color: #4a5568;
  }

  .formActions {
    background: linear-gradient(135deg, #2d3748, #4a5568);
    border-top-color: #4a5568;
  }

  .cancelButton {
    background: rgba(45, 55, 72, 0.9);
    color: #f7fafc;
    border-color: #4a5568;
  }

  .cancelButton:hover:not(:disabled) {
    background: #4a5568;
    border-color: #718096;
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  .formContent {
    /* Improve scrolling on iOS */
    -webkit-overflow-scrolling: touch;
  }

  .mobileForm.keyboardVisible .formContent {
    /* Prevent content jumping when keyboard appears */
    position: relative;
  }
}

/* Hide on desktop - mobile-only component */
@media (min-width: 769px) {
  .mobileForm {
    display: none;
  }
}
