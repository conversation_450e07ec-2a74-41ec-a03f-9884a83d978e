(()=>{var e={};e.id=6251,e.ids=[6251,660],e.modules={3339:e=>{e.exports={performanceContainer:"StaffPerformance_performanceContainer__DXTuo",header:"StaffPerformance_header__jbAJT",headerLeft:"StaffPerformance_headerLeft__rfr_L",title:"StaffPerformance_title__dZChZ",subtitle:"StaffPerformance_subtitle__6S4so",headerActions:"StaffPerformance_headerActions__EHVKD",backBtn:"StaffPerformance_backBtn__HdjB0",errorMessage:"StaffPerformance_errorMessage__tgJFR",closeError:"StaffPerformance_closeError__PvbMd",dateRangeSection:"StaffPerformance_dateRangeSection__CRQ2D",dateInputs:"StaffPerformance_dateInputs__vDsBE",inputGroup:"StaffPerformance_inputGroup__sW_XJ",dateInput:"StaffPerformance_dateInput__Exc_C",summarySection:"StaffPerformance_summarySection__5FIaR",summaryGrid:"StaffPerformance_summaryGrid__u6u57",summaryCard:"StaffPerformance_summaryCard__e7z9O",cardIcon:"StaffPerformance_cardIcon__xCK6y",cardContent:"StaffPerformance_cardContent__2V2Pm",cardValue:"StaffPerformance_cardValue__i3Tv5",cardSubtext:"StaffPerformance_cardSubtext__upvVS",metricsSection:"StaffPerformance_metricsSection__kKNvL",sectionTitle:"StaffPerformance_sectionTitle__ebm83",emptyState:"StaffPerformance_emptyState__I1XqX",emptyIcon:"StaffPerformance_emptyIcon___RePP",metricsTable:"StaffPerformance_metricsTable__OAynW",tableContainer:"StaffPerformance_tableContainer__7yVZ6",table:"StaffPerformance_table__E7HOi",tableRow:"StaffPerformance_tableRow__Jvpsl",dateCell:"StaffPerformance_dateCell__AotZ9",numberCell:"StaffPerformance_numberCell__y2wgd",percentage:"StaffPerformance_percentage__5BeW_",currencyCell:"StaffPerformance_currencyCell__XO_L8",ratingCell:"StaffPerformance_ratingCell__WjJgN",percentageCell:"StaffPerformance_percentageCell__gysUI",noData:"StaffPerformance_noData__l1_dk",loadingContainer:"StaffPerformance_loadingContainer__6u4vT",loadingSpinner:"StaffPerformance_loadingSpinner__fvvXl",spin:"StaffPerformance_spin__Z9Zjn"}},9933:(e,a,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(a),t.d(a,{config:()=>x,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>_,getStaticProps:()=>f,reportWebVitals:()=>p,routeModule:()=>b,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>g,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>j});var s=t(7093),n=t(5244),c=t(1323),l=t(2899),i=t.n(l),d=t(6814),o=t(8514),m=e([d,o]);[d,o]=m.then?(await m)():m;let u=(0,c.l)(o,"default"),f=(0,c.l)(o,"getStaticProps"),_=(0,c.l)(o,"getStaticPaths"),h=(0,c.l)(o,"getServerSideProps"),x=(0,c.l)(o,"config"),p=(0,c.l)(o,"reportWebVitals"),j=(0,c.l)(o,"unstable_getStaticProps"),S=(0,c.l)(o,"unstable_getStaticPaths"),g=(0,c.l)(o,"unstable_getStaticParams"),v=(0,c.l)(o,"unstable_getServerProps"),N=(0,c.l)(o,"unstable_getServerSideProps"),b=new s.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/staff/performance",pathname:"/admin/staff/performance",bundlePath:"",filename:""},components:{App:d.default,Document:i()},userland:o});r()}catch(e){r(e)}})},8514:(e,a,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(a),t.d(a,{default:()=>_});var s=t(997),n=t(6689),c=t(968),l=t.n(c),i=t(1163),d=t(8568),o=t(4845),m=t(3339),u=t.n(m),f=e([o]);function _(){let{user:e}=(0,d.a)(),a=(0,i.useRouter)(),{staff_id:t}=a.query,[r,c]=(0,n.useState)(!0),[m,f]=(0,n.useState)(null),[_,h]=(0,n.useState)([]),[x,p]=(0,n.useState)({totalBookings:0,completedBookings:0,cancelledBookings:0,totalRevenue:0,totalTips:0,totalHours:0,averageRating:0,averagePunctuality:0,completionRate:0,cancellationRate:0,totalPeriods:0}),[j,S]=(0,n.useState)(null),[g,v]=(0,n.useState)({start:new Date(Date.now()-2592e6).toISOString().split("T")[0],end:new Date().toISOString().split("T")[0]}),N=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),b=e=>new Date(e).toLocaleDateString("en-AU",{year:"numeric",month:"short",day:"numeric"}),P=e=>e>=4.5?"#10b981":e>=4?"#84cc16":e>=3?"#f59e0b":e>=2?"#f97316":"#ef4444",C=e=>e>=90?"#10b981":e>=75?"#84cc16":e>=60?"#f59e0b":"#ef4444";return r?s.jsx(o.Z,{children:(0,s.jsxs)("div",{className:u().loadingContainer,children:[s.jsx("div",{className:u().loadingSpinner}),s.jsx("p",{children:"Loading performance data..."})]})}):(0,s.jsxs)(o.Z,{children:[(0,s.jsxs)(l(),{children:[s.jsx("title",{children:"Staff Performance | Ocean Soul Sparkles Admin"}),s.jsx("meta",{name:"description",content:"View staff performance metrics and analytics"})]}),(0,s.jsxs)("div",{className:u().performanceContainer,children:[(0,s.jsxs)("header",{className:u().header,children:[(0,s.jsxs)("div",{className:u().headerLeft,children:[s.jsx("h1",{className:u().title,children:"Staff Performance"}),j&&(0,s.jsxs)("p",{className:u().subtitle,children:[j.firstName," ",j.lastName," - ",j.role]})]}),s.jsx("div",{className:u().headerActions,children:s.jsx("button",{onClick:()=>a.back(),className:u().backBtn,children:"← Back to Staff"})})]}),m&&(0,s.jsxs)("div",{className:u().errorMessage,children:[m,s.jsx("button",{onClick:()=>f(null),className:u().closeError,children:"\xd7"})]}),s.jsx("div",{className:u().dateRangeSection,children:(0,s.jsxs)("div",{className:u().dateInputs,children:[(0,s.jsxs)("div",{className:u().inputGroup,children:[s.jsx("label",{htmlFor:"startDate",children:"Start Date:"}),s.jsx("input",{type:"date",id:"startDate",value:g.start,onChange:e=>v(a=>({...a,start:e.target.value})),className:u().dateInput})]}),(0,s.jsxs)("div",{className:u().inputGroup,children:[s.jsx("label",{htmlFor:"endDate",children:"End Date:"}),s.jsx("input",{type:"date",id:"endDate",value:g.end,onChange:e=>v(a=>({...a,end:e.target.value})),className:u().dateInput})]})]})}),s.jsx("div",{className:u().summarySection,children:(0,s.jsxs)("div",{className:u().summaryGrid,children:[(0,s.jsxs)("div",{className:u().summaryCard,children:[s.jsx("div",{className:u().cardIcon,children:"\uD83D\uDCC5"}),(0,s.jsxs)("div",{className:u().cardContent,children:[s.jsx("h3",{children:"Total Bookings"}),s.jsx("div",{className:u().cardValue,children:x.totalBookings}),(0,s.jsxs)("div",{className:u().cardSubtext,children:[x.completedBookings," completed, ",x.cancelledBookings," cancelled"]})]})]}),(0,s.jsxs)("div",{className:u().summaryCard,children:[s.jsx("div",{className:u().cardIcon,children:"\uD83D\uDCB0"}),(0,s.jsxs)("div",{className:u().cardContent,children:[s.jsx("h3",{children:"Total Revenue"}),s.jsx("div",{className:u().cardValue,children:N(x.totalRevenue)}),(0,s.jsxs)("div",{className:u().cardSubtext,children:["+ ",N(x.totalTips)," in tips"]})]})]}),(0,s.jsxs)("div",{className:u().summaryCard,children:[s.jsx("div",{className:u().cardIcon,children:"⭐"}),(0,s.jsxs)("div",{className:u().cardContent,children:[s.jsx("h3",{children:"Average Rating"}),s.jsx("div",{className:u().cardValue,style:{color:P(x.averageRating)},children:x.averageRating.toFixed(1)}),s.jsx("div",{className:u().cardSubtext,children:"Based on customer feedback"})]})]}),(0,s.jsxs)("div",{className:u().summaryCard,children:[s.jsx("div",{className:u().cardIcon,children:"✅"}),(0,s.jsxs)("div",{className:u().cardContent,children:[s.jsx("h3",{children:"Completion Rate"}),(0,s.jsxs)("div",{className:u().cardValue,style:{color:C(x.completionRate)},children:[x.completionRate.toFixed(1),"%"]}),(0,s.jsxs)("div",{className:u().cardSubtext,children:[x.completedBookings," of ",x.totalBookings," bookings"]})]})]}),(0,s.jsxs)("div",{className:u().summaryCard,children:[s.jsx("div",{className:u().cardIcon,children:"⏰"}),(0,s.jsxs)("div",{className:u().cardContent,children:[s.jsx("h3",{children:"Hours Worked"}),s.jsx("div",{className:u().cardValue,children:x.totalHours.toFixed(1)}),(0,s.jsxs)("div",{className:u().cardSubtext,children:["Punctuality: ",x.averagePunctuality.toFixed(1),"%"]})]})]}),(0,s.jsxs)("div",{className:u().summaryCard,children:[s.jsx("div",{className:u().cardIcon,children:"\uD83D\uDCCA"}),(0,s.jsxs)("div",{className:u().cardContent,children:[s.jsx("h3",{children:"Cancellation Rate"}),(0,s.jsxs)("div",{className:u().cardValue,style:{color:x.cancellationRate>10?"#ef4444":"#10b981"},children:[x.cancellationRate.toFixed(1),"%"]}),(0,s.jsxs)("div",{className:u().cardSubtext,children:[x.cancelledBookings," cancelled bookings"]})]})]})]})}),(0,s.jsxs)("div",{className:u().metricsSection,children:[s.jsx("h2",{className:u().sectionTitle,children:"Daily Performance Metrics"}),0===_.length?(0,s.jsxs)("div",{className:u().emptyState,children:[s.jsx("div",{className:u().emptyIcon,children:"\uD83D\uDCC8"}),s.jsx("h3",{children:"No Performance Data"}),s.jsx("p",{children:"No performance metrics found for the selected date range."})]}):s.jsx("div",{className:u().metricsTable,children:s.jsx("div",{className:u().tableContainer,children:(0,s.jsxs)("table",{className:u().table,children:[s.jsx("thead",{children:(0,s.jsxs)("tr",{children:[s.jsx("th",{children:"Date"}),s.jsx("th",{children:"Bookings"}),s.jsx("th",{children:"Completed"}),s.jsx("th",{children:"Revenue"}),s.jsx("th",{children:"Tips"}),s.jsx("th",{children:"Rating"}),s.jsx("th",{children:"Hours"}),s.jsx("th",{children:"Punctuality"})]})}),s.jsx("tbody",{children:_.map(e=>(0,s.jsxs)("tr",{className:u().tableRow,children:[s.jsx("td",{className:u().dateCell,children:b(e.metric_date)}),s.jsx("td",{className:u().numberCell,children:e.total_bookings}),(0,s.jsxs)("td",{className:u().numberCell,children:[e.completed_bookings,e.total_bookings>0&&(0,s.jsxs)("span",{className:u().percentage,children:["(",Math.round(e.completed_bookings/e.total_bookings*100),"%)"]})]}),s.jsx("td",{className:u().currencyCell,children:N(e.total_revenue)}),s.jsx("td",{className:u().currencyCell,children:N(e.total_tips)}),s.jsx("td",{className:u().ratingCell,children:e.average_rating?s.jsx("span",{style:{color:P(e.average_rating)},children:e.average_rating.toFixed(1)}):s.jsx("span",{className:u().noData,children:"-"})}),s.jsx("td",{className:u().numberCell,children:e.hours_worked?e.hours_worked.toFixed(1):"-"}),s.jsx("td",{className:u().percentageCell,children:e.punctuality_score?(0,s.jsxs)("span",{style:{color:C(e.punctuality_score)},children:[e.punctuality_score.toFixed(1),"%"]}):s.jsx("span",{className:u().noData,children:"-"})})]},e.id))})]})})})]})]})]})}o=(f.then?(await f)():f)[0],r()}catch(e){r(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[2899,6212,1664,7441],()=>t(9933));module.exports=r})();