(()=>{var e={};e.id=7939,e.ids=[7939,660],e.modules={6729:e=>{e.exports={analyticsContainer:"CustomerAnalytics_analyticsContainer__dXNi7",analyticsHeader:"CustomerAnalytics_analyticsHeader__atXdX",metricsGrid:"CustomerAnalytics_metricsGrid__A1Fpz",metricCard:"CustomerAnalytics_metricCard__te1Ph",metricValue:"CustomerAnalytics_metricValue__ZrYix",metricLabel:"CustomerAnalytics_metricLabel__QDnTz",metricIcon:"CustomerAnalytics_metricIcon__u7kXj",chartsGrid:"CustomerAnalytics_chartsGrid__FpUrz",chartCard:"CustomerAnalytics_chartCard__ewo7d",barChart:"CustomerAnalytics_barChart__htKEE",barContainer:"CustomerAnalytics_barContainer__u502a",bar:"CustomerAnalytics_bar___7HFv",barLabel:"CustomerAnalytics_barLabel__5OjzC",barValue:"CustomerAnalytics_barValue__FUxRQ",topCustomersList:"CustomerAnalytics_topCustomersList__crvs4",topCustomerItem:"CustomerAnalytics_topCustomerItem__FiFiH",customerRank:"CustomerAnalytics_customerRank__kMNYK",customerInfo:"CustomerAnalytics_customerInfo__k4QTr",customerName:"CustomerAnalytics_customerName__prxWo",customerEmail:"CustomerAnalytics_customerEmail__HmKSa",customerBookings:"CustomerAnalytics_customerBookings__5nuA_",bookingCount:"CustomerAnalytics_bookingCount__EaoDj",bookingLabel:"CustomerAnalytics_bookingLabel__7L0UG",emptyState:"CustomerAnalytics_emptyState__Oq5ST",insightsGrid:"CustomerAnalytics_insightsGrid__OnpFt",insightCard:"CustomerAnalytics_insightCard__CIFV_",insightContent:"CustomerAnalytics_insightContent__gbI2r",engagementStat:"CustomerAnalytics_engagementStat__E8ZBd",trendStat:"CustomerAnalytics_trendStat__dkAb_",retentionStat:"CustomerAnalytics_retentionStat__jtyW3",percentage:"CustomerAnalytics_percentage__Sk0nw",trendValue:"CustomerAnalytics_trendValue__JiWyn",retentionValue:"CustomerAnalytics_retentionValue__Oc2fh",engagementLabel:"CustomerAnalytics_engagementLabel__u28zU",trendLabel:"CustomerAnalytics_trendLabel__0UBXk",retentionLabel:"CustomerAnalytics_retentionLabel__ivJaP"}},2765:e=>{e.exports={modalOverlay:"CustomerModal_modalOverlay__t37qY",modal:"CustomerModal_modal__2fFh3",modalSlideIn:"CustomerModal_modalSlideIn__kxyhv",modalHeader:"CustomerModal_modalHeader__34jXP",modalActions:"CustomerModal_modalActions__Q6fmk",editButton:"CustomerModal_editButton__W8kFr",deleteButton:"CustomerModal_deleteButton__HHgh_",saveButton:"CustomerModal_saveButton__w_t1X",cancelButton:"CustomerModal_cancelButton__s9wOh",closeButton:"CustomerModal_closeButton__vu3S_",modalContent:"CustomerModal_modalContent__JBzoW",customerInfo:"CustomerModal_customerInfo__dRZY2",infoGrid:"CustomerModal_infoGrid__ZGeSf",infoGroup:"CustomerModal_infoGroup__hl6qN",customerStats:"CustomerModal_customerStats__bmZkT",statsGrid:"CustomerModal_statsGrid__6Ul7n",statItem:"CustomerModal_statItem__DWmH9",statValue:"CustomerModal_statValue__MPEQD",statLabel:"CustomerModal_statLabel__ivMHl",bookingHistory:"CustomerModal_bookingHistory__x65iP",bookingsList:"CustomerModal_bookingsList__t5evm",bookingItem:"CustomerModal_bookingItem__v08eo",bookingDate:"CustomerModal_bookingDate__mECwm",bookingService:"CustomerModal_bookingService__I61z2",bookingStatus:"CustomerModal_bookingStatus__mubMt"}},5273:e=>{e.exports={customersContainer:"Customers_customersContainer___hRzb",header:"Customers_header__Xsb8L",title:"Customers_title__kuVdg",headerContent:"Customers_headerContent__1x2Ki",headerActions:"Customers_headerActions__wW_iz",analyticsBtn:"Customers_analyticsBtn__Y6xGh",exportBtn:"Customers_exportBtn__6D97q",newCustomerBtn:"Customers_newCustomerBtn__ACkta",backButton:"Customers_backButton__mwCeO",controlsPanel:"Customers_controlsPanel__QNdpL",searchContainer:"Customers_searchContainer__uP_D_",filtersContainer:"Customers_filtersContainer__B81bf",filterSelect:"Customers_filterSelect__ANSKc",sortSelect:"Customers_sortSelect__oahlL",viewControls:"Customers_viewControls__VdvPJ",viewBtn:"Customers_viewBtn__TNPmH",active:"Customers_active__rk6Up",resultsInfo:"Customers_resultsInfo__cpO5L",searchSection:"Customers_searchSection__sgj9I",searchInput:"Customers_searchInput__BB_0d",sortSection:"Customers_sortSection__qb0FO",sortOrderBtn:"Customers_sortOrderBtn__t2r88",customersContent:"Customers_customersContent__z_zag",customersHeader:"Customers_customersHeader__einsi",statsCards:"Customers_statsCards__ph2BM",statCard:"Customers_statCard__Pua_g",statValue:"Customers_statValue__357cS",emptyState:"Customers_emptyState__umGfV",customersGrid:"Customers_customersGrid__nkG51",customerCard:"Customers_customerCard__oECGs",customerMeta:"Customers_customerMeta__JcUjA",customerDate:"Customers_customerDate__dVu6L",emptyIcon:"Customers_emptyIcon__xGfzC",emptyActionBtn:"Customers_emptyActionBtn__1HOvJ",customerHeader:"Customers_customerHeader__QfMYw",customerInfo:"Customers_customerInfo__sYPpi",statusBadge:"Customers_statusBadge__nVYbG",statusVip:"Customers_statusVip__GAam3",statusActive:"Customers_statusActive__N_jFf",statusNew:"Customers_statusNew__8r02c",statusInactive:"Customers_statusInactive__inD5l",statusDefault:"Customers_statusDefault__R5R8L",customerStats:"Customers_customerStats__OLqld",statItem:"Customers_statItem__YazVS",statLabel:"Customers_statLabel__pm6R5",customerNotes:"Customers_customerNotes__KM6Mg",customerActions:"Customers_customerActions__TS_QE",bookBtn:"Customers_bookBtn__333XR",customersTable:"Customers_customersTable__c6X31",customerName:"Customers_customerName__SARmR",bookingBadge:"Customers_bookingBadge__0JdCy",tableActions:"Customers_tableActions__fP8SO",editBtn:"Customers_editBtn__3Orgk",pagination:"Customers_pagination__QsqfM",paginationBtn:"Customers_paginationBtn__RUfWW",paginationInfo:"Customers_paginationInfo__7qsKa",loadingContainer:"Customers_loadingContainer__eyNOh",loadingSpinner:"Customers_loadingSpinner__2d9G_",spin:"Customers_spin__xe_Ia"}},5236:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>x,default:()=>u,getServerSideProps:()=>C,getStaticPaths:()=>h,getStaticProps:()=>_,reportWebVitals:()=>g,routeModule:()=>S,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>p,unstable_getStaticProps:()=>j});var r=s(7093),n=s(5244),o=s(1323),i=s(2899),l=s.n(i),c=s(6814),m=s(1005),d=e([c,m]);[c,m]=d.then?(await d)():d;let u=(0,o.l)(m,"default"),_=(0,o.l)(m,"getStaticProps"),h=(0,o.l)(m,"getStaticPaths"),C=(0,o.l)(m,"getServerSideProps"),x=(0,o.l)(m,"config"),g=(0,o.l)(m,"reportWebVitals"),j=(0,o.l)(m,"unstable_getStaticProps"),p=(0,o.l)(m,"unstable_getStaticPaths"),v=(0,o.l)(m,"unstable_getStaticParams"),b=(0,o.l)(m,"unstable_getServerProps"),N=(0,o.l)(m,"unstable_getServerSideProps"),S=new r.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/customers",pathname:"/admin/customers",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:m});a()}catch(e){a(e)}})},5864:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a=s(997),r=s(6689),n=s(6729),o=s.n(n);function i({customers:e}){let t=(0,r.useMemo)(()=>{if(!e||0===e.length)return{totalCustomers:0,newThisMonth:0,activeCustomers:0,averageBookings:0,topCustomers:[],growthData:[]};let t=new Date,s=new Date(t.getFullYear(),t.getMonth(),1);t.getFullYear(),t.getMonth();let a=e.length,r=e.filter(e=>new Date(e.created_at)>=s).length,n=e.filter(e=>(e.total_bookings||0)>0).length,o=e.reduce((e,t)=>e+(t.total_bookings||0),0),i=a>0?(o/a).toFixed(1):0,l=e.filter(e=>(e.total_bookings||0)>0).sort((e,t)=>(t.total_bookings||0)-(e.total_bookings||0)).slice(0,5).map(e=>({name:`${e.first_name||""} ${e.last_name||""}`.trim(),bookings:e.total_bookings||0,email:e.email})),c=[];for(let s=5;s>=0;s--){let a=new Date(t.getFullYear(),t.getMonth()-s,1),r=new Date(t.getFullYear(),t.getMonth()-s+1,1),n=e.filter(e=>{let t=new Date(e.created_at);return t>=a&&t<r}).length;c.push({month:a.toLocaleDateString("en-US",{month:"short",year:"numeric"}),customers:n})}return{totalCustomers:a,newThisMonth:r,activeCustomers:n,averageBookings:i,topCustomers:l,growthData:c}},[e]);return(0,a.jsxs)("div",{className:o().analyticsContainer,children:[(0,a.jsxs)("div",{className:o().analyticsHeader,children:[a.jsx("h2",{children:"Customer Analytics"}),a.jsx("p",{children:"Overview of your customer base and growth trends"})]}),(0,a.jsxs)("div",{className:o().metricsGrid,children:[(0,a.jsxs)("div",{className:o().metricCard,children:[a.jsx("div",{className:o().metricValue,children:t.totalCustomers}),a.jsx("div",{className:o().metricLabel,children:"Total Customers"}),a.jsx("div",{className:o().metricIcon,children:"\uD83D\uDC65"})]}),(0,a.jsxs)("div",{className:o().metricCard,children:[a.jsx("div",{className:o().metricValue,children:t.newThisMonth}),a.jsx("div",{className:o().metricLabel,children:"New This Month"}),a.jsx("div",{className:o().metricIcon,children:"✨"})]}),(0,a.jsxs)("div",{className:o().metricCard,children:[a.jsx("div",{className:o().metricValue,children:t.activeCustomers}),a.jsx("div",{className:o().metricLabel,children:"Active Customers"}),a.jsx("div",{className:o().metricIcon,children:"\uD83C\uDFAF"})]}),(0,a.jsxs)("div",{className:o().metricCard,children:[a.jsx("div",{className:o().metricValue,children:t.averageBookings}),a.jsx("div",{className:o().metricLabel,children:"Avg. Bookings per Customer"}),a.jsx("div",{className:o().metricIcon,children:"\uD83D\uDCCA"})]})]}),(0,a.jsxs)("div",{className:o().chartsGrid,children:[(0,a.jsxs)("div",{className:o().chartCard,children:[a.jsx("h3",{children:"Customer Growth (Last 6 Months)"}),a.jsx("div",{className:o().barChart,children:t.growthData.map((e,s)=>{let r=Math.max(...t.growthData.map(e=>e.customers)),n=r>0?e.customers/r*100:0;return(0,a.jsxs)("div",{className:o().barContainer,children:[a.jsx("div",{className:o().bar,style:{height:`${n}%`},title:`${e.customers} customers`}),a.jsx("div",{className:o().barLabel,children:e.month}),a.jsx("div",{className:o().barValue,children:e.customers})]},s)})})]}),(0,a.jsxs)("div",{className:o().chartCard,children:[a.jsx("h3",{children:"Top Customers by Bookings"}),t.topCustomers.length>0?a.jsx("div",{className:o().topCustomersList,children:t.topCustomers.map((e,t)=>(0,a.jsxs)("div",{className:o().topCustomerItem,children:[(0,a.jsxs)("div",{className:o().customerRank,children:["#",t+1]}),(0,a.jsxs)("div",{className:o().customerInfo,children:[a.jsx("div",{className:o().customerName,children:e.name}),a.jsx("div",{className:o().customerEmail,children:e.email})]}),(0,a.jsxs)("div",{className:o().customerBookings,children:[a.jsx("span",{className:o().bookingCount,children:e.bookings}),a.jsx("span",{className:o().bookingLabel,children:"bookings"})]})]},t))}):a.jsx("div",{className:o().emptyState,children:a.jsx("p",{children:"No booking data available yet"})})]})]}),(0,a.jsxs)("div",{className:o().insightsGrid,children:[(0,a.jsxs)("div",{className:o().insightCard,children:[a.jsx("h4",{children:"Customer Engagement"}),a.jsx("div",{className:o().insightContent,children:(0,a.jsxs)("div",{className:o().engagementStat,children:[(0,a.jsxs)("span",{className:o().percentage,children:[t.totalCustomers>0?Math.round(t.activeCustomers/t.totalCustomers*100):0,"%"]}),a.jsx("span",{className:o().engagementLabel,children:"of customers have made bookings"})]})})]}),(0,a.jsxs)("div",{className:o().insightCard,children:[a.jsx("h4",{children:"Growth Trend"}),a.jsx("div",{className:o().insightContent,children:a.jsx("div",{className:o().trendStat,children:t.growthData.length>=2&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{className:o().trendValue,children:[t.growthData[t.growthData.length-1].customers-t.growthData[t.growthData.length-2].customers>0?"+":"",t.growthData[t.growthData.length-1].customers-t.growthData[t.growthData.length-2].customers]}),a.jsx("span",{className:o().trendLabel,children:"customers vs last month"})]})})})]}),(0,a.jsxs)("div",{className:o().insightCard,children:[a.jsx("h4",{children:"Customer Retention"}),a.jsx("div",{className:o().insightContent,children:(0,a.jsxs)("div",{className:o().retentionStat,children:[a.jsx("span",{className:o().retentionValue,children:t.topCustomers.length>0?t.topCustomers[0].bookings:0}),a.jsx("span",{className:o().retentionLabel,children:"max bookings by single customer"})]})})]})]})]})}},4796:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a=s(997),r=s(6689),n=s(2765),o=s.n(n);function i({customer:e,onClose:t,onUpdate:s,onDelete:n}){let[i,l]=(0,r.useState)(!1),[c,m]=(0,r.useState)(!1),[d,u]=(0,r.useState)([]),[_,h]=(0,r.useState)({first_name:"",last_name:"",email:"",phone:"",phone_secondary:"",date_of_birth:"",address:"",notes:""}),C=(e,t)=>{h(s=>({...s,[e]:t}))},x=async()=>{try{m(!0);let t=localStorage.getItem("admin-token"),a=await fetch(`/api/admin/customers/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(_)});if(!a.ok)throw Error("Failed to update customer");let r=await a.json();s(r.customer),l(!1)}catch(e){console.error("Error updating customer:",e),alert("Failed to update customer. Please try again.")}finally{m(!1)}},g=async()=>{if(confirm(`Are you sure you want to delete ${e.first_name} ${e.last_name}? This action cannot be undone.`))try{m(!0);let t=localStorage.getItem("admin-token"),s=await fetch(`/api/admin/customers/${e.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to delete customer")}n(e.id)}catch(e){console.error("Error deleting customer:",e),alert(e.message||"Failed to delete customer. Please try again.")}finally{m(!1)}};return e?a.jsx("div",{className:o().modalOverlay,onClick:t,children:(0,a.jsxs)("div",{className:o().modal,onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:o().modalHeader,children:[(0,a.jsxs)("h2",{children:[e.first_name," ",e.last_name]}),(0,a.jsxs)("div",{className:o().modalActions,children:[i?(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:x,className:o().saveButton,disabled:c,children:c?"Saving...":"\uD83D\uDCBE Save"}),a.jsx("button",{onClick:()=>l(!1),className:o().cancelButton,children:"❌ Cancel"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:()=>l(!0),className:o().editButton,children:"✏️ Edit"}),a.jsx("button",{onClick:g,className:o().deleteButton,disabled:c,children:"\uD83D\uDDD1️ Delete"})]}),a.jsx("button",{onClick:t,className:o().closeButton,children:"✕"})]})]}),(0,a.jsxs)("div",{className:o().modalContent,children:[(0,a.jsxs)("div",{className:o().customerInfo,children:[a.jsx("h3",{children:"Customer Information"}),(0,a.jsxs)("div",{className:o().infoGrid,children:[(0,a.jsxs)("div",{className:o().infoGroup,children:[a.jsx("label",{children:"First Name"}),i?a.jsx("input",{type:"text",value:_.first_name,onChange:e=>C("first_name",e.target.value)}):a.jsx("span",{children:e.first_name})]}),(0,a.jsxs)("div",{className:o().infoGroup,children:[a.jsx("label",{children:"Last Name"}),i?a.jsx("input",{type:"text",value:_.last_name,onChange:e=>C("last_name",e.target.value)}):a.jsx("span",{children:e.last_name})]}),(0,a.jsxs)("div",{className:o().infoGroup,children:[a.jsx("label",{children:"Email"}),i?a.jsx("input",{type:"email",value:_.email,onChange:e=>C("email",e.target.value)}):a.jsx("span",{children:e.email})]}),(0,a.jsxs)("div",{className:o().infoGroup,children:[a.jsx("label",{children:"Phone"}),i?a.jsx("input",{type:"text",value:_.phone,onChange:e=>C("phone",e.target.value)}):a.jsx("span",{children:e.phone})]}),(0,a.jsxs)("div",{className:o().infoGroup,children:[a.jsx("label",{children:"Date of Birth"}),i?a.jsx("input",{type:"date",value:_.date_of_birth,onChange:e=>C("date_of_birth",e.target.value)}):a.jsx("span",{children:e.date_of_birth?new Date(e.date_of_birth).toLocaleDateString():"Not provided"})]}),(0,a.jsxs)("div",{className:o().infoGroup,children:[a.jsx("label",{children:"Address"}),i?a.jsx("textarea",{value:_.address,onChange:e=>C("address",e.target.value),rows:3}):a.jsx("span",{children:e.address||"Not provided"})]})]}),(0,a.jsxs)("div",{className:o().infoGroup,children:[a.jsx("label",{children:"Notes"}),i?a.jsx("textarea",{value:_.notes,onChange:e=>C("notes",e.target.value),rows:4,placeholder:"Add notes about this customer..."}):a.jsx("span",{children:e.notes||"No notes"})]})]}),(0,a.jsxs)("div",{className:o().customerStats,children:[a.jsx("h3",{children:"Customer Statistics"}),(0,a.jsxs)("div",{className:o().statsGrid,children:[(0,a.jsxs)("div",{className:o().statItem,children:[a.jsx("span",{className:o().statValue,children:e.total_bookings||0}),a.jsx("span",{className:o().statLabel,children:"Total Bookings"})]}),(0,a.jsxs)("div",{className:o().statItem,children:[a.jsx("span",{className:o().statValue,children:new Date(e.created_at).toLocaleDateString()}),a.jsx("span",{className:o().statLabel,children:"Customer Since"})]})]})]}),d.length>0&&(0,a.jsxs)("div",{className:o().bookingHistory,children:[a.jsx("h3",{children:"Recent Bookings"}),a.jsx("div",{className:o().bookingsList,children:d.slice(0,5).map(e=>(0,a.jsxs)("div",{className:o().bookingItem,children:[a.jsx("div",{className:o().bookingDate,children:new Date(e.start_time).toLocaleDateString()}),a.jsx("div",{className:o().bookingService,children:e.service_name||"Service"}),a.jsx("div",{className:o().bookingStatus,children:e.status})]},e.id))})]})]})]})}):null}},1005:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>g});var r=s(997),n=s(6689),o=s(968),i=s.n(o),l=s(1664),c=s.n(l),m=s(4845),d=s(8568),u=s(4796),_=s(5864),h=s(5273),C=s.n(h),x=e([m]);function g(){let{user:e,loading:t}=(0,d.a)(),[s,a]=(0,n.useState)([]),[o,l]=(0,n.useState)([]),[h,x]=(0,n.useState)(!0),[g,j]=(0,n.useState)(""),[p,v]=(0,n.useState)("created_at"),[b,N]=(0,n.useState)("desc"),[S,y]=(0,n.useState)("all"),[k,f]=(0,n.useState)("grid"),[A,D]=(0,n.useState)(null),[w,B]=(0,n.useState)(!1),[L,M]=(0,n.useState)(!1),[I,P]=(0,n.useState)(1),[G]=(0,n.useState)(12),V=Math.ceil(o.length/G),E=(I-1)*G,F=E+G,O=o.slice(E,F),H=e=>{D(e),B(!0)};return t||h?r.jsx(m.Z,{children:(0,r.jsxs)("div",{className:C().loadingContainer,children:[r.jsx("div",{className:C().loadingSpinner}),r.jsx("p",{children:"Loading customers..."})]})}):(0,r.jsxs)(m.Z,{children:[r.jsx(i(),{children:r.jsx("title",{children:"Customers Management - Ocean Soul Sparkles Admin"})}),(0,r.jsxs)("div",{className:C().customersContainer,children:[(0,r.jsxs)("header",{className:C().header,children:[(0,r.jsxs)("div",{className:C().headerContent,children:[r.jsx("h1",{children:"Customer Management"}),r.jsx("p",{children:"Manage your customer database and view customer analytics"})]}),(0,r.jsxs)("div",{className:C().headerActions,children:[r.jsx("button",{onClick:()=>M(!L),className:C().analyticsBtn,children:"\uD83D\uDCCA Analytics"}),r.jsx("button",{onClick:()=>{let e=[["Name","Email","Phone","Total Bookings","Created Date"],...o.map(e=>[`${e.first_name||""} ${e.last_name||""}`,e.email||"",e.phone||"",e.total_bookings||0,new Date(e.created_at).toLocaleDateString()])].map(e=>e.join(",")).join("\n"),t=new Blob([e],{type:"text/csv"}),s=window.URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download=`customers-${new Date().toISOString().split("T")[0]}.csv`,a.click(),window.URL.revokeObjectURL(s)},className:C().exportBtn,children:"\uD83D\uDCE5 Export CSV"}),r.jsx(c(),{href:"/admin/customers/new",className:C().newCustomerBtn,children:"➕ New Customer"})]})]}),L&&r.jsx(_.Z,{customers:s}),(0,r.jsxs)("div",{className:C().controlsPanel,children:[r.jsx("div",{className:C().searchContainer,children:r.jsx("input",{type:"text",placeholder:"Search by name, email, or phone...",value:g,onChange:e=>j(e.target.value),className:C().searchInput})}),(0,r.jsxs)("div",{className:C().filtersContainer,children:[(0,r.jsxs)("select",{value:S,onChange:e=>y(e.target.value),className:C().filterSelect,children:[r.jsx("option",{value:"all",children:"All Customers"}),r.jsx("option",{value:"recent",children:"Recent (Last 7 days)"}),r.jsx("option",{value:"active",children:"Active (Has bookings)"}),r.jsx("option",{value:"inactive",children:"Inactive (No bookings)"})]}),(0,r.jsxs)("select",{value:p,onChange:e=>v(e.target.value),className:C().sortSelect,children:[r.jsx("option",{value:"created_at",children:"Sort by Date Added"}),r.jsx("option",{value:"name",children:"Sort by Name"}),r.jsx("option",{value:"email",children:"Sort by Email"}),r.jsx("option",{value:"bookings",children:"Sort by Bookings"})]}),r.jsx("button",{onClick:()=>N("asc"===b?"desc":"asc"),className:C().sortOrderBtn,title:`Sort ${"asc"===b?"Descending":"Ascending"}`,children:"asc"===b?"↑":"↓"})]}),(0,r.jsxs)("div",{className:C().viewControls,children:[r.jsx("button",{onClick:()=>f("grid"),className:`${C().viewBtn} ${"grid"===k?C().active:""}`,children:"⊞ Grid"}),r.jsx("button",{onClick:()=>f("table"),className:`${C().viewBtn} ${"table"===k?C().active:""}`,children:"☰ Table"})]})]}),r.jsx("div",{className:C().resultsInfo,children:(0,r.jsxs)("span",{children:["Showing ",E+1,"-",Math.min(F,o.length)," of ",o.length," customers"]})}),0===O.length?(0,r.jsxs)("div",{className:C().emptyState,children:[r.jsx("div",{className:C().emptyIcon,children:"\uD83D\uDC65"}),r.jsx("h3",{children:"No customers found"}),r.jsx("p",{children:g||"all"!==S?"Try adjusting your search or filters":"Get started by adding your first customer"}),!g&&"all"===S&&r.jsx(c(),{href:"/admin/customers/new",className:C().emptyActionBtn,children:"➕ Add First Customer"})]}):"grid"===k?r.jsx("div",{className:C().customersGrid,children:O.map(e=>(0,r.jsxs)("div",{className:C().customerCard,onClick:()=>H(e),children:[(0,r.jsxs)("div",{className:C().customerHeader,children:[(0,r.jsxs)("h3",{children:[e.first_name," ",e.last_name]}),(0,r.jsxs)("span",{className:C().bookingCount,children:[e.total_bookings||0," bookings"]})]}),(0,r.jsxs)("div",{className:C().customerDetails,children:[r.jsx("p",{className:C().customerEmail,children:e.email}),r.jsx("p",{className:C().customerPhone,children:e.phone}),e.notes&&r.jsx("p",{className:C().customerNotes,children:e.notes})]}),r.jsx("div",{className:C().customerMeta,children:(0,r.jsxs)("span",{className:C().customerDate,children:["Added ",new Date(e.created_at).toLocaleDateString()]})})]},e.id))}):r.jsx("div",{className:C().customersTable,children:(0,r.jsxs)("table",{children:[r.jsx("thead",{children:(0,r.jsxs)("tr",{children:[r.jsx("th",{children:"Name"}),r.jsx("th",{children:"Email"}),r.jsx("th",{children:"Phone"}),r.jsx("th",{children:"Bookings"}),r.jsx("th",{children:"Added"}),r.jsx("th",{children:"Actions"})]})}),r.jsx("tbody",{children:O.map(e=>(0,r.jsxs)("tr",{children:[r.jsx("td",{children:(0,r.jsxs)("div",{className:C().customerName,children:[e.first_name," ",e.last_name]})}),r.jsx("td",{children:e.email}),r.jsx("td",{children:e.phone}),r.jsx("td",{children:r.jsx("span",{className:C().bookingBadge,children:e.total_bookings||0})}),r.jsx("td",{children:new Date(e.created_at).toLocaleDateString()}),r.jsx("td",{children:(0,r.jsxs)("div",{className:C().tableActions,children:[r.jsx("button",{onClick:()=>H(e),className:C().viewBtn,children:"View"}),r.jsx(c(),{href:`/admin/customers/${e.id}/edit`,className:C().editBtn,children:"Edit"})]})})]},e.id))})]})}),V>1&&(0,r.jsxs)("div",{className:C().pagination,children:[r.jsx("button",{onClick:()=>P(e=>Math.max(e-1,1)),disabled:1===I,className:C().paginationBtn,children:"← Previous"}),(0,r.jsxs)("div",{className:C().paginationInfo,children:["Page ",I," of ",V]}),r.jsx("button",{onClick:()=>P(e=>Math.min(e+1,V)),disabled:I===V,className:C().paginationBtn,children:"Next →"})]}),w&&A&&r.jsx(u.Z,{customer:A,onClose:()=>{B(!1),D(null)},onUpdate:e=>{a(t=>t.map(t=>t.id===e.id?e:t)),B(!1),D(null)},onDelete:e=>{a(t=>t.filter(t=>t.id!==e)),B(!1),D(null)}})]})]})}m=(x.then?(await x)():x)[0],a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2899,6212,1664,7441],()=>s(5236));module.exports=a})();