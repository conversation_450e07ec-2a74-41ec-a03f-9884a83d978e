"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[202],{4202:function(t,e,n){n.d(e,{endPOSPaymentOperation:function(){return a},startPOSPaymentOperation:function(){return o}});let r=null;function o(){try{return r=setTimeout(()=>{a(),console.warn("POS payment operation timed out for security")},3e5),console.log("POS payment operation started with security protection"),!0}catch(t){return console.error("Error starting POS payment operation:",t),!1}}function a(){try{return r&&(clearTimeout(r),r=null),console.log("POS payment operation ended"),!0}catch(t){return console.error("Error ending POS payment operation:",t),!1}}}}]);