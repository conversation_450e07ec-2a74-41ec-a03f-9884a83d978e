import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyCustomerToken, logCustomerActivity } from '../../../lib/auth/customer-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Get token from Authorization header or cookie
  const authHeader = req.headers.authorization;
  const token = authHeader?.startsWith('Bearer ') 
    ? authHeader.substring(7)
    : req.cookies['customer-token'];

  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // Verify customer authentication
  const authResult = await verifyCustomerToken(token);
  if (!authResult.valid || !authResult.user) {
    return res.status(401).json({ error: 'Invalid authentication token' });
  }

  const customer = authResult.user;

  try {
    if (req.method === 'GET') {
      return await handleGetProfile(req, res, customer);
    } else if (req.method === 'PUT') {
      return await handleUpdateProfile(req, res, customer);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Customer profile API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetProfile(req: NextApiRequest, res: NextApiResponse, customer: any) {
  // Get customer profile with preferences and loyalty info
  const { data: customerData, error: customerError } = await supabase
    .from('customers')
    .select(`
      id,
      first_name,
      last_name,
      email,
      phone,
      date_of_birth,
      address,
      created_at,
      customer_preferences (
        notification_email,
        notification_sms,
        notification_booking_reminders,
        notification_promotions,
        preferred_artist_id,
        preferred_time_slots,
        preferred_services,
        booking_notes
      ),
      customer_loyalty (
        points_balance,
        total_points_earned,
        total_points_redeemed,
        tier_level,
        tier_start_date,
        next_tier_points,
        lifetime_spend,
        referral_code
      )
    `)
    .eq('id', customer.customerId)
    .single();

  if (customerError || !customerData) {
    console.error('Customer profile query error:', customerError);
    return res.status(500).json({ error: 'Failed to fetch customer profile' });
  }

  // Get booking statistics
  const { count: totalBookings } = await supabase
    .from('bookings')
    .select('*', { count: 'exact', head: true })
    .eq('customer_id', customer.customerId);

  const { count: completedBookings } = await supabase
    .from('bookings')
    .select('*', { count: 'exact', head: true })
    .eq('customer_id', customer.customerId)
    .eq('status', 'completed');

  // Get recent bookings
  const { data: recentBookings } = await supabase
    .from('bookings')
    .select(`
      id,
      booking_date,
      start_time,
      status,
      total_amount,
      services (name),
      artist_profiles!assigned_artist_id (name)
    `)
    .eq('customer_id', customer.customerId)
    .order('booking_date', { ascending: false })
    .limit(5);

  // Get preferred artist info if set
  let preferredArtist = null;
  if (customerData.customer_preferences?.preferred_artist_id) {
    const { data: artistData } = await supabase
      .from('artist_profiles')
      .select('id, name, specializations')
      .eq('id', customerData.customer_preferences.preferred_artist_id)
      .single();
    preferredArtist = artistData;
  }

  const profile = {
    id: customerData.id,
    first_name: customerData.first_name,
    last_name: customerData.last_name,
    email: customerData.email,
    phone: customerData.phone,
    date_of_birth: customerData.date_of_birth,
    address: customerData.address,
    created_at: customerData.created_at,
    preferences: {
      notification_email: customerData.customer_preferences?.notification_email ?? true,
      notification_sms: customerData.customer_preferences?.notification_sms ?? true,
      notification_booking_reminders: customerData.customer_preferences?.notification_booking_reminders ?? true,
      notification_promotions: customerData.customer_preferences?.notification_promotions ?? false,
      preferred_artist: preferredArtist,
      preferred_time_slots: customerData.customer_preferences?.preferred_time_slots || [],
      preferred_services: customerData.customer_preferences?.preferred_services || [],
      booking_notes: customerData.customer_preferences?.booking_notes
    },
    loyalty: {
      points_balance: customerData.customer_loyalty?.points_balance || 0,
      total_points_earned: customerData.customer_loyalty?.total_points_earned || 0,
      total_points_redeemed: customerData.customer_loyalty?.total_points_redeemed || 0,
      tier_level: customerData.customer_loyalty?.tier_level || 'Bronze',
      tier_start_date: customerData.customer_loyalty?.tier_start_date,
      next_tier_points: customerData.customer_loyalty?.next_tier_points || 100,
      lifetime_spend: customerData.customer_loyalty?.lifetime_spend || 0,
      referral_code: customerData.customer_loyalty?.referral_code
    },
    statistics: {
      total_bookings: totalBookings || 0,
      completed_bookings: completedBookings || 0,
      recent_bookings: recentBookings?.map(booking => ({
        id: booking.id,
        date: booking.booking_date,
        time: booking.start_time,
        status: booking.status,
        amount: booking.total_amount,
        service_name: booking.services?.name,
        artist_name: booking.artist_profiles?.name
      })) || []
    }
  };

  return res.status(200).json({ profile });
}

async function handleUpdateProfile(req: NextApiRequest, res: NextApiResponse, customer: any) {
  const {
    first_name,
    last_name,
    phone,
    date_of_birth,
    address,
    preferences
  } = req.body;

  // Update customer basic info
  const customerUpdates: any = {};
  if (first_name !== undefined) customerUpdates.first_name = first_name;
  if (last_name !== undefined) customerUpdates.last_name = last_name;
  if (phone !== undefined) customerUpdates.phone = phone;
  if (date_of_birth !== undefined) customerUpdates.date_of_birth = date_of_birth;
  if (address !== undefined) customerUpdates.address = address;

  if (Object.keys(customerUpdates).length > 0) {
    customerUpdates.updated_at = new Date().toISOString();
    
    const { error: customerUpdateError } = await supabase
      .from('customers')
      .update(customerUpdates)
      .eq('id', customer.customerId);

    if (customerUpdateError) {
      console.error('Customer update error:', customerUpdateError);
      return res.status(500).json({ error: 'Failed to update customer profile' });
    }
  }

  // Update customer preferences
  if (preferences) {
    const preferencesUpdates: any = {};
    if (preferences.notification_email !== undefined) preferencesUpdates.notification_email = preferences.notification_email;
    if (preferences.notification_sms !== undefined) preferencesUpdates.notification_sms = preferences.notification_sms;
    if (preferences.notification_booking_reminders !== undefined) preferencesUpdates.notification_booking_reminders = preferences.notification_booking_reminders;
    if (preferences.notification_promotions !== undefined) preferencesUpdates.notification_promotions = preferences.notification_promotions;
    if (preferences.preferred_artist_id !== undefined) preferencesUpdates.preferred_artist_id = preferences.preferred_artist_id;
    if (preferences.preferred_time_slots !== undefined) preferencesUpdates.preferred_time_slots = preferences.preferred_time_slots;
    if (preferences.preferred_services !== undefined) preferencesUpdates.preferred_services = preferences.preferred_services;
    if (preferences.booking_notes !== undefined) preferencesUpdates.booking_notes = preferences.booking_notes;

    if (Object.keys(preferencesUpdates).length > 0) {
      preferencesUpdates.updated_at = new Date().toISOString();

      const { error: preferencesError } = await supabase
        .from('customer_preferences')
        .upsert({
          customer_id: customer.customerId,
          ...preferencesUpdates
        });

      if (preferencesError) {
        console.error('Customer preferences update error:', preferencesError);
        return res.status(500).json({ error: 'Failed to update customer preferences' });
      }
    }
  }

  // Log activity
  await logCustomerActivity(
    customer.customerId,
    'profile_update',
    'Customer updated profile',
    req.headers['x-forwarded-for'] as string
  );

  return res.status(200).json({
    success: true,
    message: 'Profile updated successfully'
  });
}
