(()=>{var e={};e.id=6296,e.ids=[6296,660],e.modules={1242:e=>{e.exports={purchaseOrders:"PurchaseOrders_purchaseOrders__nd7hq",header:"PurchaseOrders_header__w_l_v",headerContent:"PurchaseOrders_headerContent__PVf2q",title:"PurchaseOrders_title__utnll",subtitle:"PurchaseOrders_subtitle__1wcrJ",headerActions:"PurchaseOrders_headerActions__SQeEm",addBtn:"PurchaseOrders_addBtn__KPfpC",controls:"PurchaseOrders_controls__9p8X2",searchSection:"PurchaseOrders_searchSection__I6PTA",searchInput:"PurchaseOrders_searchInput__R_JTF",filters:"PurchaseOrders_filters__b5Bor",filterSelect:"PurchaseOrders_filterSelect__R7jx2",sortSelect:"PurchaseOrders_sortSelect__L3GvC",ordersContainer:"PurchaseOrders_ordersContainer__5dBmg",loading:"PurchaseOrders_loading__Fmcxh",loadingSpinner:"PurchaseOrders_loadingSpinner__KO_Gz",spin:"PurchaseOrders_spin__HjZwU",emptyState:"PurchaseOrders_emptyState__O5SNT",emptyIcon:"PurchaseOrders_emptyIcon__JM1LS",addFirstBtn:"PurchaseOrders_addFirstBtn__vMTvw",ordersGrid:"PurchaseOrders_ordersGrid__RZktU",orderCard:"PurchaseOrders_orderCard__eJr_j",cardHeader:"PurchaseOrders_cardHeader__kKvK9",orderInfo:"PurchaseOrders_orderInfo__7lOLc",poNumber:"PurchaseOrders_poNumber___XsZw",statusBadge:"PurchaseOrders_statusBadge__40_vf",statusDraft:"PurchaseOrders_statusDraft__nhguh",statusSent:"PurchaseOrders_statusSent__Y5Ryb",statusConfirmed:"PurchaseOrders_statusConfirmed__wp_Rp",statusReceived:"PurchaseOrders_statusReceived__Z_SG3",statusCancelled:"PurchaseOrders_statusCancelled__BYw7u",cardBody:"PurchaseOrders_cardBody__JNNls",supplierInfo:"PurchaseOrders_supplierInfo__GTMI5",orderDetails:"PurchaseOrders_orderDetails__jguk6",detailItem:"PurchaseOrders_detailItem__emrhK",label:"PurchaseOrders_label__acljc",value:"PurchaseOrders_value__Trteb",notes:"PurchaseOrders_notes__klfoF",notesText:"PurchaseOrders_notesText__xxhwJ",cardFooter:"PurchaseOrders_cardFooter__iRQ7M",cardActions:"PurchaseOrders_cardActions__VMaHv",viewBtn:"PurchaseOrders_viewBtn__QBUc6",editBtn:"PurchaseOrders_editBtn__l0Pkx",receiveBtn:"PurchaseOrders_receiveBtn__qOMk6",deleteBtn:"PurchaseOrders_deleteBtn__5Sgw6",cardMeta:"PurchaseOrders_cardMeta__G_4c6",createdBy:"PurchaseOrders_createdBy__EuC7t",createdDate:"PurchaseOrders_createdDate__W_8eI",purchaseOrderForm:"PurchaseOrders_purchaseOrderForm__Jo6eJ",formSection:"PurchaseOrders_formSection__PPc2x",sectionHeader:"PurchaseOrders_sectionHeader__GOGWN",addItemBtn:"PurchaseOrders_addItemBtn__Xw7Of",formGrid:"PurchaseOrders_formGrid__6K2p_",formGroup:"PurchaseOrders_formGroup__LyNDG",fullWidth:"PurchaseOrders_fullWidth__3t1vV",formLabel:"PurchaseOrders_formLabel__k12f1",formInput:"PurchaseOrders_formInput__fCTM_",formTextarea:"PurchaseOrders_formTextarea__pvtWy",formSelect:"PurchaseOrders_formSelect__j_xYw",emptyItems:"PurchaseOrders_emptyItems__kHx1F",itemsList:"PurchaseOrders_itemsList__7hKjk",itemRow:"PurchaseOrders_itemRow__qpnoC",itemFields:"PurchaseOrders_itemFields__5hnFe",totalCost:"PurchaseOrders_totalCost__5x9Ve",removeItemBtn:"PurchaseOrders_removeItemBtn__I3BZk",orderSummary:"PurchaseOrders_orderSummary__ZpQ7a",summaryRow:"PurchaseOrders_summaryRow__bgIZv",totalRow:"PurchaseOrders_totalRow__tlYfR",formActions:"PurchaseOrders_formActions__BL5KV",saveBtn:"PurchaseOrders_saveBtn__mjzET",cancelBtn:"PurchaseOrders_cancelBtn__J1l9Y"}},5044:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>f,default:()=>h,getServerSideProps:()=>p,getStaticPaths:()=>_,getStaticProps:()=>m,reportWebVitals:()=>x,routeModule:()=>y,unstable_getServerProps:()=>g,unstable_getServerSideProps:()=>O,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>P});var a=t(7093),d=t(5244),i=t(1323),n=t(2899),o=t.n(n),c=t(6814),l=t(5978),u=e([c,l]);[c,l]=u.then?(await u)():u;let h=(0,i.l)(l,"default"),m=(0,i.l)(l,"getStaticProps"),_=(0,i.l)(l,"getStaticPaths"),p=(0,i.l)(l,"getServerSideProps"),f=(0,i.l)(l,"config"),x=(0,i.l)(l,"reportWebVitals"),P=(0,i.l)(l,"unstable_getStaticProps"),v=(0,i.l)(l,"unstable_getStaticPaths"),j=(0,i.l)(l,"unstable_getStaticParams"),g=(0,i.l)(l,"unstable_getServerProps"),O=(0,i.l)(l,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:d.x.PAGES,page:"/admin/purchase-orders/new",pathname:"/admin/purchase-orders/new",bundlePath:"",filename:""},components:{App:c.default,Document:o()},userland:l});s()}catch(e){s(e)}})},218:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.d(r,{Z:()=>m});var a=t(997),d=t(6689),i=t(1163),n=t(1664),o=t.n(n),c=t(3590),l=t(1242),u=t.n(l),h=e([c]);function m(){let e=(0,i.useRouter)(),[r,t]=(0,d.useState)(!1),[s,n]=(0,d.useState)([]),[l,h]=(0,d.useState)([]),[m,_]=(0,d.useState)(""),[p,f]=(0,d.useState)(new Date().toISOString().split("T")[0]),[x,P]=(0,d.useState)(""),[v,j]=(0,d.useState)(""),[g,O]=(0,d.useState)([]),y=e=>{O(g.filter((r,t)=>t!==e))},S=(e,r,t)=>{let s=[...g];if(s[e]={...s[e],[r]:t},"inventoryId"===r){let r=l.find(e=>e.id===t);r&&(s[e].productName=r.name)}("quantity"===r||"unitCost"===r)&&(s[e].totalCost=s[e].quantity*s[e].unitCost),O(s)},b=()=>g.reduce((e,r)=>e+r.totalCost,0),N=()=>.1*b(),C=()=>{let e=[];return m||e.push("Please select a supplier"),p||e.push("Order date is required"),0===g.length&&e.push("At least one item is required"),g.forEach((r,t)=>{r.inventoryId||e.push(`Item ${t+1}: Please select a product`),r.quantity<=0&&e.push(`Item ${t+1}: Quantity must be greater than 0`),r.unitCost<=0&&e.push(`Item ${t+1}: Unit cost must be greater than 0`)}),e},I=async r=>{r.preventDefault();let s=C();if(s.length>0){s.forEach(e=>c.toast.error(e));return}t(!0);try{let r=localStorage.getItem("adminToken");if(!r)throw Error("No authentication token found");let t=await fetch("/api/admin/purchase-orders",{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify({supplierId:m,orderDate:p,expectedDeliveryDate:x||null,notes:v.trim()||null,items:g.map(e=>({inventoryId:e.inventoryId,quantity:e.quantity,unitCost:e.unitCost}))})});if(!t.ok){let e=await t.json();throw Error(e.message||"Failed to create purchase order")}let s=await t.json();c.toast.success("Purchase order created successfully"),e.push(`/admin/purchase-orders/${s.purchaseOrder.id}`)}catch(e){console.error("Error creating purchase order:",e),c.toast.error(e instanceof Error?e.message:"Failed to create purchase order")}finally{t(!1)}},w=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e);return(0,a.jsxs)("div",{className:u().purchaseOrderForm,children:[a.jsx("div",{className:u().header,children:(0,a.jsxs)("div",{className:u().headerContent,children:[a.jsx("h1",{className:u().title,children:"Create Purchase Order"}),a.jsx("p",{className:u().subtitle,children:"Create a new purchase order for supplier deliveries"})]})}),(0,a.jsxs)("form",{onSubmit:I,children:[(0,a.jsxs)("div",{className:u().formSection,children:[a.jsx("h3",{children:"Order Information"}),(0,a.jsxs)("div",{className:u().formGrid,children:[(0,a.jsxs)("div",{className:u().formGroup,children:[a.jsx("label",{htmlFor:"supplier",className:u().formLabel,children:"Supplier *"}),(0,a.jsxs)("select",{id:"supplier",value:m,onChange:e=>_(e.target.value),className:u().formSelect,required:!0,children:[a.jsx("option",{value:"",children:"Select a supplier"}),s.map(e=>a.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:u().formGroup,children:[a.jsx("label",{htmlFor:"orderDate",className:u().formLabel,children:"Order Date *"}),a.jsx("input",{type:"date",id:"orderDate",value:p,onChange:e=>f(e.target.value),className:u().formInput,required:!0})]}),(0,a.jsxs)("div",{className:u().formGroup,children:[a.jsx("label",{htmlFor:"expectedDeliveryDate",className:u().formLabel,children:"Expected Delivery Date"}),a.jsx("input",{type:"date",id:"expectedDeliveryDate",value:x,onChange:e=>P(e.target.value),className:u().formInput})]}),(0,a.jsxs)("div",{className:`${u().formGroup} ${u().fullWidth}`,children:[a.jsx("label",{htmlFor:"notes",className:u().formLabel,children:"Notes"}),a.jsx("textarea",{id:"notes",value:v,onChange:e=>j(e.target.value),className:u().formTextarea,placeholder:"Additional notes for this purchase order...",rows:3})]})]})]}),(0,a.jsxs)("div",{className:u().formSection,children:[(0,a.jsxs)("div",{className:u().sectionHeader,children:[a.jsx("h3",{children:"Order Items"}),a.jsx("button",{type:"button",onClick:()=>{O([...g,{inventoryId:"",productName:"",quantity:1,unitCost:0,totalCost:0}])},className:u().addItemBtn,children:"+ Add Item"})]}),0===g.length?a.jsx("div",{className:u().emptyItems,children:a.jsx("p",{children:'No items added yet. Click "Add Item" to get started.'})}):a.jsx("div",{className:u().itemsList,children:g.map((e,r)=>(0,a.jsxs)("div",{className:u().itemRow,children:[(0,a.jsxs)("div",{className:u().itemFields,children:[(0,a.jsxs)("div",{className:u().formGroup,children:[a.jsx("label",{className:u().formLabel,children:"Product *"}),(0,a.jsxs)("select",{value:e.inventoryId,onChange:e=>S(r,"inventoryId",e.target.value),className:u().formSelect,required:!0,children:[a.jsx("option",{value:"",children:"Select a product"}),l.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.name," ",e.sku?`(${e.sku})`:""]},e.id))]})]}),(0,a.jsxs)("div",{className:u().formGroup,children:[a.jsx("label",{className:u().formLabel,children:"Quantity *"}),a.jsx("input",{type:"number",value:e.quantity,onChange:e=>S(r,"quantity",parseInt(e.target.value)||0),className:u().formInput,min:"1",required:!0})]}),(0,a.jsxs)("div",{className:u().formGroup,children:[a.jsx("label",{className:u().formLabel,children:"Unit Cost *"}),a.jsx("input",{type:"number",value:e.unitCost,onChange:e=>S(r,"unitCost",parseFloat(e.target.value)||0),className:u().formInput,min:"0",step:"0.01",required:!0})]}),(0,a.jsxs)("div",{className:u().formGroup,children:[a.jsx("label",{className:u().formLabel,children:"Total"}),a.jsx("div",{className:u().totalCost,children:w(e.totalCost)})]})]}),a.jsx("button",{type:"button",onClick:()=>y(r),className:u().removeItemBtn,title:"Remove item",children:"\xd7"})]},r))})]}),g.length>0&&(0,a.jsxs)("div",{className:u().orderSummary,children:[a.jsx("h3",{children:"Order Summary"}),(0,a.jsxs)("div",{className:u().summaryRow,children:[a.jsx("span",{children:"Subtotal:"}),a.jsx("span",{children:w(b())})]}),(0,a.jsxs)("div",{className:u().summaryRow,children:[a.jsx("span",{children:"Tax (10% GST):"}),a.jsx("span",{children:w(N())})]}),(0,a.jsxs)("div",{className:`${u().summaryRow} ${u().totalRow}`,children:[a.jsx("span",{children:"Total:"}),a.jsx("span",{children:w(b()+N())})]})]}),(0,a.jsxs)("div",{className:u().formActions,children:[a.jsx(o(),{href:"/admin/purchase-orders",className:u().cancelBtn,children:"Cancel"}),a.jsx("button",{type:"submit",className:u().saveBtn,disabled:r||0===g.length,children:r?"Creating...":"Create Purchase Order"})]})]})]})}c=(h.then?(await h)():h)[0],s()}catch(e){s(e)}})},5978:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>m});var a=t(997),d=t(9816),i=t.n(d);t(6689);var n=t(968),o=t.n(n),c=t(8568),l=t(4845),u=t(218),h=e([l,u]);function m(){let{user:e,loading:r}=(0,c.a)();return r?a.jsx(l.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("div",{style:{width:"32px",height:"32px",border:"3px solid #e2e8f0",borderTop:"3px solid #667eea",borderRadius:"50%",animation:"spin 1s linear infinite"}}),a.jsx("p",{style:{color:"#64748b"},children:"Authenticating..."})]})}):e?["DEV","Admin"].includes(e.role)?(0,a.jsxs)(l.Z,{children:[(0,a.jsxs)(o(),{children:[a.jsx("title",{className:"jsx-ff161281ed666c63",children:"Create Purchase Order | Ocean Soul Sparkles Admin"}),a.jsx("meta",{name:"description",content:"Create a new purchase order for supplier deliveries",className:"jsx-ff161281ed666c63"})]}),a.jsx(u.Z,{}),a.jsx(i(),{id:"ff161281ed666c63",children:"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]}):a.jsx(l.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("h2",{style:{color:"#ef4444"},children:"Access Denied"}),a.jsx("p",{style:{color:"#64748b"},children:"You do not have permission to create purchase orders."})]})}):a.jsx(l.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("h2",{style:{color:"#ef4444"},children:"Authentication Required"}),a.jsx("p",{style:{color:"#64748b"},children:"Please log in to create purchase orders."})]})})}[l,u]=h.then?(await h)():h,s()}catch(e){s(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},9816:e=>{"use strict";e.exports=require("styled-jsx/style")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2899,6212,1664,7441],()=>t(5044));module.exports=s})();