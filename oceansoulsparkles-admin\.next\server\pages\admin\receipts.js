(()=>{var e={};e.id=6548,e.ids=[6548,660],e.modules={639:e=>{e.exports={receiptCustomizer:"ReceiptCustomizer_receiptCustomizer___xcqY",templatesSection:"ReceiptCustomizer_templatesSection__mXm3r",sectionHeader:"ReceiptCustomizer_sectionHeader__my2P0",createButton:"ReceiptCustomizer_createButton__4ec8B",description:"ReceiptCustomizer_description__wPe27",templateGrid:"ReceiptCustomizer_templateGrid__jr_H8",templateCard:"ReceiptCustomizer_templateCard__kDgIc",selected:"ReceiptCustomizer_selected__ACnk1",templateHeader:"ReceiptCustomizer_templateHeader__rRww2",templateIcon:"ReceiptCustomizer_templateIcon__Srxl_",templateInfo:"ReceiptCustomizer_templateInfo__h23k2",templateName:"ReceiptCustomizer_templateName__uk3z8",defaultBadge:"ReceiptCustomizer_defaultBadge__SUgXq",templateDescription:"ReceiptCustomizer_templateDescription__TSoFE",templateFeatures:"ReceiptCustomizer_templateFeatures__iwBUY",featureList:"ReceiptCustomizer_featureList__CYj_U",feature:"ReceiptCustomizer_feature__d9Byw",templateMeta:"ReceiptCustomizer_templateMeta__gqViy",templateType:"ReceiptCustomizer_templateType__PNl3i",businessName:"ReceiptCustomizer_businessName__E0OmH",templateActions:"ReceiptCustomizer_templateActions__HQGqx",actionButton:"ReceiptCustomizer_actionButton___jOWP",deleteButton:"ReceiptCustomizer_deleteButton___EWs_",previewSection:"ReceiptCustomizer_previewSection__dIl5u",previewContainer:"ReceiptCustomizer_previewContainer__dzDqg",receiptPreview:"ReceiptCustomizer_receiptPreview__HtJXa",previewHeader:"ReceiptCustomizer_previewHeader__wWiko",previewLabel:"ReceiptCustomizer_previewLabel__JFo7D",refreshBtn:"ReceiptCustomizer_refreshBtn__FgVQ7",previewContent:"ReceiptCustomizer_previewContent__UMFEW",loading:"ReceiptCustomizer_loading__716Gm",previewLoading:"ReceiptCustomizer_previewLoading__vjDL_",loadingSpinner:"ReceiptCustomizer_loadingSpinner__yGDWU",spin:"ReceiptCustomizer_spin__DWbsz",error:"ReceiptCustomizer_error__vbyZV",retryBtn:"ReceiptCustomizer_retryBtn__5NnSo",noTemplates:"ReceiptCustomizer_noTemplates__WWmEb",modalOverlay:"ReceiptCustomizer_modalOverlay__UkZ_k",modal:"ReceiptCustomizer_modal__MMDt6",modalHeader:"ReceiptCustomizer_modalHeader__09P89",closeButton:"ReceiptCustomizer_closeButton__H_6ev",modalForm:"ReceiptCustomizer_modalForm__SBIQZ",formGrid:"ReceiptCustomizer_formGrid__QVIE5",formSection:"ReceiptCustomizer_formSection__TQWnf",formGroup:"ReceiptCustomizer_formGroup___04tR",checkboxGroup:"ReceiptCustomizer_checkboxGroup__GWZbA",checkboxLabel:"ReceiptCustomizer_checkboxLabel__sT_OL",modalActions:"ReceiptCustomizer_modalActions__Rq3tJ",cancelButton:"ReceiptCustomizer_cancelButton__H4wfS",saveButton:"ReceiptCustomizer_saveButton__TI_tT"}},2190:e=>{e.exports={receiptsPage:"Receipts_receiptsPage__An9MG",header:"Receipts_header__Tgjld",headerContent:"Receipts_headerContent__BwCBu",message:"Receipts_message__N57Jh",success:"Receipts_success__V3pS6",error:"Receipts_error__bkQIl",tabNavigation:"Receipts_tabNavigation__7KXHx",tabButton:"Receipts_tabButton__dIsYM",active:"Receipts_active__KWnVy",tabContent:"Receipts_tabContent__hIB4m",tabHeader:"Receipts_tabHeader__O4oln",templatesTab:"Receipts_templatesTab__oo3Xe",settingsTab:"Receipts_settingsTab__PL01P",receiptSettings:"Receipts_receiptSettings__OAeC7",settingsGrid:"Receipts_settingsGrid__QU7aj",settingsSection:"Receipts_settingsSection__VlV_E",settingGroup:"Receipts_settingGroup__waunI",checkboxLabel:"Receipts_checkboxLabel__FnREX",settingsActions:"Receipts_settingsActions__tZqMs",saveButton:"Receipts_saveButton__hE1zC",loading:"Receipts_loading__QxdFG",loadingSpinner:"Receipts_loadingSpinner__OEKwL",spin:"Receipts_spin__qtEpY"}},2665:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>x,default:()=>m,getServerSideProps:()=>_,getStaticPaths:()=>h,getStaticProps:()=>u,reportWebVitals:()=>g,routeModule:()=>N,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>C,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>b});var i=s(7093),n=s(5244),r=s(1323),l=s(2899),c=s.n(l),o=s(6814),d=s(1886),p=e([o,d]);[o,d]=p.then?(await p)():p;let m=(0,r.l)(d,"default"),u=(0,r.l)(d,"getStaticProps"),h=(0,r.l)(d,"getStaticPaths"),_=(0,r.l)(d,"getServerSideProps"),x=(0,r.l)(d,"config"),g=(0,r.l)(d,"reportWebVitals"),b=(0,r.l)(d,"unstable_getStaticProps"),j=(0,r.l)(d,"unstable_getStaticPaths"),v=(0,r.l)(d,"unstable_getStaticParams"),f=(0,r.l)(d,"unstable_getServerProps"),C=(0,r.l)(d,"unstable_getServerSideProps"),N=new i.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/receipts",pathname:"/admin/receipts",bundlePath:"",filename:""},components:{App:o.default,Document:c()},userland:d});a()}catch(e){a(e)}})},3076:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a=s(997),i=s(6689),n=s(639),r=s.n(n);function l({onTemplateSelect:e,selectedTemplate:t,showPreview:s=!0}){let[n,l]=(0,i.useState)([]),[d,p]=(0,i.useState)(!0),[m,u]=(0,i.useState)(null),[h,_]=(0,i.useState)(null),[x,g]=(0,i.useState)(!1),[b,j]=(0,i.useState)(!1),[v,f]=(0,i.useState)(null),C=async()=>{try{p(!0);let s=await fetch("/api/admin/receipts",{headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}});if(!s.ok)throw Error("Failed to load receipt templates");let a=await s.json();if(l(a.templates||[]),!t&&a.templates?.length>0){let t=a.templates.find(e=>e.is_default)||a.templates[0];e?.(t)}}catch(e){console.error("Error loading templates:",e),u(e.message)}finally{p(!1)}},N=t=>{e?.(t)},S=e=>{f(e),j(!0)},R=async s=>{if(s.is_default){alert("Cannot delete the default template");return}if(confirm(`Are you sure you want to delete "${s.name}"?`))try{if(!(await fetch(`/api/admin/receipts?id=${s.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}})).ok)throw Error("Failed to delete template");if(await C(),t?.id===s.id){let t=n.find(e=>e.is_default)||n[0];e?.(t)}}catch(e){console.error("Error deleting template:",e),alert("Failed to delete template. Please try again.")}},w=async e=>{try{let t=v?`/api/admin/receipts?id=${v.id}`:"/api/admin/receipts";if(!(await fetch(t,{method:v?"PUT":"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify(e)})).ok)throw Error("Failed to save template");await C(),g(!1),j(!1),f(null)}catch(e){console.error("Error saving template:",e),alert("Failed to save template. Please try again.")}},y=e=>{switch(e){case"compact":return"\uD83D\uDCC4";case"detailed":return"\uD83D\uDCCB";default:return"\uD83E\uDDFE"}},k=e=>{switch(e){case"compact":return"Minimal receipt with essential information only";case"detailed":return"Comprehensive receipt with all available details";default:return"Standard receipt with balanced information"}};return d?(0,a.jsxs)("div",{className:r().loading,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Loading receipt templates..."})]}):m?(0,a.jsxs)("div",{className:r().error,children:[(0,a.jsxs)("p",{children:["Error loading templates: ",m]}),a.jsx("button",{onClick:C,className:r().retryBtn,children:"Try Again"})]}):(0,a.jsxs)("div",{className:r().receiptCustomizer,children:[(0,a.jsxs)("div",{className:r().templatesSection,children:[(0,a.jsxs)("div",{className:r().sectionHeader,children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{children:"Receipt Templates"}),a.jsx("p",{className:r().description,children:"Choose a receipt template for your transactions. You can customize these templates in the admin settings."})]}),a.jsx("button",{onClick:()=>{g(!0)},className:r().createButton,children:"➕ Create Template"})]}),a.jsx("div",{className:r().templateGrid,children:n.map(e=>(0,a.jsxs)("div",{className:`${r().templateCard} ${t?.id===e.id?r().selected:""}`,onClick:()=>N(e),children:[(0,a.jsxs)("div",{className:r().templateHeader,children:[a.jsx("span",{className:r().templateIcon,children:y(e.template_type)}),(0,a.jsxs)("div",{className:r().templateInfo,children:[a.jsx("h4",{className:r().templateName,children:e.name}),e.is_default&&a.jsx("span",{className:r().defaultBadge,children:"Default"})]})]}),a.jsx("p",{className:r().templateDescription,children:e.description||k(e.template_type)}),a.jsx("div",{className:r().templateFeatures,children:(0,a.jsxs)("div",{className:r().featureList,children:[e.show_customer_details&&a.jsx("span",{className:r().feature,children:"\uD83D\uDC64 Customer Details"}),e.show_service_details&&a.jsx("span",{className:r().feature,children:"\uD83C\uDFA8 Service Info"}),e.show_artist_details&&a.jsx("span",{className:r().feature,children:"✨ Artist Info"}),e.show_payment_details&&a.jsx("span",{className:r().feature,children:"\uD83D\uDCB3 Payment Details"})]})}),(0,a.jsxs)("div",{className:r().templateMeta,children:[a.jsx("span",{className:r().templateType,children:e.template_type.charAt(0).toUpperCase()+e.template_type.slice(1)}),a.jsx("span",{className:r().businessName,children:e.business_name})]}),(0,a.jsxs)("div",{className:r().templateActions,children:[a.jsx("button",{onClick:t=>{t.stopPropagation(),S(e)},className:r().actionButton,title:"Edit Template",children:"✏️"}),!e.is_default&&a.jsx("button",{onClick:t=>{t.stopPropagation(),R(e)},className:`${r().actionButton} ${r().deleteButton}`,title:"Delete Template",children:"\uD83D\uDDD1️"})]})]},e.id))}),0===n.length&&(0,a.jsxs)("div",{className:r().noTemplates,children:[a.jsx("p",{children:"No receipt templates found."}),a.jsx("p",{children:"Default templates will be created automatically."})]})]}),s&&t&&h&&(0,a.jsxs)("div",{className:r().previewSection,children:[a.jsx("h3",{children:"Receipt Preview"}),a.jsx("div",{className:r().previewContainer,children:a.jsx(c,{template:t,data:h})})]}),(x||b)&&a.jsx(o,{template:v,onSave:w,onClose:()=>{g(!1),j(!1),f(null)}})]})}function c({template:e,data:t}){let[s,n]=(0,i.useState)(""),[l,c]=(0,i.useState)(!0),o=async()=>{try{c(!0);let s=await fetch("/api/admin/receipts/preview",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({templateId:e.id,bookingData:t})});if(!s.ok)throw Error("Failed to generate preview");let a=await s.json();n(a.html||"")}catch(e){console.error("Error generating preview:",e),n("<p>Preview not available</p>")}finally{c(!1)}};return l?(0,a.jsxs)("div",{className:r().previewLoading,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Generating preview..."})]}):(0,a.jsxs)("div",{className:r().receiptPreview,children:[(0,a.jsxs)("div",{className:r().previewHeader,children:[a.jsx("span",{className:r().previewLabel,children:"Preview"}),a.jsx("button",{onClick:o,className:r().refreshBtn,title:"Refresh Preview",children:"\uD83D\uDD04"})]}),a.jsx("div",{className:r().previewContent,dangerouslySetInnerHTML:{__html:s}})]})}function o({template:e,onSave:t,onClose:s}){let[n,l]=(0,i.useState)({name:e?.name||"",description:e?.description||"",template_type:e?.template_type||"standard",business_name:e?.business_name||"Ocean Soul Sparkles",business_address:e?.business_address||"",business_phone:e?.business_phone||"",business_email:e?.business_email||"",business_website:e?.business_website||"",business_abn:e?.business_abn||"",show_logo:e?.show_logo??!0,logo_position:e?.logo_position||"center",header_color:e?.header_color||"#667eea",text_color:e?.text_color||"#333333",font_family:e?.font_family||"Arial",font_size:e?.font_size||12,show_customer_details:e?.show_customer_details??!0,show_service_details:e?.show_service_details??!0,show_artist_details:e?.show_artist_details??!0,show_payment_details:e?.show_payment_details??!0,show_booking_notes:e?.show_booking_notes??!1,show_terms_conditions:e?.show_terms_conditions??!0,footer_message:e?.footer_message||"Thank you for choosing Ocean Soul Sparkles!",show_social_media:e?.show_social_media??!1}),c=(e,t)=>{l(s=>({...s,[e]:t}))};return a.jsx("div",{className:r().modalOverlay,children:(0,a.jsxs)("div",{className:r().modal,children:[(0,a.jsxs)("div",{className:r().modalHeader,children:[a.jsx("h3",{children:e?"Edit Template":"Create Template"}),a.jsx("button",{onClick:s,className:r().closeButton,children:"✕"})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(n)},className:r().modalForm,children:[(0,a.jsxs)("div",{className:r().formGrid,children:[(0,a.jsxs)("div",{className:r().formSection,children:[a.jsx("h4",{children:"Basic Information"}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Template Name"}),a.jsx("input",{type:"text",value:n.name,onChange:e=>c("name",e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Description"}),a.jsx("textarea",{value:n.description,onChange:e=>c("description",e.target.value),rows:3})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Template Type"}),(0,a.jsxs)("select",{value:n.template_type,onChange:e=>c("template_type",e.target.value),children:[a.jsx("option",{value:"standard",children:"Standard"}),a.jsx("option",{value:"compact",children:"Compact"}),a.jsx("option",{value:"detailed",children:"Detailed"})]})]})]}),(0,a.jsxs)("div",{className:r().formSection,children:[a.jsx("h4",{children:"Business Information"}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Business Name"}),a.jsx("input",{type:"text",value:n.business_name,onChange:e=>c("business_name",e.target.value)})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Address"}),a.jsx("textarea",{value:n.business_address,onChange:e=>c("business_address",e.target.value),rows:2})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Phone"}),a.jsx("input",{type:"text",value:n.business_phone,onChange:e=>c("business_phone",e.target.value)})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Email"}),a.jsx("input",{type:"email",value:n.business_email,onChange:e=>c("business_email",e.target.value)})]})]}),(0,a.jsxs)("div",{className:r().formSection,children:[a.jsx("h4",{children:"Display Options"}),(0,a.jsxs)("div",{className:r().checkboxGroup,children:[(0,a.jsxs)("label",{className:r().checkboxLabel,children:[a.jsx("input",{type:"checkbox",checked:n.show_customer_details,onChange:e=>c("show_customer_details",e.target.checked)}),a.jsx("span",{children:"Show Customer Details"})]}),(0,a.jsxs)("label",{className:r().checkboxLabel,children:[a.jsx("input",{type:"checkbox",checked:n.show_service_details,onChange:e=>c("show_service_details",e.target.checked)}),a.jsx("span",{children:"Show Service Details"})]}),(0,a.jsxs)("label",{className:r().checkboxLabel,children:[a.jsx("input",{type:"checkbox",checked:n.show_artist_details,onChange:e=>c("show_artist_details",e.target.checked)}),a.jsx("span",{children:"Show Artist Details"})]}),(0,a.jsxs)("label",{className:r().checkboxLabel,children:[a.jsx("input",{type:"checkbox",checked:n.show_payment_details,onChange:e=>c("show_payment_details",e.target.checked)}),a.jsx("span",{children:"Show Payment Details"})]})]})]})]}),(0,a.jsxs)("div",{className:r().modalActions,children:[a.jsx("button",{type:"button",onClick:s,className:r().cancelButton,children:"Cancel"}),a.jsx("button",{type:"submit",className:r().saveButton,children:e?"Update Template":"Create Template"})]})]})]})})}},1886:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>h});var i=s(997),n=s(6689),r=s(968),l=s.n(r),c=s(4845),o=s(3076),d=s(8568),p=s(2190),m=s.n(p),u=e([c]);function h(){let{user:e,loading:t}=(0,d.a)(),[s,a]=(0,n.useState)(null),[r,p]=(0,n.useState)("templates"),[u,h]=(0,n.useState)(!1),[x,g]=(0,n.useState)(null);return t?i.jsx(c.Z,{children:(0,i.jsxs)("div",{className:m().loading,children:[i.jsx("div",{className:m().loadingSpinner}),i.jsx("p",{children:"Loading..."})]})}):(0,i.jsxs)(c.Z,{children:[i.jsx(l(),{children:i.jsx("title",{children:"Receipt Management - Ocean Soul Sparkles Admin"})}),(0,i.jsxs)("div",{className:m().receiptsPage,children:[i.jsx("div",{className:m().header,children:(0,i.jsxs)("div",{className:m().headerContent,children:[i.jsx("h1",{children:"Receipt Management"}),i.jsx("p",{children:"Customize receipt templates and manage receipt settings for your POS system."})]})}),x&&i.jsx("div",{className:`${m().message} ${m()[x.type]}`,children:x.text}),(0,i.jsxs)("div",{className:m().tabNavigation,children:[i.jsx("button",{className:`${m().tabButton} ${"templates"===r?m().active:""}`,onClick:()=>p("templates"),children:"\uD83D\uDCC4 Templates"}),i.jsx("button",{className:`${m().tabButton} ${"settings"===r?m().active:""}`,onClick:()=>p("settings"),children:"⚙️ Settings"})]}),(0,i.jsxs)("div",{className:m().tabContent,children:["templates"===r&&(0,i.jsxs)("div",{className:m().templatesTab,children:[(0,i.jsxs)("div",{className:m().tabHeader,children:[i.jsx("h2",{children:"Receipt Templates"}),i.jsx("p",{children:"Choose and customize receipt templates for different transaction types."})]}),i.jsx(o.Z,{onTemplateSelect:e=>{a(e)},selectedTemplate:s,showPreview:!0})]}),"settings"===r&&(0,i.jsxs)("div",{className:m().settingsTab,children:[(0,i.jsxs)("div",{className:m().tabHeader,children:[i.jsx("h2",{children:"Receipt Settings"}),i.jsx("p",{children:"Configure global receipt settings and business information."})]}),i.jsx(_,{onMessage:(e,t="success")=>{g({text:e,type:t}),setTimeout(()=>g(null),5e3)}})]})]})]})]})}function _({onMessage:e}){let[t,s]=(0,n.useState)({autoGenerateReceipts:!0,emailReceiptsToCustomers:!0,printReceiptsAutomatically:!1,receiptNumberPrefix:"OSS",includeQRCode:!1,defaultTemplate:"",businessInfo:{name:"Ocean Soul Sparkles",address:"",phone:"",email:"",website:"",abn:""}}),[a,r]=(0,n.useState)(!1),[l,c]=(0,n.useState)([]),o=(e,t)=>{if(e.includes(".")){let[a,i]=e.split(".");s(e=>({...e,[a]:{...e[a],[i]:t}}))}else s(s=>({...s,[e]:t}))},d=async()=>{try{r(!0);let s={"receipt.autoGenerateReceipts":t.autoGenerateReceipts.toString(),"receipt.emailReceiptsToCustomers":t.emailReceiptsToCustomers.toString(),"receipt.printReceiptsAutomatically":t.printReceiptsAutomatically.toString(),"receipt.receiptNumberPrefix":t.receiptNumberPrefix,"receipt.includeQRCode":t.includeQRCode.toString(),"receipt.defaultTemplate":t.defaultTemplate,"general.businessName":t.businessInfo.name,"general.businessAddress":t.businessInfo.address,"general.businessPhone":t.businessInfo.phone,"general.businessEmail":t.businessInfo.email,"general.businessWebsite":t.businessInfo.website,"general.businessABN":t.businessInfo.abn};if(!(await fetch("/api/admin/settings",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({settings:s})})).ok)throw Error("Failed to save settings");e?.("Receipt settings saved successfully!","success")}catch(t){console.error("Error saving settings:",t),e?.("Failed to save settings. Please try again.","error")}finally{r(!1)}};return(0,i.jsxs)("div",{className:m().receiptSettings,children:[(0,i.jsxs)("div",{className:m().settingsGrid,children:[(0,i.jsxs)("div",{className:m().settingsSection,children:[i.jsx("h3",{children:"Receipt Generation"}),i.jsx("div",{className:m().settingGroup,children:(0,i.jsxs)("label",{className:m().checkboxLabel,children:[i.jsx("input",{type:"checkbox",checked:t.autoGenerateReceipts,onChange:e=>o("autoGenerateReceipts",e.target.checked)}),i.jsx("span",{children:"Automatically generate receipts for all transactions"})]})}),i.jsx("div",{className:m().settingGroup,children:(0,i.jsxs)("label",{className:m().checkboxLabel,children:[i.jsx("input",{type:"checkbox",checked:t.emailReceiptsToCustomers,onChange:e=>o("emailReceiptsToCustomers",e.target.checked)}),i.jsx("span",{children:"Email receipts to customers automatically"})]})}),i.jsx("div",{className:m().settingGroup,children:(0,i.jsxs)("label",{className:m().checkboxLabel,children:[i.jsx("input",{type:"checkbox",checked:t.printReceiptsAutomatically,onChange:e=>o("printReceiptsAutomatically",e.target.checked)}),i.jsx("span",{children:"Print receipts automatically"})]})})]}),(0,i.jsxs)("div",{className:m().settingsSection,children:[i.jsx("h3",{children:"Receipt Configuration"}),(0,i.jsxs)("div",{className:m().settingGroup,children:[i.jsx("label",{children:"Receipt Number Prefix"}),i.jsx("input",{type:"text",value:t.receiptNumberPrefix,onChange:e=>o("receiptNumberPrefix",e.target.value),placeholder:"OSS",maxLength:10})]}),(0,i.jsxs)("div",{className:m().settingGroup,children:[i.jsx("label",{children:"Default Template"}),(0,i.jsxs)("select",{value:t.defaultTemplate,onChange:e=>o("defaultTemplate",e.target.value),children:[i.jsx("option",{value:"",children:"Select default template"}),l.map(e=>i.jsx("option",{value:e.id,children:e.name},e.id))]})]}),i.jsx("div",{className:m().settingGroup,children:(0,i.jsxs)("label",{className:m().checkboxLabel,children:[i.jsx("input",{type:"checkbox",checked:t.includeQRCode,onChange:e=>o("includeQRCode",e.target.checked)}),i.jsx("span",{children:"Include QR code on receipts"})]})})]}),(0,i.jsxs)("div",{className:m().settingsSection,children:[i.jsx("h3",{children:"Business Information"}),(0,i.jsxs)("div",{className:m().settingGroup,children:[i.jsx("label",{children:"Business Name"}),i.jsx("input",{type:"text",value:t.businessInfo.name,onChange:e=>o("businessInfo.name",e.target.value),placeholder:"Ocean Soul Sparkles"})]}),(0,i.jsxs)("div",{className:m().settingGroup,children:[i.jsx("label",{children:"Address"}),i.jsx("textarea",{value:t.businessInfo.address,onChange:e=>o("businessInfo.address",e.target.value),placeholder:"Business address",rows:3})]}),(0,i.jsxs)("div",{className:m().settingGroup,children:[i.jsx("label",{children:"Phone"}),i.jsx("input",{type:"text",value:t.businessInfo.phone,onChange:e=>o("businessInfo.phone",e.target.value),placeholder:"+61 XXX XXX XXX"})]}),(0,i.jsxs)("div",{className:m().settingGroup,children:[i.jsx("label",{children:"Email"}),i.jsx("input",{type:"email",value:t.businessInfo.email,onChange:e=>o("businessInfo.email",e.target.value),placeholder:"<EMAIL>"})]}),(0,i.jsxs)("div",{className:m().settingGroup,children:[i.jsx("label",{children:"Website"}),i.jsx("input",{type:"text",value:t.businessInfo.website,onChange:e=>o("businessInfo.website",e.target.value),placeholder:"oceansoulsparkles.com.au"})]}),(0,i.jsxs)("div",{className:m().settingGroup,children:[i.jsx("label",{children:"ABN"}),i.jsx("input",{type:"text",value:t.businessInfo.abn,onChange:e=>o("businessInfo.abn",e.target.value),placeholder:"**************"})]})]})]}),i.jsx("div",{className:m().settingsActions,children:i.jsx("button",{onClick:d,disabled:a,className:m().saveButton,children:a?"Saving...":"Save Settings"})})]})}c=(u.then?(await u)():u)[0],a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2899,6212,1664,7441],()=>s(2665));module.exports=a})();