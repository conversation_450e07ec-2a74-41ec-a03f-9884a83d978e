"use strict";(()=>{var e={};e.id=2879,e.ids=[2879],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},7191:e=>{e.exports=require("square")},1919:(e,t,r)=>{r.r(t),r.d(t,{config:()=>d,default:()=>u,routeModule:()=>l});var s={};r.r(s),r.d(s,{default:()=>c});var o=r(1802),a=r(7153),n=r(8781);r(8456);var i=r(7474);async function c(e,t){let s=Math.random().toString(36).substring(2,8);console.log(`[${s}] Square Terminal devices API called`);try{let{user:o,error:a}=await (0,i.ZQ)(e);if(a||!o)return t.status(401).json({error:"Authentication required",message:a?.message||"Authentication failed",requestId:s});let n=process.env.SQUARE_ACCESS_TOKEN,c="location-placeholder",u=process.env.SQUARE_ENVIRONMENT||"sandbox";if(!n||!c)return t.status(500).json({error:"Square configuration missing",message:"Square Terminal integration is not properly configured",requestId:s});let{Client:d}=r(7191),l=new d({accessToken:n,environment:"production"===u?"production":"sandbox"});if("GET"===e.method)try{let e=l.terminalApi,{result:r,statusCode:o}=await e.listDevices({locationId:c});if(console.log(`[${s}] Terminal devices API response:`,o),200!==o||!r.devices)return console.error(`[${s}] Failed to list devices:`,r),t.status(500).json({error:"Failed to list terminal devices",message:r.errors?.[0]?.detail||"Unknown error",requestId:s});{let e=r.devices.filter(e=>"PAIRED"===e.status||"IDLE"===e.status);return t.status(200).json({success:!0,devices:e.map(e=>({id:e.id,name:e.name||`Terminal ${e.id?.substring(0,8)}`,status:e.status,deviceType:e.deviceType,productType:e.productType,locationId:e.locationId})),requestId:s})}}catch(e){return console.error(`[${s}] Error listing terminal devices:`,e),t.status(500).json({error:"Failed to list terminal devices",message:e.message,requestId:s})}else if("POST"===e.method){let{deviceId:r,amountMoney:o,orderDetails:a}=e.body;if(!r||!o)return t.status(400).json({error:"Missing required fields",message:"deviceId and amountMoney are required",requestId:s});try{let e=l.terminalApi,n={idempotencyKey:`terminal_${s}_${Date.now()}`,checkout:{amountMoney:{amount:o.amount,currency:o.currency||"AUD"},deviceOptions:{deviceId:r},paymentType:"CARD_PRESENT",note:a?.service||"POS Terminal Payment"}};console.log(`[${s}] Creating terminal checkout:`,{deviceId:r,amount:o.amount,currency:o.currency});let{result:i,statusCode:c}=await e.createTerminalCheckout(n);if(console.log(`[${s}] Terminal checkout response:`,c),200===c&&i.checkout)return t.status(200).json({success:!0,checkout:{id:i.checkout.id,status:i.checkout.status,amountMoney:i.checkout.amountMoney,deviceId:i.checkout.deviceOptions?.deviceId,createdAt:i.checkout.createdAt,updatedAt:i.checkout.updatedAt},requestId:s});return console.error(`[${s}] Failed to create checkout:`,i),t.status(400).json({error:"Failed to create terminal checkout",message:i.errors?.[0]?.detail||"Unknown error",requestId:s})}catch(e){return console.error(`[${s}] Error creating terminal checkout:`,e),t.status(500).json({error:"Failed to create terminal checkout",message:e.message,requestId:s})}}else{if("PUT"!==e.method)return t.status(405).json({error:"Method not allowed",message:"Only GET, POST, and PUT methods are supported",requestId:s});let{checkoutId:r}=e.body;if(!r)return t.status(400).json({error:"Missing checkoutId",message:"checkoutId is required for status updates",requestId:s});try{let e=l.terminalApi,{result:o,statusCode:a}=await e.getTerminalCheckout(r);if(console.log(`[${s}] Terminal checkout status:`,a),200===a&&o.checkout)return t.status(200).json({success:!0,checkout:{id:o.checkout.id,status:o.checkout.status,amountMoney:o.checkout.amountMoney,paymentId:o.checkout.paymentId,deviceId:o.checkout.deviceOptions?.deviceId,createdAt:o.checkout.createdAt,updatedAt:o.checkout.updatedAt},requestId:s});return console.error(`[${s}] Failed to get checkout status:`,o),t.status(404).json({error:"Checkout not found",message:o.errors?.[0]?.detail||"Unknown error",requestId:s})}catch(e){return console.error(`[${s}] Error getting checkout status:`,e),t.status(500).json({error:"Failed to get checkout status",message:e.message,requestId:s})}}}catch(e){return console.error(`[${s}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:s})}}let u=(0,n.l)(s,"default"),d=(0,n.l)(s,"config"),l=new o.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/pos/terminal-devices",pathname:"/api/admin/pos/terminal-devices",bundlePath:"",filename:""},userland:s})},8456:(e,t,r)=>{r.d(t,{pR:()=>i});var s=r(2885);let o="https://ndlgbcsbidyhxbpqzgqp.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!o||!a)throw Error("Missing Supabase environment variables");(0,s.createClient)(o,a,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let i=(0,s.createClient)(o,n||a,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2805],()=>r(1919));module.exports=s})();