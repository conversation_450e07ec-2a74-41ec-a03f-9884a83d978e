/* Ocean Soul Sparkles - Mobile Dashboard Styles */

.mobileDashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  padding-bottom: 100px; /* Account for bottom navigation */
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 2rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.greeting h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.dateTime {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date {
  font-size: 0.9rem;
  color: #4a5568;
  font-weight: 500;
}

.time {
  font-size: 1.1rem;
  color: #2d3748;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.headerIcon {
  font-size: 2rem;
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(20deg); }
  75% { transform: rotate(-10deg); }
}

.waveEmoji {
  display: block;
  transform-origin: 70% 70%;
}

.statsSection {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.statsSection h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.statCard {
  background: linear-gradient(135deg, #f8fafc, #edf2f7);
  border-radius: 12px;
  padding: 1.25rem 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.statIcon {
  font-size: 1.8rem;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  color: white;
  flex-shrink: 0;
}

.statContent {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 0;
}

.statValue {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2d3748;
  line-height: 1;
}

.statLabel {
  font-size: 0.8rem;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.actionsSection {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.actionsSection h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
}

.actionsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.actionCard {
  background: linear-gradient(135deg, #f8fafc, #edf2f7);
  border-radius: 12px;
  padding: 1.25rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  text-decoration: none;
  color: #2d3748;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 120px;
}

.actionCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--action-color, #667eea);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.actionCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.actionCard:hover::before {
  transform: scaleX(1);
}

.actionIcon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--action-color, #667eea);
  border-radius: 14px;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.actionContent {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.actionLabel {
  font-size: 0.95rem;
  font-weight: 600;
  color: #2d3748;
  line-height: 1.2;
}

.actionDescription {
  font-size: 0.75rem;
  color: #718096;
  font-weight: 400;
  line-height: 1.3;
}

.actionArrow {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1rem;
  color: #a0aec0;
  transition: all 0.3s ease;
}

.actionCard:hover .actionArrow {
  color: var(--action-color, #667eea);
  transform: translateX(3px);
}

.summarySection {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.summarySection h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
}

.summaryCards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.summaryCard {
  background: linear-gradient(135deg, #f8fafc, #edf2f7);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.summaryHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.summaryIcon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  color: white;
}

.summaryTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #4a5568;
}

.summaryValue {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.summaryChange {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.changePositive {
  color: #38a169;
  font-weight: 600;
  font-size: 0.9rem;
}

.changeLabel {
  color: #718096;
  font-size: 0.85rem;
}

.activitySection {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.activityHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.activityHeader h2 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
}

.viewAllLink {
  color: #667eea;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.viewAllLink:hover {
  color: #764ba2;
}

.activityList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activityItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #edf2f7);
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

.activityIcon {
  font-size: 1.2rem;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  color: white;
  flex-shrink: 0;
}

.activityContent {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.activityText {
  font-size: 0.9rem;
  font-weight: 500;
  color: #2d3748;
  line-height: 1.3;
}

.activityTime {
  font-size: 0.8rem;
  color: #718096;
  font-weight: 400;
}

.linksSection {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.linksSection h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
}

.linksList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quickLink {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #edf2f7);
  border-radius: 10px;
  text-decoration: none;
  color: #2d3748;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.quickLink:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: translateX(5px);
}

.linkIcon {
  font-size: 1.2rem;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  color: white;
  flex-shrink: 0;
}

.linkText {
  flex: 1;
  font-size: 0.95rem;
  font-weight: 500;
  color: #2d3748;
}

.linkArrow {
  font-size: 1rem;
  color: #a0aec0;
  transition: all 0.3s ease;
}

.quickLink:hover .linkArrow {
  color: #667eea;
  transform: translateX(3px);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .mobileDashboard {
    padding: 0.75rem;
    gap: 1.25rem;
  }

  .header {
    padding: 1.5rem 1rem;
  }

  .greeting h1 {
    font-size: 1.3rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .actionsGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .actionCard {
    flex-direction: row;
    text-align: left;
    min-height: auto;
    padding: 1rem;
  }

  .actionIcon {
    font-size: 1.5rem;
    width: 48px;
    height: 48px;
    margin-bottom: 0;
    margin-right: 0.75rem;
  }

  .actionContent {
    flex: 1;
  }

  .actionArrow {
    position: static;
    margin-left: auto;
  }

  .summaryCards {
    gap: 0.75rem;
  }

  .summaryCard {
    padding: 1.25rem;
  }

  .summaryValue {
    font-size: 1.75rem;
  }
}

/* Hide on desktop - mobile-only component */
@media (min-width: 769px) {
  .mobileDashboard {
    display: none;
  }
}
