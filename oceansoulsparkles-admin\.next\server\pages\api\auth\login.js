"use strict";(()=>{var e={};e.id=3908,e.ids=[3908],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},8686:(e,r,t)=>{t.r(r),t.d(r,{config:()=>f,default:()=>l,routeModule:()=>c});var s={};t.r(s),t.d(s,{default:()=>d});var a=t(1802),n=t(7153),o=t(8781),i=t(7474),u=t(1775);async function d(e,r){if("POST"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let t=await (0,u.ge)(e);if(!t.allowed)return r.status(429).json({error:"Too many login attempts. Please try again later.",resetTime:t.resetTime});let{email:s,password:a}=e.body;if(!s||!a)return r.status(400).json({error:"Email and password are required"});let n=e.headers["x-forwarded-for"]||e.headers["x-real-ip"]||e.connection.remoteAddress||"unknown",o=await (0,i.$g)(s,a,n);if(!o.success)return r.status(401).json({error:o.error});if(o.token&&r.setHeader("Set-Cookie",[`admin-token=${o.token}; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=28800`]),o.requiresMFA)return r.status(200).json({success:!0,requiresMFA:!0,user:o.user});return r.status(200).json({success:!0,token:o.token,user:o.user})}catch(e){return console.error("Login API error:",e),r.status(500).json({error:"Internal server error"})}}let l=(0,o.l)(s,"default"),f=(0,o.l)(s,"config"),c=new a.PagesAPIRouteModule({definition:{kind:n.x.PAGES_API,page:"/api/auth/login",pathname:"/api/auth/login",bundlePath:"",filename:""},userland:s})},1775:(e,r,t)=>{t.d(r,{ge:()=>n});let s=new Map,a={"/api/auth/login":{windowMs:9e5,maxRequests:5},"/api/auth/mfa":{windowMs:3e5,maxRequests:10},"/api/auth/forgot-password":{windowMs:36e5,maxRequests:3},"/api/admin":{windowMs:6e4,maxRequests:100},default:{windowMs:6e4,maxRequests:60}};async function n(e){let r=function(e){if(e.headers&&"function"==typeof e.headers.get){let r=e.headers.get("x-forwarded-for"),t=e.headers.get("x-real-ip");return e.headers.get("cf-connecting-ip")||(r?r.split(",")[0].trim():t||e.ip||"unknown")}if(e.headers&&"object"==typeof e.headers){let r=e.headers["x-forwarded-for"],t=e.headers["x-real-ip"],s=e.headers["cf-connecting-ip"];if(s)return Array.isArray(s)?s[0]:s;if(r)return Array.isArray(r)?r[0]:r.split(",")[0].trim();if(t)return Array.isArray(t)?t[0]:t}return"unknown"}(e),t=e.nextUrl?.pathname||e.url||"/unknown",n=function(e){if(a[e])return a[e];for(let[r,t]of Object.entries(a))if("default"!==r&&e.startsWith(r))return t;return a.default}(t),o=`${r}:${t}`,i=Date.now();n.windowMs;let u=s.get(o);return(u&&u.resetTime<i&&(s.delete(o),u=void 0),u||(u={count:0,resetTime:i+n.windowMs}),u.count>=n.maxRequests)?{allowed:!1,ip:r,remaining:0,resetTime:u.resetTime,reason:"Rate limit exceeded"}:(u.count++,s.set(o,u),.01>Math.random()&&function(){let e=Date.now(),r=[];s.forEach((t,s)=>{t.resetTime<e&&r.push(s)}),r.forEach(e=>s.delete(e))}(),{allowed:!0,ip:r,remaining:n.maxRequests-u.count,resetTime:u.resetTime})}}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2805],()=>t(8686));module.exports=s})();