/* Ocean Soul Sparkles - Customer Dashboard Styles */

.dashboard {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.welcomeSection {
  margin-bottom: 1rem;
}

.welcomeCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcomeCard h2 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcomeCard p {
  margin: 0;
  color: #718096;
  font-size: 1.1rem;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.statIcon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.statContent h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d3748;
}

.statContent p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
  font-weight: 500;
}

.contentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.contentCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.cardHeader {
  padding: 1.5rem 1.5rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardHeader h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
}

.viewAllLink {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.viewAllLink:hover {
  color: #764ba2;
  text-decoration: underline;
}

.cardContent {
  padding: 1rem 1.5rem 1.5rem;
}

.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bookingItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.bookingInfo h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
}

.bookingInfo p {
  margin: 0 0 0.25rem 0;
  color: #718096;
  font-size: 0.9rem;
}

.bookingDate {
  font-size: 0.8rem;
  color: #a0aec0;
}

.bookingStatus {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

.statusBadge.completed {
  background: #c6f6d5;
  color: #2f855a;
}

.statusBadge.confirmed {
  background: #bee3f8;
  color: #2b6cb0;
}

.statusBadge.pending {
  background: #fef5e7;
  color: #d69e2e;
}

.bookingAmount {
  font-weight: 600;
  color: #2d3748;
}

.emptyState {
  text-align: center;
  padding: 2rem 1rem;
  color: #718096;
}

.emptyState p {
  margin: 0 0 1rem 0;
}

.ctaButton {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.loyaltyProgress {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.tierInfo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.currentTier {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tierBadge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.points {
  font-weight: 600;
  color: #2d3748;
}

.nextTier p {
  margin: 0 0 0.5rem 0;
  color: #718096;
  font-size: 0.9rem;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.referralInfo {
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.referralInfo h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1rem;
}

.referralInfo p {
  margin: 0.25rem 0;
  color: #718096;
  font-size: 0.9rem;
}

.actionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem 1rem;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 15px;
  text-decoration: none;
  color: #4a5568;
  transition: all 0.3s ease;
}

.actionButton:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.actionIcon {
  font-size: 1.5rem;
}

.actionButton span:last-child {
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
}

.loadingContainer, .errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: 1rem;
  color: white;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.retryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .statCard {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
  }

  .statIcon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .contentGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .bookingItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .bookingStatus {
    align-items: flex-start;
    flex-direction: row;
    gap: 1rem;
  }

  .actionGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .welcomeCard {
    padding: 1.5rem;
  }

  .welcomeCard h2 {
    font-size: 1.5rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .actionGrid {
    grid-template-columns: 1fr;
  }
}
