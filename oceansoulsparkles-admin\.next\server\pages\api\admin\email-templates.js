"use strict";(()=>{var e={};e.id=3577,e.ids=[3577],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},6112:(e,t,a)=>{a.r(t),a.d(t,{config:()=>c,default:()=>p,routeModule:()=>f});var r={};a.r(r),a.d(r,{default:()=>d});var s=a(1802),i=a(7153),n=a(8781),o=a(7474),l=a(2885);let m=process.env.SUPABASE_SERVICE_ROLE_KEY,u=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",m);async function d(e,t){let a=Math.random().toString(36).substring(2,8);console.log(`[${a}] Email templates API called - ${e.method}`);try{let r=await (0,o.SA)(e);if(!r.success)return t.status(401).json({error:"Authentication required",message:r.message||"Authentication failed",requestId:a});let{user:s}=r;if(!s)return t.status(401).json({error:"User not found",requestId:a});if("Admin"!==s.role&&"DEV"!==s.role)return t.status(403).json({error:"Insufficient permissions",message:"Only admins can manage email templates",requestId:a});if("GET"===e.method){let{type:r,active_only:s}=e.query,i=u.from("email_templates").select(`
          id,
          name,
          type,
          subject,
          html_content,
          text_content,
          variables,
          is_active,
          is_default,
          created_at,
          updated_at
        `).order("type",{ascending:!0}).order("name",{ascending:!0});r&&"all"!==r&&(i=i.eq("type",r)),"true"===s&&(i=i.eq("is_active",!0));let{data:n,error:o}=await i;if(o)return console.error(`[${a}] Database error:`,o),t.status(500).json({error:"Failed to fetch email templates",message:o.message,requestId:a});return t.status(200).json({templates:n||[],total:n?.length||0,requestId:a})}if("POST"===e.method){let{name:r,type:i,subject:n,html_content:o,text_content:l,variables:m,is_active:d=!0,is_default:p=!1}=e.body;if(!r||!i||!n||!o)return t.status(400).json({error:"Missing required fields",message:"Name, type, subject, and HTML content are required",requestId:a});let{data:c}=await u.from("email_templates").select("id").eq("name",r).single();if(c)return t.status(409).json({error:"Template name already exists",message:"A template with this name already exists",requestId:a});p&&await u.from("email_templates").update({is_default:!1}).eq("type",i);let{data:f,error:g}=await u.from("email_templates").insert([{name:r,type:i,subject:n,html_content:o,text_content:l,variables:m||[],is_active:d,is_default:p,created_by:s.id,updated_by:s.id}]).select().single();if(g)return console.error(`[${a}] Error creating template:`,g),t.status(500).json({error:"Failed to create email template",message:g.message,requestId:a});return t.status(201).json({template:f,message:"Email template created successfully",requestId:a})}return t.status(405).json({error:"Method not allowed",requestId:a})}catch(e){return console.error(`[${a}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:e instanceof Error?e.message:"Unknown error",requestId:a})}}let p=(0,n.l)(r,"default"),c=(0,n.l)(r,"config"),f=new s.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/email-templates",pathname:"/api/admin/email-templates",bundlePath:"",filename:""},userland:r})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[2805],()=>a(6112));module.exports=r})();