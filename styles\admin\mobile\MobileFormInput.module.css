/* Ocean Soul Sparkles - Mobile Form Input Styles */

.mobileInput {
  position: relative;
  margin-bottom: 1.5rem;
  width: 100%;
}

.inputContainer {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.input {
  width: 100%;
  padding: 1.25rem 1rem 0.75rem 1rem;
  border: none;
  background: transparent;
  font-size: 1.1rem;
  font-weight: 500;
  color: #2d3748;
  outline: none;
  transition: all 0.3s ease;
  min-height: 56px; /* Touch-friendly minimum height */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.input::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.input:focus {
  outline: none;
}

.label {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.1rem;
  font-weight: 500;
  color: #718096;
  pointer-events: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  padding: 0 0.25rem;
  cursor: text;
}

.label.floating {
  top: 0.75rem;
  transform: translateY(0);
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--admin-primary);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
}

.label.required::after {
  content: '*';
  color: #e53e3e;
  margin-left: 0.25rem;
}

.labelIcon {
  font-size: 1rem;
  opacity: 0.7;
}

.inputBorder {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--admin-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.inputBorder.focused {
  transform: scaleX(1);
}

.inputContainer:focus-within {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.helperText {
  margin-top: 0.5rem;
  padding: 0 1rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.hintText {
  color: #718096;
  font-weight: 400;
}

.errorText {
  color: #e53e3e;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.errorIcon {
  font-size: 0.9rem;
}

/* Error State */
.mobileInput.error .inputContainer {
  border-color: #e53e3e;
  background: rgba(229, 62, 62, 0.05);
}

.mobileInput.error .inputBorder {
  background: #e53e3e;
}

.mobileInput.error .label.floating {
  color: #e53e3e;
}

/* Disabled State */
.mobileInput.disabled .inputContainer {
  background: #f7fafc;
  border-color: #e2e8f0;
  opacity: 0.6;
}

.mobileInput.disabled .input {
  color: #a0aec0;
  cursor: not-allowed;
}

.mobileInput.disabled .label {
  color: #a0aec0;
  cursor: not-allowed;
}

/* Input Type Specific Styles */
.input[type="number"] {
  -moz-appearance: textfield;
}

.input[type="number"]::-webkit-outer-spin-button,
.input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.input[type="search"] {
  -webkit-appearance: none;
  border-radius: 0;
}

.input[type="search"]::-webkit-search-decoration,
.input[type="search"]::-webkit-search-cancel-button,
.input[type="search"]::-webkit-search-results-button,
.input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

/* Mobile Keyboard Optimizations */
.input[inputmode="numeric"] {
  text-align: left;
}

.input[inputmode="decimal"] {
  text-align: left;
}

.input[inputmode="tel"] {
  text-align: left;
}

/* Focus Improvements for Mobile */
@media (max-width: 768px) {
  .input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 1.5rem 1rem 1rem 1rem;
    min-height: 60px; /* Larger touch target on mobile */
  }

  .label {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .label.floating {
    font-size: 0.8rem;
    top: 0.5rem;
  }

  .inputContainer {
    border-radius: 8px;
  }

  .helperText {
    font-size: 0.85rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .inputContainer {
    border-width: 3px;
  }

  .label {
    font-weight: 600;
  }

  .inputBorder {
    height: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .input,
  .label,
  .inputBorder,
  .inputContainer {
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .inputContainer {
    background: rgba(45, 55, 72, 0.95);
    border-color: #4a5568;
  }

  .input {
    color: #f7fafc;
  }

  .input::placeholder {
    color: #718096;
  }

  .label {
    color: #a0aec0;
  }

  .label.floating {
    color: #63b3ed;
    background: rgba(45, 55, 72, 0.9);
  }

  .hintText {
    color: #a0aec0;
  }

  .mobileInput.disabled .inputContainer {
    background: #2d3748;
  }
}
