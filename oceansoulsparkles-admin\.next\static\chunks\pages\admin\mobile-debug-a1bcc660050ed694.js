(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[264],{1140:function(r,e,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/mobile-debug",function(){return o(5847)}])},5847:function(r,e,o){"use strict";o.r(e),o.d(e,{default:function(){return i}}),function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}();var n=o(645),t=o.n(n);!function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}();var d=o(9008),c=o.n(d);function i(){let[r,e]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())({width:0,height:0,isMobile:!1,userAgent:""});return Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(()=>{let r=()=>{e({width:window.innerWidth,height:window.innerHeight,isMobile:window.innerWidth<=768,userAgent:window.navigator.userAgent})};return r(),window.addEventListener("resize",r),()=>window.removeEventListener("resize",r)},[]),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}()),{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(c(),{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("title",{className:"jsx-5dc5df920014103c",children:"Mobile Debug - Ocean Soul Sparkles Admin"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0",className:"jsx-5dc5df920014103c"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{padding:"20px",fontFamily:"system-ui, sans-serif",backgroundColor:"#f8f9fa",minHeight:"100vh"},className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h1",{style:{color:"#16213e",marginBottom:"20px",fontSize:r.isMobile?"1.5rem":"2rem"},className:"jsx-5dc5df920014103c",children:"Mobile Debug Page"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"20px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"Screen Information"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{display:"grid",gap:"10px"},className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{className:"jsx-5dc5df920014103c",children:"Width:"})," ",r.width,"px"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{className:"jsx-5dc5df920014103c",children:"Height:"})," ",r.height,"px"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{className:"jsx-5dc5df920014103c",children:"Is Mobile:"})," ",r.isMobile?"YES":"NO"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{className:"jsx-5dc5df920014103c",children:"Breakpoint:"})," ",r.width<=480?"Small Mobile":r.width<=768?"Mobile":r.width<=1024?"Tablet":"Desktop"]})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"20px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"CSS Media Query Test"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{padding:"10px",borderRadius:"4px",marginBottom:"10px",backgroundColor:"#e3f2fd"},className:"jsx-5dc5df920014103c desktop-only",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{className:"jsx-5dc5df920014103c",children:"Desktop Only:"})," This should only be visible on desktop (width > 768px)"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{padding:"10px",borderRadius:"4px",marginBottom:"10px",backgroundColor:"#f3e5f5"},className:"jsx-5dc5df920014103c mobile-only",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{className:"jsx-5dc5df920014103c",children:"Mobile Only:"})," This should only be visible on mobile (width ≤ 768px)"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{padding:"10px",borderRadius:"4px",backgroundColor:"#e8f5e8"},className:"jsx-5dc5df920014103c always-visible",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{className:"jsx-5dc5df920014103c",children:"Always Visible:"})," This should always be visible"]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"20px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"Touch-Friendly Elements"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{display:"flex",gap:"10px",flexWrap:"wrap",marginBottom:"15px"},className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",backgroundColor:"#16213e",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},className:"jsx-5dc5df920014103c",children:"Touch Button 1"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",backgroundColor:"#4CAF50",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},className:"jsx-5dc5df920014103c",children:"Touch Button 2"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("input",{type:"text",placeholder:"Touch-friendly input (44px min height)",style:{width:"100%",padding:"12px",fontSize:"16px",border:"2px solid #e0e0e0",borderRadius:"8px",minHeight:"44px",boxSizing:"border-box"},className:"jsx-5dc5df920014103c"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"20px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"Responsive Layout Test"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{display:"grid",gridTemplateColumns:r.isMobile?"1fr":"repeat(auto-fit, minmax(200px, 1fr))",gap:"15px"},className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{backgroundColor:"#f0f0f0",padding:"15px",borderRadius:"8px",textAlign:"center"},className:"jsx-5dc5df920014103c",children:"Card 1"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{backgroundColor:"#f0f0f0",padding:"15px",borderRadius:"8px",textAlign:"center"},className:"jsx-5dc5df920014103c",children:"Card 2"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{backgroundColor:"#f0f0f0",padding:"15px",borderRadius:"8px",textAlign:"center"},className:"jsx-5dc5df920014103c",children:"Card 3"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"80px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"Navigation Test"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{className:"jsx-5dc5df920014103c",children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{className:"jsx-5dc5df920014103c",children:"Expected behavior:"})}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("ul",{className:"jsx-5dc5df920014103c",children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("li",{className:"jsx-5dc5df920014103c",children:"On mobile (≤768px): Bottom navigation should be visible"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("li",{className:"jsx-5dc5df920014103c",children:"On desktop (>768px): Sidebar should be visible"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("li",{className:"jsx-5dc5df920014103c",children:"Mobile hamburger menu should work on mobile"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{marginTop:"20px"},className:"jsx-5dc5df920014103c",children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("a",{href:"/admin/dashboard",style:{display:"inline-block",padding:"12px 24px",backgroundColor:"#16213e",color:"white",textDecoration:"none",borderRadius:"8px",fontSize:"16px"},className:"jsx-5dc5df920014103c",children:"Back to Dashboard"})})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(t(),{id:"5dc5df920014103c",children:".desktop-only.jsx-5dc5df920014103c{display:block}.mobile-only.jsx-5dc5df920014103c{display:none}@media(max-width:768px){.desktop-only.jsx-5dc5df920014103c{display:none}.mobile-only.jsx-5dc5df920014103c{display:block}}"})]})}}},function(r){r.O(0,[736,888,179],function(){return r(r.s=1140)}),_N_E=r.O()}]);