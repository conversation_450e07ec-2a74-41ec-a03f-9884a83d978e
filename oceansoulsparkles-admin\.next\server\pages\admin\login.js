(()=>{var e={};e.id=2300,e.ids=[2300,660],e.modules={6314:e=>{e.exports={loginContainer:"Login_loginContainer__dUCdv",loginCard:"Login_loginCard__MZM1M",logoSection:"Login_logoSection__ftcs9",logo:"Login_logo__9G_Xv",formSection:"Login_formSection__Nrrdb",securityNotice:"Login_securityNotice__WWjsv",securityIcon:"Login_securityIcon__tE_SC",securityText:"Login_securityText__ln1pM",backgroundPattern:"Login_backgroundPattern__Ya2Gl",sparkle:"Login_sparkle___YK5d",sparkleFloat:"Login_sparkleFloat__gY7r2"}},632:e=>{e.exports={loginForm:"LoginForm_loginForm__pceGN",header:"LoginForm_header__ablwe",errorAlert:"LoginForm_errorAlert__6NNFd",errorIcon:"LoginForm_errorIcon__jgGSS",errorMessage:"LoginForm_errorMessage__1duR_",form:"LoginForm_form__lYjuY",formGroup:"LoginForm_formGroup__63KFU",label:"LoginForm_label__KGtO8",inputContainer:"LoginForm_inputContainer__DeSIM",input:"LoginForm_input__kRvMT",inputError:"LoginForm_inputError__N2E6c",inputIcon:"LoginForm_inputIcon__86wOJ",passwordToggle:"LoginForm_passwordToggle__AZPTP",fieldError:"LoginForm_fieldError__jH2Ga",formOptions:"LoginForm_formOptions__QLAd_",checkboxLabel:"LoginForm_checkboxLabel__zfFon",checkbox:"LoginForm_checkbox__xCK1f",checkboxText:"LoginForm_checkboxText__MlC_J",forgotLink:"LoginForm_forgotLink__c717T",submitButton:"LoginForm_submitButton__M1ELN",loadingContainer:"LoginForm_loadingContainer__6cPrB",spinner:"LoginForm_spinner__spZcn",spin:"LoginForm_spin__qVqZL",buttonIcon:"LoginForm_buttonIcon__dwVEd",footer:"LoginForm_footer__alFut",securityFeatures:"LoginForm_securityFeatures__52Pzv",feature:"LoginForm_feature__TBM8a",featureIcon:"LoginForm_featureIcon__5NgOr"}},9709:e=>{e.exports={mfaContainer:"MFAForm_mfaContainer__ZhOni",header:"MFAForm_header__yXYFs",errorAlert:"MFAForm_errorAlert__3RYVK",errorIcon:"MFAForm_errorIcon__YDT7e",errorMessage:"MFAForm_errorMessage__UzQUG",form:"MFAForm_form__cTPFx",inputSection:"MFAForm_inputSection__82H2Q",inputLabel:"MFAForm_inputLabel____sRq",codeInputContainer:"MFAForm_codeInputContainer__U1LxV",codeInput:"MFAForm_codeInput__N8Hmw",filled:"MFAForm_filled__NyzDm",error:"MFAForm_error__TLMKg",shake:"MFAForm_shake__c3wEE",inputHint:"MFAForm_inputHint__rwfdH",submitButton:"MFAForm_submitButton__bINm2",loadingContainer:"MFAForm_loadingContainer__Vk5tR",spinner:"MFAForm_spinner__XsnDp",spin:"MFAForm_spin__hqNUH",buttonIcon:"MFAForm_buttonIcon__u_IgK",footer:"MFAForm_footer__CWs9A",backButton:"MFAForm_backButton__KdYg3",backIcon:"MFAForm_backIcon__7B2lW",helpText:"MFAForm_helpText__ix4Oe",securityBadge:"MFAForm_securityBadge__ricz8",securityIcon:"MFAForm_securityIcon__DGKQp"}},166:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>g,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>_,getStaticProps:()=>p,reportWebVitals:()=>x,routeModule:()=>N,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>F,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>f});var o=s(7093),n=s(5244),i=s(1323),a=s(2899),c=s.n(a),l=s(6814),d=s(4642),m=e([l,d]);[l,d]=m.then?(await m)():m;let u=(0,i.l)(d,"default"),p=(0,i.l)(d,"getStaticProps"),_=(0,i.l)(d,"getStaticPaths"),h=(0,i.l)(d,"getServerSideProps"),g=(0,i.l)(d,"config"),x=(0,i.l)(d,"reportWebVitals"),f=(0,i.l)(d,"unstable_getStaticProps"),j=(0,i.l)(d,"unstable_getStaticPaths"),F=(0,i.l)(d,"unstable_getStaticParams"),b=(0,i.l)(d,"unstable_getServerProps"),v=(0,i.l)(d,"unstable_getServerSideProps"),N=new o.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/login",pathname:"/admin/login",bundlePath:"",filename:""},components:{App:l.default,Document:c()},userland:d});t()}catch(e){t(e)}})},3913:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{Z:()=>u});var o=s(997),n=s(6689),i=s(5641),a=s(1664),c=s.n(a),l=s(632),d=s.n(l),m=e([i]);function u({onSubmit:e,isLoading:r,error:s}){let[t,a]=(0,n.useState)(!1),{register:l,handleSubmit:m,formState:{errors:u},reset:p}=(0,i.useForm)(),_=async r=>{try{await e(r.email,r.password)}catch(e){console.error("Login form error:",e)}};return(0,o.jsxs)("div",{className:d().loginForm,children:[(0,o.jsxs)("div",{className:d().header,children:[o.jsx("h2",{children:"Welcome Back"}),o.jsx("p",{children:"Sign in to your admin account"})]}),s&&(0,o.jsxs)("div",{className:d().errorAlert,children:[o.jsx("div",{className:d().errorIcon,children:"⚠️"}),o.jsx("div",{className:d().errorMessage,children:s})]}),(0,o.jsxs)("form",{onSubmit:m(_),className:d().form,children:[(0,o.jsxs)("div",{className:d().formGroup,children:[o.jsx("label",{htmlFor:"email",className:d().label,children:"Email Address"}),(0,o.jsxs)("div",{className:d().inputContainer,children:[o.jsx("input",{id:"email",type:"email",className:`${d().input} ${u.email?d().inputError:""}`,placeholder:"Enter your email",...l("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}}),disabled:r}),o.jsx("div",{className:d().inputIcon,children:"\uD83D\uDCE7"})]}),u.email&&o.jsx("div",{className:d().fieldError,children:u.email.message})]}),(0,o.jsxs)("div",{className:d().formGroup,children:[o.jsx("label",{htmlFor:"password",className:d().label,children:"Password"}),(0,o.jsxs)("div",{className:d().inputContainer,children:[o.jsx("input",{id:"password",type:t?"text":"password",className:`${d().input} ${u.password?d().inputError:""}`,placeholder:"Enter your password",...l("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}}),disabled:r}),o.jsx("button",{type:"button",className:d().passwordToggle,onClick:()=>a(!t),disabled:r,children:t?"\uD83D\uDE48":"\uD83D\uDC41️"})]}),u.password&&o.jsx("div",{className:d().fieldError,children:u.password.message})]}),(0,o.jsxs)("div",{className:d().formOptions,children:[(0,o.jsxs)("label",{className:d().checkboxLabel,children:[o.jsx("input",{type:"checkbox",className:d().checkbox,...l("rememberMe"),disabled:r}),o.jsx("span",{className:d().checkboxText,children:"Remember me"})]}),o.jsx(c(),{href:"/admin/forgot-password",className:d().forgotLink,children:"Forgot password?"})]}),o.jsx("button",{type:"submit",className:d().submitButton,disabled:r,children:r?(0,o.jsxs)("div",{className:d().loadingContainer,children:[o.jsx("div",{className:d().spinner}),o.jsx("span",{children:"Signing in..."})]}):(0,o.jsxs)(o.Fragment,{children:[o.jsx("span",{children:"Sign In"}),o.jsx("div",{className:d().buttonIcon,children:"→"})]})})]}),o.jsx("div",{className:d().footer,children:(0,o.jsxs)("div",{className:d().securityFeatures,children:[(0,o.jsxs)("div",{className:d().feature,children:[o.jsx("div",{className:d().featureIcon,children:"\uD83D\uDD10"}),o.jsx("span",{children:"Multi-Factor Authentication"})]}),(0,o.jsxs)("div",{className:d().feature,children:[o.jsx("div",{className:d().featureIcon,children:"\uD83D\uDEE1️"}),o.jsx("span",{children:"Advanced Security"})]}),(0,o.jsxs)("div",{className:d().feature,children:[o.jsx("div",{className:d().featureIcon,children:"\uD83D\uDCCA"}),o.jsx("span",{children:"Audit Logging"})]})]})})]})}i=(m.then?(await m)():m)[0],t()}catch(e){t(e)}})},7430:(e,r,s)=>{"use strict";s.d(r,{Z:()=>a});var t=s(997),o=s(6689),n=s(9709),i=s.n(n);function a({user:e,onSubmit:r,onBack:s,isLoading:n}){let[a,c]=(0,o.useState)(["","","","","",""]),[l,d]=(0,o.useState)(""),[m,u]=(0,o.useState)(30),p=(0,o.useRef)([]),_=(e,r)=>{if(!/^\d*$/.test(r))return;let s=[...a];s[e]=r.slice(-1),c(s),d(""),r&&e<5&&p.current[e+1]?.focus(),s.every(e=>""!==e)&&!n&&x(s.join(""))},h=(e,r)=>{"Backspace"===r.key&&!a[e]&&e>0?p.current[e-1]?.focus():"ArrowLeft"===r.key&&e>0?p.current[e-1]?.focus():"ArrowRight"===r.key&&e<5&&p.current[e+1]?.focus()},g=e=>{e.preventDefault();let r=e.clipboardData.getData("text").replace(/\D/g,"").slice(0,6);6!==r.length||(c(r.split("")),d(""),p.current[5]?.focus(),n||x(r))},x=async e=>{if(6!==e.length){d("Please enter a complete 6-digit code");return}try{await r(e)}catch(e){d("Invalid verification code. Please try again."),c(["","","","","",""]),p.current[0]?.focus()}},f=async()=>{try{(await fetch("/api/auth/resend-mfa",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e.id})})).ok?(u(30),d("")):d("Failed to resend code. Please try again.")}catch(e){d("Failed to resend code. Please try again.")}};return(0,t.jsxs)("div",{className:i().mfaForm,children:[(0,t.jsxs)("div",{className:i().header,children:[t.jsx("button",{type:"button",className:i().backButton,onClick:s,disabled:n,children:"← Back"}),t.jsx("h2",{children:"Two-Factor Authentication"}),t.jsx("p",{children:"Enter the 6-digit code from your authenticator app"})]}),(0,t.jsxs)("div",{className:i().userInfo,children:[(0,t.jsxs)("div",{className:i().avatar,children:[e.firstName.charAt(0),e.lastName.charAt(0)]}),(0,t.jsxs)("div",{className:i().userDetails,children:[(0,t.jsxs)("div",{className:i().userName,children:[e.firstName," ",e.lastName]}),t.jsx("div",{className:i().userEmail,children:e.email})]})]}),l&&(0,t.jsxs)("div",{className:i().errorAlert,children:[t.jsx("div",{className:i().errorIcon,children:"⚠️"}),t.jsx("div",{className:i().errorMessage,children:l})]}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),x(a.join(""))},className:i().form,children:[t.jsx("div",{className:i().codeInputContainer,children:a.map((e,r)=>t.jsx("input",{ref:e=>{p.current[r]=e},type:"text",inputMode:"numeric",pattern:"\\d*",maxLength:1,value:e,onChange:e=>_(r,e.target.value),onKeyDown:e=>h(r,e),onPaste:g,className:`${i().codeInput} ${l?i().inputError:""}`,disabled:n,autoComplete:"one-time-code"},r))}),t.jsx("button",{type:"submit",className:i().submitButton,disabled:n||a.some(e=>""===e),children:n?(0,t.jsxs)("div",{className:i().loadingContainer,children:[t.jsx("div",{className:i().spinner}),t.jsx("span",{children:"Verifying..."})]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx("span",{children:"Verify Code"}),t.jsx("div",{className:i().buttonIcon,children:"✓"})]})})]}),(0,t.jsxs)("div",{className:i().footer,children:[t.jsx("div",{className:i().resendSection,children:m>0?(0,t.jsxs)("p",{className:i().resendTimer,children:["Resend code in ",m," seconds"]}):t.jsx("button",{type:"button",className:i().resendButton,onClick:f,disabled:n,children:"Resend Code"})}),t.jsx("div",{className:i().helpText,children:t.jsx("p",{children:"Having trouble? Contact your administrator for assistance."})})]})]})}},6814:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>l});var o=s(997),n=s(968),i=s.n(n);s(6689);var a=s(3590);s(8819),s(6764);var c=e([a]);function l({Component:e,pageProps:r}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(i(),{children:[o.jsx("meta",{charSet:"utf-8"}),o.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),o.jsx("meta",{httpEquiv:"X-Content-Type-Options",content:"nosniff"}),o.jsx("meta",{httpEquiv:"X-XSS-Protection",content:"1; mode=block"}),o.jsx("meta",{name:"referrer",content:"strict-origin-when-cross-origin"}),o.jsx("meta",{name:"robots",content:"noindex, nofollow, noarchive, nosnippet"}),o.jsx("meta",{name:"googlebot",content:"noindex, nofollow"}),o.jsx("meta",{name:"description",content:"Ocean Soul Sparkles Admin Portal - Secure staff access only"}),o.jsx("link",{rel:"icon",href:"/admin/favicon.ico"}),o.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/admin/apple-touch-icon.png"}),o.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/admin/favicon-32x32.png"}),o.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/admin/favicon-16x16.png"}),o.jsx("meta",{name:"theme-color",content:"#3788d8"}),o.jsx("meta",{name:"msapplication-TileColor",content:"#3788d8"}),o.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),o.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),o.jsx("link",{rel:"preconnect",href:"https://ndlgbcsbidyhxbpqzgqp.supabase.co"}),o.jsx("link",{rel:"dns-prefetch",href:"https://js.squareup.com"}),o.jsx("link",{rel:"dns-prefetch",href:"https://api.onesignal.com"}),o.jsx("title",{children:"Ocean Soul Sparkles Admin Portal"})]}),o.jsx(e,{...r}),o.jsx(a.ToastContainer,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light",toastStyle:{fontFamily:"inherit",fontSize:"14px"}}),o.jsx("div",{style:{position:"fixed",bottom:"10px",right:"10px",background:"rgba(0, 0, 0, 0.1)",color:"rgba(0, 0, 0, 0.3)",padding:"4px 8px",borderRadius:"4px",fontSize:"10px",fontWeight:"bold",pointerEvents:"none",zIndex:9999,userSelect:"none"},children:"ADMIN PORTAL"}),!1]})}a=(c.then?(await c)():c)[0],t()}catch(e){t(e)}})},4642:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>h});var o=s(997),n=s(6689),i=s(1163),a=s(968),c=s.n(a),l=s(3590),d=s(3913),m=s(7430),u=s(6314),p=s.n(u),_=e([l,d]);function h(){let e=(0,i.useRouter)(),[r,s]=(0,n.useState)({step:"login"}),[t,a]=(0,n.useState)(!1),u=async(r,t)=>{a(!0);try{let o=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,password:t})}),n=await o.json();if(!o.ok)throw Error(n.error||"Login failed");n.requiresMFA?(s({step:"mfa",user:n.user}),l.toast.info("Please enter your MFA code to complete login.")):(localStorage.setItem("admin-token",n.token),document.cookie=`admin-token=${n.token}; path=/; secure; samesite=strict`,l.toast.success(`Welcome back, ${n.user.firstName}!`),e.push("/admin/dashboard"))}catch(e){console.error("Login error:",e),s({step:"login",error:e instanceof Error?e.message:"Login failed"}),l.toast.error(e instanceof Error?e.message:"Login failed")}finally{a(!1)}},_=async s=>{if(r.user){a(!0);try{let t=await fetch("/api/auth/mfa-verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:r.user.id,mfaCode:s})}),o=await t.json();if(!t.ok)throw Error(o.error||"MFA verification failed");localStorage.setItem("admin-token",o.token),document.cookie=`admin-token=${o.token}; path=/; secure; samesite=strict`,l.toast.success(`Welcome back, ${o.user.firstName}!`),e.push("/admin/dashboard")}catch(e){console.error("MFA error:",e),l.toast.error(e instanceof Error?e.message:"MFA verification failed")}finally{a(!1)}}};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(c(),{children:[o.jsx("title",{children:"Admin Login - Ocean Soul Sparkles"}),o.jsx("meta",{name:"description",content:"Secure admin portal login for Ocean Soul Sparkles staff"}),o.jsx("meta",{name:"robots",content:"noindex, nofollow"})]}),(0,o.jsxs)("div",{className:p().loginContainer,children:[(0,o.jsxs)("div",{className:p().loginCard,children:[o.jsx("div",{className:p().logoSection,children:(0,o.jsxs)("div",{className:p().logo,children:[o.jsx("h1",{children:"Ocean Soul Sparkles"}),o.jsx("p",{children:"Admin Portal"})]})}),o.jsx("div",{className:p().formSection,children:"login"===r.step?o.jsx(d.Z,{onSubmit:u,isLoading:t,error:r.error}):o.jsx(m.Z,{user:r.user,onSubmit:_,onBack:()=>{s({step:"login"})},isLoading:t})}),(0,o.jsxs)("div",{className:p().securityNotice,children:[o.jsx("div",{className:p().securityIcon,children:"\uD83D\uDD12"}),(0,o.jsxs)("div",{className:p().securityText,children:[o.jsx("p",{children:"This is a secure admin portal."}),o.jsx("p",{children:"All access is monitored and logged."})]})]})]}),(0,o.jsxs)("div",{className:p().backgroundPattern,children:[o.jsx("div",{className:p().sparkle}),o.jsx("div",{className:p().sparkle}),o.jsx("div",{className:p().sparkle}),o.jsx("div",{className:p().sparkle}),o.jsx("div",{className:p().sparkle})]})]})]})}[l,d]=_.then?(await _)():_,t()}catch(e){t(e)}})},6764:()=>{},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},5641:e=>{"use strict";e.exports=import("react-hook-form")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[2899,6212,1664],()=>s(166));module.exports=t})();