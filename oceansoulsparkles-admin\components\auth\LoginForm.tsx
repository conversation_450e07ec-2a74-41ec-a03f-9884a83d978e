import { useState } from 'react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import styles from '../../styles/admin/LoginForm.module.css';

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface LoginFormProps {
  onSubmit: (email: string, password: string) => Promise<void>;
  isLoading: boolean;
  error?: string;
}

export default function LoginForm({ onSubmit, isLoading, error }: LoginFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<LoginFormData>();

  const handleFormSubmit = async (data: LoginFormData) => {
    try {
      await onSubmit(data.email, data.password);
    } catch (error) {
      // Error handling is done in parent component
      console.error('Login form error:', error);
    }
  };

  return (
    <div className={styles.loginForm}>
      <div className={styles.header}>
        <h2>Welcome Back</h2>
        <p>Sign in to your admin account</p>
      </div>

      {error && (
        <div className={styles.errorAlert}>
          <div className={styles.errorIcon}>⚠️</div>
          <div className={styles.errorMessage}>{error}</div>
        </div>
      )}

      <form onSubmit={handleSubmit(handleFormSubmit)} className={styles.form}>
        <div className={styles.formGroup}>
          <label htmlFor="email" className={styles.label}>
            Email Address
          </label>
          <div className={styles.inputContainer}>
            <input
              id="email"
              type="email"
              className={`${styles.input} ${errors.email ? styles.inputError : ''}`}
              placeholder="Enter your email"
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })}
              disabled={isLoading}
            />
            <div className={styles.inputIcon}>
              📧
            </div>
          </div>
          {errors.email && (
            <div className={styles.fieldError}>{errors.email.message}</div>
          )}
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="password" className={styles.label}>
            Password
          </label>
          <div className={styles.inputContainer}>
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              className={`${styles.input} ${errors.password ? styles.inputError : ''}`}
              placeholder="Enter your password"
              {...register('password', {
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters'
                }
              })}
              disabled={isLoading}
            />
            <button
              type="button"
              className={styles.passwordToggle}
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
            >
              {showPassword ? '🙈' : '👁️'}
            </button>
          </div>
          {errors.password && (
            <div className={styles.fieldError}>{errors.password.message}</div>
          )}
        </div>

        <div className={styles.formOptions}>
          <label className={styles.checkboxLabel}>
            <input
              type="checkbox"
              className={styles.checkbox}
              {...register('rememberMe')}
              disabled={isLoading}
            />
            <span className={styles.checkboxText}>Remember me</span>
          </label>

          <Link href="/admin/forgot-password" className={styles.forgotLink}>
            Forgot password?
          </Link>
        </div>

        <button
          type="submit"
          className={styles.submitButton}
          disabled={isLoading}
        >
          {isLoading ? (
            <div className={styles.loadingContainer}>
              <div className={styles.spinner}></div>
              <span>Signing in...</span>
            </div>
          ) : (
            <>
              <span>Sign In</span>
              <div className={styles.buttonIcon}>→</div>
            </>
          )}
        </button>
      </form>

      <div className={styles.footer}>
        <div className={styles.securityFeatures}>
          <div className={styles.feature}>
            <div className={styles.featureIcon}>🔐</div>
            <span>Multi-Factor Authentication</span>
          </div>
          <div className={styles.feature}>
            <div className={styles.featureIcon}>🛡️</div>
            <span>Advanced Security</span>
          </div>
          <div className={styles.feature}>
            <div className={styles.featureIcon}>📊</div>
            <span>Audit Logging</span>
          </div>
        </div>
      </div>
    </div>
  );
}
