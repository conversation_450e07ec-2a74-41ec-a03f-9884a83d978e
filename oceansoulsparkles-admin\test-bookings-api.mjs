import fetch from 'node-fetch';

async function testBookingsAPI() {
  console.log('🔍 Testing Bookings API Endpoint...\n');

  const baseUrl = 'http://localhost:3002';
  const apiBaseUrl = 'http://localhost:3002/api';

  try {
    // Step 1: Login to get token
    console.log('1. Logging in...');
    const loginData = {
      email: '<EMAIL>',
      password: 'admin123'
    };

    const loginResponse = await fetch(`${apiBaseUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(loginData)
    });

    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      console.log(`❌ Login failed: ${errorText}`);
      return;
    }

    const loginResult = await loginResponse.json();
    const token = loginResult.token;
    console.log(`✅ Login successful`);

    // Step 2: Test bookings API
    console.log('\n2. Testing bookings API...');
    const bookingsResponse = await fetch(`${apiBaseUrl}/admin/bookings`, {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Status: ${bookingsResponse.status}`);
    
    if (bookingsResponse.ok) {
      const bookingsData = await bookingsResponse.json();
      console.log(`✅ Bookings API working!`);
      console.log(`📅 Found ${bookingsData.bookings?.length || 0} bookings`);
      
      if (bookingsData.bookings && bookingsData.bookings.length > 0) {
        const sample = bookingsData.bookings[0];
        console.log(`\n📋 Sample booking:`);
        console.log(`   ID: ${sample.id}`);
        console.log(`   Customer: ${sample.customer_name}`);
        console.log(`   Service: ${sample.service_name}`);
        console.log(`   Date: ${sample.booking_date}`);
        console.log(`   Time: ${sample.booking_time}`);
        console.log(`   Status: ${sample.status}`);
        console.log(`   Amount: $${sample.total_amount}`);
        console.log(`   Artist: ${sample.artist_name}`);
      }
    } else {
      const errorText = await bookingsResponse.text();
      console.log(`❌ Bookings API failed: ${errorText}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testBookingsAPI();
