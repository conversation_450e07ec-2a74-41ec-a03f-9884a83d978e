(()=>{var e={};e.id=2371,e.ids=[2371,660],e.modules={6806:e=>{e.exports={exportButton:"ExportButton_exportButton__utvly",exportBtn:"ExportButton_exportBtn__tIopM",exporting:"ExportButton_exporting__sXhBs",spinner:"ExportButton_spinner__9VMfk",spin:"ExportButton_spin__rvyvr",dropdownArrow:"ExportButton_dropdownArrow__ukQOV",exportDropdown:"ExportButton_exportDropdown__Rv4p1",slideDown:"ExportButton_slideDown__Cf7OP",dropdownHeader:"ExportButton_dropdownHeader__ZrlI_",dropdownItem:"ExportButton_dropdownItem__fkbTm",disabled:"ExportButton_disabled__D8pyR",formatIcon:"ExportButton_formatIcon__jrB0p",formatInfo:"ExportButton_formatInfo__CpXUd",formatName:"ExportButton_formatName__yKvvD",formatDesc:"ExportButton_formatDesc__vDwii",success:"ExportButton_success__suWHu",successPulse:"ExportButton_successPulse__zvjZh"}},9722:e=>{e.exports={inventoryContainer:"Inventory_inventoryContainer__GcfkW",header:"Inventory_header__YsGK_",title:"Inventory_title__Y0smo",headerActions:"Inventory_headerActions__sakeA",newItemBtn:"Inventory_newItemBtn__L0yQN",backButton:"Inventory_backButton__j1uiL",controlsPanel:"Inventory_controlsPanel__ptM_3",searchSection:"Inventory_searchSection__Y9Hgr",searchInput:"Inventory_searchInput__MExRL",filters:"Inventory_filters__LHQyS",categoryFilter:"Inventory_categoryFilter__0FZb_",stockFilter:"Inventory_stockFilter__Fq41i",sortSelect:"Inventory_sortSelect__w6Bd6",inventoryContent:"Inventory_inventoryContent__MZtM5",statsCards:"Inventory_statsCards__zQ0vN",statCard:"Inventory_statCard__281a_",statValue:"Inventory_statValue__fmJux",emptyState:"Inventory_emptyState__JwMRE",inventoryGrid:"Inventory_inventoryGrid__6nI_9",inventoryCard:"Inventory_inventoryCard__h1i83",itemHeader:"Inventory_itemHeader__6a78v",itemInfo:"Inventory_itemInfo__6vyyD",category:"Inventory_category__uUYPL",statusBadge:"Inventory_statusBadge__293g1",statusInStock:"Inventory_statusInStock__4rFKW",statusLowStock:"Inventory_statusLowStock__mOjky",statusCritical:"Inventory_statusCritical__iI7Nz",statusOutOfStock:"Inventory_statusOutOfStock__8GWFB",statusDefault:"Inventory_statusDefault__bCVl8",itemDetails:"Inventory_itemDetails__Jlyif",stockInfo:"Inventory_stockInfo__dQ4ll",stockLevel:"Inventory_stockLevel__0YpyD",currentStock:"Inventory_currentStock__0ZyIE",stockRange:"Inventory_stockRange__tajLE",stockBar:"Inventory_stockBar__R6ZDA",stockFill:"Inventory_stockFill__IbWUp",itemStats:"Inventory_itemStats__orFkd",statItem:"Inventory_statItem__Q085T",statLabel:"Inventory_statLabel__yHJ_o",itemActions:"Inventory_itemActions__F5Ub9",editBtn:"Inventory_editBtn__Gdnet",restockBtn:"Inventory_restockBtn__yaupq",loadingContainer:"Inventory_loadingContainer__28CuE",loadingSpinner:"Inventory_loadingSpinner__DNa6M",spin:"Inventory_spin__kif5C"}},6614:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>v,default:()=>m,getServerSideProps:()=>y,getStaticPaths:()=>p,getStaticProps:()=>_,reportWebVitals:()=>x,routeModule:()=>j,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>g,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>k,unstable_getStaticProps:()=>h});var s=r(7093),n=r(5244),o=r(1323),l=r(2899),i=r.n(l),c=r(6814),d=r(1586),u=e([c,d]);[c,d]=u.then?(await u)():u;let m=(0,o.l)(d,"default"),_=(0,o.l)(d,"getStaticProps"),p=(0,o.l)(d,"getStaticPaths"),y=(0,o.l)(d,"getServerSideProps"),v=(0,o.l)(d,"config"),x=(0,o.l)(d,"reportWebVitals"),h=(0,o.l)(d,"unstable_getStaticProps"),k=(0,o.l)(d,"unstable_getStaticPaths"),f=(0,o.l)(d,"unstable_getStaticParams"),b=(0,o.l)(d,"unstable_getServerProps"),g=(0,o.l)(d,"unstable_getServerSideProps"),j=new s.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/inventory",pathname:"/admin/inventory",bundlePath:"",filename:""},components:{App:c.default,Document:i()},userland:d});a()}catch(e){a(e)}})},5179:(e,t,r)=>{"use strict";r.d(t,{F:()=>c});var a=r(997),s=r(6689),n=r(8370),o=r(6806),l=r.n(o);function i({data:e,type:t,className:o="",disabled:i=!1,options:c={},onExportStart:d,onExportComplete:u,onExportError:m}){let[_,p]=(0,s.useState)(!1),[y,v]=(0,s.useState)(!1),x=(0,s.useRef)(null),h=async(a="csv")=>{if(!e||0===e.length){m?.(Error("No data to export"));return}p(!0),v(!1),d?.();try{if("csv"===a)switch(t){case"bookings":(0,n.FC)(e,c);break;case"services":(0,n.r8)(e,c);break;case"customers":(0,n.Aq)(e,c);break;case"products":(0,n.yA)(e,c);break;case"inventory":(0,n.sg)(e,c);break;case"custom":let{exportToCSV:s}=await Promise.resolve().then(r.bind(r,8370));s(e,c);break;default:throw Error(`Unsupported export type: ${t}`)}else throw Error(`Export format ${a} not yet supported`);u?.()}catch(e){console.error("Export error:",e),m?.(e instanceof Error?e:Error("Export failed"))}finally{p(!1)}};return(0,a.jsxs)("div",{className:`${l().exportButton} ${o}`,ref:x,children:[(0,a.jsxs)("button",{onClick:()=>v(!y),disabled:i||_||0===e.length,className:`${l().exportBtn} ${_?l().exporting:""}`,title:i||0===e.length?"No data available to export":`Export ${e.length} ${t} records`,children:[_&&a.jsx("div",{className:l().spinner}),_?"Exporting...":0===e.length?"No Data":`📥 Export (${e.length})`,!_&&e.length>0&&a.jsx("span",{className:l().dropdownArrow,children:"▼"})]}),y&&e.length>0&&(0,a.jsxs)("div",{className:l().exportDropdown,children:[a.jsx("div",{className:l().dropdownHeader,children:a.jsx("span",{children:"Export Format"})}),(0,a.jsxs)("button",{onClick:()=>h("csv"),className:l().dropdownItem,disabled:_,children:[a.jsx("span",{className:l().formatIcon,children:"\uD83D\uDCC4"}),(0,a.jsxs)("div",{className:l().formatInfo,children:[a.jsx("div",{className:l().formatName,children:"CSV File"}),a.jsx("div",{className:l().formatDesc,children:"Comma-separated values"})]})]}),(0,a.jsxs)("button",{onClick:()=>h("excel"),className:`${l().dropdownItem} ${l().disabled}`,disabled:!0,title:"Excel export coming soon",children:[a.jsx("span",{className:l().formatIcon,children:"\uD83D\uDCCA"}),(0,a.jsxs)("div",{className:l().formatInfo,children:[a.jsx("div",{className:l().formatName,children:"Excel File"}),a.jsx("div",{className:l().formatDesc,children:"Coming soon"})]})]}),(0,a.jsxs)("button",{onClick:()=>h("pdf"),className:`${l().dropdownItem} ${l().disabled}`,disabled:!0,title:"PDF export coming soon",children:[a.jsx("span",{className:l().formatIcon,children:"\uD83D\uDCCB"}),(0,a.jsxs)("div",{className:l().formatInfo,children:[a.jsx("div",{className:l().formatName,children:"PDF Report"}),a.jsx("div",{className:l().formatDesc,children:"Coming soon"})]})]})]})]})}let c=({data:e,type:t,className:r="",...s})=>a.jsx(i,{data:e,type:t,className:r,onExportError:e=>{console.error("Export error:",e),alert(`Export failed: ${e.message}`)},...s})},8370:(e,t,r)=>{"use strict";function a(e,t={}){let{filename:r="export",columns:a,includeTimestamp:s=!0,dateFormat:n="short"}=t;if(!e||0===e.length)throw Error("No data to export");let o=a||Object.keys(e[0]).map(e=>({key:e,label:e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())})),l=[o.map(e=>e.label),...e.map(e=>o.map(t=>{let r=t.key.split(".").reduce((e,t)=>e?.[t],e);return t.formatter?t.formatter(r,e):null==r?"":"boolean"==typeof r?r?"Yes":"No":r instanceof Date||"string"==typeof r&&/^\d{4}-\d{2}-\d{2}/.test(r)&&!isNaN(Date.parse(r))?d(r):String(r)}))].map(e=>e.map(e=>`"${String(e||"").replace(/"/g,'""')}"`).join(",")).join("\n"),i=s?`-${new Date().toISOString().split("T")[0]}`:"";(function(e,t,r){let a=new Blob([e],{type:r}),s=window.URL.createObjectURL(a),n=document.createElement("a");n.href=s,n.download=t,document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(s),document.body.removeChild(n)})(l,`${r}${i}.csv`,"text/csv")}function s(e,t={}){a(e,{filename:"bookings",columns:[{key:"customer_name",label:"Customer Name"},{key:"customer_email",label:"Customer Email"},{key:"customer_phone",label:"Customer Phone"},{key:"service_name",label:"Service"},{key:"artist_name",label:"Artist"},{key:"booking_date",label:"Date",formatter:e=>c(e)},{key:"booking_time",label:"Time"},{key:"status",label:"Status",formatter:e=>e?.toUpperCase()||"UNKNOWN"},{key:"total_amount",label:"Amount",formatter:e=>u(e)},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function n(e,t={}){a(e,{filename:"services",columns:[{key:"name",label:"Service Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"base_price",label:"Base Price",formatter:e=>u(e)},{key:"duration",label:"Duration (min)"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"total_bookings",label:"Total Bookings"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function o(e,t={}){a(e,{filename:"customers",columns:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"email",label:"Email"},{key:"phone",label:"Phone"},{key:"total_bookings",label:"Total Bookings"},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function l(e,t={}){a(e,{filename:"products",columns:[{key:"name",label:"Product Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"price",label:"Price",formatter:e=>u(e)},{key:"stock_quantity",label:"Stock"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function i(e,t={}){a(e,{filename:"inventory",columns:[{key:"name",label:"Item Name"},{key:"category",label:"Category"},{key:"current_stock",label:"Current Stock"},{key:"minimum_stock",label:"Minimum Stock"},{key:"unit_cost",label:"Unit Cost",formatter:e=>u(e)},{key:"supplier_name",label:"Supplier"},{key:"last_restocked",label:"Last Restocked",formatter:e=>c(e)},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function c(e){return e?new Date(e).toLocaleDateString("en-AU"):""}function d(e){if(!e)return"";let t=new Date(e);return t.toLocaleDateString("en-AU")+" "+t.toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit"})}function u(e){return!e||isNaN(e)?"$0.00":new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(Number(e))}r.d(t,{Aq:()=>o,FC:()=>s,exportToCSV:()=>a,r8:()=>n,sg:()=>i,yA:()=>l})},1586:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>v});var s=r(997),n=r(6689),o=r(968),l=r.n(o),i=r(1664),c=r.n(i),d=r(8568),u=r(4845),m=r(5179),_=r(9722),p=r.n(_),y=e([u]);function v(){let{user:e,loading:t}=(0,d.a)(),[r,a]=(0,n.useState)(!0),[o,i]=(0,n.useState)([]),[_,y]=(0,n.useState)([]),[v,x]=(0,n.useState)(""),[h,k]=(0,n.useState)("all"),[f,b]=(0,n.useState)("all"),[g,j]=(0,n.useState)("name"),N=e=>{switch(e){case"in_stock":return"#22c55e";case"low_stock":return"#f59e0b";case"out_of_stock":return"#ef4444";default:return"#6b7280"}},I=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e);return t||r?s.jsx(u.Z,{children:(0,s.jsxs)("div",{className:p().loadingContainer,children:[s.jsx("div",{className:p().loadingSpinner}),s.jsx("p",{children:"Loading inventory..."})]})}):e?(0,s.jsxs)(u.Z,{children:[(0,s.jsxs)(l(),{children:[s.jsx("title",{children:"Inventory Management | Ocean Soul Sparkles Admin"}),s.jsx("meta",{name:"description",content:"Manage product inventory and stock levels"})]}),(0,s.jsxs)("div",{className:p().inventoryContainer,children:[(0,s.jsxs)("header",{className:p().header,children:[s.jsx("h1",{className:p().title,children:"Inventory Management"}),(0,s.jsxs)("div",{className:p().headerActions,children:[s.jsx(m.F,{data:_,type:"inventory",className:p().exportBtn}),s.jsx(c(),{href:"/admin/inventory/new",className:p().newItemBtn,children:"+ Add Product"})]})]}),(0,s.jsxs)("div",{className:p().controlsPanel,children:[s.jsx("div",{className:p().searchSection,children:s.jsx("input",{type:"text",placeholder:"Search products by name, category, or SKU...",value:v,onChange:e=>x(e.target.value),className:p().searchInput})}),(0,s.jsxs)("div",{className:p().filtersSection,children:[(0,s.jsxs)("div",{className:p().filterGroup,children:[s.jsx("label",{children:"Category:"}),(0,s.jsxs)("select",{value:h,onChange:e=>k(e.target.value),className:p().filterSelect,children:[s.jsx("option",{value:"all",children:"All Categories"}),s.jsx("option",{value:"Braiding Hair",children:"Braiding Hair"}),s.jsx("option",{value:"Marley Hair",children:"Marley Hair"}),s.jsx("option",{value:"Hair Products",children:"Hair Products"}),s.jsx("option",{value:"Accessories",children:"Accessories"})]})]}),(0,s.jsxs)("div",{className:p().filterGroup,children:[s.jsx("label",{children:"Stock Status:"}),(0,s.jsxs)("select",{value:f,onChange:e=>b(e.target.value),className:p().filterSelect,children:[s.jsx("option",{value:"all",children:"All Stock Levels"}),s.jsx("option",{value:"in_stock",children:"In Stock"}),s.jsx("option",{value:"low_stock",children:"Low Stock"}),s.jsx("option",{value:"out_of_stock",children:"Out of Stock"})]})]}),(0,s.jsxs)("div",{className:p().filterGroup,children:[s.jsx("label",{children:"Sort by:"}),(0,s.jsxs)("select",{value:g,onChange:e=>j(e.target.value),className:p().filterSelect,children:[s.jsx("option",{value:"name",children:"Name"}),s.jsx("option",{value:"category",children:"Category"}),s.jsx("option",{value:"stock",children:"Stock Level"}),s.jsx("option",{value:"price",children:"Price"})]})]})]})]}),s.jsx("div",{className:p().inventoryContent,children:0===_.length?(0,s.jsxs)("div",{className:p().emptyState,children:[s.jsx("h3",{children:"No inventory items found"}),s.jsx("p",{children:0===o.length?"Get started by adding your first product to the inventory.":"Try adjusting your search or filter criteria."}),s.jsx(c(),{href:"/admin/inventory/new",className:p().addFirstBtn,children:"Add First Product"})]}):s.jsx("div",{className:p().inventoryGrid,children:_.map(e=>(0,s.jsxs)("div",{className:p().inventoryCard,children:[(0,s.jsxs)("div",{className:p().cardHeader,children:[s.jsx("h3",{className:p().itemName,children:e.name}),s.jsx("span",{className:p().statusBadge,style:{backgroundColor:N(e.status)},children:e.status.replace("_"," ").toUpperCase()})]}),(0,s.jsxs)("div",{className:p().cardBody,children:[(0,s.jsxs)("div",{className:p().itemInfo,children:[s.jsx("p",{className:p().category,children:e.category}),e.sku&&(0,s.jsxs)("p",{className:p().sku,children:["SKU: ",e.sku]}),e.description&&s.jsx("p",{className:p().description,children:e.description})]}),(0,s.jsxs)("div",{className:p().stockInfo,children:[(0,s.jsxs)("div",{className:p().stockLevel,children:[s.jsx("span",{className:p().label,children:"Stock:"}),(0,s.jsxs)("span",{className:p().value,children:[e.stock_quantity||0,e.min_stock_level&&` (Min: ${e.min_stock_level})`]})]}),e.sale_price&&(0,s.jsxs)("div",{className:p().priceInfo,children:[s.jsx("span",{className:p().label,children:"Price:"}),s.jsx("span",{className:p().value,children:I(e.sale_price)})]}),e.supplier&&(0,s.jsxs)("div",{className:p().supplierInfo,children:[s.jsx("span",{className:p().label,children:"Supplier:"}),s.jsx("span",{className:p().value,children:e.supplier})]})]})]}),(0,s.jsxs)("div",{className:p().cardActions,children:[s.jsx(c(),{href:`/admin/inventory/${e.id}`,className:p().viewBtn,children:"View Details"}),s.jsx("button",{className:p().editBtn,children:"Edit"}),(e.stock_quantity||0)<=(e.min_stock_level||0)&&s.jsx("button",{className:p().restockBtn,children:"Restock"})]})]},e.id))})})]})]}):null}u=(y.then?(await y)():y)[0],a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[2899,6212,1664,7441],()=>r(6614));module.exports=a})();