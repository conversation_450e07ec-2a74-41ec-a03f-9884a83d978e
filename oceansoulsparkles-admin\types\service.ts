/**
 * Ocean Soul Sparkles Admin Dashboard - Service Types
 * Service management and pricing types
 */

import { 
  ID, 
  Timestamp, 
  Status,
  Money,
  PriceRange,
  MediaFile,
  Category,
  Tag,
  SEOMetadata,
  CustomField,
  Rating
} from './common';

// Core service interface
export interface Service {
  id: ID;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  
  // Categorization
  categoryId?: ID;
  category?: Category;
  tags?: Tag[];
  
  // Pricing
  basePrice: Money;
  priceRange?: PriceRange;
  pricingType: 'fixed' | 'tiered' | 'hourly' | 'custom';
  
  // Timing
  duration: number; // Minutes
  minDuration?: number;
  maxDuration?: number;
  bufferTime: number; // Minutes between bookings
  setupTime?: number; // Minutes for preparation
  cleanupTime?: number; // Minutes for cleanup
  
  // Availability
  isActive: boolean;
  isBookable: boolean;
  isOnlineBookable: boolean;
  requiresConsultation: boolean;
  
  // Media and presentation
  featuredImage?: MediaFile;
  gallery?: MediaFile[];
  videoUrl?: string;
  
  // Service details
  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  ageRestrictions?: {
    minAge?: number;
    maxAge?: number;
    requiresParentConsent?: boolean;
  };
  
  // Requirements and restrictions
  prerequisites?: string[];
  contraindications?: string[];
  aftercareInstructions?: string;
  
  // Location and equipment
  availableLocations: Array<'studio' | 'mobile' | 'client_home'>;
  requiredEquipment?: string[];
  requiredProducts?: ID[]; // Product IDs
  
  // Artist requirements
  requiredSkills?: string[];
  certificationRequired?: boolean;
  experienceLevel?: 'junior' | 'senior' | 'master';
  
  // Booking settings
  advanceBookingDays: number;
  cancellationPolicy?: string;
  reschedulePolicy?: string;
  depositRequired: boolean;
  depositAmount?: Money;
  depositPercentage?: number;
  
  // SEO and marketing
  seoMetadata?: SEOMetadata;
  isFeatured: boolean;
  isPopular: boolean;
  promotionalText?: string;
  
  // Analytics and performance
  bookingCount: number;
  rating?: Rating;
  revenue: Money;
  
  // Custom fields
  customFields?: CustomField[];
  
  // System fields
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy?: ID;
  updatedBy?: ID;
}

// Service pricing tiers
export interface ServiceTier {
  id: ID;
  serviceId: ID;
  name: string;
  description?: string;
  price: Money;
  duration?: number; // Override service duration
  features?: string[];
  isDefault: boolean;
  isActive: boolean;
  sortOrder: number;
  
  // Availability
  maxBookingsPerDay?: number;
  maxBookingsPerWeek?: number;
  availableDays?: number[]; // 0-6 (Sunday-Saturday)
  availableTimeSlots?: Array<{
    start: string; // HH:MM
    end: string; // HH:MM
  }>;
  
  // Requirements
  requiredArtistLevel?: Service['experienceLevel'];
  requiredCertifications?: string[];
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Service creation and update types
export interface CreateServiceData {
  name: string;
  slug?: string;
  description: string;
  shortDescription?: string;
  categoryId?: ID;
  tags?: string[];
  basePrice: Money;
  pricingType: Service['pricingType'];
  duration: number;
  bufferTime?: number;
  setupTime?: number;
  cleanupTime?: number;
  isActive?: boolean;
  isBookable?: boolean;
  isOnlineBookable?: boolean;
  requiresConsultation?: boolean;
  skillLevel?: Service['skillLevel'];
  ageRestrictions?: Service['ageRestrictions'];
  prerequisites?: string[];
  contraindications?: string[];
  aftercareInstructions?: string;
  availableLocations?: Service['availableLocations'];
  requiredEquipment?: string[];
  requiredProducts?: ID[];
  requiredSkills?: string[];
  certificationRequired?: boolean;
  experienceLevel?: Service['experienceLevel'];
  advanceBookingDays?: number;
  cancellationPolicy?: string;
  reschedulePolicy?: string;
  depositRequired?: boolean;
  depositAmount?: Money;
  depositPercentage?: number;
  seoMetadata?: SEOMetadata;
  isFeatured?: boolean;
  promotionalText?: string;
  customFields?: CustomField[];
}

export interface UpdateServiceData extends Partial<CreateServiceData> {
  id: ID;
}

// Service search and filtering
export interface ServiceSearchParams {
  query?: string;
  categoryId?: ID[];
  tags?: string[];
  priceMin?: number;
  priceMax?: number;
  durationMin?: number;
  durationMax?: number;
  skillLevel?: Service['skillLevel'][];
  availableLocations?: Service['availableLocations'];
  isActive?: boolean;
  isBookable?: boolean;
  isOnlineBookable?: boolean;
  isFeatured?: boolean;
  isPopular?: boolean;
  requiresConsultation?: boolean;
  depositRequired?: boolean;
  experienceLevel?: Service['experienceLevel'][];
}

export interface ServiceFilters {
  search: string;
  category: ID | 'all';
  priceRange: {
    min?: number;
    max?: number;
  } | null;
  duration: {
    min?: number;
    max?: number;
  } | null;
  skillLevel: Service['skillLevel'] | 'all';
  location: Service['availableLocations'][0] | 'all';
  status: 'active' | 'inactive' | 'all';
  featured: boolean | null;
  bookable: boolean | null;
}

// Service analytics and reporting
export interface ServiceAnalytics {
  totalServices: number;
  activeServices: number;
  bookableServices: number;
  featuredServices: number;
  
  // Performance metrics
  topPerformingServices: Array<{
    serviceId: ID;
    serviceName: string;
    bookingCount: number;
    revenue: Money;
    averageRating?: number;
    conversionRate: number; // Percentage
  }>;
  
  // Revenue analysis
  revenueByService: Array<{
    serviceId: ID;
    serviceName: string;
    revenue: Money;
    percentage: number;
  }>;
  
  // Booking patterns
  bookingsByService: Array<{
    serviceId: ID;
    serviceName: string;
    bookingCount: number;
    averageBookingValue: Money;
    repeatCustomerRate: number; // Percentage
  }>;
  
  // Category performance
  categoryPerformance: Array<{
    categoryId: ID;
    categoryName: string;
    serviceCount: number;
    bookingCount: number;
    revenue: Money;
  }>;
  
  // Pricing analysis
  pricingAnalysis: {
    averageServicePrice: Money;
    priceDistribution: Array<{
      range: string; // e.g., "$0-$50"
      count: number;
      percentage: number;
    }>;
    mostExpensiveService: {
      id: ID;
      name: string;
      price: Money;
    };
    leastExpensiveService: {
      id: ID;
      name: string;
      price: Money;
    };
  };
  
  // Duration analysis
  durationAnalysis: {
    averageDuration: number; // Minutes
    durationDistribution: Array<{
      range: string; // e.g., "30-60 min"
      count: number;
      percentage: number;
    }>;
    longestService: {
      id: ID;
      name: string;
      duration: number;
    };
    shortestService: {
      id: ID;
      name: string;
      duration: number;
    };
  };
}

// Service packages and bundles
export interface ServicePackage {
  id: ID;
  name: string;
  description?: string;
  services: Array<{
    serviceId: ID;
    quantity: number;
    discountPercentage?: number;
  }>;
  packagePrice: Money;
  individualPrice: Money;
  savings: Money;
  validityPeriod?: number; // Days
  maxUsagePerService?: number;
  isActive: boolean;
  isFeatured: boolean;
  terms?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Service add-ons and extras
export interface ServiceAddon {
  id: ID;
  serviceId: ID;
  name: string;
  description?: string;
  price: Money;
  duration?: number; // Additional minutes
  isOptional: boolean;
  isDefault: boolean;
  maxQuantity?: number;
  requiredFor?: string[]; // Conditions when required
  availableFor?: string[]; // Service tiers this applies to
  isActive: boolean;
  sortOrder: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Service availability and scheduling
export interface ServiceAvailability {
  serviceId: ID;
  artistId?: ID; // If null, applies to all artists
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  timeSlots: Array<{
    start: string; // HH:MM
    end: string; // HH:MM
    maxBookings?: number;
  }>;
  isActive: boolean;
  effectiveFrom?: string; // ISO date
  effectiveUntil?: string; // ISO date
}

export interface ServiceBlackout {
  id: ID;
  serviceId: ID;
  artistId?: ID;
  startDate: string; // ISO date
  endDate: string; // ISO date
  startTime?: string; // HH:MM
  endTime?: string; // HH:MM
  reason: string;
  isRecurring: boolean;
  recurrencePattern?: {
    type: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    endDate?: string;
  };
  createdAt: Timestamp;
  createdBy: ID;
}

// Service reviews and ratings
export interface ServiceReview {
  id: ID;
  serviceId: ID;
  customerId: ID;
  bookingId?: ID;
  rating: number; // 1-5
  title?: string;
  comment: string;
  pros?: string[];
  cons?: string[];
  wouldRecommend: boolean;
  isVerified: boolean;
  isPublic: boolean;
  helpfulVotes: number;
  reportCount: number;
  response?: {
    comment: string;
    respondedBy: ID;
    respondedAt: Timestamp;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Service templates and presets
export interface ServiceTemplate {
  id: ID;
  name: string;
  description?: string;
  category: string;
  template: Partial<CreateServiceData>;
  isPublic: boolean;
  usageCount: number;
  createdBy: ID;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Service import/export
export interface ServiceImportData {
  name: string;
  description: string;
  categoryName?: string;
  basePrice: number;
  duration: number;
  isActive?: boolean;
  isBookable?: boolean;
  tags?: string; // Comma-separated
}

export interface ServiceExportData extends Service {
  categoryName?: string;
  tagsFormatted?: string; // Comma-separated
  totalBookings: number;
  totalRevenue: number;
  averageRating?: number;
}
