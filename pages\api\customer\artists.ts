import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { specialization } = req.query;

    // Build query for active artists
    let query = supabase
      .from('artist_profiles')
      .select(`
        id,
        name,
        specializations,
        bio,
        rating,
        total_bookings,
        artist_portfolio_items (
          id,
          image_url,
          title,
          category,
          is_featured,
          is_public
        )
      `)
      .eq('is_active', true);

    // Filter by specialization if provided
    if (specialization && typeof specialization === 'string') {
      query = query.contains('specializations', [specialization]);
    }

    const { data: artists, error } = await query
      .order('rating', { ascending: false })
      .order('total_bookings', { ascending: false });

    if (error) {
      console.error('Artists query error:', error);
      return res.status(500).json({ error: 'Failed to fetch artists' });
    }

    // Transform artists data for customer portal
    const transformedArtists = artists?.map(artist => {
      // Filter portfolio items to only show public and featured items
      const publicPortfolio = artist.artist_portfolio_items?.filter(item => 
        item.is_public && item.is_featured
      ) || [];

      return {
        id: artist.id,
        name: artist.name,
        specializations: artist.specializations || [],
        bio: artist.bio,
        rating: artist.rating,
        total_bookings: artist.total_bookings,
        portfolio_preview: publicPortfolio.slice(0, 6).map(item => ({
          id: item.id,
          image_url: item.image_url,
          title: item.title,
          category: item.category
        })),
        portfolio_count: publicPortfolio.length
      };
    }) || [];

    // Group artists by specialization
    const artistsBySpecialization = transformedArtists.reduce((acc, artist) => {
      artist.specializations.forEach(spec => {
        if (!acc[spec]) {
          acc[spec] = [];
        }
        acc[spec].push(artist);
      });
      return acc;
    }, {} as Record<string, any[]>);

    return res.status(200).json({
      artists: transformedArtists,
      artistsBySpecialization,
      total: transformedArtists.length,
      specializations: Object.keys(artistsBySpecialization)
    });

  } catch (error) {
    console.error('Customer artists API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
