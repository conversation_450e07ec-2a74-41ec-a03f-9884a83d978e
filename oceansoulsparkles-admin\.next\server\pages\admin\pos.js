(()=>{var e={};e.id=2451,e.ids=[2451,660],e.modules={1073:e=>{e.exports={posContainer:"POS_posContainer__6tGk7",header:"POS_header__NK5rM",title:"POS_title__aubSl",headerActions:"POS_headerActions__qWBFv",backButton:"POS_backButton__LBI7I",posLayout:"POS_posLayout__E66LX",servicesPanel:"POS_servicesPanel__5pgzh",serviceCategories:"POS_serviceCategories__GWW0s",categorySection:"POS_categorySection__VeYj5",categoryTitle:"POS_categoryTitle__4bPnN",serviceGrid:"POS_serviceGrid__ebdfY",serviceCard:"POS_serviceCard__895l8",serviceName:"POS_serviceName__HDmnI",servicePrice:"POS_servicePrice__8UUJz",serviceDuration:"POS_serviceDuration__9hI3C",cartPanel:"POS_cartPanel__6lBFL",customerSection:"POS_customerSection__59W3o",customerInfo:"POS_customerInfo__B3ZRB",changeCustomerBtn:"POS_changeCustomerBtn__E2veb",customerActions:"POS_customerActions__GZ3NQ",addCustomerBtn:"POS_addCustomerBtn__wIE0A",walkInBtn:"POS_walkInBtn__vNBwC",cartItems:"POS_cartItems__fuaCS",emptyCart:"POS_emptyCart__LyoKT",cartItem:"POS_cartItem__l0Fl8",itemInfo:"POS_itemInfo__qS4zm",itemName:"POS_itemName__rLI5l",itemPrice:"POS_itemPrice__iy5yI",itemControls:"POS_itemControls__EQzIb",quantityBtn:"POS_quantityBtn__MK9lD",quantity:"POS_quantity__tdGdn",removeBtn:"POS_removeBtn__AD3a2",paymentSection:"POS_paymentSection__j0cHT",total:"POS_total__xKac7",paymentMethods:"POS_paymentMethods__ov_uJ",processPaymentBtn:"POS_processPaymentBtn__C3Gwh",loadingContainer:"POS_loadingContainer__kkXgY",loadingSpinner:"POS_loadingSpinner__6iT1G",spin:"POS_spin__lN_rC",tipContainer:"POS_tipContainer__WAda1",tipContent:"POS_tipContent__ZMLeX",tipHeader:"POS_tipHeader__fKlmQ",tipCancelButton:"POS_tipCancelButton__OJGRW",tipError:"POS_tipError__Ud654",tipTypeSelector:"POS_tipTypeSelector__M1AHY",tipTypeButton:"POS_tipTypeButton__XlWOo",active:"POS_active__0NVhY",tipPercentageOptions:"POS_tipPercentageOptions__d73A_",tipCustomAmount:"POS_tipCustomAmount__WgJfq",tipPercentageGrid:"POS_tipPercentageGrid__89Hw3",tipPercentageButton:"POS_tipPercentageButton__d_62n",tipAmount:"POS_tipAmount__akuv3",customPercentageInput:"POS_customPercentageInput__aU4YQ",customAmountInput:"POS_customAmountInput__6_IUM",currencySymbol:"POS_currencySymbol__eHGaF",tipSummary:"POS_tipSummary__4XL8d",tipSummaryRow:"POS_tipSummaryRow___7Qgq",tipActions:"POS_tipActions__GvIDY",tipConfirmButton:"POS_tipConfirmButton__BbmXo"}},4965:e=>{e.exports={actionSheetOverlay:"ActionSheet_actionSheetOverlay__yen_3",open:"ActionSheet_open__Hqn_E",actionSheet:"ActionSheet_actionSheet__HwaQY",header:"ActionSheet_header__WkQyp",title:"ActionSheet_title__Kafbq",message:"ActionSheet_message__M0048",actions:"ActionSheet_actions__5vv86",action:"ActionSheet_action__9jhG3",disabled:"ActionSheet_disabled__OuV96",actionIcon:"ActionSheet_actionIcon__KEHQ3",actionLabel:"ActionSheet_actionLabel__gHSk7",default:"ActionSheet_default__R4F3P",primary:"ActionSheet_primary__1jAts",destructive:"ActionSheet_destructive__lYuSp",cancelSection:"ActionSheet_cancelSection__lYm__",cancel:"ActionSheet_cancel__4RRXj"}},3071:e=>{e.exports={modalOverlay:"MobileModal_modalOverlay__ZdRL2",open:"MobileModal_open__X9QxD",modal:"MobileModal_modal__wTj8E",small:"MobileModal_small__JcgAU",medium:"MobileModal_medium__WXukv",large:"MobileModal_large__6mrX0",fullscreen:"MobileModal_fullscreen__W3TP5",swipeIndicator:"MobileModal_swipeIndicator__N0XDY",swipeHandle:"MobileModal_swipeHandle__fQnw5",header:"MobileModal_header__vvWME",headerLeft:"MobileModal_headerLeft__HMkuh",headerRight:"MobileModal_headerRight__OJHbB",headerCenter:"MobileModal_headerCenter__7jTO8",title:"MobileModal_title__s6Bam",closeButton:"MobileModal_closeButton__OtLTW",content:"MobileModal_content__Aqrjr",confirmationContent:"MobileModal_confirmationContent__j6zqr",confirmationMessage:"MobileModal_confirmationMessage__076fN",confirmationActions:"MobileModal_confirmationActions__QISCA",button:"MobileModal_button__0S4Iy",buttonPrimary:"MobileModal_buttonPrimary__XnvLd",buttonSecondary:"MobileModal_buttonSecondary__rx2ZA"}},2839:e=>{e.exports={mobilePOS:"MobilePOS_mobilePOS__G_HO7",header:"MobilePOS_header__E9WLi",title:"MobilePOS_title__4SeOQ",cartBadge:"MobilePOS_cartBadge__Sitl4",tabNavigation:"MobilePOS_tabNavigation__m8feC",tab:"MobilePOS_tab__1nknS",active:"MobilePOS_active__hxYB7",content:"MobilePOS_content__D1sfS",searchContainer:"MobilePOS_searchContainer__6WXTS",searchInput:"MobilePOS_searchInput__oz9Jr",itemsGrid:"MobilePOS_itemsGrid__0RBw2",itemCard:"MobilePOS_itemCard__osn8j",itemInfo:"MobilePOS_itemInfo__Lbp76",itemName:"MobilePOS_itemName__zx_Cm",itemCategory:"MobilePOS_itemCategory__TVNtI",itemDetails:"MobilePOS_itemDetails__KLnU4",price:"MobilePOS_price__DuKxM",duration:"MobilePOS_duration__b2Cca",stock:"MobilePOS_stock__OO_dI",addButton:"MobilePOS_addButton___zCbT",cartView:"MobilePOS_cartView__YJO2a",emptyCart:"MobilePOS_emptyCart__OnsoD",emptyIcon:"MobilePOS_emptyIcon__nHNg5",cartItems:"MobilePOS_cartItems__o8Lcb",cartItem:"MobilePOS_cartItem__e2nQs",itemType:"MobilePOS_itemType__jLHXl",itemPrice:"MobilePOS_itemPrice__kLBo5",quantityControls:"MobilePOS_quantityControls__UoFql",quantityBtn:"MobilePOS_quantityBtn__uIZ0l",quantity:"MobilePOS_quantity___F0Je",itemTotal:"MobilePOS_itemTotal__Yvh3r",cartSummary:"MobilePOS_cartSummary__tB7IJ",totalRow:"MobilePOS_totalRow__FdV3b",totalLabel:"MobilePOS_totalLabel___9PZ3",totalAmount:"MobilePOS_totalAmount__1YzAl",customerSection:"MobilePOS_customerSection__O3bZf",selectedCustomer:"MobilePOS_selectedCustomer__lYJoZ",changeCustomerBtn:"MobilePOS_changeCustomerBtn__qZtZC",customerSelect:"MobilePOS_customerSelect__I_pmS",checkoutBtn:"MobilePOS_checkoutBtn__tggAp"}},3237:e=>{e.exports={pullToRefreshContainer:"PullToRefresh_pullToRefreshContainer__zmtPc",refreshIndicator:"PullToRefresh_refreshIndicator__AmSHN",refreshContent:"PullToRefresh_refreshContent__aproj",refreshIcon:"PullToRefresh_refreshIcon__ON6wi",refreshIconReady:"PullToRefresh_refreshIconReady__b8LaY",pulse:"PullToRefresh_pulse__tqvxs",refreshIconSpinning:"PullToRefresh_refreshIconSpinning__Vs5cA",spin:"PullToRefresh_spin__nOnEn",refreshText:"PullToRefresh_refreshText__ajJ8B",progressBar:"PullToRefresh_progressBar__zkFea",progressFill:"PullToRefresh_progressFill__S4_Ry",content:"PullToRefresh_content__OoTAo",shimmer:"PullToRefresh_shimmer__ivhV5",shake:"PullToRefresh_shake__4eKCP",bounce:"PullToRefresh_bounce__3zSCp",fadeIn:"PullToRefresh_fadeIn__y5VyL"}},519:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>x,default:()=>u,getServerSideProps:()=>_,getStaticPaths:()=>p,getStaticProps:()=>h,reportWebVitals:()=>v,routeModule:()=>b,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>g,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>j});var i=s(7093),n=s(5244),r=s(1323),l=s(2899),o=s.n(l),c=s(6814),d=s(4679),m=e([c,d]);[c,d]=m.then?(await m)():m;let u=(0,r.l)(d,"default"),h=(0,r.l)(d,"getStaticProps"),p=(0,r.l)(d,"getStaticPaths"),_=(0,r.l)(d,"getServerSideProps"),x=(0,r.l)(d,"config"),v=(0,r.l)(d,"reportWebVitals"),j=(0,r.l)(d,"unstable_getStaticProps"),S=(0,r.l)(d,"unstable_getStaticPaths"),g=(0,r.l)(d,"unstable_getStaticParams"),y=(0,r.l)(d,"unstable_getServerProps"),N=(0,r.l)(d,"unstable_getServerSideProps"),b=new i.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/pos",pathname:"/admin/pos",bundlePath:"",filename:""},components:{App:c.default,Document:o()},userland:d});a()}catch(e){a(e)}})},6141:(e,t,s)=>{"use strict";s.d(t,{Z:()=>j});var a=s(997),i=s(6689),n=s(1989),r=s(6405),l=s(4965),o=s.n(l);function c({isOpen:e,onClose:t,title:s,message:l,actions:c,showCancel:d=!0,cancelLabel:m="Cancel",className:u=""}){let[h,p]=(0,i.useState)(!1),[_,x]=(0,i.useState)(!1),v=()=>{n.s.light(),t()},j=e=>{e.disabled||("destructive"===e.variant?n.s.heavy():"primary"===e.variant?n.s.medium():n.s.light(),e.onClick(),t())};if(!h)return null;let S=a.jsx("div",{className:`${o().actionSheetOverlay} ${_?o().open:""}`,onClick:e=>{e.target===e.currentTarget&&v()},children:(0,a.jsxs)("div",{className:`${o().actionSheet} ${u}`,children:[(s||l)&&(0,a.jsxs)("div",{className:o().header,children:[s&&a.jsx("h3",{className:o().title,children:s}),l&&a.jsx("p",{className:o().message,children:l})]}),a.jsx("div",{className:o().actions,children:c.map(e=>(0,a.jsxs)("button",{className:`${o().action} ${o()[e.variant||"default"]} ${e.disabled?o().disabled:""}`,onClick:()=>j(e),disabled:e.disabled,children:[e.icon&&a.jsx("span",{className:o().actionIcon,children:e.icon}),a.jsx("span",{className:o().actionLabel,children:e.label})]},e.id))}),d&&a.jsx("div",{className:o().cancelSection,children:a.jsx("button",{className:`${o().action} ${o().cancel}`,onClick:v,children:a.jsx("span",{className:o().actionLabel,children:m})})})]})});return(0,r.createPortal)(S,document.body)}var d=s(3071),m=s.n(d);function u({isOpen:e,onClose:t,title:s,children:l,size:o="medium",showCloseButton:c=!0,closeOnBackdrop:d=!0,closeOnSwipeDown:u=!0,className:h="",headerActions:p}){let[_,x]=(0,i.useState)(!1),[v,j]=(0,i.useState)(!1),[S,g]=(0,i.useState)(0),y=(0,i.useRef)(null);(0,i.useRef)(0),(0,i.useRef)(0),(0,i.useRef)(!1);let N=()=>{n.s.light(),t()};if(!_)return null;let b=a.jsx("div",{className:`${m().modalOverlay} ${v?m().open:""}`,onClick:e=>{d&&e.target===e.currentTarget&&N()},children:(0,a.jsxs)("div",{ref:y,className:`${m().modal} ${m()[o]} ${h}`,style:{transform:(S>0?`translateY(${S}px)`:"")||(v?"translateY(0)":"translateY(100%)"),opacity:S>0?Math.max(.5,1-S/200):1},children:[u&&a.jsx("div",{className:m().swipeIndicator,children:a.jsx("div",{className:m().swipeHandle})}),(s||c||p)&&(0,a.jsxs)("div",{className:m().header,children:[a.jsx("div",{className:m().headerLeft,children:c&&a.jsx("button",{className:m().closeButton,onClick:N,"aria-label":"Close modal",children:"✕"})}),a.jsx("div",{className:m().headerCenter,children:s&&a.jsx("h2",{className:m().title,children:s})}),a.jsx("div",{className:m().headerRight,children:p})]}),a.jsx("div",{className:m().content,children:l})]})});return(0,r.createPortal)(b,document.body)}var h=s(3237),p=s.n(h);function _({onRefresh:e,children:t,threshold:s=80,maxPullDistance:n=120,disabled:r=!1,className:l=""}){let[o,c]=(0,i.useState)({startY:0,currentY:0,pullDistance:0,isRefreshing:!1,canRefresh:!1}),d=(0,i.useRef)(null),m=(0,i.useRef)(null);return(0,i.useRef)(!1),(0,i.useRef)(!1),(0,a.jsxs)("div",{className:`${p().pullToRefreshContainer} ${l}`,ref:d,children:[(0,a.jsxs)("div",{className:p().refreshIndicator,ref:m,style:(()=>{let{pullDistance:e,isRefreshing:t,canRefresh:s}=o;return{transform:`translateY(${e-60}px)`,opacity:e>20?1:0,transition:t||0===e?"all 0.3s ease-out":"none"}})(),children:[(0,a.jsxs)("div",{className:p().refreshContent,children:[a.jsx("div",{className:(()=>{let{isRefreshing:e,canRefresh:t}=o;return e?p().refreshIconSpinning:t?p().refreshIconReady:p().refreshIcon})(),children:o.isRefreshing?"\uD83D\uDD04":"⬇️"}),a.jsx("span",{className:p().refreshText,children:(()=>{let{isRefreshing:e,canRefresh:t}=o;return e?"Refreshing...":t?"Release to refresh":"Pull to refresh"})()})]}),a.jsx("div",{className:p().progressBar,children:a.jsx("div",{className:p().progressFill,style:{width:`${Math.min(o.pullDistance/s*100,100)}%`,backgroundColor:o.canRefresh?"#4CAF50":"#2196F3"}})})]}),a.jsx("div",{className:p().content,style:(()=>{let{pullDistance:e,isRefreshing:t}=o;return{transform:`translateY(${e}px)`,transition:t||0===e?"transform 0.3s ease-out":"none"}})(),children:t})]})}var x=s(2839),v=s.n(x);function j({onTransactionComplete:e,services:t,products:s,customers:r}){let[l,o]=(0,i.useState)("services"),[d,m]=(0,i.useState)([]),[h,p]=(0,i.useState)(null),[x,j]=(0,i.useState)(""),[S,g]=(0,i.useState)(0),[y,N]=(0,i.useState)(!1),{show:b,ActionSheet:f}=function(){let[e,t]=(0,i.useState)(!1),[s,n]=(0,i.useState)({actions:[]}),r=()=>{t(!1)};return{show:e=>{n(e),t(!0)},hide:r,isOpen:e,ActionSheet:()=>a.jsx(c,{...s,isOpen:e,onClose:r})}}(),{show:C,MobileModal:P}=function(){let[e,t]=(0,i.useState)(!1),[s,n]=(0,i.useState)({}),r=()=>{t(!1)};return{show:(e,s)=>(n(s||{}),t(!0),e),hide:r,isOpen:e,MobileModal:({children:t})=>a.jsx(u,{...s,isOpen:e,onClose:r,children:t})}}(),k=async()=>{await new Promise(e=>setTimeout(e,1e3)),n.s.success()},w=(e,t)=>{n.s.light(),d.find(s=>s.id===e.id&&s.type===t)?m(d.map(s=>s.id===e.id&&s.type===t?{...s,quantity:s.quantity+1}:s)):m([...d,{id:e.id,name:e.name,price:e.price||0,quantity:1,type:t,duration:e.duration}])},D=(e,t)=>{n.s.medium(),m(d.filter(s=>!(s.id===e&&s.type===t)))},O=(e,t,s)=>{if(s<=0){D(e,t);return}n.s.light(),m(d.map(a=>a.id===e&&a.type===t?{...a,quantity:s}:a))},$=e=>{n.s.medium(),b({title:e.name,actions:[{id:"edit-quantity",label:"Edit Quantity",icon:"✏️",onClick:()=>A(e)},{id:"remove",label:"Remove from Cart",icon:"\uD83D\uDDD1️",variant:"destructive",onClick:()=>D(e.id,e.type)}]})},A=e=>{console.log("Show quantity modal for:",e)},I=e=>{n.s.light(),p(e)},M=async()=>{if(0!==d.length){N(!0),n.s.heavy();try{await new Promise(e=>setTimeout(e,2e3));let t={id:`txn_${Date.now()}`,items:d,customer:h,total:S,timestamp:new Date().toISOString(),paymentMethod:"card"};e(t),m([]),p(null),o("services"),n.s.success()}catch(e){console.error("Transaction failed:",e),n.s.error()}finally{N(!1)}}},T=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),B=e=>{if(!e)return"";if(e>=60){let t=Math.floor(e/60),s=e%60;return s>0?`${t}h ${s}m`:`${t}h`}return`${e}m`},q=t.filter(e=>e.name.toLowerCase().includes(x.toLowerCase())||e.category.toLowerCase().includes(x.toLowerCase())),R=s.filter(e=>e.name.toLowerCase().includes(x.toLowerCase())||e.category&&e.category.toLowerCase().includes(x.toLowerCase()));return(0,a.jsxs)("div",{className:v().mobilePOS,children:[(0,a.jsxs)("div",{className:v().header,children:[a.jsx("h1",{className:v().title,children:"Mobile POS"}),(0,a.jsxs)("div",{className:v().cartBadge,children:["\uD83D\uDED2 ",d.length]})]}),(0,a.jsxs)("div",{className:v().tabNavigation,children:[a.jsx("button",{className:`${v().tab} ${"services"===l?v().active:""}`,onClick:()=>o("services"),children:"✂️ Services"}),a.jsx("button",{className:`${v().tab} ${"products"===l?v().active:""}`,onClick:()=>o("products"),children:"\uD83D\uDECD️ Products"}),(0,a.jsxs)("button",{className:`${v().tab} ${"cart"===l?v().active:""}`,onClick:()=>o("cart"),children:["\uD83D\uDED2 Cart (",d.length,")"]})]}),(0,a.jsxs)(_,{onRefresh:k,className:v().content,children:[("services"===l||"products"===l)&&a.jsx("div",{className:v().searchContainer,children:a.jsx("input",{type:"text",placeholder:`Search ${l}...`,value:x,onChange:e=>j(e.target.value),className:v().searchInput})}),"services"===l&&a.jsx("div",{className:v().itemsGrid,children:q.map(e=>(0,a.jsxs)("div",{className:v().itemCard,children:[(0,a.jsxs)("div",{className:v().itemInfo,children:[a.jsx("h3",{className:v().itemName,children:e.name}),a.jsx("p",{className:v().itemCategory,children:e.category}),(0,a.jsxs)("div",{className:v().itemDetails,children:[a.jsx("span",{className:v().price,children:T(e.price||50)}),e.duration&&a.jsx("span",{className:v().duration,children:B(e.duration)})]})]}),a.jsx("button",{className:v().addButton,onClick:()=>w(e,"service"),children:"+"})]},e.id))}),"products"===l&&a.jsx("div",{className:v().itemsGrid,children:R.map(e=>(0,a.jsxs)("div",{className:v().itemCard,children:[(0,a.jsxs)("div",{className:v().itemInfo,children:[a.jsx("h3",{className:v().itemName,children:e.name}),a.jsx("p",{className:v().itemCategory,children:e.category||"Product"}),(0,a.jsxs)("div",{className:v().itemDetails,children:[a.jsx("span",{className:v().price,children:T(e.price||25)}),void 0!==e.stock_quantity&&(0,a.jsxs)("span",{className:v().stock,children:["Stock: ",e.stock_quantity]})]})]}),a.jsx("button",{className:v().addButton,onClick:()=>w(e,"product"),disabled:0===e.stock_quantity,children:"+"})]},e.id))}),"cart"===l&&a.jsx("div",{className:v().cartView,children:0===d.length?(0,a.jsxs)("div",{className:v().emptyCart,children:[a.jsx("div",{className:v().emptyIcon,children:"\uD83D\uDED2"}),a.jsx("h3",{children:"Cart is Empty"}),a.jsx("p",{children:"Add services or products to get started"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:v().cartItems,children:d.map(e=>(0,a.jsxs)("div",{className:v().cartItem,onTouchStart:()=>$(e),children:[(0,a.jsxs)("div",{className:v().itemInfo,children:[a.jsx("h4",{className:v().itemName,children:e.name}),a.jsx("p",{className:v().itemType,children:"service"===e.type?"✂️ Service":"\uD83D\uDECD️ Product"}),a.jsx("span",{className:v().itemPrice,children:T(e.price)})]}),(0,a.jsxs)("div",{className:v().quantityControls,children:[a.jsx("button",{className:v().quantityBtn,onClick:()=>O(e.id,e.type,e.quantity-1),children:"-"}),a.jsx("span",{className:v().quantity,children:e.quantity}),a.jsx("button",{className:v().quantityBtn,onClick:()=>O(e.id,e.type,e.quantity+1),children:"+"})]}),a.jsx("div",{className:v().itemTotal,children:T(e.price*e.quantity)})]},`${e.id}-${e.type}`))}),(0,a.jsxs)("div",{className:v().cartSummary,children:[(0,a.jsxs)("div",{className:v().totalRow,children:[a.jsx("span",{className:v().totalLabel,children:"Total:"}),a.jsx("span",{className:v().totalAmount,children:T(S)})]}),(0,a.jsxs)("div",{className:v().customerSection,children:[a.jsx("h4",{children:"Customer"}),h?(0,a.jsxs)("div",{className:v().selectedCustomer,children:[a.jsx("span",{children:h.name}),a.jsx("button",{className:v().changeCustomerBtn,onClick:()=>p(null),children:"Change"})]}):(0,a.jsxs)("select",{className:v().customerSelect,onChange:e=>{let t=r.find(t=>t.id===e.target.value);t&&I(t)},value:"",children:[a.jsx("option",{value:"",children:"Select Customer (Optional)"}),r.map(e=>a.jsx("option",{value:e.id,children:e.name},e.id))]})]}),a.jsx("button",{className:v().checkoutBtn,onClick:M,disabled:y,children:y?"Processing...":`Checkout ${T(S)}`})]})]})})]}),a.jsx(f,{}),a.jsx(P,{children:a.jsx("div",{})})]})}},583:(e,t,s)=>{"use strict";s.d(t,{Z:()=>v});var a=s(997),i=s(6689),n=s(1073),r=s.n(n);function l({baseAmount:e,paymentMethod:t,onTipCalculated:s,onCancel:n,customerName:l="Customer"}){let[o,c]=(0,i.useState)(null),[d,m]=(0,i.useState)("percentage"),[u,h]=(0,i.useState)(null),[p,_]=(0,i.useState)(""),[x,v]=(0,i.useState)(""),[j,S]=(0,i.useState)(0),[g,y]=(0,i.useState)(!0),[N,b]=(0,i.useState)(null),f=()=>{s({tipAmount:0,tipMethod:"none",tipPercentage:0,baseAmount:e,totalAmount:e})},C=e=>`$${e.toFixed(2)}`;return g?a.jsx("div",{className:r().tipContainer,children:a.jsx("div",{className:r().tipHeader,children:a.jsx("h3",{children:"Loading tip options..."})})}):o?.enableTips!=="true"?(f(),null):(0,a.jsxs)("div",{className:r().tipContainer,children:[(0,a.jsxs)("div",{className:r().tipHeader,children:[(0,a.jsxs)("h3",{children:["\uD83D\uDCB0 Add Tip for ",l]}),(0,a.jsxs)("p",{children:["Service Total: ",C(e)]}),a.jsx("button",{className:r().tipCancelButton,onClick:n,children:"✕"})]}),N&&a.jsx("div",{className:r().tipError,children:N}),(0,a.jsxs)("div",{className:r().tipContent,children:[(0,a.jsxs)("div",{className:r().tipTypeSelector,children:[a.jsx("button",{className:`${r().tipTypeButton} ${"none"===d?r().active:""}`,onClick:()=>m("none"),children:"No Tip"}),a.jsx("button",{className:`${r().tipTypeButton} ${"percentage"===d?r().active:""}`,onClick:()=>m("percentage"),children:"Percentage"}),o?.customTipAllowed==="true"&&a.jsx("button",{className:`${r().tipTypeButton} ${"amount"===d?r().active:""}`,onClick:()=>m("amount"),children:"Custom Amount"})]}),"percentage"===d&&(0,a.jsxs)("div",{className:r().tipPercentageOptions,children:[a.jsx("h4",{children:"Select Tip Percentage"}),a.jsx("div",{className:r().tipPercentageGrid,children:(o?.defaultTipPercentages?o.defaultTipPercentages.split(",").map(e=>parseInt(e.trim())):[15,18,20,25]).map(t=>(0,a.jsxs)("button",{className:`${r().tipPercentageButton} ${u===t?r().active:""}`,onClick:()=>{h(t),v("")},children:[t,"%",a.jsx("span",{className:r().tipAmount,children:C(e*t/100)})]},t))}),o?.customTipAllowed==="true"&&(0,a.jsxs)("div",{className:r().customPercentageInput,children:[a.jsx("label",{children:"Custom Percentage:"}),a.jsx("input",{type:"number",min:"0",max:o?.maximumTipPercentage||"50",step:"0.1",value:x,onChange:e=>{v(e.target.value),h(null)},placeholder:"Enter %"}),a.jsx("span",{children:"%"})]})]}),"amount"===d&&(0,a.jsxs)("div",{className:r().tipCustomAmount,children:[a.jsx("h4",{children:"Enter Tip Amount"}),(0,a.jsxs)("div",{className:r().customAmountInput,children:[a.jsx("span",{className:r().currencySymbol,children:"$"}),a.jsx("input",{type:"number",min:o?.minimumTipAmount||"1.00",step:"0.01",value:p,onChange:e=>_(e.target.value),placeholder:"0.00"})]})]}),"none"!==d&&j>0&&(0,a.jsxs)("div",{className:r().tipSummary,children:[(0,a.jsxs)("div",{className:r().tipSummaryRow,children:[a.jsx("span",{children:"Service Total:"}),a.jsx("span",{children:C(e)})]}),(0,a.jsxs)("div",{className:r().tipSummaryRow,children:[a.jsx("span",{children:"Tip Amount:"}),a.jsx("span",{children:C(j)})]}),(0,a.jsxs)("div",{className:`${r().tipSummaryRow} ${r().total}`,children:[a.jsx("span",{children:"Total with Tip:"}),a.jsx("span",{children:C(e+j)})]})]}),a.jsx("div",{className:r().tipActions,children:"none"===d?a.jsx("button",{className:r().tipConfirmButton,onClick:f,children:"Continue Without Tip"}):(0,a.jsxs)("button",{className:r().tipConfirmButton,onClick:()=>{s({tipAmount:j,tipMethod:"cash"===t?"cash":"card",tipPercentage:"percentage"===d?u||parseFloat(x):j/e*100,baseAmount:e,totalAmount:e+j})},disabled:j<=0,children:["Add ",C(j)," Tip"]})})]})]})}function o({amount:e,onMethodSelect:t,onCancel:s,customerName:n="Customer"}){let[o,c]=(0,i.useState)(null),[d,m]=(0,i.useState)(""),[u,h]=(0,i.useState)(!1),[p,_]=(0,i.useState)(!1),[x,v]=(0,i.useState)(null),[j,S]=(0,i.useState)(null),g=parseFloat(e||0),y=.029*g+.3,N=.026*g+.1,b=g+y,f=g+N,C=e=>{c(e),"cash"!==e&&(v(e),S({originalAmount:g,processingFee:"square_payment"===e?y:"square_terminal"===e?N:0,totalAmount:"square_payment"===e?b:"square_terminal"===e?f:g}),_(!0))},P=e=>`$${e.toFixed(2)}`;return u?a.jsx("div",{className:r().paymentMethodContainer,children:(0,a.jsxs)("div",{className:r().processingPayment,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Processing payment..."})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:r().paymentMethodContainer,children:[(0,a.jsxs)("div",{className:r().paymentHeader,children:[a.jsx("h3",{children:"Select Payment Method"}),(0,a.jsxs)("div",{className:r().totalAmount,children:["Service Total: ",P(g)]})]}),(0,a.jsxs)("div",{className:r().paymentMethods,children:[(0,a.jsxs)("div",{className:`${r().paymentMethodCard} ${"square_payment"===o?r().selected:""}`,onClick:()=>C("square_payment"),children:[a.jsx("div",{className:r().methodIcon,children:"\uD83D\uDCB3"}),(0,a.jsxs)("div",{className:r().methodInfo,children:[a.jsx("h4",{children:"Card Payment"}),a.jsx("p",{children:"Credit/Debit Card Entry"}),a.jsx("div",{className:r().methodPricing,children:(0,a.jsxs)("div",{className:r().priceBreakdown,children:[(0,a.jsxs)("span",{children:["Service: ",P(g)]}),(0,a.jsxs)("span",{children:["Processing: ",P(y)]}),(0,a.jsxs)("strong",{children:["Total: ",P(b)]})]})})]}),a.jsx("div",{className:r().methodBadge,children:a.jsx("span",{children:"Online"})})]}),(0,a.jsxs)("div",{className:`${r().paymentMethodCard} ${"square_terminal"===o?r().selected:""}`,onClick:()=>C("square_terminal"),children:[a.jsx("div",{className:r().methodIcon,children:"\uD83D\uDCF1"}),(0,a.jsxs)("div",{className:r().methodInfo,children:[a.jsx("h4",{children:"Square Terminal"}),a.jsx("p",{children:"Hardware Card Reader"}),a.jsx("div",{className:r().methodPricing,children:(0,a.jsxs)("div",{className:r().priceBreakdown,children:[(0,a.jsxs)("span",{children:["Service: ",P(g)]}),(0,a.jsxs)("span",{children:["Processing: ",P(N)]}),(0,a.jsxs)("strong",{children:["Total: ",P(f)]})]})})]}),a.jsx("div",{className:r().methodBadge,children:a.jsx("span",{children:"Hardware"})})]}),(0,a.jsxs)("div",{className:`${r().paymentMethodCard} ${"cash"===o?r().selected:""}`,onClick:()=>c("cash"),children:[a.jsx("div",{className:r().methodIcon,children:"\uD83D\uDCB5"}),(0,a.jsxs)("div",{className:r().methodInfo,children:[a.jsx("h4",{children:"Cash Payment"}),a.jsx("p",{children:"Physical Currency"}),a.jsx("div",{className:r().methodPricing,children:(0,a.jsxs)("div",{className:r().priceBreakdown,children:[(0,a.jsxs)("span",{children:["Service: ",P(g)]}),(0,a.jsxs)("span",{children:["Processing: ",P(0)]}),(0,a.jsxs)("strong",{children:["Total: ",P(g)]})]})})]}),a.jsx("div",{className:r().methodBadge,children:a.jsx("span",{children:"No Fees"})})]})]}),"cash"===o&&(0,a.jsxs)("div",{className:r().cashPaymentInterface,children:[a.jsx("h4",{children:"Cash Payment Details"}),(0,a.jsxs)("div",{className:r().cashInputs,children:[a.jsx("div",{className:r().inputGroup,children:(0,a.jsxs)("label",{children:["Amount Due: ",P(g)]})}),(0,a.jsxs)("div",{className:r().inputGroup,children:[a.jsx("label",{children:"Cash Received:"}),a.jsx("input",{type:"number",step:"0.01",min:g,value:d,onChange:e=>m(e.target.value),placeholder:g.toFixed(2),className:r().cashInput})]}),d&&parseFloat(d)>=g&&a.jsx("div",{className:r().changeAmount,children:(0,a.jsxs)("strong",{children:["Change: ",P(parseFloat(d)-g)]})})]}),a.jsx("div",{className:r().cashActions,children:a.jsx("button",{onClick:()=>{let e=parseFloat(d);if(!e||e<g){alert(`Please enter at least $${g.toFixed(2)}`);return}let t=e-g;v("cash"),S({originalAmount:g,processingFee:0,totalAmount:g,cashReceived:e,changeAmount:t}),_(!0)},disabled:!d||parseFloat(d)<g,className:r().processCashButton,children:"Process Cash Payment"})})]}),a.jsx("div",{className:r().paymentActions,children:a.jsx("button",{onClick:s,className:r().cancelButton,children:"Cancel"})}),a.jsx("div",{className:r().paymentNote,children:a.jsx("p",{children:"\uD83D\uDCA1 Processing fees are automatically calculated and included in the total"})})]}),p&&a.jsx(l,{baseAmount:j?.totalAmount||g,paymentMethod:x,customerName:n,onTipCalculated:e=>{_(!1),h(!0),t(x,{...j,tipAmount:e.tipAmount,tipMethod:e.tipMethod,tipPercentage:e.tipPercentage,totalAmount:e.totalAmount})},onCancel:()=>{_(!1),v(null),S(null),c(null)}})]})}function c({amount:e,currency:t="AUD",onSuccess:n,onError:l,orderDetails:o={}}){let[c,d]=(0,i.useState)(null),[m,u]=(0,i.useState)(!0),[h,p]=(0,i.useState)(!1),[_,x]=(0,i.useState)(""),v=(0,i.useRef)(!1),[j,S]=(0,i.useState)({addressLine1:"1455 Market St",addressLine2:"Suite 600",locality:"San Francisco",administrativeDistrictLevel1:"CA",postalCode:"94103",country:"US"}),[g,y]=(0,i.useState)(!1),N=(0,i.useRef)(null),b=(0,i.useRef)(!1),f=(0,i.useRef)(null),C=(0,i.useRef)(!1);(0,i.useRef)(null),(0,i.useRef)(null);let P=(0,i.useCallback)(async()=>{performance.now();try{if(C.current=!0,!b.current){console.warn("InitializeSquareForm: Component unmounted before starting."),C.current=!1;return}if(v.current){console.log("InitializeSquareForm: Already attempted in this lifecycle."),C.current=!1;return}if(!N.current){console.error("InitializeSquareForm: Container ref not available"),C.current=!1;return}throw x(""),Error("Square SDK not loaded")}catch(e){console.error("Square form initialization error:",e),x(e.message||"Failed to initialize payment form"),u(!1)}finally{C.current=!1}},[]),k=()=>{v.current=!1,x(""),u(!0),P()},w=(0,i.useCallback)(async()=>{if(c&&!h){p(!0),x("");try{try{let{startPOSPaymentOperation:e}=await s.e(5815).then(s.bind(s,5815));e()}catch(e){console.warn("POS payment protection not available:",e)}console.log("\uD83D\uDD04 Tokenizing card...");let e=g?{billingContact:{addressLines:[j.addressLine1,j.addressLine2].filter(Boolean),city:j.locality,countryCode:j.country,postalCode:j.postalCode,state:j.administrativeDistrictLevel1}}:{},t=await c.tokenize(e);if("OK"===t.status){console.log("✅ Card tokenized successfully");let e=await D(t.token);n(e)}else{console.error("❌ Tokenization failed:",t.errors);let e=t.errors?.[0]?.message||"Card tokenization failed";x(e),l(Error(e))}}catch(e){console.error("Payment processing error:",e),x(e.message||"Payment failed. Please try again."),l(e)}finally{p(!1);try{let{endPOSPaymentOperation:e}=await s.e(5815).then(s.bind(s,5815));e()}catch(e){console.warn("Error ending POS payment protection:",e)}}}},[h,g,j,e,t,n,l]),D=async s=>{console.log("\uD83D\uDD04 Processing payment with token...");try{let a=await fetch("/api/admin/pos/process-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sourceId:s,amount:Math.round(100*parseFloat(e)),currency:t,orderDetails:o,idempotencyKey:`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`})});if(!a.ok){let e=await a.json();throw Error(e.message||`Payment failed: ${a.status}`)}let i=await a.json();return console.log("✅ Payment processed successfully:",i),i}catch(e){throw console.error("Payment API error:",e),e}};return console.log("\uD83D\uDD0D POSSquarePayment render state:",{showBillingAddress:g,isLoading:m,paymentForm:!!f.current,squareSDKLoaded:!1,initializationAttempted:v.current}),(0,a.jsxs)("div",{className:r().squarePaymentContainer,children:[(0,a.jsxs)("div",{className:r().paymentFormHeader,children:[a.jsx("h4",{children:"Card Payment"}),(0,a.jsxs)("div",{className:r().paymentAmount,children:["Amount: ",(0,a.jsxs)("span",{children:["$",parseFloat(e||0).toFixed(2)," ",t]})]})]}),_&&(0,a.jsxs)("div",{className:r().paymentError,children:[a.jsx("span",{className:r().errorIcon,children:"⚠️"}),(0,a.jsxs)("div",{className:r().errorContent,children:[a.jsx("div",{className:r().errorText,children:_}),a.jsx("button",{onClick:k,className:r().retryButton,children:"Retry"})]})]}),(0,a.jsxs)("div",{className:r().cardFormContainer,children:[a.jsx("div",{ref:N,className:r().cardForm,style:{minHeight:"60px",border:"1px solid #e0e0e0",borderRadius:"8px",padding:"16px",background:"white"},children:m&&(0,a.jsxs)("div",{className:r().cardFormPlaceholder,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Initializing secure payment form..."})]})}),g&&(0,a.jsxs)("div",{className:r().billingAddressSection,children:[a.jsx("h5",{children:"Billing Address"}),(0,a.jsxs)("div",{className:r().addressGrid,children:[(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"Address Line 1"}),a.jsx("input",{type:"text",value:j.addressLine1,onChange:e=>S(t=>({...t,addressLine1:e.target.value})),placeholder:"1455 Market St"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"Address Line 2"}),a.jsx("input",{type:"text",value:j.addressLine2,onChange:e=>S(t=>({...t,addressLine2:e.target.value})),placeholder:"Suite 600"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"City"}),a.jsx("input",{type:"text",value:j.locality,onChange:e=>S(t=>({...t,locality:e.target.value})),placeholder:"San Francisco"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"State"}),a.jsx("input",{type:"text",value:j.administrativeDistrictLevel1,onChange:e=>S(t=>({...t,administrativeDistrictLevel1:e.target.value})),placeholder:"CA"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"ZIP Code"}),a.jsx("input",{type:"text",value:j.postalCode,onChange:e=>S(t=>({...t,postalCode:e.target.value})),placeholder:"94103"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"Country"}),(0,a.jsxs)("select",{value:j.country,onChange:e=>S(t=>({...t,country:e.target.value})),children:[a.jsx("option",{value:"US",children:"United States"}),a.jsx("option",{value:"AU",children:"Australia"}),a.jsx("option",{value:"CA",children:"Canada"}),a.jsx("option",{value:"GB",children:"United Kingdom"})]})]})]}),a.jsx("div",{className:r().addressNote,children:a.jsx("small",{children:"\uD83D\uDCA1 Billing address is required for card verification in sandbox mode"})})]})]}),(0,a.jsxs)("div",{className:r().paymentActions,children:[a.jsx("button",{onClick:w,disabled:!c||h||m,className:r().payButton,children:h?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:r().buttonSpinner}),"Processing..."]}):`Pay ${t} $${parseFloat(e||0).toFixed(2)}`}),!c&&!m&&!_&&(0,a.jsxs)("div",{className:r().formNotReady,children:[a.jsx("span",{className:r().errorIcon,children:"⚠️"}),"Card form not initialized. Please wait for the form to load."]}),_&&(0,a.jsxs)("div",{className:r().errorContainer,children:[a.jsx("p",{className:r().errorMessage,children:_}),a.jsx("button",{onClick:k,className:r().retryButton,children:"Retry"})]})]}),(0,a.jsxs)("div",{className:r().paymentSecurity,children:[(0,a.jsxs)("div",{className:r().securityBadges,children:[a.jsx("span",{className:r().securityBadge,children:"\uD83D\uDD12 SSL Encrypted"}),a.jsx("span",{className:r().securityBadge,children:"✅ PCI Compliant"}),a.jsx("span",{className:r().securityBadge,children:"\uD83D\uDEE1️ Square Secure"})]}),a.jsx("p",{className:r().securityText,children:"Your payment information is processed securely by Square and never stored on our servers."})]})]})}function d({amount:e,currency:t="AUD",orderDetails:s,onSuccess:n,onError:l,onCancel:o}){let[c,d]=(0,i.useState)([]),[m,u]=(0,i.useState)(null),[h,p]=(0,i.useState)(!1),[_,x]=(0,i.useState)(null),[v,j]=(0,i.useState)(null),[S,g]=(0,i.useState)(""),[y,N]=(0,i.useState)(!0),b=async()=>{try{N(!0);let e=await fetch("/api/admin/pos/terminal-devices",{headers:{Authorization:`Bearer ${localStorage.getItem("adminToken")}`}});if(e.ok){let t=await e.json();d(t.devices||[])}else console.error("Failed to load terminal devices"),d([])}catch(e){console.error("Error loading terminal devices:",e),d([])}finally{N(!1)}},f=async()=>{if(!m){g("Please select a terminal device");return}try{p(!0),g("");let a=await fetch("/api/admin/pos/terminal-checkout",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("adminToken")}`},body:JSON.stringify({deviceId:m.id,amountMoney:{amount:Math.round(100*parseFloat(e)),currency:t},note:s?.service||"POS Payment",orderId:s?.orderId||`pos_${Date.now()}`,paymentOptions:{autocomplete:!0,collectSignature:!0,allowTipping:!1}})});if(!a.ok){let e=await a.json();throw Error(e.message||"Failed to create terminal checkout")}let i=await a.json();x(i.checkout.id),j(i.checkout.status),console.log("Terminal checkout created:",i.checkout)}catch(e){console.error("Error creating terminal checkout:",e),g(e.message),p(!1),l(e)}};return y?a.jsx("div",{className:r().terminalPaymentContainer,children:(0,a.jsxs)("div",{className:r().loadingState,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Loading terminal devices..."})]})}):0===c.length?a.jsx("div",{className:r().terminalPaymentContainer,children:(0,a.jsxs)("div",{className:r().noDevicesState,children:[a.jsx("div",{className:r().noDevicesIcon,children:"\uD83D\uDCF1"}),a.jsx("h3",{children:"No Terminal Devices Available"}),a.jsx("p",{children:"No paired Square Terminal devices found. Please pair a device first."}),a.jsx("button",{className:r().refreshButton,onClick:b,children:"Refresh Devices"})]})}):(0,a.jsxs)("div",{className:r().terminalPaymentContainer,children:[(0,a.jsxs)("div",{className:r().terminalHeader,children:[a.jsx("h3",{children:"Square Terminal Payment"}),(0,a.jsxs)("div",{className:r().paymentAmount,children:["$",parseFloat(e||0).toFixed(2)," ",t]})]}),!h&&(0,a.jsxs)("div",{className:r().deviceSelection,children:[a.jsx("h4",{children:"Select Terminal Device"}),a.jsx("div",{className:r().deviceList,children:c.map(e=>(0,a.jsxs)("div",{className:`${r().deviceCard} ${m?.id===e.id?r().selected:""}`,onClick:()=>u(e),children:[a.jsx("div",{className:r().deviceIcon,children:"\uD83D\uDCF1"}),(0,a.jsxs)("div",{className:r().deviceInfo,children:[a.jsx("div",{className:r().deviceName,children:e.name||`Terminal ${e.id.slice(-4)}`}),(0,a.jsxs)("div",{className:r().deviceStatus,children:["\uD83D\uDFE2 ",e.status]})]})]},e.id))})]}),h&&(0,a.jsxs)("div",{className:r().processingState,children:[a.jsx("div",{className:r().statusIcon,children:(()=>{switch(v){case"PENDING":case"IN_PROGRESS":return"⏳";case"COMPLETED":return"✅";case"CANCELED":case"ERROR":return"❌";default:return"\uD83D\uDCF1"}})()}),a.jsx("div",{className:r().statusMessage,children:(()=>{switch(v){case"PENDING":return"Waiting for customer to complete payment on terminal...";case"IN_PROGRESS":return"Payment in progress on terminal...";case"COMPLETED":return"Payment completed successfully!";case"CANCELED":return"Payment was cancelled";case"ERROR":return"Payment failed";default:return"Preparing terminal checkout..."}})()}),"PENDING"===v&&(0,a.jsxs)("div",{className:r().terminalInstructions,children:[a.jsx("p",{children:"Customer should now see the payment screen on the terminal device."}),a.jsx("p",{children:"They can insert their card, tap for contactless, or use mobile payment."})]})]}),S&&(0,a.jsxs)("div",{className:r().errorMessage,children:[a.jsx("div",{className:r().errorIcon,children:"⚠️"}),a.jsx("div",{className:r().errorText,children:S})]}),a.jsx("div",{className:r().terminalActions,children:h?a.jsx("button",{className:r().cancelButton,onClick:()=>{p(!1),x(null),j(null),o()},children:"Cancel Payment"}):a.jsx("button",{className:r().startPaymentButton,onClick:f,disabled:!m,children:"Start Terminal Payment"})})]})}var m=s(8316);async function u(e,t=null){try{let s=await h(t);if(!s){console.log("No template found, using default template");let t=p(),s=_(t,e);return{success:!0,html:s,template:t}}let a=_(s,e);return{success:!0,html:a,template:s}}catch(t){console.error("Error generating receipt:",t);try{console.log("Falling back to default template due to error");let t=p(),s=_(t,e);return{success:!0,html:s,template:t}}catch(e){return console.error("Fallback template generation failed:",e),{success:!1,error:t.message}}}}async function h(e=null){try{let t=m.pR.from("receipt_templates").select("*").eq("is_active",!0);t=e?t.eq("id",e):t.eq("is_default",!0);let{data:s,error:a}=await t.limit(1);if(a){if("42P01"===a.code)return console.log("Receipt templates table not found, using default template"),p();throw Error(`Database error: ${a.message}`)}if(!s||0===s.length){let{data:e}=await m.pR.from("receipt_templates").select("*").eq("is_active",!0).limit(1);return e?.[0]||p()}return s[0]}catch(e){return console.error("Error fetching receipt template:",e),p()}}function p(){return{id:"default-standard",name:"Standard Receipt",description:"Default receipt template",template_type:"standard",is_default:!0,is_active:!0,business_name:"Ocean Soul Sparkles",business_address:"Australia",business_phone:"+61 XXX XXX XXX",business_email:"<EMAIL>",business_website:"oceansoulsparkles.com.au",business_abn:"",show_logo:!0,logo_position:"center",header_color:"#667eea",text_color:"#333333",font_family:"Arial",font_size:12,show_customer_details:!0,show_service_details:!0,show_artist_details:!0,show_payment_details:!0,show_booking_notes:!1,show_terms_conditions:!0,footer_message:"Thank you for choosing Ocean Soul Sparkles!",show_social_media:!1,social_media_links:null,custom_fields:[]}}function _(e,t){let{business_name:s,business_address:a,business_phone:i,business_email:n,business_website:r,business_abn:l,show_logo:o,logo_position:c,header_color:d,text_color:m,font_family:u,font_size:h,show_customer_details:p,show_service_details:_,show_artist_details:x,show_payment_details:v,show_booking_notes:j,show_terms_conditions:S,footer_message:g,show_social_media:y,social_media_links:N,template_type:b}=e,f=t.receipt_number||`OSS-${Date.now()}`,C=new Date().toLocaleDateString("en-AU",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),P=`
    <style>
      body { 
        font-family: ${u}, sans-serif; 
        font-size: ${h}px; 
        color: ${m}; 
        margin: 0; 
        padding: 20px; 
        line-height: 1.4;
        max-width: 400px;
        margin: 0 auto;
      }
      .receipt-header { 
        text-align: ${c}; 
        margin-bottom: 20px; 
        padding-bottom: 15px;
        border-bottom: 2px solid ${d};
      }
      .business-name { 
        font-size: ${Math.round(1.5*h)}px; 
        font-weight: bold; 
        color: ${d}; 
        margin: 0 0 5px 0;
      }
      .business-info { 
        font-size: ${Math.round(.9*h)}px; 
        color: #666; 
        margin: 2px 0;
      }
      .receipt-title { 
        font-size: ${Math.round(1.2*h)}px; 
        font-weight: bold; 
        text-align: center; 
        margin: 20px 0 15px 0;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      .receipt-info { 
        margin-bottom: 20px; 
        padding: 10px 0;
        border-bottom: 1px solid #eee;
      }
      .section { 
        margin-bottom: 15px; 
      }
      .section-title { 
        font-weight: bold; 
        margin-bottom: 8px; 
        color: ${d};
        font-size: ${Math.round(1.1*h)}px;
      }
      .detail-row { 
        display: flex; 
        justify-content: space-between; 
        margin-bottom: 3px;
        align-items: flex-start;
      }
      .detail-label { 
        font-weight: 500; 
        flex: 1;
      }
      .detail-value { 
        text-align: right; 
        flex: 1;
        word-break: break-word;
      }
      .total-section { 
        border-top: 2px solid ${d}; 
        padding-top: 10px; 
        margin-top: 15px;
      }
      .total-row { 
        display: flex; 
        justify-content: space-between; 
        font-weight: bold; 
        font-size: ${Math.round(1.1*h)}px;
        margin-bottom: 5px;
      }
      .footer { 
        text-align: center; 
        margin-top: 20px; 
        padding-top: 15px; 
        border-top: 1px solid #eee;
        font-size: ${Math.round(.9*h)}px;
      }
      .footer-message { 
        font-style: italic; 
        color: #666; 
        margin-bottom: 10px;
      }
      .compact { font-size: ${Math.round(.9*h)}px; }
      .detailed { font-size: ${h}px; }
      @media print {
        body { margin: 0; padding: 10px; }
        .receipt-header { page-break-inside: avoid; }
      }
    </style>
  `,k=`
    <div class="receipt-header">
      ${o?`<div class="business-name">${s}</div>`:""}
      ${a?`<div class="business-info">${a}</div>`:""}
      ${i?`<div class="business-info">${i}</div>`:""}
      ${n?`<div class="business-info">${n}</div>`:""}
      ${r?`<div class="business-info">${r}</div>`:""}
      ${l?`<div class="business-info">ABN: ${l}</div>`:""}
    </div>
  `,w=`
    <div class="receipt-title">Receipt</div>
    <div class="receipt-info">
      <div class="detail-row">
        <span class="detail-label">Receipt #:</span>
        <span class="detail-value">${f}</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Date:</span>
        <span class="detail-value">${C}</span>
      </div>
    </div>
  `,D="";p&&t.customer_name&&(D=`
      <div class="section">
        <div class="section-title">Customer Details</div>
        <div class="detail-row">
          <span class="detail-label">Name:</span>
          <span class="detail-value">${t.customer_name}</span>
        </div>
        ${t.customer_email?`
        <div class="detail-row">
          <span class="detail-label">Email:</span>
          <span class="detail-value">${t.customer_email}</span>
        </div>`:""}
        ${t.customer_phone?`
        <div class="detail-row">
          <span class="detail-label">Phone:</span>
          <span class="detail-value">${t.customer_phone}</span>
        </div>`:""}
      </div>
    `);let O="";if(_){let e=t.start_time?new Date(t.start_time).toLocaleString("en-AU"):"N/A",s=t.duration?`${t.duration} minutes`:"N/A";O=`
      <div class="section">
        <div class="section-title">Service Details</div>
        <div class="detail-row">
          <span class="detail-label">Service:</span>
          <span class="detail-value">${t.service_name||"N/A"}</span>
        </div>
        ${t.tier_name?`
        <div class="detail-row">
          <span class="detail-label">Tier:</span>
          <span class="detail-value">${t.tier_name}</span>
        </div>`:""}
        <div class="detail-row">
          <span class="detail-label">Date & Time:</span>
          <span class="detail-value">${e}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Duration:</span>
          <span class="detail-value">${s}</span>
        </div>
      </div>
    `}let $="";x&&t.artist_name&&($=`
      <div class="section">
        <div class="section-title">Artist Details</div>
        <div class="detail-row">
          <span class="detail-label">Artist:</span>
          <span class="detail-value">${t.artist_name}</span>
        </div>
      </div>
    `);let A="";if(v){let e=t.total_amount||0,s=t.tip_amount||0;A=`
      <div class="section">
        <div class="section-title">Payment Details</div>
        ${s>0?`
        <div class="detail-row">
          <span class="detail-label">Subtotal:</span>
          <span class="detail-value">$${(e-s).toFixed(2)}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Tip:</span>
          <span class="detail-value">$${s.toFixed(2)}</span>
        </div>`:""}
        <div class="total-section">
          <div class="total-row">
            <span>Total:</span>
            <span>$${e.toFixed(2)}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Payment Method:</span>
            <span class="detail-value">${t.payment_method||"N/A"}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Status:</span>
            <span class="detail-value">Paid</span>
          </div>
        </div>
      </div>
    `}let I="";j&&t.notes&&(I=`
      <div class="section">
        <div class="section-title">Notes</div>
        <div>${t.notes}</div>
      </div>
    `);let M="";return(g||S)&&(M=`
      <div class="footer">
        ${g?`<div class="footer-message">${g}</div>`:""}
        ${S?`
        <div style="font-size: ${Math.round(.8*h)}px; color: #888;">
          Terms & Conditions apply. Visit our website for details.
        </div>`:""}
        ${y&&N?`
        <div style="margin-top: 10px;">
          Follow us on social media for updates and inspiration!
        </div>`:""}
      </div>
    `),`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Receipt - ${f}</title>
      ${P}
    </head>
    <body class="${b}">
      ${k}
      ${w}
      ${D}
      ${O}
      ${$}
      ${A}
      ${I}
      ${M}
    </body>
    </html>
  `}async function x(e,t=null){let s={receipt_number:e.receiptNumber||`POS-${Date.now()}`,customer_name:e.customerName||"Walk-in Customer",customer_email:e.customerEmail,customer_phone:e.customerPhone,service_name:e.serviceName,tier_name:e.tierName,artist_name:e.artistName,start_time:e.startTime||new Date().toISOString(),duration:e.duration,total_amount:e.totalAmount,tip_amount:e.tipAmount||0,payment_method:e.paymentMethod,notes:e.notes};return await u(s,t)}function v({service:e,artist:t,tier:n,timeSlot:l,onBack:m,onComplete:u}){let[h,p]=(0,i.useState)("customer"),[_,v]=(0,i.useState)(null),[S,g]=(0,i.useState)(null),[y,N]=(0,i.useState)(null),[b,f]=(0,i.useState)(!1),[C,P]=(0,i.useState)(null),k=n?.price||0,w=async e=>{try{f(!0),P(null);let t=await A("cash",{cashReceived:e?.cashReceived,changeAmount:e?.changeAmount,totalAmount:e?.totalAmount||k});if(t.success)await $(t,"cash",e),u(t);else throw Error(t.error||"Failed to process cash payment")}catch(e){console.error("Cash payment error:",e),P(e.message)}finally{f(!1)}},D=async e=>{try{f(!0),P(null);let t=e.paymentDetails?.deviceId?"square_terminal":"card",s=await A(t,e);if(s.success)await $(s,t,e),u(s);else throw Error(s.error||"Failed to record payment")}catch(e){console.error("Payment completion error:",e),P(e.message)}finally{f(!1)}},O=e=>{console.error("Square payment error:",e),P(e.message||"Card payment failed")},$=async(s,a,i)=>{try{let r={receiptNumber:`POS-${Date.now()}`,customerName:_?.name||"Walk-in Customer",customerEmail:_?.email,customerPhone:_?.phone,serviceName:e.name,tierName:n.name,artistName:t.name,startTime:l.time,duration:n.duration,totalAmount:i?.totalAmount||k,tipAmount:i?.tipAmount||0,paymentMethod:a,notes:`POS Transaction - ${t.name} - ${n.name}`},o=await x(r);o.success?(console.log("Receipt generated successfully"),s.receiptHtml=o.html,s.receiptData=r):console.warn("Failed to generate receipt:",o.error)}catch(e){console.error("Error generating receipt:",e)}},A=async(a,i=null)=>{try{console.log("\uD83D\uDD04 Creating POS booking...");let{supabase:r}=await Promise.resolve().then(s.bind(s,8316)),{data:{session:o},error:c}=await r.auth.getSession();if(console.log("Booking authentication check:",{hasSession:!!o,hasUser:!!o?.user,hasToken:!!o?.access_token,userEmail:o?.user?.email,error:c?.message}),!o?.access_token)throw Error("Authentication required. Please refresh the page and try again.");let d={customer_name:_?.name||"Walk-in Customer",customer_email:_?.email||"",customer_phone:_?.phone||"",service_id:e.id,service_name:e.name,artist_id:t.id,artist_name:t.name,tier_id:n.id,tier_name:n.name,duration:n.duration,start_time:l.time,end_time:new Date(new Date(l.time).getTime()+6e4*n.duration).toISOString(),total_amount:y?.totalAmount||k,status:"completed",payment_method:a,payment_status:"completed",notes:`POS Booking - ${t.name} - ${n.name}${y?.tipAmount>0?` - Tip: $${y.tipAmount.toFixed(2)}`:""}`,booking_type:"pos",created_via:"admin_pos",tip_amount:y?.tipAmount||0,tip_method:y?.tipMethod||"none",tip_percentage:y?.tipPercentage||0};i&&("cash"===a?(d.cash_received=i.cashReceived,d.change_amount=i.changeAmount):(d.payment_transaction_id=i.paymentId||i.transactionId,d.processing_fee=y?.processingFee||0)),console.log("Creating booking with data:",d);let m=await fetch("/api/admin/pos/create-booking",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.access_token}`},body:JSON.stringify(d)});if(!m.ok){let e=await m.text();throw console.error("Booking creation failed:",m.status,e),Error(`Failed to create booking: ${m.status}`)}let u=await m.json();return console.log("✅ Booking created successfully:",u),{success:!0,booking:u.booking,payment:u.payment,message:"Booking and payment processed successfully!"}}catch(e){return console.error("Error creating booking:",e),{success:!1,error:e.message}}};return(0,a.jsxs)("div",{className:r().posCheckoutContainer,children:[(0,a.jsxs)("div",{className:r().checkoutHeader,children:[a.jsx("button",{onClick:m,className:r().backButton,children:"← Back"}),a.jsx("h2",{children:"Complete Booking"})]}),(0,a.jsxs)("div",{className:r().bookingSummary,children:[a.jsx("h3",{children:"Booking Details"}),(0,a.jsxs)("div",{className:r().summaryGrid,children:[(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Service:"})," ",e.name]}),(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Artist:"})," ",t.name]}),(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Duration:"})," ",(e=>{if(!e)return"";let t=Math.floor(e/60),s=e%60;return t>0?s>0?`${t}h ${s}m`:`${t}h`:`${s}m`})(n.duration)]}),(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Price:"})," $",n.price?.toFixed(2)]}),(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Date & Time:"})," ",new Date(l.time).toLocaleString()]})]})]}),C&&(0,a.jsxs)("div",{className:r().errorAlert,children:[a.jsx("span",{className:r().errorIcon,children:"⚠️"}),a.jsx("div",{className:r().errorText,children:C}),a.jsx("button",{onClick:()=>P(null),className:r().closeError,children:"\xd7"})]}),"customer"===h&&(0,a.jsxs)("div",{className:r().customerStep,children:[a.jsx("h3",{children:"Customer Information"}),a.jsx("div",{className:r().customerForm,children:a.jsx(j,{onSubmit:e=>{v(e),p("payment")}})})]}),"payment"===h&&!S&&a.jsx(o,{amount:k,customerName:_?.name||"Walk-in Customer",onMethodSelect:(e,t)=>{g(e),N(t),"cash"===e&&w(t)},onCancel:()=>p("customer")}),"payment"===h&&"square_payment"===S&&a.jsx(c,{amount:y?.totalAmount||k,currency:"AUD",onSuccess:D,onError:O,orderDetails:{service:e.name,tier:n.name,customer:_?.name||"Walk-in Customer",orderId:`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`}}),"payment"===h&&"square_terminal"===S&&a.jsx(d,{amount:y?.totalAmount||k,currency:"AUD",onSuccess:D,onError:O,onCancel:()=>g(null),orderDetails:{service:e.name,tier:n.name,customer:_?.name||"Walk-in Customer",orderId:`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`}}),b&&a.jsx("div",{className:r().processingOverlay,children:(0,a.jsxs)("div",{className:r().processingContent,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Processing booking and payment..."})]})})]})}function j({onSubmit:e}){let[t,s]=(0,i.useState)({name:"",email:"",phone:"",notes:""}),[n,l]=(0,i.useState)(!1),o=(e,t)=>{s(s=>({...s,[e]:t}))};return(0,a.jsxs)("form",{onSubmit:s=>{if(s.preventDefault(),n)e({name:"Walk-in Customer",email:"",phone:"",notes:t.notes});else{if(!t.name.trim()){alert("Please enter customer name");return}e(t)}},className:r().customerForm,children:[a.jsx("div",{className:r().walkInToggle,children:(0,a.jsxs)("label",{children:[a.jsx("input",{type:"checkbox",checked:n,onChange:e=>l(e.target.checked)}),"Walk-in Customer (no details required)"]})}),!n&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Customer Name *"}),a.jsx("input",{type:"text",value:t.name,onChange:e=>o("name",e.target.value),placeholder:"Enter customer name",required:!0})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Email"}),a.jsx("input",{type:"email",value:t.email,onChange:e=>o("email",e.target.value),placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Phone"}),a.jsx("input",{type:"tel",value:t.phone,onChange:e=>o("phone",e.target.value),placeholder:"+61 XXX XXX XXX"})]})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Special Notes"}),a.jsx("textarea",{value:t.notes,onChange:e=>o("notes",e.target.value),placeholder:"Any special requirements or notes...",rows:3})]}),a.jsx("button",{type:"submit",className:r().continueButton,children:"Continue to Payment"})]})}},84:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var a=s(997),i=s(6689);function n(e,t=null){try{if("function"==typeof e)return e();return e}catch(e){return console.warn("Safe render error:",e),t}}var r=s(1073),l=s.n(r);function o({service:e,onBookingSlotSelect:t,onBack:s}){let[r,o]=(0,i.useState)(new Date().toISOString().split("T")[0]),[c,d]=(0,i.useState)(null),[m,u]=(0,i.useState)(null),[h,p]=(0,i.useState)([]),[_,x]=(0,i.useState)(!1),[v,j]=(0,i.useState)(null),S=e?.availableArtists?.filter(e=>e.isAvailableToday)||[],g=e?.pricing_tiers||[],y=e=>{d(e)},N=e=>{u(e)},b=e=>{"available"===e.status&&c&&m&&t(c,m,{...e,time:e.time})},f=e=>new Date(e).toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit",hour12:!1}),C=e=>`$${parseFloat(e||0).toFixed(2)}`,P=c&&m;return(0,a.jsxs)("div",{className:l().serviceBookingAvailability,children:[(0,a.jsxs)("div",{className:l().bookingHeader,children:[a.jsx("button",{className:l().backButton,onClick:s,children:"← Back to Services"}),(0,a.jsxs)("div",{className:l().serviceInfo,children:[a.jsx("h2",{className:l().serviceName,children:n(e.name,"Service")}),a.jsx("p",{className:l().serviceDescription,children:n(e.description,"")})]})]}),(0,a.jsxs)("div",{className:l().bookingSelectionGrid,children:[(0,a.jsxs)("div",{className:l().selectionSection,children:[a.jsx("h3",{className:l().sectionTitle,children:"Choose Artist"}),0===S.length?a.jsx("div",{className:l().noOptions,children:a.jsx("p",{children:"No artists available for this service today"})}):a.jsx("div",{className:l().artistGrid,children:S.map(e=>(0,a.jsxs)("div",{className:`${l().artistCard} ${c?.id===e.id?l().selected:""}`,onClick:()=>y(e),children:[a.jsx("div",{className:l().artistAvatar,children:e.name?.charAt(0)?.toUpperCase()||"?"}),(0,a.jsxs)("div",{className:l().artistInfo,children:[a.jsx("h4",{className:l().artistName,children:n(e.name,"Unknown Artist")}),a.jsx("p",{className:l().artistRole,children:n(e.role||e.specialties?.[0],"Artist")})]})]},e.id))})]}),(0,a.jsxs)("div",{className:l().selectionSection,children:[a.jsx("h3",{className:l().sectionTitle,children:"Choose Duration & Price"}),0===g.length?a.jsx("div",{className:l().noOptions,children:a.jsx("p",{children:"No pricing options available"})}):a.jsx("div",{className:l().tierGrid,children:g.map(e=>(0,a.jsxs)("div",{className:`${l().tierCard} ${m?.id===e.id?l().selected:""}`,onClick:()=>N(e),children:[a.jsx("h4",{className:l().tierName,children:n(e.name,"Standard")}),a.jsx("div",{className:l().tierPrice,children:C(e.price)}),(0,a.jsxs)("div",{className:l().tierDuration,children:[n(e.duration,"30")," minutes"]}),e.description&&a.jsx("p",{className:l().tierDescription,children:e.description})]},e.id))})]}),(0,a.jsxs)("div",{className:l().selectionSection,children:[a.jsx("h3",{className:l().sectionTitle,children:"Choose Time Slot"}),(0,a.jsxs)("div",{className:l().dateSelector,children:[a.jsx("label",{htmlFor:"booking-date-select",children:"Date:"}),a.jsx("input",{id:"booking-date-select",type:"date",value:r,onChange:e=>{o(e.target.value)},className:l().dateInput,min:new Date().toISOString().split("T")[0]})]}),P?_?(0,a.jsxs)("div",{className:l().loading,children:[a.jsx("div",{className:l().loadingSpinner}),a.jsx("p",{children:"Loading time slots..."})]}):v?a.jsx("div",{className:`${l().errorNotice} p-3 bg-red-100 border border-red-400 text-red-700 rounded-md`,children:(0,a.jsxs)("p",{children:["Error: ",v]})}):h.length>0?a.jsx("div",{className:l().timeSlots,children:h.map(e=>a.jsx("div",{className:`${l().timeSlot} ${l()[e.status]||("available"===e.status?l().available:l().unavailable)} ${"available"===e.status?l().clickable:""}`,onClick:()=>b(e),title:`${f(e.time)} - ${e.status}`,children:f(e.time)},e.id||e.time))}):a.jsx("div",{className:l().noOptions,children:a.jsx("p",{children:"No available slots for this day, artist, or service duration. Try changing the date or service options."})}):a.jsx("div",{className:l().selectPrompt,children:a.jsx("p",{children:"Please select an artist and duration first"})})]})]}),(c||m)&&(0,a.jsxs)("div",{className:l().selectionSummary,children:[a.jsx("h4",{children:"Current Selection"}),(0,a.jsxs)("div",{className:l().summaryGrid,children:[c&&(0,a.jsxs)("div",{className:l().summaryItem,children:[a.jsx("strong",{children:"Artist:"})," ",c.name]}),m&&(0,a.jsxs)("div",{className:l().summaryItem,children:[a.jsx("strong",{children:"Duration:"})," ",m.duration," minutes"]}),m&&(0,a.jsxs)("div",{className:l().summaryItem,children:[a.jsx("strong",{children:"Price:"})," ",C(m.price)]})]}),P&&a.jsx("p",{className:l().nextStep,children:"Now select an available time slot to continue"})]})]})}},8316:(e,t,s)=>{"use strict";s.d(t,{supabase:()=>l,pR:()=>o});let a=require("@supabase/supabase-js"),i="https://ndlgbcsbidyhxbpqzgqp.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",r=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i||!n)throw Error("Missing Supabase environment variables");let l=(0,a.createClient)(i,n,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}}),o=(0,a.createClient)(i,r||n,{auth:{autoRefreshToken:!1,persistSession:!1}})},4679:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>x});var i=s(997),n=s(6689),r=s(968),l=s.n(r),o=s(8568),c=s(4845),d=s(84),m=s(583),u=s(6141),h=s(1073),p=s.n(h),_=e([c]);function x(){let{user:e,loading:t}=(0,o.a)(),[s,a]=(0,n.useState)(!0),[r,h]=(0,n.useState)([]),[_,x]=(0,n.useState)([]),[v,j]=(0,n.useState)([]),[S,g]=(0,n.useState)(!1),[y,N]=(0,n.useState)("services"),[b,f]=(0,n.useState)(null),[C,P]=(0,n.useState)(null),[k,w]=(0,n.useState)([]),[D,O]=(0,n.useState)(null),[$,A]=(0,n.useState)(0),[I,M]=(0,n.useState)("card"),[T,B]=(0,n.useState)(!1),[q,R]=(0,n.useState)(""),[F,E]=(0,n.useState)(""),L=e=>{f(e),N("booking")},z=e=>{k.find(t=>t.id===e.id)?w(k.map(t=>t.id===e.id?{...t,quantity:t.quantity+1}:t)):w([...k,{id:e.id,name:e.name,price:e.price||0,quantity:1,duration:e.duration}])},G=e=>{w(k.filter(t=>t.id!==e))},X=(e,t)=>{if(t<=0){G(e);return}w(k.map(s=>s.id===e?{...s,quantity:t}:s))},H=()=>{w([]),O(null),A(0)},Y=async()=>{if(0!==k.length){B(!0);try{let e=localStorage.getItem("admin-token"),t={customer_id:D?.id,customer_name:D?.name||"Walk-in Customer",customer_email:D?.email||"",customer_phone:D?.phone||"",services:k.map(e=>({service_id:e.id,service_name:e.name,quantity:e.quantity,price:e.price,duration:e.duration})),total_amount:$,payment_method:I,status:"completed",booking_date:new Date().toISOString(),notes:"POS Transaction"};if((await fetch("/api/admin/bookings",{method:"POST",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify(t)})).ok)alert("Transaction completed successfully!"),H();else throw Error("Failed to process transaction")}catch(e){console.error("Error processing transaction:",e),alert("Error processing transaction. Please try again.")}finally{B(!1)}}},U=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),W=e=>{if(!e)return"";if(e>=60){let t=Math.floor(e/60),s=e%60;return s>0?`${t}h ${s}m`:`${t}h`}return`${e}m`},Z=r.filter(e=>e.name.toLowerCase().includes(q.toLowerCase())||e.category.toLowerCase().includes(q.toLowerCase()));return(_.filter(e=>e.name.toLowerCase().includes(F.toLowerCase())||e.email.toLowerCase().includes(F.toLowerCase())||e.phone&&e.phone.includes(F)),t||s)?i.jsx(c.Z,{children:(0,i.jsxs)("div",{className:p().loadingContainer,children:[i.jsx("div",{className:p().loadingSpinner}),i.jsx("p",{children:"Loading POS system..."})]})}):e?S?(0,i.jsxs)(c.Z,{children:[(0,i.jsxs)(l(),{children:[i.jsx("title",{children:"Mobile POS | Ocean Soul Sparkles Admin"}),i.jsx("meta",{name:"description",content:"Mobile point of sale interface"})]}),i.jsx(u.Z,{onTransactionComplete:e=>{console.log("Mobile transaction completed:",e),alert(`Transaction completed! Total: $${e.total.toFixed(2)}`)},services:r,products:v,customers:_})]}):(0,i.jsxs)(c.Z,{children:[(0,i.jsxs)(l(),{children:[i.jsx("title",{children:"Point of Sale | Ocean Soul Sparkles Admin"}),i.jsx("meta",{name:"description",content:"Process transactions and manage sales"})]}),(0,i.jsxs)("div",{className:p().posContainer,children:["services"===y&&(0,i.jsxs)("div",{className:p().posContent,children:[(0,i.jsxs)("div",{className:p().servicesSection,children:[(0,i.jsxs)("div",{className:p().sectionHeader,children:[i.jsx("h2",{children:"Services"}),i.jsx("p",{children:"Select a service to start booking with calendar and payment processing"}),i.jsx("input",{type:"text",placeholder:"Search services...",value:q,onChange:e=>R(e.target.value),className:p().searchInput})]}),i.jsx("div",{className:p().servicesGrid,children:Z.map(e=>(0,i.jsxs)("div",{className:p().serviceCard,children:[i.jsx("h3",{className:p().serviceName,children:e.name}),i.jsx("p",{className:p().serviceCategory,children:e.category}),(0,i.jsxs)("div",{className:p().serviceDetails,children:[i.jsx("span",{className:p().price,children:e.price?U(e.price):"From $50"}),e.duration&&i.jsx("span",{className:p().duration,children:W(e.duration)})]}),(0,i.jsxs)("div",{className:p().serviceActions,children:[i.jsx("button",{onClick:()=>L(e),className:p().bookServiceBtn,children:"\uD83D\uDCC5 Book with Calendar"}),i.jsx("button",{onClick:()=>z(e),className:p().addToCartBtn,disabled:!e.price,children:"\uD83D\uDED2 Quick Add"})]})]},e.id))})]}),k.length>0&&(0,i.jsxs)("div",{className:p().quickCart,children:[(0,i.jsxs)("h3",{children:["Quick Cart (",k.length," items)"]}),i.jsx("div",{className:p().cartItems,children:k.map(e=>(0,i.jsxs)("div",{className:p().cartItem,children:[(0,i.jsxs)("div",{className:p().itemInfo,children:[i.jsx("span",{className:p().itemName,children:e.name}),i.jsx("span",{className:p().itemPrice,children:U(e.price)})]}),(0,i.jsxs)("div",{className:p().quantityControls,children:[i.jsx("button",{onClick:()=>X(e.id,e.quantity-1),className:p().quantityBtn,children:"-"}),i.jsx("span",{className:p().quantity,children:e.quantity}),i.jsx("button",{onClick:()=>X(e.id,e.quantity+1),className:p().quantityBtn,children:"+"})]}),i.jsx("span",{className:p().itemTotal,children:U(e.price*e.quantity)}),i.jsx("button",{onClick:()=>G(e.id),className:p().removeBtn,children:"\xd7"})]},e.id))}),(0,i.jsxs)("div",{className:p().cartTotal,children:["Total: ",U($)]}),(0,i.jsxs)("div",{className:p().cartActions,children:[i.jsx("button",{onClick:H,className:p().clearBtn,children:"Clear"}),i.jsx("button",{onClick:Y,className:p().quickCheckoutBtn,disabled:T,children:T?"Processing...":"Quick Checkout"})]})]})]}),"booking"===y&&b&&i.jsx(d.Z,{service:b,onBookingSlotSelect:(e,t,s)=>{P({artist:e,tier:t,timeSlot:s}),N("checkout")},onBack:()=>{N("services"),f(null),P(null)}}),"checkout"===y&&C&&i.jsx(m.Z,{service:b,artist:C.artist,tier:C.tier,timeSlot:C.timeSlot,onBack:()=>{N("booking"),P(null)},onComplete:e=>{console.log("Checkout completed:",e),N("services"),f(null),P(null),alert("Booking completed successfully!")}})]})]}):null}c=(_.then?(await _)():_)[0],a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2899,6212,1664,7441],()=>s(519));module.exports=a})();