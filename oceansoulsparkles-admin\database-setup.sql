-- Ocean Soul Sparkles Admin Subdomain Database Setup
-- Run this SQL in your Supabase SQL Editor

-- 1. Create admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
  last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
  role VARCHAR(20) NOT NULL CHECK (role IN ('DEV', 'Admin', 'Artist', 'Braider')),
  is_active BOOLEAN DEFAULT true,
  mfa_enabled BOOLEAN DEFAULT false,
  mfa_secret TEXT,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  action VARCHAR(100) NOT NULL,
  user_id UUID REFERENCES admin_users(id),
  user_role VARCHAR(20),
  email VARCHAR(255),
  ip_address INET,
  path TEXT,
  resource VARCHAR(100),
  resource_id TEXT,
  old_values JSONB,
  new_values JSONB,
  reason TEXT,
  error TEXT,
  metadata JSONB,
  severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create user_roles table
CREATE TABLE IF NOT EXISTS user_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  role VARCHAR(20) NOT NULL CHECK (role IN ('DEV', 'Admin', 'Artist', 'Braider', 'User')),
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, role)
);

-- 4. Enable Row Level Security
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

-- 5. Create RLS Policies
CREATE POLICY IF NOT EXISTS "Admin users can manage all users" ON admin_users
FOR ALL USING (true);

CREATE POLICY IF NOT EXISTS "Admin users can read audit logs" ON audit_logs
FOR SELECT USING (true);

CREATE POLICY IF NOT EXISTS "Admin users can insert audit logs" ON audit_logs
FOR INSERT WITH CHECK (true);

CREATE POLICY IF NOT EXISTS "Admin users can manage roles" ON user_roles
FOR ALL USING (true);

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_role ON admin_users(role);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);

-- 7. Create development admin user (password: DevPassword123!)
INSERT INTO admin_users (email, password_hash, first_name, last_name, role, is_active, mfa_enabled)
VALUES (
  '<EMAIL>',
  '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtGtJbPiOy', -- DevPassword123!
  'Development',
  'Admin',
  'DEV',
  true,
  false
)
ON CONFLICT (email) DO NOTHING;

-- Success message
SELECT 'Database setup completed successfully!' as message;
