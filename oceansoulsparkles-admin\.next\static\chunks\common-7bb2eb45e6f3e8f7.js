(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[592],{99:function(e,r,t){"use strict";t.d(r,{Z:function(){return P}});var o=t(1163),n=t(1664),a=t.n(n),i=t(2920),c=t(1976),s=t.n(c);!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();let d=[{id:"dashboard",label:"Dashboard",icon:"\uD83D\uDCCA",href:"/admin/dashboard",roles:["DEV","Admin","Artist","Braider"]},{id:"bookings",label:"Bookings",icon:"\uD83D\uDCC5",href:"/admin/bookings",roles:["DEV","Admin","Artist","Braider"]},{id:"customers",label:"Customers",icon:"\uD83D\uDC65",href:"/admin/customers",roles:["DEV","Admin"]},{id:"services",label:"Services",icon:"✨",href:"/admin/services",roles:["DEV","Admin"]},{id:"products",label:"Products",icon:"\uD83D\uDECD️",href:"/admin/products",roles:["DEV","Admin"]},{id:"suppliers",label:"Suppliers",icon:"\uD83D\uDCE6",href:"/admin/suppliers",roles:["DEV","Admin"]},{id:"purchase-orders",label:"Purchase Orders",icon:"\uD83D\uDCCB",href:"/admin/purchase-orders",roles:["DEV","Admin"]},{id:"staff",label:"Staff Management",icon:"\uD83D\uDC68‍\uD83D\uDCBC",href:"/admin/staff",roles:["DEV","Admin"],children:[{id:"staff-overview",label:"Staff Overview",icon:"\uD83D\uDC65",href:"/admin/staff",roles:["DEV","Admin"]},{id:"staff-onboarding",label:"Onboarding",icon:"\uD83D\uDCCB",href:"/admin/staff/onboarding",roles:["DEV","Admin"]},{id:"staff-training",label:"Training",icon:"\uD83C\uDF93",href:"/admin/staff/training",roles:["DEV","Admin"]},{id:"staff-performance",label:"Performance",icon:"\uD83D\uDCCA",href:"/admin/staff/performance",roles:["DEV","Admin"]},{id:"staff-schedule",label:"Schedule Management",icon:"\uD83D\uDDD3️",href:"/admin/staff/schedule",roles:["DEV","Admin"]}]},{id:"artists",label:"Artists",icon:"\uD83C\uDFA8",href:"/admin/artists",roles:["DEV","Admin"],children:[{id:"artists-overview",label:"Artists Overview",icon:"\uD83D\uDC68‍\uD83C\uDFA8",href:"/admin/artists",roles:["DEV","Admin"]},{id:"artists-portfolio",label:"Portfolio Management",icon:"\uD83D\uDDBC️",href:"/admin/artists/portfolio",roles:["DEV","Admin"]},{id:"artists-schedule",label:"Artist Scheduling",icon:"\uD83D\uDCC5",href:"/admin/artists/schedule",roles:["DEV","Admin"]},{id:"artists-commissions",label:"Commission Tracking",icon:"\uD83D\uDCB0",href:"/admin/artists/commissions",roles:["DEV","Admin"]}]},{id:"tips",label:"Tip Management",icon:"\uD83D\uDCB0",href:"/admin/tips",roles:["DEV","Admin"]},{id:"receipts",label:"Receipts",icon:"\uD83E\uDDFE",href:"/admin/receipts",roles:["DEV","Admin"]},{id:"reports",label:"Reports",icon:"\uD83D\uDCC8",href:"/admin/reports",roles:["DEV","Admin"]},{id:"communications",label:"Communications",icon:"\uD83D\uDCE7",href:"/admin/communications",roles:["DEV","Admin"],children:[{id:"email-templates",label:"Email Templates",icon:"\uD83D\uDCDD",href:"/admin/email-templates",roles:["DEV","Admin"]},{id:"sms-templates",label:"SMS Templates",icon:"\uD83D\uDCF1",href:"/admin/sms-templates",roles:["DEV","Admin"]},{id:"communications-log",label:"Communications Log",icon:"\uD83D\uDCCB",href:"/admin/communications",roles:["DEV","Admin"]},{id:"feedback",label:"Customer Feedback",icon:"⭐",href:"/admin/feedback",roles:["DEV","Admin"]}]},{id:"notifications",label:"Notifications",icon:"\uD83D\uDD14",href:"/admin/notifications",roles:["DEV","Admin"]},{id:"settings",label:"Settings",icon:"⚙️",href:"/admin/settings",roles:["DEV","Admin"]}];function l(e){let{user:r,collapsed:t,onToggle:n,isMobile:i}=e,c=(0,o.useRouter)(),[l,u]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),m=e=>{u(r=>r.includes(e)?r.filter(r=>r!==e):[...r,e])},_=e=>e.includes(r.role),f=e=>c.pathname===e||c.pathname.startsWith(e+"/"),O=d.filter(e=>_(e.roles));return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("aside",{className:"".concat(s().sidebar," ").concat(t?s().collapsed:""," ").concat(i?s().mobile:""),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().sidebarHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().logo,children:[!t&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().logoIcon,children:"\uD83C\uDF0A"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().logoText,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().logoTitle,children:"Ocean Soul"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().logoSubtitle,children:"Admin"})]})]}),t&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().logoIconOnly,children:"\uD83C\uDF0A"})]}),!i&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:s().toggleButton,onClick:n,title:t?"Expand sidebar":"Collapse sidebar",children:t?"→":"←"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().userInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().userAvatar,children:[r.firstName.charAt(0),r.lastName.charAt(0)]}),!t&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().userDetails,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().userName,children:[r.firstName," ",r.lastName]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().userRole,children:r.role})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("nav",{className:s().navigation,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("ul",{className:s().menuList,children:O.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{className:s().menuItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:e.href,className:"".concat(s().menuLink," ").concat(f(e.href)?s().active:""),title:t?e.label:void 0,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:s().menuIcon,children:e.icon}),!t&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:s().menuLabel,children:e.label}),!t&&e.children&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:s().expandButton,onClick:r=>{r.preventDefault(),m(e.id)},children:l.includes(e.id)?"▼":"▶"})]}),!t&&e.children&&l.includes(e.id)&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("ul",{className:s().submenu,children:e.children.filter(e=>_(e.roles)).map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{className:s().submenuItem,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:e.href,className:"".concat(s().submenuLink," ").concat(f(e.href)?s().active:""),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:s().submenuIcon,children:e.icon}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:s().submenuLabel,children:e.label})]})},e.id))})]},e.id))})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().sidebarFooter,children:[!t&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().footerContent,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().versionInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().version,children:"v1.0.0"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().environment,children:"PROD"})]})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().securityIndicator,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().securityIcon,children:"\uD83D\uDD12"}),!t&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:s().securityText,children:"Secure Portal"})]})]})]})}var u=t(9675),m=t.n(u);function _(e){let{placeholder:r="Search customers, bookings, services...",className:t=""}=e,[n,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[i,c]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[s,d]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[l,u]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[_,f]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[O,h]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(-1),D=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),b=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),N=(0,o.useRouter)();Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!n.trim()){c([]),u(!1);return}let e=setTimeout(()=>{p(n)},300);return()=>clearTimeout(e)},[n]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=e=>{D.current&&!D.current.contains(e.target)&&(u(!1),h(-1))};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=e=>{if(l&&0!==i.length)switch(e.key){case"ArrowDown":e.preventDefault(),h(e=>e<i.length-1?e+1:e);break;case"ArrowUp":e.preventDefault(),h(e=>e>0?e-1:-1);break;case"Enter":e.preventDefault(),O>=0&&i[O]&&v(i[O]);break;case"Escape":var r;u(!1),h(-1),null===(r=b.current)||void 0===r||r.blur()}};if(l)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[l,i,O]);let p=async e=>{if(e.trim()){d(!0),f(null);try{let r=localStorage.getItem("admin-token"),t=await fetch("/api/admin/search?q=".concat(encodeURIComponent(e),"&limit=10"),{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}});if(!t.ok)throw Error("Search failed");let o=await t.json();c(o.results),u(!0),h(-1)}catch(e){console.error("Search error:",e),f("Search failed. Please try again."),c([])}finally{d(!1)}}},v=e=>{u(!1),a(""),h(-1),N.push(e.url)},E=e=>{switch(e){case"customer":return"\uD83D\uDC64";case"booking":return"\uD83D\uDCC5";case"service":return"✨";default:return"\uD83D\uDCC4"}},C=e=>{switch(e){case"customer":return"Customer";case"booking":return"Booking";case"service":return"Service";default:return"Result"}};return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"".concat(m().globalSearch," ").concat(t),ref:D,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().searchInput,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().searchIcon,children:"\uD83D\uDD0D"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{ref:b,type:"text",value:n,onChange:e=>a(e.target.value),onFocus:()=>{i.length>0&&u(!0)},placeholder:r,className:m().input,autoComplete:"off","data-global-search":"true"}),s&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().loadingSpinner,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().spinner})}),n&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:m().clearButton,onClick:()=>{var e;a(""),c([]),u(!1),null===(e=b.current)||void 0===e||e.focus()},title:"Clear search",children:"✕"})]}),l&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().searchResults,children:_?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().errorMessage,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:m().errorIcon,children:"⚠️"}),_]}):i.length>0?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().resultsHeader,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:m().resultsCount,children:[i.length," result",1!==i.length?"s":"",' for "',n,'"']})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().resultsList,children:i.map((e,r)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"".concat(m().resultItem," ").concat(r===O?m().selected:""),onClick:()=>v(e),onMouseEnter:()=>h(r),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().resultIcon,children:E(e.type)}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().resultContent,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().resultTitle,children:e.title}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().resultSubtitle,children:e.subtitle}),e.description&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().resultDescription,children:e.description})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().resultType,children:C(e.type)})]},"".concat(e.type,"-").concat(e.id)))}),i.length>=10&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().resultsFooter,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:m().moreResults,children:"Showing first 10 results. Refine your search for more specific results."})})]}):n.trim()&&!s?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().noResults,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:m().noResultsIcon,children:"\uD83D\uDD0D"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().noResultsText,children:['No results found for "',n,'"']}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:m().noResultsHint,children:"Try searching for customer names, booking dates, or service names"})]}):null})]})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var f=t(73),O=t.n(f);!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();let h={"/admin":{label:"Admin",icon:"\uD83C\uDFE0"},"/admin/dashboard":{label:"Dashboard",icon:"\uD83D\uDCCA",parent:"/admin"},"/admin/services":{label:"Services",icon:"✨",parent:"/admin/dashboard"},"/admin/services/new":{label:"New Service",icon:"➕",parent:"/admin/services"},"/admin/services/[id]":{label:"Service Details",icon:"\uD83D\uDCDD",parent:"/admin/services"},"/admin/products":{label:"Products",icon:"\uD83D\uDECD️",parent:"/admin/dashboard"},"/admin/products/new":{label:"New Product",icon:"➕",parent:"/admin/products"},"/admin/products/[id]":{label:"Product Details",icon:"\uD83D\uDCDD",parent:"/admin/products"},"/admin/inventory":{label:"Inventory",icon:"\uD83D\uDCE6",parent:"/admin/dashboard"},"/admin/inventory/new":{label:"New Item",icon:"➕",parent:"/admin/inventory"},"/admin/pos":{label:"POS Terminal",icon:"\uD83D\uDCB3",parent:"/admin/dashboard"},"/admin/customers":{label:"Customers",icon:"\uD83D\uDC65",parent:"/admin/dashboard"},"/admin/customers/new":{label:"New Customer",icon:"➕",parent:"/admin/customers"},"/admin/customers/[id]":{label:"Customer Details",icon:"\uD83D\uDC64",parent:"/admin/customers"},"/admin/bookings":{label:"Bookings",icon:"\uD83D\uDCC5",parent:"/admin/dashboard"},"/admin/bookings/new":{label:"New Booking",icon:"➕",parent:"/admin/bookings"},"/admin/bookings/[id]":{label:"Booking Details",icon:"\uD83D\uDCDD",parent:"/admin/bookings"},"/admin/staff":{label:"Staff Management",icon:"\uD83D\uDC68‍\uD83D\uDCBC",parent:"/admin/dashboard"},"/admin/staff/onboarding":{label:"Staff Onboarding",icon:"\uD83C\uDFAF",parent:"/admin/staff"},"/admin/staff/training":{label:"Training",icon:"\uD83D\uDCDA",parent:"/admin/staff"},"/admin/staff/performance":{label:"Performance",icon:"\uD83D\uDCC8",parent:"/admin/staff"},"/admin/artists":{label:"Artists",icon:"\uD83C\uDFA8",parent:"/admin/dashboard"},"/admin/artists/portfolio":{label:"Portfolio",icon:"\uD83D\uDDBC️",parent:"/admin/artists"},"/admin/artists/[id]":{label:"Artist Details",icon:"\uD83D\uDC68‍\uD83C\uDFA8",parent:"/admin/artists"},"/admin/communications":{label:"Communications",icon:"\uD83D\uDCE7",parent:"/admin/dashboard"},"/admin/email-templates":{label:"Email Templates",icon:"\uD83D\uDCE7",parent:"/admin/communications"},"/admin/sms-templates":{label:"SMS Templates",icon:"\uD83D\uDCF1",parent:"/admin/communications"},"/admin/notifications":{label:"Notifications",icon:"\uD83D\uDD14",parent:"/admin/communications"},"/admin/reports":{label:"Reports & Analytics",icon:"\uD83D\uDCCA",parent:"/admin/dashboard"},"/admin/receipts":{label:"Receipt Templates",icon:"\uD83E\uDDFE",parent:"/admin/reports"},"/admin/feedback":{label:"Customer Feedback",icon:"\uD83D\uDCAC",parent:"/admin/reports"},"/admin/suppliers":{label:"Suppliers",icon:"\uD83C\uDFE2",parent:"/admin/inventory"},"/admin/suppliers/new":{label:"New Supplier",icon:"➕",parent:"/admin/suppliers"},"/admin/purchase-orders":{label:"Purchase Orders",icon:"\uD83D\uDCCB",parent:"/admin/inventory"},"/admin/purchase-orders/new":{label:"New Order",icon:"➕",parent:"/admin/purchase-orders"},"/admin/settings":{label:"Settings",icon:"⚙️",parent:"/admin/dashboard"},"/admin/tips":{label:"Tips Management",icon:"\uD83D\uDCB0",parent:"/admin/settings"},"/admin/mobile-debug":{label:"Mobile Debug",icon:"\uD83D\uDD27",parent:"/admin/settings"},"/admin/mobile-test":{label:"Mobile Test",icon:"\uD83D\uDCF1",parent:"/admin/settings"}};function D(e){let{className:r="",showIcons:t=!0,maxItems:n=5}=e,i=(0,o.useRouter)(),c=function(){let e=(0,o.useRouter)(),[r,t]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({loading:!1});return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{(async()=>{let{pathname:r,query:o}=e;if(!o.id||"string"!=typeof o.id){t({loading:!1});return}t({loading:!0});try{let e=localStorage.getItem("admin-token");if(!e){t({loading:!1,error:"No authentication token"});return}let n="",a="";if(r.includes("/customers/[id]"))n="/api/admin/customers/".concat(o.id),a="customer";else if(r.includes("/bookings/[id]"))n="/api/admin/bookings/".concat(o.id),a="booking";else if(r.includes("/services/[id]"))n="/api/admin/services/".concat(o.id),a="service";else if(r.includes("/products/[id]"))n="/api/admin/products/".concat(o.id),a="product";else if(r.includes("/artists/[id]"))n="/api/admin/artists/".concat(o.id),a="artist";else{t({loading:!1});return}let i=await fetch(n,{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok)throw Error("Failed to fetch ".concat(a," data"));let c=await i.json(),s={loading:!1};if("customer"===a&&c.customer)s.customerName="".concat(c.customer.first_name," ").concat(c.customer.last_name);else if("booking"===a&&c.booking){if(s.bookingId=c.booking.id,c.booking.customers){let e=Array.isArray(c.booking.customers)?c.booking.customers[0]:c.booking.customers;e&&(s.customerName="".concat(e.first_name," ").concat(e.last_name))}if(c.booking.services){let e=Array.isArray(c.booking.services)?c.booking.services[0]:c.booking.services;e&&(s.serviceName=e.name)}}else"service"===a&&c.service?s.serviceName=c.service.name:"product"===a&&c.product?s.productName=c.product.name:"artist"===a&&c.artist&&(s.artistName=c.artist.artist_name||c.artist.display_name);t(s)}catch(e){console.error("Error fetching breadcrumb data:",e),t({loading:!1,error:e instanceof Error?e.message:"Unknown error"})}})()},[e.pathname,e.query.id]),r}(),s=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=i.asPath.split("?")[0];e.split("/").filter(Boolean);let r=[];e.startsWith("/admin")&&"/admin/dashboard"!==e&&r.push({label:"Dashboard",href:"/admin/dashboard",icon:"\uD83D\uDCCA"});let t=e=>{let r=h[e];if(!r){let r=h[e.replace(/\/[^/]+$/,"/[id]")];if(r){let o=[];r.parent&&o.push(...t(r.parent));let n=e.split("/"),a=n[n.length-1],i=r.label,s=c.loading?"Loading...":e.includes("/customers/")&&c.customerName?c.customerName:e.includes("/bookings/")&&c.bookingId?c.customerName&&c.serviceName?"".concat(c.customerName," - ").concat(c.serviceName):c.customerName?"Booking for ".concat(c.customerName):"Booking #".concat(c.bookingId.slice(0,8)):e.includes("/services/")&&c.serviceName?c.serviceName:e.includes("/products/")&&c.productName?c.productName:e.includes("/artists/")&&c.artistName?c.artistName:null;return s?i=s:"new"!==a&&(e.includes("/customers/")?i="Customer #".concat(a.slice(0,8)):e.includes("/bookings/")?i="Booking #".concat(a.slice(0,8)):e.includes("/services/")?i="Service #".concat(a.slice(0,8)):e.includes("/products/")?i="Product #".concat(a.slice(0,8)):e.includes("/artists/")&&(i="Artist #".concat(a.slice(0,8)))),o.push({label:i,href:e,icon:r.icon}),o}let o=e.split("/").filter(Boolean);return[{label:o[o.length-1].charAt(0).toUpperCase()+o[o.length-1].slice(1),href:e,icon:"\uD83D\uDCC4"}]}let o=[];return r.parent&&o.push(...t(r.parent)),o.push({label:r.label,href:e,icon:r.icon}),o},o=t(e),a=[...r];return(o.forEach(e=>{a.some(r=>r.href===e.href)||a.push(e)}),a.length>0&&(a[a.length-1].isActive=!0),n&&a.length>n)?[a[0],{label:"...",href:"",icon:"⋯"},...a.slice(-2)]:a},[i.asPath,n]);return s.length<=1?null:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("nav",{className:"".concat(O().breadcrumbNavigation," ").concat(r),"aria-label":"Breadcrumb",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("ol",{className:O().breadcrumbList,children:s.map((e,r)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{className:O().breadcrumbItem,children:[e.href&&!e.isActive?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:e.href,className:O().breadcrumbLink,children:[t&&e.icon&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:O().breadcrumbIcon,children:e.icon}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:O().breadcrumbLabel,children:e.label})]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"".concat(O().breadcrumbCurrent," ").concat(e.isActive?O().active:""),children:[t&&e.icon&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:O().breadcrumbIcon,children:e.icon}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:O().breadcrumbLabel,children:e.label})]}),r<s.length-1&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:O().breadcrumbSeparator,"aria-hidden":"true",children:"/"})]},"".concat(e.href,"-").concat(r)))})})}var b=t(5593),N=t.n(b);!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react-dom'");throw e.code="MODULE_NOT_FOUND",e}();let p=[{keys:["Ctrl","K"],description:"Open global search",category:"Navigation"},{keys:["Ctrl","H"],description:"Go to dashboard",category:"Navigation"},{keys:["Ctrl","U"],description:"Go to customers",category:"Navigation"},{keys:["Ctrl","B"],description:"Go to bookings",category:"Navigation"},{keys:["Ctrl","S"],description:"Go to services",category:"Navigation"},{keys:["Ctrl","P"],description:"Go to products",category:"Navigation"},{keys:["Ctrl","I"],description:"Go to inventory",category:"Navigation"},{keys:["Ctrl","T"],description:"Go to staff management",category:"Navigation"},{keys:["Ctrl","N"],description:"Create new item",category:"Actions"},{keys:["Ctrl","E"],description:"Export current data",category:"Actions"},{keys:["Ctrl","A"],description:"Select all items",category:"Actions"},{keys:["Delete"],description:"Delete selected items",category:"Actions"},{keys:["Ctrl","Z"],description:"Undo last action",category:"Actions"},{keys:["Ctrl","Y"],description:"Redo last action",category:"Actions"},{keys:["Escape"],description:"Close modal/dropdown",category:"Interface"},{keys:["Tab"],description:"Navigate between elements",category:"Interface"},{keys:["Shift","Tab"],description:"Navigate backwards",category:"Interface"},{keys:["Enter"],description:"Confirm action",category:"Interface"},{keys:["Space"],description:"Toggle selection",category:"Interface"},{keys:["Ctrl","F"],description:"Focus search field",category:"Search & Filters"},{keys:["Ctrl","L"],description:"Clear all filters",category:"Search & Filters"},{keys:["↑","↓"],description:"Navigate search results",category:"Search & Filters"},{keys:["Ctrl","?"],description:"Show keyboard shortcuts",category:"Help"},{keys:["F1"],description:"Open help documentation",category:"Help"}];function v(e){let{isOpen:r,onClose:t}=e,[o,n]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1);if(Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{n(!0)},[]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!r)return;let e=e=>{"Escape"===e.key&&t()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[r,t]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>(r?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[r]),!o||!r)return null;let a=Array.from(new Set(p.map(e=>e.category))),i=e=>({Ctrl:"⌘",Alt:"⌥",Shift:"⇧",Enter:"↵",Space:"␣",Tab:"⇥",Escape:"⎋",Delete:"⌫","↑":"↑","↓":"↓","←":"←","→":"→"})[e]||e,c=Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().overlay,onClick:t,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().modal,onClick:e=>e.stopPropagation(),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("header",{className:N().header,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{className:N().title,children:"Keyboard Shortcuts"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:t,className:N().closeBtn,"aria-label":"Close shortcuts help",children:"✕"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().content,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().intro,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Use these keyboard shortcuts to navigate and interact with the admin dashboard more efficiently."})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().shortcutsGrid,children:a.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().category,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:N().categoryTitle,children:e}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().shortcutsList,children:p.filter(r=>r.category===e).map((e,r)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().shortcutItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().keys,children:e.keys.map((r,t)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("kbd",{className:N().key,children:i(r)}),t<e.keys.length-1&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:N().keySeparator,children:"+"})]},t))}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().description,children:e.description})]},r))})]},e))}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().footer,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:N().tip,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"\uD83D\uDCA1 Tip:"})," Most shortcuts work globally throughout the admin dashboard. Press ",Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("kbd",{className:N().key,children:"Ctrl"})," + ",Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("kbd",{className:N().key,children:"?"})," anytime to open this help."]})})]})]})});return Object(function(){var e=Error("Cannot find module 'react-dom'");throw e.code="MODULE_NOT_FOUND",e}())(c,document.body)}function E(e){let{className:r=""}=e,{showShortcuts:t,openShortcuts:o,closeShortcuts:n}=function(){let[e,r]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1);return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=e=>{var t;if(!(e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement)&&(null===(t=e.target)||void 0===t?void 0:t.contentEditable)!=="true"){if(e.ctrlKey||e.metaKey)switch(e.key.toLowerCase()){case"?":e.preventDefault(),r(!0);break;case"k":e.preventDefault();let t=document.querySelector("[data-global-search]");t&&t.focus();break;case"h":e.preventDefault(),window.location.href="/admin";break;case"u":e.preventDefault(),window.location.href="/admin/customers";break;case"b":e.preventDefault(),window.location.href="/admin/bookings";break;case"s":e.preventDefault(),window.location.href="/admin/services";break;case"p":e.preventDefault(),window.location.href="/admin/products";break;case"i":e.preventDefault(),window.location.href="/admin/inventory";break;case"t":e.preventDefault(),window.location.href="/admin/staff";break;case"f":e.preventDefault();let o=document.querySelector('input[type="search"], input[placeholder*="search" i]');o&&o.focus()}"F1"===e.key&&(e.preventDefault(),r(!0))}};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]),{showShortcuts:e,setShowShortcuts:r,openShortcuts:()=>r(!0),closeShortcuts:()=>r(!1)}}();return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:o,className:"".concat(N().helpButton," ").concat(r),title:"Keyboard shortcuts (Ctrl + ?)","aria-label":"Show keyboard shortcuts help",children:"⌨️"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(v,{isOpen:t,onClose:n})]})}var C=t(3790),U=t.n(C);function g(e){let{user:r,onLogout:t,onToggleSidebar:o,sidebarCollapsed:n}=e,[i,c]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[s,d]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),l=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),u=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=e=>{l.current&&!l.current.contains(e.target)&&c(!1),u.current&&!u.current.contains(e.target)&&d(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let m=e=>{switch(e){case"DEV":return"#dc3545";case"Admin":return"#3788d8";case"Artist":return"#28a745";case"Braider":return"#fd7e14";default:return"#6c757d"}};return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("header",{className:U().adminHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().headerLeft,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:U().sidebarToggle,onClick:()=>{console.log("\uD83C\uDF54 Hamburger menu clicked!",{sidebarCollapsed:n}),o()},title:n?"Expand sidebar":"Collapse sidebar",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:U().hamburger,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{})]})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(D,{className:U().breadcrumb,showIcons:!0,maxItems:4})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().headerCenter,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(_,{})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().headerRight,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().quickActions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/bookings/new",className:U().quickAction,title:"New Booking",children:"\uD83D\uDCC5"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/customers/new",className:U().quickAction,title:"New Customer",children:"\uD83D\uDC64"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:U().quickAction,title:"Refresh",children:"\uD83D\uDD04"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(E,{className:U().quickAction})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notifications,ref:u,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:U().notificationButton,onClick:()=>d(!s),title:"Notifications",children:["\uD83D\uDD14",Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:U().notificationBadge,children:"3"})]}),s&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationDropdown,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Notifications"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:U().markAllRead,children:"Mark all read"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationList,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationIcon,children:"\uD83D\uDCC5"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationContent,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationTitle,children:"New booking request"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationTime,children:"5 minutes ago"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationIcon,children:"\uD83D\uDCB0"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationContent,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationTitle,children:"Payment received"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationTime,children:"1 hour ago"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationIcon,children:"⚠️"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationContent,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationTitle,children:"Low inventory alert"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationTime,children:"2 hours ago"})]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().notificationFooter,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/notifications",children:"View all notifications"})})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userMenu,ref:l,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:U().userButton,onClick:()=>{try{console.log("\uD83D\uDC64 User menu clicked!",{current:i,willBe:!i}),c(!i)}catch(e){console.error("❌ Error toggling user menu:",e)}},children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userAvatar,children:[r.firstName.charAt(0),r.lastName.charAt(0)]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userName,children:[r.firstName," ",r.lastName]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userRole,style:{color:m(r.role)},children:r.role})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().dropdownArrow,children:i?"▲":"▼"})]}),i&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userDropdown,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userDropdownHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userEmail,children:r.email}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userRoleBadge,style:{backgroundColor:m(r.role)},children:r.role})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().userDropdownMenu,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/profile",className:U().dropdownItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:U().dropdownIcon,children:"\uD83D\uDC64"}),"Profile Settings"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/security",className:U().dropdownItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:U().dropdownIcon,children:"\uD83D\uDD12"}),"Security & MFA"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/preferences",className:U().dropdownItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:U().dropdownIcon,children:"⚙️"}),"Preferences"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().dropdownDivider}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/help",className:U().dropdownItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:U().dropdownIcon,children:"❓"}),"Help & Support"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:U().dropdownDivider}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:"".concat(U().dropdownItem," ").concat(U().logoutItem),onClick:t,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:U().dropdownIcon,children:"\uD83D\uDEAA"}),"Sign Out"]})]})]})]})]})]})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var w=t(6904),j=t(7822),y=t.n(j);function L(e){let{userRole:r}=e,t=(0,o.useRouter)(),n=e=>{if(w.s.light(),t.pathname===e.href)return},i=e=>"/admin/dashboard"===e?"/admin/dashboard"===t.pathname||"/admin"===t.pathname:t.pathname.startsWith(e),c=[{id:"dashboard",label:"Dashboard",icon:"\uD83D\uDCCA",href:"/admin/dashboard",roles:["Admin","Manager","Staff"]},{id:"pos",label:"POS",icon:"\uD83D\uDCB3",href:"/admin/pos",roles:["Admin","Manager","Staff"]},{id:"bookings",label:"Bookings",icon:"\uD83D\uDCC5",href:"/admin/bookings",roles:["Admin","Manager","Staff"]},{id:"customers",label:"Customers",icon:"\uD83D\uDC65",href:"/admin/customers",roles:["Admin","Manager","Staff"]},{id:"more",label:"More",icon:"⋯",href:"/admin/menu",roles:["Admin","Manager","Staff"]}].filter(e=>e.roles.includes(r));return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("nav",{className:y().mobileBottomNav,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:y().navContainer,children:c.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:e.href,className:"".concat(y().navItem," ").concat(i(e.href)?y().active:""),onClick:()=>n(e),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:y().navIcon,children:e.icon}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:y().navLabel,children:e.label}),i(e.href)&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:y().activeIndicator})]},e.id))}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:y().safeAreaPadding})]})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();class T{async initialize(){return new Promise((e,r)=>{let t=indexedDB.open(this.config.dbName,this.config.version);t.onerror=()=>{console.error("Failed to open IndexedDB:",t.error),r(t.error)},t.onsuccess=()=>{this.db=t.result,console.log("PWA Cache Manager initialized"),e()},t.onupgradeneeded=e=>{let r=e.target.result;if(!r.objectStoreNames.contains(this.config.stores.transactions)){let e=r.createObjectStore(this.config.stores.transactions,{keyPath:"id"});e.createIndex("timestamp","timestamp",{unique:!1}),e.createIndex("type","type",{unique:!1}),e.createIndex("synced","synced",{unique:!1})}if(!r.objectStoreNames.contains(this.config.stores.bookings)){let e=r.createObjectStore(this.config.stores.bookings,{keyPath:"id"});e.createIndex("timestamp","timestamp",{unique:!1}),e.createIndex("synced","synced",{unique:!1})}r.objectStoreNames.contains(this.config.stores.customers)||r.createObjectStore(this.config.stores.customers,{keyPath:"id"}).createIndex("lastUpdated","lastUpdated",{unique:!1}),r.objectStoreNames.contains(this.config.stores.settings)||r.createObjectStore(this.config.stores.settings,{keyPath:"key"})}})}async storeOfflineTransaction(e,r){if(!this.db)throw Error("Cache manager not initialized");let t={id:"".concat(e,"_").concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),timestamp:Date.now(),type:e,data:r,synced:!1};return new Promise((e,r)=>{let o=this.db.transaction([this.config.stores.transactions],"readwrite").objectStore(this.config.stores.transactions).add(t);o.onsuccess=()=>{console.log("Offline transaction stored:",t.id),e(t.id)},o.onerror=()=>{console.error("Failed to store offline transaction:",o.error),r(o.error)}})}async getUnsyncedTransactions(){if(!this.db)throw Error("Cache manager not initialized");return new Promise((e,r)=>{let t=this.db.transaction([this.config.stores.transactions],"readonly").objectStore(this.config.stores.transactions).index("synced").getAll(IDBKeyRange.only(!1));t.onsuccess=()=>{e(t.result)},t.onerror=()=>{console.error("Failed to get unsynced transactions:",t.error),r(t.error)}})}async markTransactionSynced(e){if(!this.db)throw Error("Cache manager not initialized");return new Promise((r,t)=>{let o=this.db.transaction([this.config.stores.transactions],"readwrite").objectStore(this.config.stores.transactions),n=o.get(e);n.onsuccess=()=>{let a=n.result;if(a){a.synced=!0;let n=o.put(a);n.onsuccess=()=>{console.log("Transaction marked as synced:",e),r()},n.onerror=()=>{t(n.error)}}else t(Error("Transaction not found"))},n.onerror=()=>{t(n.error)}})}async cacheCustomerData(e){if(!this.db)throw Error("Cache manager not initialized");return new Promise((r,t)=>{let o=this.db.transaction([this.config.stores.customers],"readwrite").objectStore(this.config.stores.customers),n=0,a=e.length;if(0===a){r();return}e.forEach(e=>{let i={...e,lastUpdated:Date.now()},c=o.put(i);c.onsuccess=()=>{++n===a&&(console.log("Cached ".concat(a," customers for offline access")),r())},c.onerror=()=>{console.error("Failed to cache customer:",e.id,c.error),t(c.error)}})})}async getCachedCustomers(){if(!this.db)throw Error("Cache manager not initialized");return new Promise((e,r)=>{let t=this.db.transaction([this.config.stores.customers],"readonly").objectStore(this.config.stores.customers).getAll();t.onsuccess=()=>{e(t.result)},t.onerror=()=>{console.error("Failed to get cached customers:",t.error),r(t.error)}})}isOnline(){return navigator.onLine}async registerBackgroundSync(e){if("serviceWorker"in navigator&&"sync"in window.ServiceWorkerRegistration.prototype)try{let r=await navigator.serviceWorker.ready;await r.sync.register(e),console.log("Background sync registered:",e)}catch(e){console.error("Failed to register background sync:",e)}}async clearOldCache(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6048e5;if(!this.db)throw Error("Cache manager not initialized");let r=Date.now()-e;return new Promise((e,t)=>{let o=this.db.transaction([this.config.stores.transactions],"readwrite").objectStore(this.config.stores.transactions).index("timestamp"),n=IDBKeyRange.upperBound(r),a=o.openCursor(n),i=0;a.onsuccess=r=>{let t=r.target.result;t?(t.value.synced&&(t.delete(),i++),t.continue()):(console.log("Cleared ".concat(i," old cached transactions")),e())},a.onerror=()=>{console.error("Failed to clear old cache:",a.error),t(a.error)}})}async getCacheStats(){if(!this.db)throw Error("Cache manager not initialized");let[e,r,t]=await Promise.all([this.getTransactionCount(),this.getUnsyncedTransactionCount(),this.getCachedCustomerCount()]),o=this.formatBytes(1024*e+512*t);return{totalTransactions:e,unsyncedTransactions:r,cachedCustomers:t,cacheSize:o}}async getTransactionCount(){return new Promise((e,r)=>{let t=this.db.transaction([this.config.stores.transactions],"readonly").objectStore(this.config.stores.transactions).count();t.onsuccess=()=>e(t.result),t.onerror=()=>r(t.error)})}async getUnsyncedTransactionCount(){return new Promise((e,r)=>{let t=this.db.transaction([this.config.stores.transactions],"readonly").objectStore(this.config.stores.transactions).index("synced").count(IDBKeyRange.only(!1));t.onsuccess=()=>e(t.result),t.onerror=()=>r(t.error)})}async getCachedCustomerCount(){return new Promise((e,r)=>{let t=this.db.transaction([this.config.stores.customers],"readonly").objectStore(this.config.stores.customers).count();t.onsuccess=()=>e(t.result),t.onerror=()=>r(t.error)})}formatBytes(e){if(0===e)return"0 Bytes";let r=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,r)).toFixed(2))+" "+["Bytes","KB","MB","GB"][r]}constructor(){this.db=null,this.config={dbName:"OSSAdminCache",version:1,stores:{transactions:"offline_transactions",bookings:"offline_bookings",customers:"offline_customers",settings:"cache_settings"}}}}let F=new T;var x=t(4155);class M{async initialize(){if(!("serviceWorker"in navigator)||!("PushManager"in window))return console.warn("Push notifications not supported"),!1;try{let e=await this.requestPermission();if("granted"!==e)return console.warn("Notification permission not granted"),!1;let r=await navigator.serviceWorker.ready;return await this.subscribeToPush(r),console.log("Push notifications initialized successfully"),!0}catch(e){return console.error("Failed to initialize push notifications:",e),!1}}async requestPermission(){if(!("Notification"in window))throw Error("Notifications not supported");let e=Notification.permission;return"default"===e&&(e=await Notification.requestPermission()),e}async subscribeToPush(e){try{let r=await e.pushManager.getSubscription();return r||(r=await e.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:this.urlBase64ToUint8Array(this.vapidPublicKey)})),this.subscription=r,await this.sendSubscriptionToServer(r),r}catch(e){return console.error("Failed to subscribe to push notifications:",e),null}}async sendSubscriptionToServer(e){try{let r={endpoint:e.endpoint,keys:{p256dh:this.arrayBufferToBase64(e.getKey("p256dh")),auth:this.arrayBufferToBase64(e.getKey("auth"))}};if(!(await fetch("/api/admin/notifications/push/subscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})).ok)throw Error("Failed to send subscription to server");console.log("Push subscription sent to server")}catch(e){console.error("Failed to send subscription to server:",e)}}async showNotification(e){if(!("serviceWorker"in navigator))throw Error("Service worker not supported");try{let r=await navigator.serviceWorker.ready,t={body:e.body,icon:e.icon||"/icons/icon-192x192.png",badge:e.badge||"/icons/badge-72x72.png",data:e.data,tag:e.tag,requireInteraction:e.requireInteraction||!1,silent:e.silent||!1};e.image&&(t.image=e.image),e.vibrate?t.vibrate=e.vibrate:t.vibrate=[100,50,100],e.actions&&(t.actions=e.actions),await r.showNotification(e.title,t)}catch(e){console.error("Failed to show notification:",e)}}async sendPushNotification(e,r){try{return(await fetch("/api/admin/notifications/push/send",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e,payload:r})})).ok}catch(e){return console.error("Failed to send push notification:",e),!1}}async unsubscribe(){if(!this.subscription)return!0;try{let e=await this.subscription.unsubscribe();return e&&(await fetch("/api/admin/notifications/push/unsubscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({endpoint:this.subscription.endpoint})}),this.subscription=null,console.log("Unsubscribed from push notifications")),e}catch(e){return console.error("Failed to unsubscribe from push notifications:",e),!1}}async getSubscriptionStatus(){let e;let r="serviceWorker"in navigator&&"PushManager"in window,t=r?Notification.permission:"denied",o=!1;if(r&&"granted"===t)try{let r=await navigator.serviceWorker.ready;o=!!(e=await r.pushManager.getSubscription()||void 0)}catch(e){console.error("Failed to get subscription status:",e)}return{supported:r,permission:t,subscribed:o,subscription:e}}getNotificationTemplates(){return{newBooking:(e,r,t)=>({title:"New Booking Received",body:"".concat(e," booked ").concat(r," for ").concat(t),icon:"/icons/booking-notification.png",tag:"new-booking",data:{type:"booking",action:"view"},actions:[{action:"view",title:"View Booking",icon:"/icons/view.png"},{action:"dismiss",title:"Dismiss",icon:"/icons/dismiss.png"}],vibrate:[100,50,100,50,100]}),lowInventory:(e,r)=>({title:"Low Inventory Alert",body:"".concat(e," is running low (").concat(r," remaining)"),icon:"/icons/inventory-alert.png",tag:"low-inventory",requireInteraction:!0,data:{type:"inventory",product:e},actions:[{action:"reorder",title:"Reorder",icon:"/icons/reorder.png"},{action:"dismiss",title:"Dismiss",icon:"/icons/dismiss.png"}],vibrate:[200,100,200]}),staffSchedule:e=>({title:"Schedule Update",body:e,icon:"/icons/schedule-notification.png",tag:"staff-schedule",data:{type:"schedule"},actions:[{action:"view",title:"View Schedule",icon:"/icons/calendar.png"},{action:"dismiss",title:"Dismiss",icon:"/icons/dismiss.png"}]}),paymentReceived:(e,r)=>({title:"Payment Received",body:"$".concat(e," payment received from ").concat(r),icon:"/icons/payment-notification.png",tag:"payment-received",data:{type:"payment",amount:e,customer:r},actions:[{action:"view",title:"View Transaction",icon:"/icons/receipt.png"},{action:"dismiss",title:"Dismiss",icon:"/icons/dismiss.png"}],vibrate:[100,50,100]})}}urlBase64ToUint8Array(e){let r="=".repeat((4-e.length%4)%4),t=(e+r).replace(/-/g,"+").replace(/_/g,"/"),o=window.atob(t),n=new Uint8Array(o.length);for(let e=0;e<o.length;++e)n[e]=o.charCodeAt(e);return n}arrayBufferToBase64(e){let r=new Uint8Array(e),t="";for(let e=0;e<r.byteLength;e++)t+=String.fromCharCode(r[e]);return window.btoa(t)}constructor(){this.vapidPublicKey=x.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY||"",this.subscription=null}}let S=new M;function k(e){let{children:r}=e,[t,o]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({serviceWorkerRegistered:!1,cacheInitialized:!1,pushNotificationsEnabled:!1,isOnline:navigator.onLine,installPromptAvailable:!1}),[n,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[i,c]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>(s(),_(),()=>{f()}),[]);let s=async()=>{try{await d(),await l(),await u(),p(),console.log("PWA initialization complete")}catch(e){console.error("PWA initialization failed:",e)}},d=async()=>{if("serviceWorker"in navigator)try{let e=await navigator.serviceWorker.register("/sw.js",{scope:"/admin"});console.log("Service Worker registered:",e),o(e=>({...e,serviceWorkerRegistered:!0})),e.addEventListener("updatefound",()=>{let r=e.installing;r&&r.addEventListener("statechange",()=>{"installed"===r.state&&navigator.serviceWorker.controller&&v()})})}catch(e){console.error("Service Worker registration failed:",e)}},l=async()=>{try{await F.initialize(),o(e=>({...e,cacheInitialized:!0})),await m()}catch(e){console.error("Cache manager initialization failed:",e)}},u=async()=>{try{let e=await S.initialize();o(r=>({...r,pushNotificationsEnabled:e}))}catch(e){console.error("Push notifications initialization failed:",e)}},m=async()=>{try{let e=await fetch("/api/admin/customers");if(e.ok){let r=await e.json();await F.cacheCustomerData(r)}let r=await fetch("/api/admin/services");r.ok&&await r.json(),console.log("Critical data pre-cached for offline use")}catch(e){console.error("Failed to pre-cache critical data:",e)}},_=()=>{window.addEventListener("online",O),window.addEventListener("offline",O),window.addEventListener("beforeinstallprompt",h),window.addEventListener("appinstalled",D)},f=()=>{window.removeEventListener("online",O),window.removeEventListener("offline",O),window.removeEventListener("beforeinstallprompt",h),window.removeEventListener("appinstalled",D)},O=()=>{let e=navigator.onLine;o(r=>({...r,isOnline:e})),e?(N(),E()):C()},h=e=>{e.preventDefault(),a(e),o(e=>({...e,installPromptAvailable:!0})),setTimeout(()=>{c(!0)},5e3)},D=()=>{console.log("PWA was installed"),a(null),c(!1),o(e=>({...e,installPromptAvailable:!1}))},b=async()=>{if(n){n.prompt();let{outcome:e}=await n.userChoice;"accepted"===e?console.log("User accepted the install prompt"):console.log("User dismissed the install prompt"),a(null),c(!1)}},N=async()=>{try{await F.registerBackgroundSync("background-sync-pos"),await F.registerBackgroundSync("background-sync-bookings"),console.log("Background sync registered")}catch(e){console.error("Failed to sync offline data:",e)}},p=()=>{let e=window.matchMedia("(display-mode: standalone)").matches,r=!0===window.navigator.standalone;(e||r)&&console.log("App is running in installed mode")},v=()=>{t.pushNotificationsEnabled&&S.showNotification({title:"Update Available",body:"A new version of Ocean Soul Sparkles Admin is available. Refresh to update.",tag:"app-update",requireInteraction:!0,actions:[{action:"refresh",title:"Refresh Now"},{action:"dismiss",title:"Later"}]})},E=()=>{t.pushNotificationsEnabled&&S.showNotification({title:"Back Online",body:"Connection restored. Syncing offline data...",tag:"online-status",silent:!0})},C=()=>{t.pushNotificationsEnabled&&S.showNotification({title:"Offline Mode",body:"You are now offline. Some features may be limited.",tag:"offline-status",silent:!0})};return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r,i&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{style:{position:"fixed",bottom:"20px",left:"20px",right:"20px",background:"#16213e",color:"white",padding:"16px",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.3)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"space-between"},children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Install Ocean Soul Sparkles Admin"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{style:{margin:"4px 0 0 0",fontSize:"14px",opacity:.9},children:"Add to home screen for quick access"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{style:{display:"flex",gap:"8px"},children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:b,style:{background:"#4CAF50",color:"white",border:"none",padding:"8px 16px",borderRadius:"4px",cursor:"pointer"},children:"Install"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>c(!1),style:{background:"transparent",color:"white",border:"1px solid rgba(255,255,255,0.3)",padding:"8px 16px",borderRadius:"4px",cursor:"pointer"},children:"Later"})]})]}),!t.isOnline&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{style:{position:"fixed",top:"0",left:"0",right:"0",background:"#f44336",color:"white",padding:"8px",textAlign:"center",fontSize:"14px",zIndex:10001},children:"\uD83D\uDCF1 Offline Mode - Some features may be limited"})]})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var A=t(6026),B=t(5391),I=t.n(B);function P(e){let{children:r}=e,t=(0,o.useRouter)(),{user:n,loading:c}=(0,A.a)(),[s,d]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[u,m]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[_,f]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=()=>{m(window.innerWidth<=1024),window.innerWidth<=1024&&d(!0)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{c||n||t.push("/admin/login")},[n,c,t]);let O=async()=>{try{if((await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}})).ok)localStorage.removeItem("admin-token"),i.Am.success("Logged out successfully"),t.push("/admin/login");else throw Error("Logout failed")}catch(e){console.error("Logout error:",e),localStorage.removeItem("admin-token"),t.push("/admin/login")}},h=()=>{d(!s)};return c?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().loadingContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().loadingSpinner}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Loading admin portal..."})]}):n?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(k,{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().adminLayout,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(l,{user:n,collapsed:s,onToggle:h,isMobile:u}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"".concat(I().mainContent," ").concat(s?I().sidebarCollapsed:""),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(g,{user:n,onLogout:O,onToggleSidebar:u?()=>{console.log("\uD83D\uDCF1 Mobile menu toggle:",{current:_,willBe:!_,isMobile:u}),f(!_)}:h,sidebarCollapsed:s}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("main",{className:I().pageContent,children:r}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("footer",{className:I().adminFooter,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().footerContent,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().footerLeft,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:"\xa9 2024 Ocean Soul Sparkles Admin Portal"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:I().version,children:"v1.0.0"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().footerRight,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/help",className:I().footerLink,children:"Help"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/privacy",className:I().footerLink,children:"Privacy"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{href:"/admin/terms",className:I().footerLink,children:"Terms"})]})]})})]}),u&&_&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().mobileOverlay,onClick:()=>f(!1)}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().mobileBottomNav,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(L,{userRole:(null==n?void 0:n.role)||"Admin"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().debugMobile,children:["Mobile: ",u?"YES":"NO"," | Width: ",window.innerWidth]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().securityBanner,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:I().securityIcon,children:"\uD83D\uDD12"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:"Secure Admin Portal - All actions are logged and monitored"})]})]})}):null}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}()},8935:function(e,r,t){"use strict";t.d(r,{F:function(){return c}}),function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var o=t(744),n=t(1267),a=t.n(n);function i(e){let{data:r,type:n,className:i="",disabled:c=!1,options:s={},onExportStart:d,onExportComplete:l,onExportError:u}=e,[m,_]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[f,O]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),h=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=e=>{h.current&&!h.current.contains(e.target)&&O(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let D=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"csv";if(!r||0===r.length){null==u||u(Error("No data to export"));return}_(!0),O(!1),null==d||d();try{if("csv"===e)switch(n){case"bookings":(0,o.FC)(r,s);break;case"services":(0,o.r8)(r,s);break;case"customers":(0,o.Aq)(r,s);break;case"products":(0,o.yA)(r,s);break;case"inventory":(0,o.sg)(r,s);break;case"custom":let{exportToCSV:a}=await Promise.resolve().then(t.bind(t,744));a(r,s);break;default:throw Error("Unsupported export type: ".concat(n))}else throw Error("Export format ".concat(e," not yet supported"));null==l||l()}catch(e){console.error("Export error:",e),null==u||u(e instanceof Error?e:Error("Export failed"))}finally{_(!1)}};return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"".concat(a().exportButton," ").concat(i),ref:h,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>O(!f),disabled:c||m||0===r.length,className:"".concat(a().exportBtn," ").concat(m?a().exporting:""),title:c||0===r.length?"No data available to export":"Export ".concat(r.length," ").concat(n," records"),children:[m&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().spinner}),m?"Exporting...":0===r.length?"No Data":"\uD83D\uDCE5 Export (".concat(r.length,")"),!m&&r.length>0&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:a().dropdownArrow,children:"▼"})]}),f&&r.length>0&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().exportDropdown,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().dropdownHeader,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:"Export Format"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>D("csv"),className:a().dropdownItem,disabled:m,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:a().formatIcon,children:"\uD83D\uDCC4"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().formatInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().formatName,children:"CSV File"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().formatDesc,children:"Comma-separated values"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>D("excel"),className:"".concat(a().dropdownItem," ").concat(a().disabled),disabled:!0,title:"Excel export coming soon",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:a().formatIcon,children:"\uD83D\uDCCA"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().formatInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().formatName,children:"Excel File"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().formatDesc,children:"Coming soon"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>D("pdf"),className:"".concat(a().dropdownItem," ").concat(a().disabled),disabled:!0,title:"PDF export coming soon",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:a().formatIcon,children:"\uD83D\uDCCB"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().formatInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().formatName,children:"PDF Report"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:a().formatDesc,children:"Coming soon"})]})]})]})]})}let c=e=>{let{data:r,type:t,className:o="",...n}=e;return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(i,{data:r,type:t,className:o,onExportError:e=>{console.error("Export error:",e),alert("Export failed: ".concat(e.message))},...n})}},4632:function(e,r,t){"use strict";t.d(r,{Z:function(){return i}}),function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var o=t(2906),n=t.n(o);let a=[{value:"face_painting",label:"Face Painting"},{value:"hair_braiding",label:"Hair Braiding"},{value:"glitter_art",label:"Glitter Art"},{value:"body_art",label:"Body Art"},{value:"special_effects",label:"Special Effects"}];function i(e){let{artistId:r,onItemAdded:t,onItemUpdated:o,onItemDeleted:i}=e,[c,s]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[d,l]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[u,m]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[_,f]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[O,h]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[D,b]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[N,p]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("all"),[v,E]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(r||"all"),[C,U]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({artist_id:r||"",title:"",description:"",category:"face_painting",image_url:"",thumbnail_url:"",tags:"",is_featured:!1,is_public:!0,work_date:"",customer_consent:!1});Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{g(),r||w()},[r,N,v]);let g=async()=>{try{m(!0);let e=localStorage.getItem("adminToken"),t="/api/admin/artists/portfolio",o=new URLSearchParams;(r||"all"!==v)&&o.append("artist_id",r||v),"all"!==N&&o.append("category",N),o.toString()&&(t+="?".concat(o.toString()));let n=await fetch(t,{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!n.ok)throw Error("Failed to load portfolio items");let a=await n.json();s(a.portfolioItems||[])}catch(e){console.error("Error loading portfolio items:",e),f("Failed to load portfolio items")}finally{m(!1)}},w=async()=>{try{let e=localStorage.getItem("adminToken"),r=await fetch("/api/admin/artists",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!r.ok)throw Error("Failed to load artists");let t=await r.json();l(t.artists||[])}catch(e){console.error("Error loading artists:",e)}},j=async e=>{e.preventDefault();try{let e=localStorage.getItem("adminToken"),r=C.tags?C.tags.split(",").map(e=>e.trim()):[],n={...C,tags:r,work_date:C.work_date||null},a="/api/admin/artists/portfolio",i="POST";D&&(a="/api/admin/artists/".concat(D.artist_id,"/portfolio?item_id=").concat(D.id),i="PUT");let c=await fetch(a,{method:i,headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(n)});if(!c.ok)throw Error("Failed to save portfolio item");let d=await c.json();D?(s(e=>e.map(e=>e.id===D.id?d.portfolioItem:e)),null==o||o(d.portfolioItem),b(null)):(s(e=>[d.portfolioItem,...e]),null==t||t(d.portfolioItem)),T(),h(!1)}catch(e){console.error("Error saving portfolio item:",e),f("Failed to save portfolio item")}},y=e=>{var r;b(e),U({artist_id:e.artist_id,title:e.title,description:e.description||"",category:e.category,image_url:e.image_url,thumbnail_url:e.thumbnail_url||"",tags:(null===(r=e.tags)||void 0===r?void 0:r.join(", "))||"",is_featured:e.is_featured,is_public:e.is_public,work_date:e.work_date||"",customer_consent:e.customer_consent}),h(!0)},L=async e=>{if(confirm('Are you sure you want to delete "'.concat(e.title,'"?')))try{let r=localStorage.getItem("adminToken");if(!(await fetch("/api/admin/artists/".concat(e.artist_id,"/portfolio?item_id=").concat(e.id),{method:"DELETE",headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})).ok)throw Error("Failed to delete portfolio item");s(r=>r.filter(r=>r.id!==e.id)),null==i||i(e.id)}catch(e){console.error("Error deleting portfolio item:",e),f("Failed to delete portfolio item")}},T=()=>{U({artist_id:r||"",title:"",description:"",category:"face_painting",image_url:"",thumbnail_url:"",tags:"",is_featured:!1,is_public:!0,work_date:"",customer_consent:!1}),b(null)},F=()=>{T(),h(!1)};return u?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().loading,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().spinner}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Loading portfolio..."})]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().portfolioManager,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().header,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{children:"Portfolio Management"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:n().addButton,onClick:()=>h(!0),children:"+ Add Portfolio Item"})]}),_&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().error,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:_}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>f(null),children:"\xd7"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().filters,children:[!r&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().filterGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Artist:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:v,onChange:e=>E(e.target.value),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"all",children:"All Artists"}),d.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.id,children:e.name},e.id))]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().filterGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Category:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:N,onChange:e=>p(e.target.value),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"all",children:"All Categories"}),a.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.value,children:e.label},e.value))]})]})]}),O&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().modal,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().modalContent,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().modalHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:D?"Edit Portfolio Item":"Add Portfolio Item"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:n().closeButton,onClick:F,children:"\xd7"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("form",{onSubmit:j,className:n().form,children:[!r&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Artist *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:C.artist_id,onChange:e=>U(r=>({...r,artist_id:e.target.value})),required:!0,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"",children:"Select Artist"}),d.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.id,children:e.name},e.id))]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Title *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",value:C.title,onChange:e=>U(r=>({...r,title:e.target.value})),required:!0})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Description"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("textarea",{value:C.description,onChange:e=>U(r=>({...r,description:e.target.value})),rows:3})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formRow,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Category *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:C.category,onChange:e=>U(r=>({...r,category:e.target.value})),required:!0,children:a.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.value,children:e.label},e.value))})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Work Date"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"date",value:C.work_date,onChange:e=>U(r=>({...r,work_date:e.target.value}))})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Image URL *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"url",value:C.image_url,onChange:e=>U(r=>({...r,image_url:e.target.value})),required:!0})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Thumbnail URL"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"url",value:C.thumbnail_url,onChange:e=>U(r=>({...r,thumbnail_url:e.target.value}))})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:"Tags (comma-separated)"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",value:C.tags,onChange:e=>U(r=>({...r,tags:e.target.value})),placeholder:"e.g. butterfly, colorful, glitter"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().checkboxGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"checkbox",checked:C.is_featured,onChange:e=>U(r=>({...r,is_featured:e.target.checked}))}),"Featured Item"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"checkbox",checked:C.is_public,onChange:e=>U(r=>({...r,is_public:e.target.checked}))}),"Public (visible to customers)"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"checkbox",checked:C.customer_consent,onChange:e=>U(r=>({...r,customer_consent:e.target.checked}))}),"Customer Consent Obtained"]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().formActions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{type:"button",onClick:F,className:n().cancelButton,children:"Cancel"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{type:"submit",className:n().submitButton,children:[D?"Update":"Add"," Portfolio Item"]})]})]})]})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().portfolioGrid,children:0===c.length?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().emptyState,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"No portfolio items found."}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>h(!0),children:"Add your first portfolio item"})]}):c.map(e=>{var r;return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().portfolioCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().imageContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("img",{src:e.thumbnail_url||e.image_url,alt:e.title,className:n().portfolioImage}),e.is_featured&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().featuredBadge,children:"⭐ Featured"}),!e.is_public&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().privateBadge,children:"\uD83D\uDD12 Private"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().cardContent,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:e.title}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:n().category,children:null===(r=a.find(r=>r.value===e.category))||void 0===r?void 0:r.label}),e.description&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:n().description,children:e.description}),e.tags&&e.tags.length>0&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().tags,children:e.tags.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:n().tag,children:e},e))}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:n().cardActions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>y(e),className:n().editButton,children:"Edit"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>L(e),className:n().deleteButton,children:"Delete"})]})]})]},e.id)})})]})}},6026:function(e,r,t){"use strict";t.d(r,{a:function(){return n}}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var o=t(1163);function n(){let e=(0,o.useRouter)(),[r,t]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({user:null,loading:!0,error:null});Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{n()},[]);let n=async()=>{try{let e=localStorage.getItem("admin-token");if(!e){t({user:null,loading:!1,error:null});return}let r=await fetch("/api/auth/verify",{headers:{Authorization:"Bearer ".concat(e)}});if(!r.ok){localStorage.removeItem("admin-token"),t({user:null,loading:!1,error:"Session expired"});return}let o=await r.json();t({user:o.user,loading:!1,error:null})}catch(e){console.error("Auth check error:",e),localStorage.removeItem("admin-token"),t({user:null,loading:!1,error:"Authentication failed"})}},a=async(e,r)=>{try{t(e=>({...e,loading:!0,error:null}));let o=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})}),n=await o.json();if(!o.ok)throw Error(n.error||"Login failed");if(n.requiresMFA)return{requiresMFA:!0,user:n.user};return localStorage.setItem("admin-token",n.token),t({user:n.user,loading:!1,error:null}),{success:!0,user:n.user}}catch(r){let e=r instanceof Error?r.message:"Login failed";throw t(r=>({...r,loading:!1,error:e})),r}},i=async(e,r)=>{try{t(e=>({...e,loading:!0,error:null}));let o=await fetch("/api/auth/mfa-verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e,mfaCode:r})}),n=await o.json();if(!o.ok)throw Error(n.error||"MFA verification failed");return localStorage.setItem("admin-token",n.token),t({user:n.user,loading:!1,error:null}),{success:!0,user:n.user}}catch(r){let e=r instanceof Error?r.message:"MFA verification failed";throw t(r=>({...r,loading:!1,error:e})),r}},c=async()=>{try{let e=localStorage.getItem("admin-token");e&&await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:"Bearer ".concat(e)}})}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("admin-token"),t({user:null,loading:!1,error:null}),e.push("/admin/login")}},s=e=>!!r.user&&(Array.isArray(e)?e:[e]).includes(r.user.role);return{user:r.user,loading:r.loading,error:r.error,login:a,verifyMFA:i,logout:c,updateUser:e=>{t(r=>({...r,user:r.user?{...r.user,...e}:null}))},hasPermission:e=>!!r.user&&("DEV"===r.user.role||r.user.permissions.includes(e)),hasRole:s,isAdmin:()=>s(["DEV","Admin"]),isStaff:()=>s(["DEV","Admin","Artist","Braider"]),checkAuth:n}}},744:function(e,r,t){"use strict";function o(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{filename:t="export",columns:o,includeTimestamp:n=!0,dateFormat:a="short"}=r;if(!e||0===e.length)throw Error("No data to export");let i=o||Object.keys(e[0]).map(e=>({key:e,label:e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())})),c=[i.map(e=>e.label),...e.map(e=>i.map(r=>{let t=r.key.split(".").reduce((e,r)=>null==e?void 0:e[r],e);return r.formatter?r.formatter(t,e):null==t?"":"boolean"==typeof t?t?"Yes":"No":t instanceof Date||"string"==typeof t&&/^\d{4}-\d{2}-\d{2}/.test(t)&&!isNaN(Date.parse(t))?l(t):String(t)}))].map(e=>e.map(e=>'"'.concat(String(e||"").replace(/"/g,'""'),'"')).join(",")).join("\n"),s=n?"-".concat(new Date().toISOString().split("T")[0]):"";!function(e,r,t){let o=new Blob([e],{type:t}),n=window.URL.createObjectURL(o),a=document.createElement("a");a.href=n,a.download=r,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(n),document.body.removeChild(a)}(c,"".concat(t).concat(s,".csv"),"text/csv")}function n(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o(e,{filename:"bookings",columns:[{key:"customer_name",label:"Customer Name"},{key:"customer_email",label:"Customer Email"},{key:"customer_phone",label:"Customer Phone"},{key:"service_name",label:"Service"},{key:"artist_name",label:"Artist"},{key:"booking_date",label:"Date",formatter:e=>d(e)},{key:"booking_time",label:"Time"},{key:"status",label:"Status",formatter:e=>(null==e?void 0:e.toUpperCase())||"UNKNOWN"},{key:"total_amount",label:"Amount",formatter:e=>u(e)},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>l(e)}],...r})}function a(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o(e,{filename:"services",columns:[{key:"name",label:"Service Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"base_price",label:"Base Price",formatter:e=>u(e)},{key:"duration",label:"Duration (min)"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"total_bookings",label:"Total Bookings"},{key:"created_at",label:"Created",formatter:e=>l(e)}],...r})}function i(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o(e,{filename:"customers",columns:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"email",label:"Email"},{key:"phone",label:"Phone"},{key:"total_bookings",label:"Total Bookings"},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>l(e)}],...r})}function c(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o(e,{filename:"products",columns:[{key:"name",label:"Product Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"price",label:"Price",formatter:e=>u(e)},{key:"stock_quantity",label:"Stock"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"created_at",label:"Created",formatter:e=>l(e)}],...r})}function s(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o(e,{filename:"inventory",columns:[{key:"name",label:"Item Name"},{key:"category",label:"Category"},{key:"current_stock",label:"Current Stock"},{key:"minimum_stock",label:"Minimum Stock"},{key:"unit_cost",label:"Unit Cost",formatter:e=>u(e)},{key:"supplier_name",label:"Supplier"},{key:"last_restocked",label:"Last Restocked",formatter:e=>d(e)},{key:"created_at",label:"Created",formatter:e=>l(e)}],...r})}function d(e){return e?new Date(e).toLocaleDateString("en-AU"):""}function l(e){if(!e)return"";let r=new Date(e);return r.toLocaleDateString("en-AU")+" "+r.toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit"})}function u(e){return!e||isNaN(e)?"$0.00":new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(Number(e))}t.d(r,{Aq:function(){return i},FC:function(){return n},exportToCSV:function(){return o},r8:function(){return a},sg:function(){return s},yA:function(){return c}})},6904:function(e,r,t){"use strict";t.d(r,{s:function(){return o}}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();class o{static vibrate(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;"vibrate"in navigator&&navigator.vibrate(e)}static light(){this.vibrate(50)}static medium(){this.vibrate(100)}static heavy(){this.vibrate([100,50,100])}static error(){this.vibrate([100,50,100,50,100])}static success(){this.vibrate([50,25,50])}}},3790:function(e){e.exports={adminHeader:"AdminHeader_adminHeader__tAy8N",headerLeft:"AdminHeader_headerLeft__FXjXr",sidebarToggle:"AdminHeader_sidebarToggle__Vlukg",hamburger:"AdminHeader_hamburger__3oPy_",breadcrumb:"AdminHeader_breadcrumb__z_2w7",headerCenter:"AdminHeader_headerCenter__RwMCL",headerRight:"AdminHeader_headerRight__jgrCt",quickActions:"AdminHeader_quickActions___NuOX",quickAction:"AdminHeader_quickAction__XqmCI",notifications:"AdminHeader_notifications__DWNcH",notificationButton:"AdminHeader_notificationButton__hubpu",notificationBadge:"AdminHeader_notificationBadge__spKqR",notificationDropdown:"AdminHeader_notificationDropdown__mA8dq",notificationHeader:"AdminHeader_notificationHeader__Ue15C",markAllRead:"AdminHeader_markAllRead__UP_0Q",notificationList:"AdminHeader_notificationList__JuL31",notificationItem:"AdminHeader_notificationItem__ABEAH",notificationIcon:"AdminHeader_notificationIcon__BSCLh",notificationContent:"AdminHeader_notificationContent__tFkeh",notificationTitle:"AdminHeader_notificationTitle__C5Il3",notificationTime:"AdminHeader_notificationTime__DWutx",notificationFooter:"AdminHeader_notificationFooter__T4khp",userMenu:"AdminHeader_userMenu__YbO0w",userButton:"AdminHeader_userButton__uP4qu",userAvatar:"AdminHeader_userAvatar__QJdnj",userInfo:"AdminHeader_userInfo__t2PHi",userName:"AdminHeader_userName__4_RNy",userRole:"AdminHeader_userRole__fQkGv",dropdownArrow:"AdminHeader_dropdownArrow___vHwu",userDropdown:"AdminHeader_userDropdown__NFy7A",userDropdownHeader:"AdminHeader_userDropdownHeader__CYxvo",userEmail:"AdminHeader_userEmail__nZCju",userRoleBadge:"AdminHeader_userRoleBadge__W3Lbx",userDropdownMenu:"AdminHeader_userDropdownMenu__7PJEX",dropdownItem:"AdminHeader_dropdownItem__7zn2N",dropdownIcon:"AdminHeader_dropdownIcon__ZZ3_U",dropdownDivider:"AdminHeader_dropdownDivider__6AaxM",logoutItem:"AdminHeader_logoutItem__R0CHw"}},5391:function(e){e.exports={adminLayout:"AdminLayout_adminLayout__5Oi4c",mainContent:"AdminLayout_mainContent__INtLu",sidebarCollapsed:"AdminLayout_sidebarCollapsed__oAEhD",pageContent:"AdminLayout_pageContent__aWMEk",adminFooter:"AdminLayout_adminFooter__mTvA1",footerContent:"AdminLayout_footerContent__z6du0",footerLeft:"AdminLayout_footerLeft__gGY8P",version:"AdminLayout_version__vpU9q",footerRight:"AdminLayout_footerRight__kyodA",footerLink:"AdminLayout_footerLink__jvWuv",securityBanner:"AdminLayout_securityBanner__KTGT5",securityIcon:"AdminLayout_securityIcon__eZwIM",loadingContainer:"AdminLayout_loadingContainer__Wbedv",loadingSpinner:"AdminLayout_loadingSpinner__C8mvO",spin:"AdminLayout_spin__DZv4U",sidebar:"AdminLayout_sidebar__1zyFe",mobileBottomNav:"AdminLayout_mobileBottomNav__2kopO",mobileOverlay:"AdminLayout_mobileOverlay__BNO2v",debugMobile:"AdminLayout_debugMobile__hcFrB"}},1976:function(e){e.exports={sidebar:"AdminSidebar_sidebar__qOEP2",collapsed:"AdminSidebar_collapsed__mPopM",mobile:"AdminSidebar_mobile__sXELg",sidebarHeader:"AdminSidebar_sidebarHeader__h8NsD",logo:"AdminSidebar_logo__MfKT2",logoIcon:"AdminSidebar_logoIcon__ObH7O",logoIconOnly:"AdminSidebar_logoIconOnly__AoqbB",logoText:"AdminSidebar_logoText__6AwFU",logoTitle:"AdminSidebar_logoTitle__rj3SO",logoSubtitle:"AdminSidebar_logoSubtitle__ZlArc",toggleButton:"AdminSidebar_toggleButton__93srV",userInfo:"AdminSidebar_userInfo__0v9i_",userAvatar:"AdminSidebar_userAvatar__Rg3G_",userDetails:"AdminSidebar_userDetails__kA16n",userName:"AdminSidebar_userName__2reke",userRole:"AdminSidebar_userRole__Bo1eM",navigation:"AdminSidebar_navigation__LpNEH",menuList:"AdminSidebar_menuList__krOTx",menuItem:"AdminSidebar_menuItem__A5Arm",menuLink:"AdminSidebar_menuLink__ZSnZI",active:"AdminSidebar_active__4G9nw",menuIcon:"AdminSidebar_menuIcon__yJF_1",menuLabel:"AdminSidebar_menuLabel__WEpLi",expandButton:"AdminSidebar_expandButton__qS2q4",submenu:"AdminSidebar_submenu__4dAAZ",submenuItem:"AdminSidebar_submenuItem__WiecI",submenuLink:"AdminSidebar_submenuLink__ZYwCJ",submenuIcon:"AdminSidebar_submenuIcon__ThbKs",submenuLabel:"AdminSidebar_submenuLabel__ocpuH",sidebarFooter:"AdminSidebar_sidebarFooter__NML_U",footerContent:"AdminSidebar_footerContent__qOiZI",versionInfo:"AdminSidebar_versionInfo__bpisr",version:"AdminSidebar_version__EyLxD",environment:"AdminSidebar_environment__teF9S",securityIndicator:"AdminSidebar_securityIndicator__S_6EA",securityIcon:"AdminSidebar_securityIcon__GdGG2",securityText:"AdminSidebar_securityText___evKe"}},73:function(e){e.exports={breadcrumbNavigation:"BreadcrumbNavigation_breadcrumbNavigation__qrpk6",breadcrumbList:"BreadcrumbNavigation_breadcrumbList__J3l8C",breadcrumbItem:"BreadcrumbNavigation_breadcrumbItem__ntSbT",breadcrumbLink:"BreadcrumbNavigation_breadcrumbLink__zUz4h",breadcrumbCurrent:"BreadcrumbNavigation_breadcrumbCurrent__piKeq",active:"BreadcrumbNavigation_active__nMBn7",breadcrumbIcon:"BreadcrumbNavigation_breadcrumbIcon__NwAjD",breadcrumbLabel:"BreadcrumbNavigation_breadcrumbLabel__RfAkY",breadcrumbSeparator:"BreadcrumbNavigation_breadcrumbSeparator__s8MDJ",breadcrumbFadeIn:"BreadcrumbNavigation_breadcrumbFadeIn__8jkp9",truncated:"BreadcrumbNavigation_truncated__ZieS_",srOnly:"BreadcrumbNavigation_srOnly__BJXug",loading:"BreadcrumbNavigation_loading__POMFv",error:"BreadcrumbNavigation_error__3_Q_W"}},5165:function(e){e.exports={customersContainer:"Customers_customersContainer___hRzb",header:"Customers_header__Xsb8L",title:"Customers_title__kuVdg",headerContent:"Customers_headerContent__1x2Ki",headerActions:"Customers_headerActions__wW_iz",analyticsBtn:"Customers_analyticsBtn__Y6xGh",exportBtn:"Customers_exportBtn__6D97q",newCustomerBtn:"Customers_newCustomerBtn__ACkta",backButton:"Customers_backButton__mwCeO",controlsPanel:"Customers_controlsPanel__QNdpL",searchContainer:"Customers_searchContainer__uP_D_",filtersContainer:"Customers_filtersContainer__B81bf",filterSelect:"Customers_filterSelect__ANSKc",sortSelect:"Customers_sortSelect__oahlL",viewControls:"Customers_viewControls__VdvPJ",viewBtn:"Customers_viewBtn__TNPmH",active:"Customers_active__rk6Up",resultsInfo:"Customers_resultsInfo__cpO5L",searchSection:"Customers_searchSection__sgj9I",searchInput:"Customers_searchInput__BB_0d",sortSection:"Customers_sortSection__qb0FO",sortOrderBtn:"Customers_sortOrderBtn__t2r88",customersContent:"Customers_customersContent__z_zag",customersHeader:"Customers_customersHeader__einsi",statsCards:"Customers_statsCards__ph2BM",statCard:"Customers_statCard__Pua_g",statValue:"Customers_statValue__357cS",emptyState:"Customers_emptyState__umGfV",customersGrid:"Customers_customersGrid__nkG51",customerCard:"Customers_customerCard__oECGs",customerMeta:"Customers_customerMeta__JcUjA",customerDate:"Customers_customerDate__dVu6L",emptyIcon:"Customers_emptyIcon__xGfzC",emptyActionBtn:"Customers_emptyActionBtn__1HOvJ",customerHeader:"Customers_customerHeader__QfMYw",customerInfo:"Customers_customerInfo__sYPpi",statusBadge:"Customers_statusBadge__nVYbG",statusVip:"Customers_statusVip__GAam3",statusActive:"Customers_statusActive__N_jFf",statusNew:"Customers_statusNew__8r02c",statusInactive:"Customers_statusInactive__inD5l",statusDefault:"Customers_statusDefault__R5R8L",customerStats:"Customers_customerStats__OLqld",statItem:"Customers_statItem__YazVS",statLabel:"Customers_statLabel__pm6R5",customerNotes:"Customers_customerNotes__KM6Mg",customerActions:"Customers_customerActions__TS_QE",bookBtn:"Customers_bookBtn__333XR",customersTable:"Customers_customersTable__c6X31",customerName:"Customers_customerName__SARmR",bookingBadge:"Customers_bookingBadge__0JdCy",tableActions:"Customers_tableActions__fP8SO",editBtn:"Customers_editBtn__3Orgk",pagination:"Customers_pagination__QsqfM",paginationBtn:"Customers_paginationBtn__RUfWW",paginationInfo:"Customers_paginationInfo__7qsKa",loadingContainer:"Customers_loadingContainer__eyNOh",loadingSpinner:"Customers_loadingSpinner__2d9G_",spin:"Customers_spin__xe_Ia"}},1267:function(e){e.exports={exportButton:"ExportButton_exportButton__utvly",exportBtn:"ExportButton_exportBtn__tIopM",exporting:"ExportButton_exporting__sXhBs",spinner:"ExportButton_spinner__9VMfk",spin:"ExportButton_spin__rvyvr",dropdownArrow:"ExportButton_dropdownArrow__ukQOV",exportDropdown:"ExportButton_exportDropdown__Rv4p1",slideDown:"ExportButton_slideDown__Cf7OP",dropdownHeader:"ExportButton_dropdownHeader__ZrlI_",dropdownItem:"ExportButton_dropdownItem__fkbTm",disabled:"ExportButton_disabled__D8pyR",formatIcon:"ExportButton_formatIcon__jrB0p",formatInfo:"ExportButton_formatInfo__CpXUd",formatName:"ExportButton_formatName__yKvvD",formatDesc:"ExportButton_formatDesc__vDwii",success:"ExportButton_success__suWHu",successPulse:"ExportButton_successPulse__zvjZh"}},9675:function(e){e.exports={globalSearch:"GlobalSearch_globalSearch__x64r6",searchInput:"GlobalSearch_searchInput__NDaxq",searchIcon:"GlobalSearch_searchIcon__COB30",input:"GlobalSearch_input__Bshcp",loadingSpinner:"GlobalSearch_loadingSpinner__Pu_Vy",spinner:"GlobalSearch_spinner__Xfout",spin:"GlobalSearch_spin__dL1SW",clearButton:"GlobalSearch_clearButton__FHcBJ",searchResults:"GlobalSearch_searchResults__pRA9E",slideDown:"GlobalSearch_slideDown__pmrfB",resultsHeader:"GlobalSearch_resultsHeader__vSZYk",resultsCount:"GlobalSearch_resultsCount__JZUY_",resultsList:"GlobalSearch_resultsList__regZQ",resultItem:"GlobalSearch_resultItem__zKY59",selected:"GlobalSearch_selected__9T29Y",resultIcon:"GlobalSearch_resultIcon__rrjs_",resultContent:"GlobalSearch_resultContent__ayKhj",resultTitle:"GlobalSearch_resultTitle__5oylH",resultSubtitle:"GlobalSearch_resultSubtitle___rge4",resultDescription:"GlobalSearch_resultDescription__mUJl9",resultType:"GlobalSearch_resultType__va8Tu",resultsFooter:"GlobalSearch_resultsFooter__l4OQy",moreResults:"GlobalSearch_moreResults__pHkvq",noResults:"GlobalSearch_noResults__3CVFL",noResultsIcon:"GlobalSearch_noResultsIcon__sRUUy",noResultsText:"GlobalSearch_noResultsText__kE3v7",noResultsHint:"GlobalSearch_noResultsHint__U_W50",errorMessage:"GlobalSearch_errorMessage__zGgnP",errorIcon:"GlobalSearch_errorIcon___QqjF"}},5593:function(e){e.exports={overlay:"KeyboardShortcuts_overlay__50H6k",fadeIn:"KeyboardShortcuts_fadeIn__3V9UQ",modal:"KeyboardShortcuts_modal___mIHb",slideUp:"KeyboardShortcuts_slideUp__v_6v_",header:"KeyboardShortcuts_header__qglVm",title:"KeyboardShortcuts_title__I96H_",closeBtn:"KeyboardShortcuts_closeBtn__uBydE",content:"KeyboardShortcuts_content__JMnWB",intro:"KeyboardShortcuts_intro__4pzvg",shortcutsGrid:"KeyboardShortcuts_shortcutsGrid__lq1C_",category:"KeyboardShortcuts_category__atJiS",categoryTitle:"KeyboardShortcuts_categoryTitle__DcncJ",shortcutsList:"KeyboardShortcuts_shortcutsList__9cq54",shortcutItem:"KeyboardShortcuts_shortcutItem__vBzTj",keys:"KeyboardShortcuts_keys__5JoYl",key:"KeyboardShortcuts_key__oyNAe",keySeparator:"KeyboardShortcuts_keySeparator__2xiWn",description:"KeyboardShortcuts_description__e_Npl",footer:"KeyboardShortcuts_footer__hvpay",tip:"KeyboardShortcuts_tip__PMQsZ",helpButton:"KeyboardShortcuts_helpButton__2NzAk"}},2906:function(e){e.exports={portfolioManager:"Portfolio_portfolioManager__tKA9p",header:"Portfolio_header__22y_0",addButton:"Portfolio_addButton__9O8RI",loading:"Portfolio_loading__67gkW",spinner:"Portfolio_spinner__tEWyh",spin:"Portfolio_spin__6XFRQ",error:"Portfolio_error__Z8mwc",filters:"Portfolio_filters__TWqZS",filterGroup:"Portfolio_filterGroup__QARVH",modal:"Portfolio_modal__WaXc4",modalContent:"Portfolio_modalContent__3Aeg7",modalHeader:"Portfolio_modalHeader__25LHq",closeButton:"Portfolio_closeButton__9j2fq",form:"Portfolio_form__0sacd",formGroup:"Portfolio_formGroup__IDrc_",formRow:"Portfolio_formRow__flUx9",checkboxGroup:"Portfolio_checkboxGroup__hP2Kz",formActions:"Portfolio_formActions__E1NbZ",cancelButton:"Portfolio_cancelButton__MLCU9",submitButton:"Portfolio_submitButton__U8V4E",portfolioGrid:"Portfolio_portfolioGrid__NDva5",portfolioCard:"Portfolio_portfolioCard__IDcID",imageContainer:"Portfolio_imageContainer__6ED4v",portfolioImage:"Portfolio_portfolioImage__e9EB1",featuredBadge:"Portfolio_featuredBadge__n_wmK",privateBadge:"Portfolio_privateBadge__0UjxZ",cardContent:"Portfolio_cardContent__t3nl6",category:"Portfolio_category__hRzSs",description:"Portfolio_description__b5Ex_",tags:"Portfolio_tags__FqD3L",tag:"Portfolio_tag__NbHG0",cardActions:"Portfolio_cardActions__Xp4j7",editButton:"Portfolio_editButton__0OAYF",deleteButton:"Portfolio_deleteButton__uCFCn",emptyState:"Portfolio_emptyState__dtRES",portfolioPage:"Portfolio_portfolioPage___zQQJ",pageHeader:"Portfolio_pageHeader__uAgm3",headerContent:"Portfolio_headerContent__70X88",accessDenied:"Portfolio_accessDenied__XmZqh",statsGrid:"Portfolio_statsGrid__Fuk9Z",statCard:"Portfolio_statCard__q53G0",statIcon:"Portfolio_statIcon__IgjWN",statContent:"Portfolio_statContent__WXBxy",categoriesOverview:"Portfolio_categoriesOverview__Z8gbr",categoryTags:"Portfolio_categoryTags__Xt4Ys",categoryTag:"Portfolio_categoryTag__xCyC6",breadcrumb:"Portfolio_breadcrumb__lW_HI",artistHeader:"Portfolio_artistHeader__KfMO8",artistInfo:"Portfolio_artistInfo__tIz0n",artistDetails:"Portfolio_artistDetails__LqtLb",email:"Portfolio_email__Ue4Be",specializations:"Portfolio_specializations__Uvl_x",specializationTags:"Portfolio_specializationTags__VGsyr",specializationTag:"Portfolio_specializationTag__yFOTM",bio:"Portfolio_bio__u4Z0u",artistStats:"Portfolio_artistStats__gOpRj",statItem:"Portfolio_statItem___qMx2",statValue:"Portfolio_statValue__pbewa",statLabel:"Portfolio_statLabel__W70NS",backButton:"Portfolio_backButton__sCJPK"}},560:function(e){e.exports={purchaseOrders:"PurchaseOrders_purchaseOrders__nd7hq",header:"PurchaseOrders_header__w_l_v",headerContent:"PurchaseOrders_headerContent__PVf2q",title:"PurchaseOrders_title__utnll",subtitle:"PurchaseOrders_subtitle__1wcrJ",headerActions:"PurchaseOrders_headerActions__SQeEm",addBtn:"PurchaseOrders_addBtn__KPfpC",controls:"PurchaseOrders_controls__9p8X2",searchSection:"PurchaseOrders_searchSection__I6PTA",searchInput:"PurchaseOrders_searchInput__R_JTF",filters:"PurchaseOrders_filters__b5Bor",filterSelect:"PurchaseOrders_filterSelect__R7jx2",sortSelect:"PurchaseOrders_sortSelect__L3GvC",ordersContainer:"PurchaseOrders_ordersContainer__5dBmg",loading:"PurchaseOrders_loading__Fmcxh",loadingSpinner:"PurchaseOrders_loadingSpinner__KO_Gz",spin:"PurchaseOrders_spin__HjZwU",emptyState:"PurchaseOrders_emptyState__O5SNT",emptyIcon:"PurchaseOrders_emptyIcon__JM1LS",addFirstBtn:"PurchaseOrders_addFirstBtn__vMTvw",ordersGrid:"PurchaseOrders_ordersGrid__RZktU",orderCard:"PurchaseOrders_orderCard__eJr_j",cardHeader:"PurchaseOrders_cardHeader__kKvK9",orderInfo:"PurchaseOrders_orderInfo__7lOLc",poNumber:"PurchaseOrders_poNumber___XsZw",statusBadge:"PurchaseOrders_statusBadge__40_vf",statusDraft:"PurchaseOrders_statusDraft__nhguh",statusSent:"PurchaseOrders_statusSent__Y5Ryb",statusConfirmed:"PurchaseOrders_statusConfirmed__wp_Rp",statusReceived:"PurchaseOrders_statusReceived__Z_SG3",statusCancelled:"PurchaseOrders_statusCancelled__BYw7u",cardBody:"PurchaseOrders_cardBody__JNNls",supplierInfo:"PurchaseOrders_supplierInfo__GTMI5",orderDetails:"PurchaseOrders_orderDetails__jguk6",detailItem:"PurchaseOrders_detailItem__emrhK",label:"PurchaseOrders_label__acljc",value:"PurchaseOrders_value__Trteb",notes:"PurchaseOrders_notes__klfoF",notesText:"PurchaseOrders_notesText__xxhwJ",cardFooter:"PurchaseOrders_cardFooter__iRQ7M",cardActions:"PurchaseOrders_cardActions__VMaHv",viewBtn:"PurchaseOrders_viewBtn__QBUc6",editBtn:"PurchaseOrders_editBtn__l0Pkx",receiveBtn:"PurchaseOrders_receiveBtn__qOMk6",deleteBtn:"PurchaseOrders_deleteBtn__5Sgw6",cardMeta:"PurchaseOrders_cardMeta__G_4c6",createdBy:"PurchaseOrders_createdBy__EuC7t",createdDate:"PurchaseOrders_createdDate__W_8eI",purchaseOrderForm:"PurchaseOrders_purchaseOrderForm__Jo6eJ",formSection:"PurchaseOrders_formSection__PPc2x",sectionHeader:"PurchaseOrders_sectionHeader__GOGWN",addItemBtn:"PurchaseOrders_addItemBtn__Xw7Of",formGrid:"PurchaseOrders_formGrid__6K2p_",formGroup:"PurchaseOrders_formGroup__LyNDG",fullWidth:"PurchaseOrders_fullWidth__3t1vV",formLabel:"PurchaseOrders_formLabel__k12f1",formInput:"PurchaseOrders_formInput__fCTM_",formTextarea:"PurchaseOrders_formTextarea__pvtWy",formSelect:"PurchaseOrders_formSelect__j_xYw",emptyItems:"PurchaseOrders_emptyItems__kHx1F",itemsList:"PurchaseOrders_itemsList__7hKjk",itemRow:"PurchaseOrders_itemRow__qpnoC",itemFields:"PurchaseOrders_itemFields__5hnFe",totalCost:"PurchaseOrders_totalCost__5x9Ve",removeItemBtn:"PurchaseOrders_removeItemBtn__I3BZk",orderSummary:"PurchaseOrders_orderSummary__ZpQ7a",summaryRow:"PurchaseOrders_summaryRow__bgIZv",totalRow:"PurchaseOrders_totalRow__tlYfR",formActions:"PurchaseOrders_formActions__BL5KV",saveBtn:"PurchaseOrders_saveBtn__mjzET",cancelBtn:"PurchaseOrders_cancelBtn__J1l9Y"}},5194:function(e){e.exports={serviceFormContainer:"ServiceForm_serviceFormContainer__x_rm_",header:"ServiceForm_header__MJoC5",breadcrumb:"ServiceForm_breadcrumb__Sujex",backButton:"ServiceForm_backButton__qvGeh",formContent:"ServiceForm_formContent__7hqCc",form:"ServiceForm_form__y0s41",errorAlert:"ServiceForm_errorAlert__zu0M_",formGrid:"ServiceForm_formGrid__z4a_v",formGroup:"ServiceForm_formGroup__bBtrC",inputError:"ServiceForm_inputError__4HD91",errorText:"ServiceForm_errorText__3nMcX",visibilitySection:"ServiceForm_visibilitySection__L70gF",checkboxGroup:"ServiceForm_checkboxGroup__HHCB0",checkboxLabel:"ServiceForm_checkboxLabel__dHq0_",formActions:"ServiceForm_formActions__RKZe1",submitButton:"ServiceForm_submitButton__kH7U0",cancelButton:"ServiceForm_cancelButton__eloNY",loadingContainer:"ServiceForm_loadingContainer__OdLd7",errorContainer:"ServiceForm_errorContainer__Z68Z1",notFoundContainer:"ServiceForm_notFoundContainer__bRMUf",loadingSpinner:"ServiceForm_loadingSpinner__wb_pk",spin:"ServiceForm_spin__VG_ya"}},3457:function(e){e.exports={supplierManagement:"Suppliers_supplierManagement__moQ9K",header:"Suppliers_header__e_GFD",headerContent:"Suppliers_headerContent__RlAv_",title:"Suppliers_title__mJ53N",subtitle:"Suppliers_subtitle__jKx8q",headerActions:"Suppliers_headerActions__mhBy0",addBtn:"Suppliers_addBtn__m7uLG",controls:"Suppliers_controls__u6XUL",searchSection:"Suppliers_searchSection___Yh_u",searchInput:"Suppliers_searchInput__ztdWK",filters:"Suppliers_filters__m2SbG",filterSelect:"Suppliers_filterSelect__NsTpf",sortSelect:"Suppliers_sortSelect__xah3_",suppliersContainer:"Suppliers_suppliersContainer__lLuBC",loading:"Suppliers_loading__SPL55",loadingSpinner:"Suppliers_loadingSpinner__8C00Y",spin:"Suppliers_spin__m4hm1",emptyState:"Suppliers_emptyState__2EIlp",emptyIcon:"Suppliers_emptyIcon__mVvSl",addFirstBtn:"Suppliers_addFirstBtn__5YL2d",suppliersGrid:"Suppliers_suppliersGrid__8K7Uc",supplierCard:"Suppliers_supplierCard__FpznX",cardHeader:"Suppliers_cardHeader__vUyV_",supplierInfo:"Suppliers_supplierInfo__LLH3s",supplierName:"Suppliers_supplierName__P3SYy",statusBadge:"Suppliers_statusBadge__q__Jt",active:"Suppliers_active__0rFtH",inactive:"Suppliers_inactive__VhmvH",cardBody:"Suppliers_cardBody__wjlXW",contactInfo:"Suppliers_contactInfo__r93AU",businessInfo:"Suppliers_businessInfo__gDnna",infoItem:"Suppliers_infoItem__NkSUU",label:"Suppliers_label__x1sKg",value:"Suppliers_value__Rvb2Z",notes:"Suppliers_notes__aLiX9",notesText:"Suppliers_notesText__Nlgcw",cardFooter:"Suppliers_cardFooter__Kh64U",cardActions:"Suppliers_cardActions__xZeWq",viewBtn:"Suppliers_viewBtn__W23ER",editBtn:"Suppliers_editBtn__VGxzN",deleteBtn:"Suppliers_deleteBtn__K6UPw",cardMeta:"Suppliers_cardMeta__30iaq",createdDate:"Suppliers_createdDate__Et97b",supplierForm:"Suppliers_supplierForm__Ile4t",formGrid:"Suppliers_formGrid__RoRp_",formGroup:"Suppliers_formGroup__exSuf",fullWidth:"Suppliers_fullWidth__DS0su",formLabel:"Suppliers_formLabel__zmgic",formInput:"Suppliers_formInput__GWKlp",formTextarea:"Suppliers_formTextarea__oPk6B",formSelect:"Suppliers_formSelect__nAwes",formActions:"Suppliers_formActions__buaHF",saveBtn:"Suppliers_saveBtn__CGIMf",cancelBtn:"Suppliers_cancelBtn__APjcB"}},7822:function(e){e.exports={mobileBottomNav:"MobileBottomNav_mobileBottomNav__AlYiY",navContainer:"MobileBottomNav_navContainer__0GjFb",navItem:"MobileBottomNav_navItem__y5tGx",active:"MobileBottomNav_active__gBrP7",navIcon:"MobileBottomNav_navIcon__a9GVa",navLabel:"MobileBottomNav_navLabel__okVa_",activeIndicator:"MobileBottomNav_activeIndicator__QbcyY",pulse:"MobileBottomNav_pulse__jftGw",safeAreaPadding:"MobileBottomNav_safeAreaPadding__GKpOi"}}}]);