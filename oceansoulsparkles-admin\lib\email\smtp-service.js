/**
 * SMTP Email Service for Ocean Soul Sparkles Admin
 * Handles email sending functionality using Nodemailer
 */

const nodemailer = require('nodemailer');

/**
 * Create SMTP transporter based on environment configuration
 */
function createTransporter() {
  const config = {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  };

  // Fallback to console logging if SMTP not configured
  if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
    console.warn('⚠️  SMTP credentials not configured. Emails will be logged to console.');
    return null;
  }

  try {
    return nodemailer.createTransporter(config);
  } catch (error) {
    console.error('Failed to create SMTP transporter:', error);
    return null;
  }
}

/**
 * Send email using SMTP or fallback to console logging
 */
async function sendEmail({ to, subject, html, text, from }) {
  const transporter = createTransporter();
  
  const emailData = {
    from: from || process.env.SMTP_FROM || '<EMAIL>',
    to,
    subject,
    html,
    text: text || html?.replace(/<[^>]*>/g, '') // Strip HTML for text version
  };

  // If no transporter (SMTP not configured), log to console
  if (!transporter) {
    console.log('\n📧 EMAIL WOULD BE SENT:');
    console.log('To:', to);
    console.log('Subject:', subject);
    console.log('Content:', text || html);
    console.log('---\n');
    return { success: true, messageId: 'console-log-' + Date.now() };
  }

  try {
    const info = await transporter.sendMail(emailData);
    console.log('✅ Email sent successfully:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('❌ Failed to send email:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Verify SMTP connection
 */
async function verifyConnection() {
  const transporter = createTransporter();
  
  if (!transporter) {
    return { success: false, error: 'SMTP not configured' };
  }

  try {
    await transporter.verify();
    return { success: true, message: 'SMTP connection verified' };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Send test email
 */
async function sendTestEmail(to) {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e40af;">Ocean Soul Sparkles Admin</h2>
      <p>This is a test email to verify your email configuration is working correctly.</p>
      <p>If you received this email, your SMTP settings are properly configured!</p>
      <hr style="margin: 20px 0; border: none; border-top: 1px solid #e5e7eb;">
      <p style="color: #6b7280; font-size: 14px;">
        Sent from Ocean Soul Sparkles Admin Dashboard<br>
        ${new Date().toLocaleString()}
      </p>
    </div>
  `;

  return await sendEmail({
    to,
    subject: 'Ocean Soul Sparkles - Email Test',
    html
  });
}

module.exports = {
  sendEmail,
  verifyConnection,
  sendTestEmail,
  createTransporter
};
