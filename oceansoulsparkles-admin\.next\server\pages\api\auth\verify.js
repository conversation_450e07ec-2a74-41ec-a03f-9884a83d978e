"use strict";(()=>{var e={};e.id=1640,e.ids=[1640],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},2876:(e,r,t)=>{t.r(r),t.d(r,{config:()=>l,default:()=>d,routeModule:()=>p});var a={};t.r(a),t.d(a,{default:()=>u});var o=t(1802),s=t(7153),i=t(8781),n=t(7474);async function u(e,r){if("GET"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let t=e.cookies["admin-token"]||e.headers.authorization?.replace("Bearer ","");if(!t)return r.status(401).json({error:"No authentication token provided"});let a=await (0,n.Wg)(t);if(!a.valid||!a.user)return r.status(401).json({error:a.error||"Invalid token"});return r.status(200).json({valid:!0,user:a.user})}catch(e){return console.error("Token verification error:",e),r.status(401).json({error:"Token verification failed"})}}let d=(0,i.l)(a,"default"),l=(0,i.l)(a,"config"),p=new o.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/auth/verify",pathname:"/api/auth/verify",bundlePath:"",filename:""},userland:a})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[2805],()=>t(2876));module.exports=a})();