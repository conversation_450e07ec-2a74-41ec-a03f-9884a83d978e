(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{5546:function(e,r,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/staff/onboarding",function(){return t(5436)}])},5436:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return u}}),function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var n=t(9008),o=t.n(n),a=t(1163),i=t(6026),c=t(99),d=t(9507),l=t.n(d);let O={documentation:"Documentation",training:"Training",equipment:"Equipment",access:"Access & Setup"},s={documentation:"\uD83D\uDCC4",training:"\uD83C\uDF93",equipment:"\uD83D\uDEE0️",access:"\uD83D\uDD11"};function u(){let{user:e}=(0,i.a)(),r=(0,a.useRouter)(),{staff_id:t}=r.query,[n,d]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[u,f]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[_,m]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[h,N]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({total:0,completed:0,required:0,completedRequired:0,completionPercentage:0,requiredCompletionPercentage:0}),[D,b]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[E,g]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("all");Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{t&&(U(),j())},[t]);let U=async()=>{try{d(!0);let e=await fetch("/api/admin/staff/onboarding?staff_id=".concat(t),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}});if(e.ok){let r=await e.json();m(r.checklist||[]),N(r.statistics||{})}else 404===e.status?m([]):f("Failed to load onboarding data")}catch(e){console.error("Error loading onboarding data:",e),f("Failed to load onboarding data")}finally{d(!1)}},j=async()=>{try{let e=await fetch("/api/admin/staff?id=".concat(t),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}});if(e.ok){let r=await e.json();b(r.staff)}}catch(e){console.error("Error loading staff info:",e)}},p=async()=>{try{(await fetch("/api/admin/staff/onboarding",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))},body:JSON.stringify({staff_id:t,action:"initialize"})})).ok?await U():f("Failed to initialize onboarding checklist")}catch(e){console.error("Error initializing onboarding:",e),f("Failed to initialize onboarding checklist")}},C=async(e,r)=>{try{(await fetch("/api/admin/staff/onboarding",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))},body:JSON.stringify({staff_id:t,action:r?"uncomplete_item":"complete_item",checklist_item_id:e})})).ok?await U():f("Failed to update checklist item")}catch(e){console.error("Error updating checklist item:",e),f("Failed to update checklist item")}},v=e=>new Date(e).toLocaleDateString("en-AU",{year:"numeric",month:"short",day:"numeric"}),F=e=>e>=90?"#10b981":e>=70?"#f59e0b":"#ef4444",w=("all"===E?_:_.filter(e=>e.category===E)).reduce((e,r)=>{let t=r.category;return e[t]||(e[t]=[]),e[t].push(r),e},{});return n?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c.Z,{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().loadingContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().loadingSpinner}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Loading onboarding data..."})]})}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c.Z,{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(o(),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("title",{children:"Staff Onboarding | Ocean Soul Sparkles Admin"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("meta",{name:"description",content:"Manage staff onboarding process and checklist"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().onboardingContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("header",{className:l().header,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().headerLeft,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:l().title,children:"Staff Onboarding"}),D&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:l().subtitle,children:[D.firstName," ",D.lastName," - ",D.role]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().headerActions,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>r.back(),className:l().backBtn,children:"← Back to Staff"})})]}),u&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().errorMessage,children:[u,Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>f(null),className:l().closeError,children:"\xd7"})]}),0===_.length?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().emptyState,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().emptyIcon,children:"\uD83D\uDCCB"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"No Onboarding Checklist"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"This staff member doesn't have an onboarding checklist yet."}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:p,className:l().initializeBtn,children:"Initialize Onboarding Checklist"})]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().progressSection,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().progressCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Overall Progress"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().progressBar,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().progressFill,style:{width:"".concat(h.completionPercentage,"%"),backgroundColor:F(h.completionPercentage)}})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().progressStats,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:[h.completed," of ",h.total," completed"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:[h.completionPercentage,"%"]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().progressCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Required Items"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().progressBar,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().progressFill,style:{width:"".concat(h.requiredCompletionPercentage,"%"),backgroundColor:F(h.requiredCompletionPercentage)}})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().progressStats,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:[h.completedRequired," of ",h.required," completed"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:[h.requiredCompletionPercentage,"%"]})]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().filters,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().filterGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"categoryFilter",children:"Filter by Category:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"categoryFilter",value:E,onChange:e=>g(e.target.value),className:l().filterSelect,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"all",children:"All Categories"}),Object.entries(O).map(e=>{let[r,t]=e;return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:r,children:t},r)})]})]})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().checklistContent,children:Object.entries(w).map(e=>{let[r,t]=e;return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().categorySection,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:l().categoryHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().categoryIcon,children:s[r]}),O[r],Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().categoryCount,children:["(",t.filter(e=>e.is_completed).length,"/",t.length,")"]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().checklistItems,children:t.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"".concat(l().checklistItem," ").concat(e.is_completed?l().completed:""),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().itemHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{className:l().checkboxLabel,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"checkbox",checked:e.is_completed,onChange:()=>C(e.id,e.is_completed),className:l().checkbox}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().itemTitle,children:[e.checklist_item,e.is_required&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().requiredBadge,children:"Required"})]})]}),e.due_date&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().dueDate,children:["Due: ",v(e.due_date)]})]}),e.description&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:l().itemDescription,children:e.description}),e.is_completed&&e.completed_at&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().completionInfo,children:["✅ Completed on ",v(e.completed_at)]}),e.notes&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().itemNotes,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Notes:"})," ",e.notes]})]},e.id))})]},r)})})]})]})]})}},9507:function(e){e.exports={onboardingContainer:"StaffOnboarding_onboardingContainer__0gEsE",header:"StaffOnboarding_header__FQ6mN",headerLeft:"StaffOnboarding_headerLeft__D3Nfs",title:"StaffOnboarding_title__cCfXg",subtitle:"StaffOnboarding_subtitle__kPkuq",headerActions:"StaffOnboarding_headerActions__kllUR",backBtn:"StaffOnboarding_backBtn__lldWS",errorMessage:"StaffOnboarding_errorMessage__735pT",closeError:"StaffOnboarding_closeError__dFzBF",emptyState:"StaffOnboarding_emptyState__r_EgB",emptyIcon:"StaffOnboarding_emptyIcon__gK0RX",initializeBtn:"StaffOnboarding_initializeBtn__xFU4E",progressSection:"StaffOnboarding_progressSection__hVcbY",progressCard:"StaffOnboarding_progressCard__utfM0",progressBar:"StaffOnboarding_progressBar__8gPjH",progressFill:"StaffOnboarding_progressFill__qytRt",progressStats:"StaffOnboarding_progressStats__Nd_0L",filters:"StaffOnboarding_filters__dN3ph",filterGroup:"StaffOnboarding_filterGroup__Yvzux",filterSelect:"StaffOnboarding_filterSelect__dBGww",checklistContent:"StaffOnboarding_checklistContent__qPdhX",categorySection:"StaffOnboarding_categorySection__eV_fV",categoryHeader:"StaffOnboarding_categoryHeader__GlG0q",categoryIcon:"StaffOnboarding_categoryIcon__XlZGI",categoryCount:"StaffOnboarding_categoryCount__kLba9",checklistItems:"StaffOnboarding_checklistItems__TEc3i",checklistItem:"StaffOnboarding_checklistItem__zGzeh",completed:"StaffOnboarding_completed__mLyk9",itemHeader:"StaffOnboarding_itemHeader__RMK4f",checkboxLabel:"StaffOnboarding_checkboxLabel__sXZ9N",checkbox:"StaffOnboarding_checkbox__GJbK_",itemTitle:"StaffOnboarding_itemTitle__CYlBk",requiredBadge:"StaffOnboarding_requiredBadge__xQoAO",dueDate:"StaffOnboarding_dueDate__07K_M",itemDescription:"StaffOnboarding_itemDescription__OXGcF",completionInfo:"StaffOnboarding_completionInfo__FIDvn",itemNotes:"StaffOnboarding_itemNotes__tKkjM",loadingContainer:"StaffOnboarding_loadingContainer__ejKCF",loadingSpinner:"StaffOnboarding_loadingSpinner__APxxH",spin:"StaffOnboarding_spin__fxKq7"}}},function(e){e.O(0,[736,592,888,179],function(){return e(e.s=5546)}),_N_E=e.O()}]);