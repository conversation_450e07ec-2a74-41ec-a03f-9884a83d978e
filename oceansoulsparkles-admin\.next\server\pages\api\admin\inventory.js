"use strict";(()=>{var e={};e.id=9089,e.ids=[9089],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},8781:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},5111:(e,t,r)=>{r.r(t),r.d(t,{config:()=>u,default:()=>l,routeModule:()=>d});var a={};r.r(a),r.d(a,{default:()=>c});var s=r(1802),i=r(7153),o=r(8781);let n=(0,r(2885).createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function c(e,t){let r=e.headers.authorization;if(!r||!r.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized"});try{if("GET"===e.method){let e,r;if({data:e,error:r}=await n.from("products").select("*"),r&&r.message.includes('relation "products" does not exist')&&({data:e,error:r}=await n.from("inventory").select("*")),r&&r.message.includes("does not exist"))return t.status(200).json({inventory:[{id:1,name:"Kankelon Braiding Hair - Black",category:"Braiding Hair",sku:"KBH-001-BLK",stock_quantity:45,min_stock_level:10,cost_price:8.5,sale_price:15,supplier:"Hair Plus Wholesale",last_restock:"2024-01-15T00:00:00Z",status:"in_stock",description:"High-quality synthetic braiding hair in jet black",weight:"100g",color:"Black (#1B)",created_at:"2024-01-10T00:00:00Z"},{id:2,name:"Kankelon Braiding Hair - Brown",category:"Braiding Hair",sku:"KBH-002-BRN",stock_quantity:8,min_stock_level:10,cost_price:8.5,sale_price:15,supplier:"Hair Plus Wholesale",last_restock:"2024-01-10T00:00:00Z",status:"low_stock",description:"High-quality synthetic braiding hair in dark brown",weight:"100g",color:"Dark Brown (#4)",created_at:"2024-01-10T00:00:00Z"},{id:3,name:"Marley Hair - Natural",category:"Marley Hair",sku:"MH-001-NAT",stock_quantity:22,min_stock_level:5,cost_price:12,sale_price:22,supplier:"Natural Hair Co",last_restock:"2024-01-20T00:00:00Z",status:"in_stock",description:"Premium Marley hair for protective styles",weight:"100g",color:"Natural Black",created_at:"2024-01-15T00:00:00Z"},{id:4,name:"Edge Control - Strong Hold",category:"Hair Products",sku:"EC-001-STR",stock_quantity:15,min_stock_level:8,cost_price:4.5,sale_price:12,supplier:"Beauty Supply Direct",last_restock:"2024-01-18T00:00:00Z",status:"in_stock",description:"Professional edge control for long-lasting hold",weight:"118ml",color:"Clear",created_at:"2024-01-12T00:00:00Z"},{id:5,name:"Hair Serum - Moisturizing",category:"Hair Products",sku:"HS-001-MOIST",stock_quantity:0,min_stock_level:5,cost_price:6,sale_price:18,supplier:"Natural Hair Co",last_restock:"2023-12-20T00:00:00Z",status:"out_of_stock",description:"Nourishing hair serum for dry and damaged hair",weight:"100ml",color:"Amber",created_at:"2024-01-08T00:00:00Z"},{id:6,name:"Rubber Bands - Small",category:"Accessories",sku:"RB-001-SM",stock_quantity:180,min_stock_level:50,cost_price:.02,sale_price:.1,supplier:"Accessories Direct",last_restock:"2024-01-22T00:00:00Z",status:"in_stock",description:"Small elastic bands for sectioning hair",weight:"1g",color:"Clear",created_at:"2024-01-05T00:00:00Z"}],source:"mock"});if(r)throw r;return t.status(200).json({inventory:e||[],source:"database"})}if("POST"===e.method){let{name:r,category:a,sku:s,stock_quantity:i,min_stock_level:o,cost_price:c,sale_price:l,supplier:u,description:d}=e.body,{data:p,error:_}=await n.from("products").insert([{name:r,category:a,sku:s,stock_quantity:parseInt(i),min_stock_level:parseInt(o),cost_price:parseFloat(c),sale_price:parseFloat(l),supplier:u,description:d,status:i>o?"in_stock":"low_stock",created_at:new Date().toISOString()}]).select();if(_)throw _;return t.status(201).json({product:p[0]})}return t.status(405).json({error:"Method not allowed"})}catch(e){return console.error("Inventory API error:",e),t.status(500).json({error:"Internal server error"})}}let l=(0,o.l)(a,"default"),u=(0,o.l)(a,"config"),d=new s.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/inventory",pathname:"/api/admin/inventory",bundlePath:"",filename:""},userland:a})},7153:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},1802:(e,t,r)=>{e.exports=r(1287)}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=5111);module.exports=r})();