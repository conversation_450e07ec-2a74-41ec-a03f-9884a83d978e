/**
 * Ocean Soul Sparkles Admin - Pull to Refresh Component
 * Provides native mobile pull-to-refresh functionality
 */

import React, { useState, useRef, useEffect } from 'react';
import { HapticFeedback } from '../../lib/gestures/swipe-handler';
import styles from '../../styles/admin/mobile/PullToRefresh.module.css';

interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
  threshold?: number;
  maxPullDistance?: number;
  disabled?: boolean;
  className?: string;
}

interface TouchState {
  startY: number;
  currentY: number;
  pullDistance: number;
  isRefreshing: boolean;
  canRefresh: boolean;
}

export default function PullToRefresh({
  onRefresh,
  children,
  threshold = 80,
  maxPullDistance = 120,
  disabled = false,
  className = ''
}: PullToRefreshProps) {
  const [touchState, setTouchState] = useState<TouchState>({
    startY: 0,
    currentY: 0,
    pullDistance: 0,
    isRefreshing: false,
    canRefresh: false
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const refreshIndicatorRef = useRef<HTMLDivElement>(null);
  const isTracking = useRef(false);
  const hasTriggeredHaptic = useRef(false);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || disabled) return;

    const handleTouchStart = (e: TouchEvent) => {
      // Only start tracking if we're at the top of the scroll container
      if (container.scrollTop === 0 && !touchState.isRefreshing) {
        isTracking.current = true;
        hasTriggeredHaptic.current = false;
        
        setTouchState(prev => ({
          ...prev,
          startY: e.touches[0].clientY,
          currentY: e.touches[0].clientY,
          pullDistance: 0,
          canRefresh: false
        }));
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isTracking.current || touchState.isRefreshing) return;

      const currentY = e.touches[0].clientY;
      const pullDistance = Math.max(0, currentY - touchState.startY);
      
      // Limit pull distance with resistance
      const resistanceFactor = 0.5;
      const adjustedPullDistance = Math.min(
        pullDistance * resistanceFactor,
        maxPullDistance
      );

      // Check if we've reached the threshold
      const canRefresh = adjustedPullDistance >= threshold;

      // Trigger haptic feedback when threshold is reached
      if (canRefresh && !hasTriggeredHaptic.current) {
        HapticFeedback.medium();
        hasTriggeredHaptic.current = true;
      }

      setTouchState(prev => ({
        ...prev,
        currentY,
        pullDistance: adjustedPullDistance,
        canRefresh
      }));

      // Prevent default scrolling when pulling
      if (pullDistance > 0) {
        e.preventDefault();
      }
    };

    const handleTouchEnd = async () => {
      if (!isTracking.current || touchState.isRefreshing) return;

      isTracking.current = false;

      if (touchState.canRefresh) {
        // Trigger refresh
        setTouchState(prev => ({
          ...prev,
          isRefreshing: true,
          pullDistance: threshold
        }));

        HapticFeedback.success();

        try {
          await onRefresh();
        } catch (error) {
          console.error('Refresh failed:', error);
          HapticFeedback.error();
        } finally {
          // Reset state after refresh
          setTouchState(prev => ({
            ...prev,
            isRefreshing: false,
            pullDistance: 0,
            canRefresh: false
          }));
        }
      } else {
        // Snap back to original position
        setTouchState(prev => ({
          ...prev,
          pullDistance: 0,
          canRefresh: false
        }));
      }
    };

    container.addEventListener('touchstart', handleTouchStart, { passive: true });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: true });
    container.addEventListener('touchcancel', handleTouchEnd, { passive: true });

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
      container.removeEventListener('touchcancel', handleTouchEnd);
    };
  }, [onRefresh, threshold, maxPullDistance, disabled, touchState.isRefreshing, touchState.startY, touchState.canRefresh]);

  const getRefreshIndicatorStyle = (): React.CSSProperties => {
    const { pullDistance, isRefreshing, canRefresh } = touchState;
    
    return {
      transform: `translateY(${pullDistance - 60}px)`,
      opacity: pullDistance > 20 ? 1 : 0,
      transition: isRefreshing || pullDistance === 0 ? 'all 0.3s ease-out' : 'none'
    };
  };

  const getContainerStyle = (): React.CSSProperties => {
    const { pullDistance, isRefreshing } = touchState;
    
    return {
      transform: `translateY(${pullDistance}px)`,
      transition: isRefreshing || pullDistance === 0 ? 'transform 0.3s ease-out' : 'none'
    };
  };

  const getRefreshIconClass = (): string => {
    const { isRefreshing, canRefresh } = touchState;
    
    if (isRefreshing) return styles.refreshIconSpinning;
    if (canRefresh) return styles.refreshIconReady;
    return styles.refreshIcon;
  };

  const getRefreshText = (): string => {
    const { isRefreshing, canRefresh } = touchState;
    
    if (isRefreshing) return 'Refreshing...';
    if (canRefresh) return 'Release to refresh';
    return 'Pull to refresh';
  };

  return (
    <div className={`${styles.pullToRefreshContainer} ${className}`} ref={containerRef}>
      {/* Refresh Indicator */}
      <div 
        className={styles.refreshIndicator}
        ref={refreshIndicatorRef}
        style={getRefreshIndicatorStyle()}
      >
        <div className={styles.refreshContent}>
          <div className={getRefreshIconClass()}>
            {touchState.isRefreshing ? '🔄' : '⬇️'}
          </div>
          <span className={styles.refreshText}>
            {getRefreshText()}
          </span>
        </div>
        
        {/* Progress indicator */}
        <div className={styles.progressBar}>
          <div 
            className={styles.progressFill}
            style={{
              width: `${Math.min((touchState.pullDistance / threshold) * 100, 100)}%`,
              backgroundColor: touchState.canRefresh ? '#4CAF50' : '#2196F3'
            }}
          />
        </div>
      </div>

      {/* Content */}
      <div 
        className={styles.content}
        style={getContainerStyle()}
      >
        {children}
      </div>
    </div>
  );
}
