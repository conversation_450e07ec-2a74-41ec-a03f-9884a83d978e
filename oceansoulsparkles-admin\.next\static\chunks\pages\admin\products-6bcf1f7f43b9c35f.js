(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[209],{2678:function(r,t,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/products",function(){return e(7469)}])},7469:function(r,t,e){"use strict";e.r(t),e.d(t,{default:function(){return _}}),function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}(),function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}();var o=e(9008),n=e.n(o),c=e(1664),a=e.n(c),i=e(6026),d=e(99),u=e(8935),s=e(7270),l=e.n(s);function _(){let{user:r,loading:t}=(0,i.a)(),[e,o]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(!0),[c,s]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())([]),[_,O]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())([]),[m,N]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(""),[f,h]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(""),[E,U]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(""),[D,v]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())("name"),[j,p]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(null);Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(()=>{!t&&r&&w()},[t,r]),Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(()=>{C()},[c,m,f,E,D]);let w=async()=>{try{o(!0);let r=localStorage.getItem("admin-token"),t=await fetch("/api/admin/products",{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to load products");let e=await t.json();s(e.products||[])}catch(r){console.error("Error loading products:",r),p(r.message)}finally{o(!1)}},C=()=>{let r=[...c];m&&(r=r.filter(r=>{var t,e;return r.name.toLowerCase().includes(m.toLowerCase())||(null===(t=r.sku)||void 0===t?void 0:t.toLowerCase().includes(m.toLowerCase()))||(null===(e=r.description)||void 0===e?void 0:e.toLowerCase().includes(m.toLowerCase()))})),f&&(r=r.filter(r=>r.category_name===f)),E&&("active"===E?r=r.filter(r=>r.is_active):"inactive"===E?r=r.filter(r=>!r.is_active):"low_stock"===E&&(r=r.filter(r=>r.stock<=(r.low_stock_threshold||5)))),r.sort((r,t)=>{switch(D){case"name":return r.name.localeCompare(t.name);case"price":return(t.price||0)-(r.price||0);case"stock":return(t.stock||0)-(r.stock||0);case"category":return(r.category_name||"").localeCompare(t.category_name||"");default:return 0}}),O(r)},b=r=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(r||0),L=r=>r.stock<=0?{status:"out_of_stock",label:"Out of Stock",class:"outOfStock"}:r.stock<=(r.low_stock_threshold||5)?{status:"low_stock",label:"Low Stock",class:"lowStock"}:{status:"in_stock",label:"In Stock",class:"inStock"},F=[...new Set(c.map(r=>r.category_name).filter(Boolean))];return t||e?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(d.Z,{children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().loadingContainer,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().loadingSpinner}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Loading products..."})]})}):j?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(d.Z,{children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().errorContainer,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{children:"Error Loading Products"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:j}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:w,className:l().retryButton,children:"Try Again"})]})}):Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(d.Z,{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(n(),{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("title",{children:"Products Management | Ocean Soul Sparkles Admin"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("meta",{name:"description",content:"Manage product catalog and inventory"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().productsContainer,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("header",{className:l().header,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h1",{className:l().title,children:"Products Management"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().headerActions,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(u.F,{data:_,type:"products",className:l().exportBtn}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(a(),{href:"/admin/products/new",className:l().newProductBtn,children:"+ Add Product"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().filtersSection,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().searchBar,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("input",{type:"text",placeholder:"Search products by name, SKU, or description...",value:m,onChange:r=>N(r.target.value),className:l().searchInput})}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().filters,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("select",{value:f,onChange:r=>h(r.target.value),className:l().filterSelect,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"",children:"All Categories"}),F.map(r=>Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:r,children:r},r))]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("select",{value:E,onChange:r=>U(r.target.value),className:l().filterSelect,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"",children:"All Status"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"active",children:"Active"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"inactive",children:"Inactive"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"low_stock",children:"Low Stock"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("select",{value:D,onChange:r=>v(r.target.value),className:l().sortSelect,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"name",children:"Sort by Name"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"price",children:"Sort by Price"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"stock",children:"Sort by Stock"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"category",children:"Sort by Category"})]})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().statsBar,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().stat,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().statNumber,children:c.length}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().statLabel,children:"Total Products"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().stat,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().statNumber,children:c.filter(r=>r.is_active).length}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().statLabel,children:"Active"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().stat,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().statNumber,children:c.filter(r=>r.stock<=(r.low_stock_threshold||5)).length}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().statLabel,children:"Low Stock"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().stat,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().statNumber,children:b(c.reduce((r,t)=>r+(t.price||0)*(t.stock||0),0))}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().statLabel,children:"Total Value"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().productsGrid,children:0===_.length?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().emptyState,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:"No products found"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:m||f||E?"Try adjusting your filters or search terms.":"Start by adding your first product."}),!m&&!f&&!E&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(a(),{href:"/admin/products/new",className:l().addFirstBtn,children:"Add Your First Product"})]}):_.map(r=>{let t=L(r);return Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().productCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().productImage,children:r.image_url?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("img",{src:r.image_url,alt:r.name}):Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().placeholderImage,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:"\uD83D\uDCE6"})})}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().productInfo,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().productHeader,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{className:l().productName,children:r.name}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:"".concat(l().statusBadge," ").concat(l()[r.is_active?"active":"inactive"]),children:r.is_active?"Active":"Inactive"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().productDetails,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().productMeta,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().sku,children:["SKU: ",r.sku||"N/A"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().category,children:r.category_name||"Uncategorized"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().priceStock,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().pricing,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().price,children:b(r.price)}),r.sale_price&&r.sale_price<r.price&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().salePrice,children:b(r.sale_price)})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().stock,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:"".concat(l().stockStatus," ").concat(l()[t.class]),children:t.label}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:l().stockCount,children:[r.stock||0," units"]})]})]}),r.description&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{className:l().description,children:r.description.length>100?"".concat(r.description.substring(0,100),"..."):r.description})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:l().productActions,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(a(),{href:"/admin/products/".concat(r.id),className:l().viewBtn,children:"View Details"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(a(),{href:"/admin/products/".concat(r.id,"/edit"),className:l().editBtn,children:"Edit"})]})]})]},r.id)})})]})]})}},7270:function(r){r.exports={productsContainer:"Products_productsContainer__A_eJy",header:"Products_header__MesnD",title:"Products_title__JVkKQ",headerActions:"Products_headerActions__GgUlt",newProductBtn:"Products_newProductBtn__B6HYU",filtersSection:"Products_filtersSection__PVoFG",searchBar:"Products_searchBar__TQ_b8",searchInput:"Products_searchInput__IpuX4",filters:"Products_filters__4UXYp",filterSelect:"Products_filterSelect__Yj55b",sortSelect:"Products_sortSelect__bw2db",statsBar:"Products_statsBar__WO_hI",stat:"Products_stat__Ervdg",statNumber:"Products_statNumber__YW9mB",statLabel:"Products_statLabel__1hE37",productsGrid:"Products_productsGrid__MUvv7",productCard:"Products_productCard__NnRkS",productImage:"Products_productImage__gvfA_",placeholderImage:"Products_placeholderImage__Oqni0",productInfo:"Products_productInfo__8ul2C",productHeader:"Products_productHeader__PdZEO",productName:"Products_productName__PiTku",statusBadge:"Products_statusBadge__rF99R",active:"Products_active__Ksddh",inactive:"Products_inactive__P5xy_",productDetails:"Products_productDetails__IsBKI",productMeta:"Products_productMeta__WJ2VG",sku:"Products_sku__UiXDl",category:"Products_category__Z3_8B",priceStock:"Products_priceStock__cb6a5",pricing:"Products_pricing__il8UX",price:"Products_price__Vkpvv",salePrice:"Products_salePrice__F6lrv",stock:"Products_stock__n5hZ0",stockStatus:"Products_stockStatus__Xv9LX",inStock:"Products_inStock__S_LsJ",lowStock:"Products_lowStock__TNx9r",outOfStock:"Products_outOfStock__sdTDi",stockCount:"Products_stockCount__mIggZ",description:"Products_description__s2ui2",productActions:"Products_productActions__mAlgI",viewBtn:"Products_viewBtn__hU_0X",editBtn:"Products_editBtn__nrfcL",emptyState:"Products_emptyState__VF4ic",addFirstBtn:"Products_addFirstBtn__vzLL1",loadingContainer:"Products_loadingContainer__ExoGg",errorContainer:"Products_errorContainer__9B_vB",loadingSpinner:"Products_loadingSpinner__M9t_l",spin:"Products_spin__plmlc",retryButton:"Products_retryButton___SoyA"}}},function(r){r.O(0,[736,592,888,179],function(){return r(r.s=2678)}),_N_E=r.O()}]);