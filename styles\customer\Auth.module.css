/* Ocean Soul Sparkles - Customer Authentication Styles */

.authContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem 1rem;
}

.authCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.authHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.authHeader h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.authHeader p {
  margin: 0;
  color: #718096;
  font-size: 1rem;
}

.errorMessage, .successMessage {
  padding: 1rem;
  border-radius: 10px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.errorMessage {
  background: rgba(254, 178, 178, 0.2);
  border: 1px solid #feb2b2;
  color: #c53030;
}

.successMessage {
  background: rgba(154, 230, 180, 0.2);
  border: 1px solid #9ae6b4;
  color: #2f855a;
}

.errorIcon, .successIcon {
  font-size: 1.2rem;
}

.authForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
}

.formInput {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.formInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.formInput::placeholder {
  color: #a0aec0;
}

.passwordInput {
  position: relative;
  display: flex;
  align-items: center;
}

.passwordToggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: #718096;
  transition: color 0.3s ease;
}

.passwordToggle:hover {
  color: #667eea;
}

.passwordStrength {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.strengthBar {
  height: 4px;
  border-radius: 2px;
  transition: all 0.3s ease;
  background: #e2e8f0;
  flex: 1;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.forgotPassword {
  color: #667eea;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgotPassword:hover {
  color: #764ba2;
  text-decoration: underline;
}

.submitButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 50px;
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.authFooter {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.authFooter p {
  margin: 0;
  color: #718096;
}

.authLink {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.authLink:hover {
  color: #764ba2;
  text-decoration: underline;
}

.divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
  color: #a0aec0;
  font-size: 0.9rem;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
  z-index: 1;
}

.divider span {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 1rem;
  position: relative;
  z-index: 2;
}

.guestActions {
  text-align: center;
}

.guestButton {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: 2px solid #667eea;
  color: #667eea;
  text-decoration: none;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.guestButton:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: 1rem;
  color: white;
}

.loadingContainer .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .authCard {
    padding: 2rem;
    margin: 1rem;
    border-radius: 15px;
  }

  .authHeader h1 {
    font-size: 1.5rem;
  }

  .formRow {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .authContainer {
    padding: 1rem;
  }

  .authCard {
    padding: 1.5rem;
  }

  .authHeader h1 {
    font-size: 1.3rem;
  }

  .formInput {
    padding: 0.6rem 0.8rem;
  }

  .submitButton {
    padding: 0.8rem 1.5rem;
  }
}
