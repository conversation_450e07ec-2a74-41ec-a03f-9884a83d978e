"use strict";(()=>{var e={};e.id=5925,e.ids=[5925],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},7202:e=>{e.exports=require("twilio")},9743:(e,s,o)=>{o.r(s),o.d(s,{config:()=>S,default:()=>f,routeModule:()=>g});var t={};o.r(t),o.d(t,{default:()=>m});var n=o(1802),r=o(7153),a=o(8781),i=o(2885),c=o(7474),l=o(8173),u=o.n(l);let d=(0,i.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function m(e,s){let o=`sms-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let t=await (0,c.SA)(e);if(!t.success)return s.status(401).json({error:"Unauthorized",message:t.message,requestId:o});let{user:n}=t;if("POST"===e.method){let t;let{type:r,data:a}=e.body;if(!r||!a)return s.status(400).json({error:"Missing required fields",message:"Type and data are required",requestId:o});switch(console.log(`[${o}] Processing SMS request:`,{type:r,recipient:a.to||a.customerPhone||a.staffPhone}),r){case"booking_confirmation":t=await u().sendBookingConfirmation(a);break;case"booking_reminder":t=await u().sendBookingReminder(a);break;case"booking_cancellation":t=await u().sendBookingCancellation(a);break;case"staff_notification":t=await u().sendStaffNotification(a);break;case"promotional":t=await u().sendPromotionalSMS(a.customer,a.message);break;case"test_sms":if(!a.to||!a.message)return s.status(400).json({error:"Recipient phone and message are required for test SMS",requestId:o});t=await u().sendSMS({to:a.to,message:a.message,type:"test"});break;case"bulk_sms":if(!a.recipients||!a.message)return s.status(400).json({error:"Recipients and message are required for bulk SMS",requestId:o});t=await u().sendBulkSMS(a.recipients,a.message,a.type||"bulk");break;default:return s.status(400).json({error:`Unknown SMS type: ${r}`,requestId:o})}if(t.success&&!t.skipped)try{await p({type:"sms",recipient:a.to||a.customerPhone||a.staffPhone,messageId:t.messageId,status:"sent",content:a.message||`${r} SMS`,customerId:a.customerId,bookingId:a.bookingId||a.id,staffId:a.staffId,sentBy:n.id})}catch(e){console.error(`[${o}] Failed to log communication:`,e)}if(t.success)return console.log(`[${o}] SMS sent successfully:`,t.messageId),s.status(200).json({success:!0,messageId:t.messageId,message:t.skipped?`SMS skipped: ${t.reason}`:"SMS sent successfully",skipped:t.skipped||!1,fallback:t.fallback||!1,requestId:o});return console.error(`[${o}] SMS sending failed:`,t.error),s.status(500).json({error:"Failed to send SMS",message:t.error,skipped:t.skipped||!1,reason:t.reason,requestId:o})}if("GET"===e.method){let e=u().getStatus(),t=await u().verifyConfiguration(),{data:n}=await d.from("system_settings").select("*").eq("category","notifications"),r={};return n?.forEach(e=>{if(e.key.toLowerCase().includes("sms"))try{r[e.key]=JSON.parse(e.value)}catch(s){r[e.key]=e.value}}),s.status(200).json({status:e,verification:t,settings:r,requestId:o})}return s.status(405).json({error:"Method not allowed",requestId:o})}catch(e){return console.error(`[${o}] SMS API error:`,e),s.status(500).json({error:"Internal server error",message:e.message,requestId:o})}}async function p({type:e,recipient:s,messageId:o,status:t,content:n,customerId:r,bookingId:a,staffId:i,sentBy:c}){try{let{error:l}=await d.from("customer_communications").insert([{customer_id:r||null,booking_id:a||null,communication_type:e,recipient:s,subject:null,content:n,status:t,sent_at:new Date().toISOString(),external_id:o,metadata:{staff_id:i,sent_by:c,provider:"twilio"}}]);l&&console.error("Error logging SMS communication:",l)}catch(e){console.error("Error logging SMS communication:",e)}}let f=(0,a.l)(t,"default"),S=(0,a.l)(t,"config"),g=new n.PagesAPIRouteModule({definition:{kind:r.x.PAGES_API,page:"/api/admin/notifications/sms",pathname:"/api/admin/notifications/sms",bundlePath:"",filename:""},userland:t})},8173:(e,s,o)=>{let t=o(7202);class n{constructor(){this.isConfigured=!!(process.env.TWILIO_ACCOUNT_SID&&process.env.TWILIO_AUTH_TOKEN),this.client=this.isConfigured?t(process.env.TWILIO_ACCOUNT_SID,process.env.TWILIO_AUTH_TOKEN):null,this.fromNumber=process.env.TWILIO_PHONE_NUMBER||"+**********"}async checkSMSEnabled(e=null){try{let s=await fetch(`${process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3000"}/api/admin/settings`),{settings:o}=await s.json(),t=o?.notifications||{};if(!t.smsNotifications)return{enabled:!1,reason:"SMS notifications disabled globally"};if(e){let s=`sms${e.charAt(0).toUpperCase()+e.slice(1)}`;if(!1===t[s])return{enabled:!1,reason:`SMS ${e} notifications disabled`}}return{enabled:!0}}catch(e){return console.error("Error checking SMS settings:",e),{enabled:!1,reason:"Settings check failed"}}}async sendBookingConfirmation(e){let s=await this.checkSMSEnabled("bookingConfirmation");if(!s.enabled)return console.log(`SMS booking confirmation skipped: ${s.reason}`),{success:!1,skipped:!0,reason:s.reason};if(!e.customerPhone)return console.warn("No customer phone provided for booking confirmation SMS"),{success:!1,error:"No customer phone"};let o=`Hi ${e.customerName}! Your appointment for ${e.serviceName} is confirmed for ${e.date} at ${e.time}. Location: Ocean Soul Sparkles. Questions? Reply to this message.`;return await this.sendSMS({to:e.customerPhone,message:o,type:"booking_confirmation",bookingId:e.id})}async sendBookingReminder(e){let s=await this.checkSMSEnabled("bookingReminder");if(!s.enabled)return console.log(`SMS booking reminder skipped: ${s.reason}`),{success:!1,skipped:!0,reason:s.reason};if(!e.customerPhone)return console.warn("No customer phone provided for booking reminder SMS"),{success:!1,error:"No customer phone"};let o=`Reminder: Your appointment for ${e.serviceName} is tomorrow at ${e.time}. See you at Ocean Soul Sparkles! Reply CONFIRM to confirm or CANCEL to reschedule.`;return await this.sendSMS({to:e.customerPhone,message:o,type:"booking_reminder",bookingId:e.id})}async sendBookingCancellation(e){let s=await this.checkSMSEnabled("bookingCancellation");if(!s.enabled)return console.log(`SMS booking cancellation skipped: ${s.reason}`),{success:!1,skipped:!0,reason:s.reason};if(!e.customerPhone)return console.warn("No customer phone provided for booking cancellation SMS"),{success:!1,error:"No customer phone"};let o=`Your appointment for ${e.serviceName} on ${e.date} at ${e.time} has been cancelled. To reschedule, please contact Ocean Soul Sparkles. Thank you!`;return await this.sendSMS({to:e.customerPhone,message:o,type:"booking_cancellation",bookingId:e.id})}async sendStaffNotification(e){let s=await this.checkSMSEnabled("staffNotification");if(!s.enabled)return console.log(`SMS staff notification skipped: ${s.reason}`),{success:!1,skipped:!0,reason:s.reason};if(!e.staffPhone)return console.warn("No staff phone provided for notification SMS"),{success:!1,error:"No staff phone"};let o=e.message||`Staff notification: ${e.subject}`;return await this.sendSMS({to:e.staffPhone,message:o,type:"staff_notification",staffId:e.staffId})}async sendPromotionalSMS(e,s){let o=await this.checkSMSEnabled("promotional");return o.enabled?e.phone?await this.sendSMS({to:e.phone,message:s,type:"promotional",customerId:e.id}):(console.warn("No customer phone provided for promotional SMS"),{success:!1,error:"No customer phone"}):(console.log(`SMS promotional message skipped: ${o.reason}`),{success:!1,skipped:!0,reason:o.reason})}async sendSMS({to:e,message:s,type:o,bookingId:t,customerId:n,staffId:r}){let a=this.normalizePhoneNumber(e);if(!a)return{success:!1,error:"Invalid phone number format"};if(!this.client)return console.log("\n\uD83D\uDCF1 SMS WOULD BE SENT:"),console.log("To:",a),console.log("Message:",s),console.log("Type:",o),console.log("---\n"),{success:!0,messageId:"console-log-"+Date.now(),fallback:!0};try{let e=await this.client.messages.create({body:s,from:this.fromNumber,to:a});return console.log(`SMS sent successfully: ${e.sid}`),{success:!0,messageId:e.sid,status:e.status,to:a,type:o}}catch(e){return console.error("SMS sending failed:",e),{success:!1,error:e.message,code:e.code,to:a,type:o}}}async sendBulkSMS(e,s,o="bulk"){let t=await this.checkSMSEnabled(o);if(!t.enabled)return console.log(`Bulk SMS skipped: ${t.reason}`),{success:!1,skipped:!0,reason:t.reason};let n=[];for(let t of e)try{let e=await this.sendSMS({to:t.phone,message:s.replace(/{{name}}/g,t.name||"Valued Customer"),type:o,customerId:t.id});n.push({phone:t.phone,...e}),await new Promise(e=>setTimeout(e,1e3))}catch(e){n.push({phone:t.phone,success:!1,error:e.message})}return n}normalizePhoneNumber(e){if(!e)return null;let s=e.replace(/\D/g,"");return s.startsWith("61")?"+"+s:s.startsWith("0")&&10===s.length?"+61"+s.substring(1):9===s.length?"+61"+s:s.startsWith("1")&&11===s.length?"+"+s:s.length>=10?"+"+s:null}async verifyConfiguration(){if(!this.isConfigured)return{configured:!1,error:"Twilio credentials not configured"};try{let e=await this.client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();return{configured:!0,accountSid:e.sid,status:e.status,fromNumber:this.fromNumber}}catch(e){return{configured:!1,error:e.message}}}getStatus(){return{configured:this.isConfigured,provider:"Twilio",fromNumber:this.fromNumber,environment:"production"}}}e.exports=new n}};var s=require("../../../../webpack-api-runtime.js");s.C(e);var o=e=>s(s.s=e),t=s.X(0,[2805],()=>o(9743));module.exports=t})();