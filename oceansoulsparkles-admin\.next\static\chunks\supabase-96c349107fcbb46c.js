"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[172],{9743:function(e,t,s){s.r(t),s.d(t,{Headers:function(){return n},Request:function(){return a},Response:function(){return o},fetch:function(){return r}});var i=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==s.g)return s.g;throw Error("unable to locate global object")}();let r=i.fetch;t.default=i.fetch.bind(i);let n=i.Headers,a=i.Request,o=i.Response},1879:function(e,t,s){var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let r=i(s(9743)),n=i(s(9577));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=r.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,s,i;let r=null,a=null,o=null,l=e.status,h=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept?t:this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let i=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),n=null===(s=e.headers.get("content-range"))||void 0===s?void 0:s.split("/");i&&n&&n.length>1&&(o=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(r={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,h="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{r=JSON.parse(t),Array.isArray(r)&&404===e.status&&(a=[],r=null,l=200,h="OK")}catch(s){404===e.status&&""===t?(l=204,h="No Content"):r={message:t}}if(r&&this.isMaybeSingle&&(null===(i=null==r?void 0:r.details)||void 0===i?void 0:i.includes("0 rows"))&&(r=null,l=200,h="OK"),r&&this.shouldThrowOnError)throw new n.default(r)}return{error:r,data:a,count:o,status:l,statusText:h}});return this.shouldThrowOnError||(s=s.catch(e=>{var t,s,i;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(s=null==e?void 0:e.stack)&&void 0!==s?s:""}`,hint:"",code:`${null!==(i=null==e?void 0:e.code)&&void 0!==i?i:""}`},data:null,count:null,status:0,statusText:""}})),s.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},6138:function(e,t,s){var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let r=i(s(9527)),n=i(s(8483)),a=s(862);class o{constructor(e,{headers:t={},schema:s,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=i}from(e){let t=new URL(`${this.url}/${e}`);return new r.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:s=!1,get:i=!1,count:r}={}){let a,o;let l=new URL(`${this.url}/rpc/${e}`);s||i?(a=s?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let h=Object.assign({},this.headers);return r&&(h.Prefer=`count=${r}`),new n.default({method:a,url:l,headers:h,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},9577:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});class s extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=s},8483:function(e,t,s){var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let r=i(s(7486));class n extends r.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let s=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${s})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:s,type:i}={}){let r="";"plain"===i?r="pl":"phrase"===i?r="ph":"websearch"===i&&(r="w");let n=void 0===s?"":`(${s})`;return this.url.searchParams.append(e,`${r}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,s){return this.url.searchParams.append(e,`not.${t}.${s}`),this}or(e,{foreignTable:t,referencedTable:s=t}={}){let i=s?`${s}.or`:"or";return this.url.searchParams.append(i,`(${e})`),this}filter(e,t,s){return this.url.searchParams.append(e,`${t}.${s}`),this}}t.default=n},9527:function(e,t,s){var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let r=i(s(8483));class n{constructor(e,{headers:t={},schema:s,fetch:i}){this.url=e,this.headers=t,this.schema=s,this.fetch=i}select(e,{head:t=!1,count:s}={}){let i=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",n),s&&(this.headers.Prefer=`count=${s}`),new r.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:s=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),t&&i.push(`count=${t}`),s||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new r.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:s=!1,count:i,defaultToNull:n=!0}={}){let a=[`resolution=${s?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new r.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),this.headers.Prefer=s.join(","),new r.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new r.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=n},7486:function(e,t,s){var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let r=i(s(1879));class n extends r.default{select(e){let t=!1,s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:s,foreignTable:i,referencedTable:r=i}={}){let n=r?`${r}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===s?"":s?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:s=t}={}){let i=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(i,`${e}`),this}range(e,t,{foreignTable:s,referencedTable:i=s}={}){let r=void 0===i?"offset":`${i}.offset`,n=void 0===i?"limit":`${i}.limit`;return this.url.searchParams.set(r,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:i=!1,wal:r=!1,format:n="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,s?"settings":null,i?"buffers":null,r?"wal":null].filter(Boolean).join("|"),l=null!==(a=this.headers.Accept)&&void 0!==a?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=n},862:function(e,t,s){Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let i=s(8771);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${i.version}`}},159:function(e,t,s){var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let r=i(s(6138));t.PostgrestClient=r.default;let n=i(s(9527));t.PostgrestQueryBuilder=n.default;let a=i(s(8483));t.PostgrestFilterBuilder=a.default;let o=i(s(7486));t.PostgrestTransformBuilder=o.default;let l=i(s(1879));t.PostgrestBuilder=l.default;let h=i(s(9577));t.PostgrestError=h.default,t.default={PostgrestClient:r.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:h.default}},8771:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},6407:function(e,t,s){s.d(t,{eI:function(){return tz}});let i=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(s.bind(s,9743)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)};class r extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class n extends r{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class a extends r{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class o extends r{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}(g=y||(y={})).Any="any",g.ApNortheast1="ap-northeast-1",g.ApNortheast2="ap-northeast-2",g.ApSouth1="ap-south-1",g.ApSoutheast1="ap-southeast-1",g.ApSoutheast2="ap-southeast-2",g.CaCentral1="ca-central-1",g.EuCentral1="eu-central-1",g.EuWest1="eu-west-1",g.EuWest2="eu-west-2",g.EuWest3="eu-west-3",g.SaEast1="sa-east-1",g.UsEast1="us-east-1",g.UsWest1="us-west-1",g.UsWest2="us-west-2";class l{constructor(e,{headers:t={},customFetch:s,region:r=y.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=i(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s,i,r,l,h;return i=this,r=void 0,l=void 0,h=function*(){try{let i;let{headers:r,method:l,body:h}=t,c={},{region:u}=t;u||(u=this.region),u&&"any"!==u&&(c["x-region"]=u),h&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!=typeof Blob&&h instanceof Blob||h instanceof ArrayBuffer?(c["Content-Type"]="application/octet-stream",i=h):"string"==typeof h?(c["Content-Type"]="text/plain",i=h):"undefined"!=typeof FormData&&h instanceof FormData?i=h:(c["Content-Type"]="application/json",i=JSON.stringify(h)));let d=yield this.fetch(`${this.url}/${e}`,{method:l||"POST",headers:Object.assign(Object.assign(Object.assign({},c),this.headers),r),body:i}).catch(e=>{throw new n(e)}),f=d.headers.get("x-relay-error");if(f&&"true"===f)throw new a(d);if(!d.ok)throw new o(d);let p=(null!==(s=d.headers.get("Content-Type"))&&void 0!==s?s:"text/plain").split(";")[0].trim();return{data:"application/json"===p?yield d.json():"application/octet-stream"===p?yield d.blob():"text/event-stream"===p?d:"multipart/form-data"===p?yield d.formData():yield d.text(),error:null}}catch(e){return{data:null,error:e}}},new(l||(l=Promise))(function(e,t){function s(e){try{a(h.next(e))}catch(e){t(e)}}function n(e){try{a(h.throw(e))}catch(e){t(e)}}function a(t){var i;t.done?e(t.value):((i=t.value)instanceof l?i:new l(function(e){e(i)})).then(s,n)}a((h=h.apply(i,r||[])).next())})}}let{PostgrestClient:h,PostgrestQueryBuilder:c,PostgrestFilterBuilder:u,PostgrestTransformBuilder:d,PostgrestBuilder:f,PostgrestError:p}=s(159);var g,y,v,w,m,_,b,k,S,T,j,E,O,P,$,A,C,I,R,x,U,L="undefined"==typeof window?s(7026):window.WebSocket;let D={"X-Client-Info":"realtime-js/2.11.10"};(O=v||(v={}))[O.connecting=0]="connecting",O[O.open=1]="open",O[O.closing=2]="closing",O[O.closed=3]="closed",(P=w||(w={})).closed="closed",P.errored="errored",P.joined="joined",P.joining="joining",P.leaving="leaving",($=m||(m={})).close="phx_close",$.error="phx_error",$.join="phx_join",$.reply="phx_reply",$.leave="phx_leave",$.access_token="access_token",(_||(_={})).websocket="websocket",(A=b||(b={})).Connecting="connecting",A.Open="open",A.Closing="closing",A.Closed="closed";class N{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){let i=t.getUint8(1),r=t.getUint8(2),n=this.HEADER_LENGTH+2,a=s.decode(e.slice(n,n+i));n+=i;let o=s.decode(e.slice(n,n+r));return n+=r,{ref:null,topic:a,event:o,payload:JSON.parse(s.decode(e.slice(n,e.byteLength)))}}}class q{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}(C=k||(k={})).abstime="abstime",C.bool="bool",C.date="date",C.daterange="daterange",C.float4="float4",C.float8="float8",C.int2="int2",C.int4="int4",C.int4range="int4range",C.int8="int8",C.int8range="int8range",C.json="json",C.jsonb="jsonb",C.money="money",C.numeric="numeric",C.oid="oid",C.reltime="reltime",C.text="text",C.time="time",C.timestamp="timestamp",C.timestamptz="timestamptz",C.timetz="timetz",C.tsrange="tsrange",C.tstzrange="tstzrange";let B=(e,t,s={})=>{var i;let r=null!==(i=s.skipTypes)&&void 0!==i?i:[];return Object.keys(t).reduce((s,i)=>(s[i]=M(i,e,t,r),s),{})},M=(e,t,s,i)=>{let r=t.find(t=>t.name===e),n=null==r?void 0:r.type,a=s[e];return n&&!i.includes(n)?F(n,a):z(a)},F=(e,t)=>{if("_"===e.charAt(0))return H(t,e.slice(1,e.length));switch(e){case k.bool:return J(t);case k.float4:case k.float8:case k.int2:case k.int4:case k.int8:case k.numeric:case k.oid:return K(t);case k.json:case k.jsonb:return W(t);case k.timestamp:return G(t);case k.abstime:case k.date:case k.daterange:case k.int4range:case k.int8range:case k.money:case k.reltime:case k.text:case k.time:case k.timestamptz:case k.timetz:case k.tsrange:case k.tstzrange:default:return z(t)}},z=e=>e,J=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},K=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},W=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},H=(e,t)=>{if("string"!=typeof e)return e;let s=e.length-1,i=e[s];if("{"===e[0]&&"}"===i){let i;let r=e.slice(1,s);try{i=JSON.parse("["+r+"]")}catch(e){i=r?r.split(","):[]}return i.map(e=>F(t,e))}return e},G=e=>"string"==typeof e?e.replace(" ","T"):e,V=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class Y{constructor(e,t,s={},i=1e4){this.channel=e,this.event=t,this.payload=s,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t(null===(s=this.receivedResp)||void 0===s?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}(I=S||(S={})).SYNC="sync",I.JOIN="join",I.LEAVE="leave";class X{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let s=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},e=>{let{onJoin:t,onLeave:s,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=X.syncState(this.state,e,t,s),this.pendingDiffs.forEach(e=>{this.state=X.syncDiff(this.state,e,t,s)}),this.pendingDiffs=[],i()}),this.channel._on(s.diff,{},e=>{let{onJoin:t,onLeave:s,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=X.syncDiff(this.state,e,t,s),i())}),this.onJoin((e,t,s)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:s})}),this.onLeave((e,t,s)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:s})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,i){let r=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(r,(e,t)=>{n[e]||(o[e]=t)}),this.map(n,(e,t)=>{let s=r[e];if(s){let i=t.map(e=>e.presence_ref),r=s.map(e=>e.presence_ref),n=t.filter(e=>0>r.indexOf(e.presence_ref)),l=s.filter(e=>0>i.indexOf(e.presence_ref));n.length>0&&(a[e]=n),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(r,{joins:a,leaves:o},s,i)}static syncDiff(e,t,s,i){let{joins:r,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),i||(i=()=>{}),this.map(r,(t,i)=>{var r;let n=null!==(r=e[t])&&void 0!==r?r:[];if(e[t]=this.cloneDeep(i),n.length>0){let s=e[t].map(e=>e.presence_ref),i=n.filter(e=>0>s.indexOf(e.presence_ref));e[t].unshift(...i)}s(t,n,i)}),this.map(n,(t,s)=>{let r=e[t];if(!r)return;let n=s.map(e=>e.presence_ref);r=r.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=r,i(t,r,s),0===r.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,s)=>{let i=e[s];return"metas"in i?t[s]=i.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[s]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(R=T||(T={})).ALL="*",R.INSERT="INSERT",R.UPDATE="UPDATE",R.DELETE="DELETE",(x=j||(j={})).BROADCAST="broadcast",x.PRESENCE="presence",x.POSTGRES_CHANGES="postgres_changes",x.SYSTEM="system",(U=E||(E={})).SUBSCRIBED="SUBSCRIBED",U.TIMED_OUT="TIMED_OUT",U.CLOSED="CLOSED",U.CHANNEL_ERROR="CHANNEL_ERROR";class Q{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=w.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new Y(this,m.join,this.params,this.timeout),this.rejoinTimer=new q(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=w.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=w.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=w.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=w.errored,this.rejoinTimer.scheduleTimeout())}),this._on(m.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new X(this),this.broadcastEndpointURL=V(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,i;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:r,presence:n,private:a}}=this.params;this._onError(t=>null==e?void 0:e(E.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(E.CLOSED));let o={},l={broadcast:r,presence:n,postgres_changes:null!==(i=null===(s=this.bindings.postgres_changes)||void 0===s?void 0:s.map(e=>e.filter))&&void 0!==i?i:[],private:a};this.socket.accessTokenValue&&(o.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},o)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var s;if(this.socket.setAuth(),void 0===t){null==e||e(E.SUBSCRIBED);return}{let i=this.bindings.postgres_changes,r=null!==(s=null==i?void 0:i.length)&&void 0!==s?s:0,n=[];for(let s=0;s<r;s++){let r=i[s],{filter:{event:a,schema:o,table:l,filter:h}}=r,c=t&&t[s];if(c&&c.event===a&&c.schema===o&&c.table===l&&c.filter===h)n.push(Object.assign(Object.assign({},r),{id:c.id}));else{this.unsubscribe(),this.state=w.errored,null==e||e(E.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(E.SUBSCRIBED);return}}).receive("error",t=>{this.state=w.errored,null==e||e(E.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(E.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e,t={}){var s,i;if(this._canPush()||"broadcast"!==e.type)return new Promise(s=>{var i,r,n;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(n=null===(r=null===(i=this.params)||void 0===i?void 0:i.config)||void 0===r?void 0:r.broadcast)||void 0===n?void 0:n.ack)||s("ok"),a.receive("ok",()=>s("ok")),a.receive("error",()=>s("error")),a.receive("timeout",()=>s("timed out"))});{let{event:r,payload:n}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:r,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(s=t.timeout)&&void 0!==s?s:this.timeout);return await (null===(i=e.body)||void 0===i?void 0:i.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=w.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(m.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(s=>{let i=new Y(this,m.leave,{},e);i.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),i.send(),this._canPush()||i.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,s){let i=new AbortController,r=setTimeout(()=>i.abort(),s),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:i.signal}));return clearTimeout(r),n}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new Y(this,e,t,s);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var i,r;let n=e.toLocaleLowerCase(),{close:a,error:o,leave:l,join:h}=m;if(s&&[a,o,l,h].indexOf(n)>=0&&s!==this._joinRef())return;let c=this._onMessage(n,t,s);if(t&&!c)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null===(i=this.bindings.postgres_changes)||void 0===i||i.filter(e=>{var t,s,i;return(null===(t=e.filter)||void 0===t?void 0:t.event)==="*"||(null===(i=null===(s=e.filter)||void 0===s?void 0:s.event)||void 0===i?void 0:i.toLocaleLowerCase())===n}).map(e=>e.callback(c,s)):null===(r=this.bindings[n])||void 0===r||r.filter(e=>{var s,i,r,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,a=null===(s=e.filter)||void 0===s?void 0:s.event;return n&&(null===(i=t.ids)||void 0===i?void 0:i.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null===(r=t.data)||void 0===r?void 0:r.type.toLocaleLowerCase()))}{let s=null===(o=null===(a=null==e?void 0:e.filter)||void 0===a?void 0:a.event)||void 0===o?void 0:o.toLocaleLowerCase();return"*"===s||s===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof c&&"ids"in c){let e=c.data,{schema:t,table:s,commit_timestamp:i,type:r,errors:n}=e;c=Object.assign(Object.assign({},{schema:t,table:s,commit_timestamp:i,eventType:r,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(c,s)})}_isClosed(){return this.state===w.closed}_isJoined(){return this.state===w.joined}_isJoining(){return this.state===w.joining}_isLeaving(){return this.state===w.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){let i=e.toLocaleLowerCase(),r={type:i,filter:t,callback:s};return this.bindings[i]?this.bindings[i].push(r):this.bindings[i]=[r],this}_off(e,t){let s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(e=>{var i;return!((null===(i=e.type)||void 0===i?void 0:i.toLocaleLowerCase())===s&&Q.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(m.close,{},e)}_onError(e){this._on(m.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=w.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=B(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=B(e.columns,e.old_record)),t}}let Z=()=>{},ee=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class et{constructor(e,t){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=D,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Z,this.ref=0,this.logger=Z,this.conn=null,this.sendBuffer=[],this.serializer=new N,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(s.bind(s,9743)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},this.endPoint=`${e}/${_.websocket}`,this.httpEndpoint=V(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let r=null===(i=null==t?void 0:t.params)||void 0===i?void 0:i.apikey;if(r&&(this.accessTokenValue=r,this.apiKey=r),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new q(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=L),this.transport){"undefined"!=typeof window&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new es(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return this.channels=this.channels.filter(t=>t._joinRef!==e._joinRef),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case v.connecting:return b.Connecting;case v.open:return b.Open;case v.closing:return b.Closing;default:return b.Closed}}isConnected(){return this.connectionState()===b.Open}channel(e,t={config:{}}){let s=`realtime:${e}`,i=this.getChannels().find(e=>e.topic===s);if(i)return i;{let s=new Q(`realtime:${e}`,t,this);return this.channels.push(s),s}}push(e){let{topic:t,event:s,payload:i,ref:r}=e,n=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${s} (${r})`,i),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),e.joinedOnce&&e._isJoined()&&e._push(m.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:s,payload:i,ref:r}=e;"phoenix"===t&&"phx_reply"===s&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),r&&r===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${t} ${s} ${r&&"("+r+")"||""}`,i),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(s,i,r)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(m.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let s=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${s}${i}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([ee],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class es{constructor(e,t,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=v.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=s.close}}class ei extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function er(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class en extends ei{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class ea extends ei{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let eo=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(s.bind(s,9743)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},el=()=>{var e,t,i,r;return e=void 0,t=void 0,i=void 0,r=function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(s.bind(s,9743))).Response:Response},new(i||(i=Promise))(function(s,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof i?t:new i(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})},eh=e=>{if(Array.isArray(e))return e.map(e=>eh(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,s])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=eh(s)}),t};var ec=function(e,t,s,i){return new(s||(s=Promise))(function(r,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let eu=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ed=(e,t,s)=>ec(void 0,void 0,void 0,function*(){e instanceof(yield el())&&!(null==s?void 0:s.noResolveJson)?e.json().then(s=>{t(new en(eu(s),e.status||500))}).catch(e=>{t(new ea(eu(e),e))}):t(new ea(eu(e),e))}),ef=(e,t,s,i)=>{let r={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?r:(r.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),i&&(r.body=JSON.stringify(i)),Object.assign(Object.assign({},r),s))};function ep(e,t,s,i,r,n){return ec(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(s,ef(t,i,r,n)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>ed(e,o,i))})})}function eg(e,t,s,i){return ec(this,void 0,void 0,function*(){return ep(e,"GET",t,s,i)})}function ey(e,t,s,i,r){return ec(this,void 0,void 0,function*(){return ep(e,"POST",t,i,r,s)})}function ev(e,t,s,i,r){return ec(this,void 0,void 0,function*(){return ep(e,"DELETE",t,i,r,s)})}var ew=s(8764).lW,em=function(e,t,s,i){return new(s||(s=Promise))(function(r,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let e_={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},eb={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class ek{constructor(e,t={},s,i){this.url=e,this.headers=t,this.bucketId=s,this.fetch=eo(i)}uploadOrUpdate(e,t,s,i){return em(this,void 0,void 0,function*(){try{let r;let n=Object.assign(Object.assign({},eb),i),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),o=n.metadata;"undefined"!=typeof Blob&&s instanceof Blob?((r=new FormData).append("cacheControl",n.cacheControl),o&&r.append("metadata",this.encodeMetadata(o)),r.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?((r=s).append("cacheControl",n.cacheControl),o&&r.append("metadata",this.encodeMetadata(o))):(r=s,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==i?void 0:i.headers)&&(a=Object.assign(Object.assign({},a),i.headers));let l=this._removeEmptyFolders(t),h=this._getFinalPath(l),c=yield this.fetch(`${this.url}/object/${h}`,Object.assign({method:e,body:r,headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),u=yield c.json();if(c.ok)return{data:{path:l,id:u.Id,fullPath:u.Key},error:null};return{data:null,error:u}}catch(e){if(er(e))return{data:null,error:e};throw e}})}upload(e,t,s){return em(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,i){return em(this,void 0,void 0,function*(){let r=this._removeEmptyFolders(e),n=this._getFinalPath(r),a=new URL(this.url+`/object/upload/sign/${n}`);a.searchParams.set("token",t);try{let e;let t=Object.assign({upsert:eb.upsert},i),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&s instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(e=s).append("cacheControl",t.cacheControl):(e=s,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),l=yield o.json();if(o.ok)return{data:{path:r,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(er(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return em(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),i=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(i["x-upsert"]="true");let r=yield ey(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:i}),n=new URL(this.url+r.url),a=n.searchParams.get("token");if(!a)throw new ei("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:a},error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}update(e,t,s){return em(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return em(this,void 0,void 0,function*(){try{return{data:yield ey(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}copy(e,t,s){return em(this,void 0,void 0,function*(){try{return{data:{path:(yield ey(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,s){return em(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),r=yield ey(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},(null==s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers}),n=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:r={signedUrl:encodeURI(`${this.url}${r.signedURL}${n}`)},error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,s){return em(this,void 0,void 0,function*(){try{let i=yield ey(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),r=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:i.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${r}`):null})),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}download(e,t){return em(this,void 0,void 0,function*(){let s=void 0!==(null==t?void 0:t.transform),i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),r=i?`?${i}`:"";try{let t=this._getFinalPath(e),i=yield eg(this.fetch,`${this.url}/${s?"render/image/authenticated":"object"}/${t}${r}`,{headers:this.headers,noResolveJson:!0});return{data:yield i.blob(),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}info(e){return em(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield eg(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:eh(e),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}exists(e){return em(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,s,i){return ec(this,void 0,void 0,function*(){return ep(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(er(e)&&e instanceof ea){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let s=this._getFinalPath(e),i=[],r=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==r&&i.push(r);let n=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&i.push(a);let o=i.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${s}${o}`)}}}remove(e){return em(this,void 0,void 0,function*(){try{return{data:yield ev(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}list(e,t,s){return em(this,void 0,void 0,function*(){try{let i=Object.assign(Object.assign(Object.assign({},e_),t),{prefix:e||""});return{data:yield ey(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},s),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==ew?ew.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let eS={"X-Client-Info":"storage-js/2.7.1"};var eT=function(e,t,s,i){return new(s||(s=Promise))(function(r,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};class ej{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},eS),t),this.fetch=eo(s)}listBuckets(){return eT(this,void 0,void 0,function*(){try{return{data:yield eg(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}getBucket(e){return eT(this,void 0,void 0,function*(){try{return{data:yield eg(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return eT(this,void 0,void 0,function*(){try{return{data:yield ey(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return eT(this,void 0,void 0,function*(){try{return{data:yield function(e,t,s,i,r){return ec(this,void 0,void 0,function*(){return ep(e,"PUT",t,i,void 0,s)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}emptyBucket(e){return eT(this,void 0,void 0,function*(){try{return{data:yield ey(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}deleteBucket(e){return eT(this,void 0,void 0,function*(){try{return{data:yield ev(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(er(e))return{data:null,error:e};throw e}})}}class eE extends ej{constructor(e,t={},s){super(e,t,s)}from(e){return new ek(this.url,this.headers,e,this.fetch)}}let eO="";"undefined"!=typeof Deno?eO="deno":"undefined"!=typeof document?eO="web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?eO="react-native":eO="node";let eP={headers:{"X-Client-Info":`supabase-js-${eO}/2.50.0`}},e$={schema:"public"},eA={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},eC={};var eI=s(9743);let eR=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=eI.default:t=fetch,(...e)=>t(...e)},ex=()=>"undefined"==typeof Headers?eI.Headers:Headers,eU=(e,t,s)=>{let i=eR(s),r=ex();return(s,n)=>{var a,o,l,h;return a=void 0,o=void 0,l=void 0,h=function*(){var a;let o=null!==(a=yield t())&&void 0!==a?a:e,l=new r(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),i(s,Object.assign(Object.assign({},n),{headers:l}))},new(l||(l=Promise))(function(e,t){function s(e){try{r(h.next(e))}catch(e){t(e)}}function i(e){try{r(h.throw(e))}catch(e){t(e)}}function r(t){var r;t.done?e(t.value):((r=t.value)instanceof l?r:new l(function(e){e(r)})).then(s,i)}r((h=h.apply(a,o||[])).next())})}},eL="2.70.0",eD={"X-Client-Info":`gotrue-js/${eL}`},eN="X-Supabase-Api-Version",eq={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},eB=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class eM extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function eF(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class ez extends eM{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}class eJ extends eM{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class eK extends eM{constructor(e,t,s,i){super(e,s,i),this.name=t,this.status=s}}class eW extends eK{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class eH extends eK{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class eG extends eK{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class eV extends eK{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eY extends eK{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eX extends eK{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function eQ(e){return eF(e)&&"AuthRetryableFetchError"===e.name}class eZ extends eK{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class e0 extends eK{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let e1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),e2=" 	\n\r=".split(""),e4=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<e2.length;t+=1)e[e2[t].charCodeAt(0)]=-2;for(let t=0;t<e1.length;t+=1)e[e1[t].charCodeAt(0)]=t;return e})();function e8(e,t,s){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)s(e1[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)s(e1[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function e3(e,t,s){let i=e4[e];if(i>-1)for(t.queue=t.queue<<6|i,t.queuedBits+=6;t.queuedBits>=8;)s(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===i)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function e6(e){let t=[],s=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},r={queue:0,queuedBits:0},n=e=>{!function(e,t,s){if(0===t.utf8seq){if(e<=127){s(e);return}for(let s=1;s<6;s+=1)if((e>>7-s&1)==0){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}}(e,i,s)};for(let t=0;t<e.length;t+=1)e3(e.charCodeAt(t),r,n);return t.join("")}let e5=()=>"undefined"!=typeof window&&"undefined"!=typeof document,e9={tested:!1,writable:!1},e7=()=>{if(!e5())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(e9.tested)return e9.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),e9.tested=!0,e9.writable=!0}catch(e){e9.tested=!0,e9.writable=!1}return e9.writable},te=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(s.bind(s,9743)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},tt=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,ts=async(e,t,s)=>{await e.setItem(t,JSON.stringify(s))},ti=async(e,t)=>{let s=await e.getItem(t);if(!s)return null;try{return JSON.parse(s)}catch(e){return s}},tr=async(e,t)=>{await e.removeItem(t)};class tn{constructor(){this.promise=new tn.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function ta(e){let t=e.split(".");if(3!==t.length)throw new e0("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!eB.test(t[e]))throw new e0("JWT not in base64url format");return{header:JSON.parse(e6(t[0])),payload:JSON.parse(e6(t[1])),signature:function(e){let t=[],s={queue:0,queuedBits:0},i=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)e3(e.charCodeAt(t),s,i);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function to(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function tl(e){return("0"+e.toString(16)).substr(-2)}async function th(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function tc(e){return"undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder?btoa(await th(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e)}async function tu(e,t,s=!1){let i=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,s="";for(let i=0;i<56;i++)s+=e.charAt(Math.floor(Math.random()*t));return s}return crypto.getRandomValues(e),Array.from(e,tl).join("")}(),r=i;s&&(r+="/PASSWORD_RECOVERY"),await ts(e,`${t}-code-verifier`,r);let n=await tc(i),a=i===n?"plain":"s256";return[n,a]}tn.promiseConstructor=Promise;let td=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,tf=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function tp(e){if(!tf.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var tg=function(e,t){var s={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(s[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)0>t.indexOf(i[r])&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(s[i[r]]=e[i[r]]);return s};let ty=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),tv=[502,503,504];async function tw(e){var t;let s,i;if(!tt(e))throw new eX(ty(e),0);if(tv.includes(e.status))throw new eX(ty(e),e.status);try{s=await e.json()}catch(e){throw new eJ(ty(e),e)}let r=function(e){let t=e.headers.get(eN);if(!t||!t.match(td))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(r&&r.getTime()>=eq["2024-01-01"].timestamp&&"object"==typeof s&&s&&"string"==typeof s.code?i=s.code:"object"==typeof s&&s&&"string"==typeof s.error_code&&(i=s.error_code),i){if("weak_password"===i)throw new eZ(ty(s),e.status,(null===(t=s.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===i)throw new eW}else if("object"==typeof s&&s&&"object"==typeof s.weak_password&&s.weak_password&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new eZ(ty(s),e.status,s.weak_password.reasons);throw new ez(ty(s),e.status||500,i)}let tm=(e,t,s,i)=>{let r={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?r:(r.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),r.body=JSON.stringify(i),Object.assign(Object.assign({},r),s))};async function t_(e,t,s,i){var r;let n=Object.assign({},null==i?void 0:i.headers);n[eN]||(n[eN]=eq["2024-01-01"].name),(null==i?void 0:i.jwt)&&(n.Authorization=`Bearer ${i.jwt}`);let a=null!==(r=null==i?void 0:i.query)&&void 0!==r?r:{};(null==i?void 0:i.redirectTo)&&(a.redirect_to=i.redirectTo);let o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await tb(e,t,s+o,{headers:n,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(l):{data:Object.assign({},l),error:null}}async function tb(e,t,s,i,r,n){let a;let o=tm(t,i,r,n);try{a=await e(s,Object.assign({},o))}catch(e){throw console.error(e),new eX(ty(e),0)}if(a.ok||await tw(a),null==i?void 0:i.noResolveJson)return a;try{return await a.json()}catch(e){await tw(e)}}function tk(e){var t,s;let i=null;return e.access_token&&e.refresh_token&&e.expires_in&&(i=Object.assign({},e),!e.expires_at)&&(i.expires_at=(s=e.expires_in,Math.round(Date.now()/1e3)+s)),{data:{session:i,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function tS(e){let t=tk(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function tT(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function tj(e){return{data:e,error:null}}function tE(e){let{action_link:t,email_otp:s,hashed_token:i,redirect_to:r,verification_type:n}=e;return{data:{properties:{action_link:t,email_otp:s,hashed_token:i,redirect_to:r,verification_type:n},user:Object.assign({},tg(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function tO(e){return e}let tP=["global","local","others"];var t$=function(e,t){var s={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(s[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)0>t.indexOf(i[r])&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(s[i[r]]=e[i[r]]);return s};class tA{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=te(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=tP[0]){if(0>tP.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${tP.join(", ")}`);try{return await t_(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(eF(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await t_(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:tT})}catch(e){if(eF(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,s=t$(e,["options"]),i=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(i.new_email=null==s?void 0:s.newEmail,delete i.newEmail),await t_(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:tE,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(eF(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await t_(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:tT})}catch(e){if(eF(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,s,i,r,n,a,o;try{let l={nextPage:null,lastPage:0,total:0},h=await t_(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(s=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==s?s:"",per_page:null!==(r=null===(i=null==e?void 0:e.perPage)||void 0===i?void 0:i.toString())&&void 0!==r?r:""},xform:tO});if(h.error)throw h.error;let c=await h.json(),u=null!==(n=h.headers.get("x-total-count"))&&void 0!==n?n:0,d=null!==(o=null===(a=h.headers.get("link"))||void 0===a?void 0:a.split(","))&&void 0!==o?o:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),s=JSON.parse(e.split(";")[1].split("=")[1]);l[`${s}Page`]=t}),l.total=parseInt(u)),{data:Object.assign(Object.assign({},c),l),error:null}}catch(e){if(eF(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){tp(e);try{return await t_(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:tT})}catch(e){if(eF(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){tp(e);try{return await t_(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:tT})}catch(e){if(eF(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){tp(e);try{return await t_(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:tT})}catch(e){if(eF(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){tp(e.userId);try{let{data:t,error:s}=await t_(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:s}}catch(e){if(eF(e))return{data:null,error:e};throw e}}async _deleteFactor(e){tp(e.userId),tp(e.id);try{return{data:await t_(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(eF(e))return{data:null,error:e};throw e}}}let tC={getItem:e=>e7()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{e7()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{e7()&&globalThis.localStorage.removeItem(e)}};function tI(e={}){return{getItem:t=>e[t]||null,setItem:(t,s)=>{e[t]=s},removeItem:t=>{delete e[t]}}}let tR={debug:!!(globalThis&&e7()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class tx extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class tU extends tx{}async function tL(e,t,s){tR.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let i=new globalThis.AbortController;return t>0&&setTimeout(()=>{i.abort(),tR.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){tR.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await s()}finally{tR.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(0===t)throw tR.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new tU(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(tR.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await s()}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();let tD={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:eD,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function tN(e,t,s){return await s()}class tq{constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=tq.nextInstanceID,tq.nextInstanceID+=1,this.instanceID>0&&e5()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let i=Object.assign(Object.assign({},tD),e);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new tA({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=te(i.fetch),this.lock=i.lock||tN,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:e5()&&(null===(t=null==globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=tL:this.lock=tN,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:e7()?this.storage=tC:(this.memoryStorage={},this.storage=tI(this.memoryStorage)):(this.memoryStorage={},this.storage=tI(this.memoryStorage)),e5()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null===(s=this.broadcastChannel)||void 0===s||s.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${eL}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},s=new URL(e);if(s.hash&&"#"===s.hash[0])try{new URLSearchParams(s.hash.substring(1)).forEach((e,s)=>{t[s]=e})}catch(e){}return s.searchParams.forEach((e,s)=>{t[s]=e}),t}(window.location.href),s="none";if(this._isImplicitGrantCallback(t)?s="implicit":await this._isPKCECallback(t)&&(s="pkce"),e5()&&this.detectSessionInUrl&&"none"!==s){let{data:i,error:r}=await this._getSessionFromURL(t,s);if(r){if(this._debug("#_initialize()","error detecting session from URL",r),eF(r)&&"AuthImplicitGrantRedirectError"===r.name){let t=null===(e=r.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:r}}return await this._removeSession(),{error:r}}let{session:n,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",n,"redirect type",a),await this._saveSession(n),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(eF(e))return{error:e};return{error:new eJ("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,s,i;try{let{data:r,error:n}=await t_(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(s=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==s?s:{},gotrue_meta_security:{captcha_token:null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken}},xform:tk});if(n||!r)return{data:{user:null,session:null},error:n};let a=r.session,o=r.user;return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:o,session:a},error:null}}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,s,i;try{let r;if("email"in e){let{email:s,password:i,options:n}=e,a=null,o=null;"pkce"===this.flowType&&([a,o]=await tu(this.storage,this.storageKey)),r=await t_(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:s,password:i,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:a,code_challenge_method:o},xform:tk})}else if("phone"in e){let{phone:t,password:n,options:a}=e;r=await t_(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!==(s=null==a?void 0:a.data)&&void 0!==s?s:{},channel:null!==(i=null==a?void 0:a.channel)&&void 0!==i?i:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:tk})}else throw new eG("You must provide either an email or phone number and a password");let{data:n,error:a}=r;if(a||!n)return{data:{user:null,session:null},error:a};let o=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:s,password:i,options:r}=e;t=await t_(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken}},xform:tS})}else if("phone"in e){let{phone:s,password:i,options:r}=e;t=await t_(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken}},xform:tS})}else throw new eG("You must provide either an email or phone number and a password");let{data:s,error:i}=t;if(i)return{data:{user:null,session:null},error:i};if(!s||!s.session||!s.user)return{data:{user:null,session:null},error:new eH};return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:i}}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,s,i,r;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(i=e.options)||void 0===i?void 0:i.queryParams,skipBrowserRedirect:null===(r=e.options)||void 0===r?void 0:r.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,s,i,r,n,a,o,l,h,c,u,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{let u;let{chain:d,wallet:g,statement:y,options:v}=e;if(e5()){if("object"==typeof g)u=g;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))u=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}}else{if("object"!=typeof g||!(null==v?void 0:v.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");u=g}let w=new URL(null!==(t=null==v?void 0:v.url)&&void 0!==t?t:window.location.href);if("signIn"in u&&u.signIn){let e;let t=await u.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==v?void 0:v.signInWithSolana),{version:"1",domain:w.host,uri:w.href}),y?{statement:y}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)f="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),p=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in u)||"function"!=typeof u.signMessage||!("publicKey"in u)||"object"!=typeof u||!u.publicKey||!("toBase58"in u.publicKey)||"function"!=typeof u.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${w.host} wants you to sign in with your Solana account:`,u.publicKey.toBase58(),...y?["",y,""]:[""],"Version: 1",`URI: ${w.href}`,`Issued At: ${null!==(i=null===(s=null==v?void 0:v.signInWithSolana)||void 0===s?void 0:s.issuedAt)&&void 0!==i?i:new Date().toISOString()}`,...(null===(r=null==v?void 0:v.signInWithSolana)||void 0===r?void 0:r.notBefore)?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...(null===(n=null==v?void 0:v.signInWithSolana)||void 0===n?void 0:n.expirationTime)?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...(null===(a=null==v?void 0:v.signInWithSolana)||void 0===a?void 0:a.chainId)?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...(null===(o=null==v?void 0:v.signInWithSolana)||void 0===o?void 0:o.nonce)?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...(null===(l=null==v?void 0:v.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...(null===(c=null===(h=null==v?void 0:v.signInWithSolana)||void 0===h?void 0:h.resources)||void 0===c?void 0:c.length)?["Resources",...v.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await u.signMessage(new TextEncoder().encode(f),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{let{data:t,error:s}=await t_(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:function(e){let t=[],s={queue:0,queuedBits:0},i=e=>{t.push(e)};return e.forEach(e=>e8(e,s,i)),e8(null,s,i),t.join("")}(p)},(null===(u=e.options)||void 0===u?void 0:u.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:tk});if(s)throw s;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new eH};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:s}}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await ti(this.storage,`${this.storageKey}-code-verifier`),[s,i]=(null!=t?t:"").split("/");try{let{data:t,error:r}=await t_(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:tk});if(await tr(this.storage,`${this.storageKey}-code-verifier`),r)throw r;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new eH};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=i?i:null}),error:r}}catch(e){if(eF(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:s,token:i,access_token:r,nonce:n}=e,{data:a,error:o}=await t_(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:i,access_token:r,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:tk});if(o)return{data:{user:null,session:null},error:o};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new eH};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:o}}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,s,i,r,n;try{if("email"in e){let{email:i,options:r}=e,n=null,a=null;"pkce"===this.flowType&&([n,a]=await tu(this.storage,this.storageKey));let{error:o}=await t_(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:i,data:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:{},create_user:null===(s=null==r?void 0:r.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},code_challenge:n,code_challenge_method:a},redirectTo:null==r?void 0:r.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:s}=e,{data:a,error:o}=await t_(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(i=null==s?void 0:s.data)&&void 0!==i?i:{},create_user:null===(r=null==s?void 0:s.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},channel:null!==(n=null==s?void 0:s.channel)&&void 0!==n?n:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new eG("You must provide either an email or phone number.")}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,s;try{let i,r;"options"in e&&(i=null===(t=e.options)||void 0===t?void 0:t.redirectTo,r=null===(s=e.options)||void 0===s?void 0:s.captchaToken);let{data:n,error:a}=await t_(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:r}}),redirectTo:i,xform:tk});if(a)throw a;if(!n)throw Error("An error occurred on token verification.");let o=n.session,l=n.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,s,i;try{let r=null,n=null;return"pkce"===this.flowType&&([r,n]=await tu(this.storage,this.storageKey)),await t_(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==s?s:void 0}),(null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:r,code_challenge_method:n}),headers:this.headers,xform:tj})}catch(e){if(eF(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new eW;let{error:i}=await t_(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:i}})}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:s,type:i,options:r}=e,{error:n}=await t_(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken}},redirectTo:null==r?void 0:r.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){let{phone:s,type:i,options:r}=e,{data:n,error:a}=await t_(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:a}}throw new eG("You must provide either an email or phone number and a type")}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch(e){}})()),s}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await ti(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let s=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,s,i)=>(t||"user"!==s||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,s,i))})}return{data:{session:e},error:null}}let{session:i,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{session:null},error:r};return{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await t_(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:tT});return await this._useSession(async e=>{var t,s,i;let{data:r,error:n}=e;if(n)throw n;return(null===(t=r.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await t_(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(i=null===(s=r.session)||void 0===s?void 0:s.access_token)&&void 0!==i?i:void 0,xform:tT}):{data:{user:null},error:new eW}})}catch(e){if(eF(e))return eF(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await tr(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async s=>{let{data:i,error:r}=s;if(r)throw r;if(!i.session)throw new eW;let n=i.session,a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await tu(this.storage,this.storageKey));let{data:l,error:h}=await t_(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:n.access_token,xform:tT});if(h)throw h;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}})}catch(e){if(eF(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new eW;let t=Date.now()/1e3,s=t,i=!0,r=null,{payload:n}=ta(e.access_token);if(n.exp&&(i=(s=n.exp)<=t),i){let{session:t,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{user:null,session:null},error:s};if(!t)return{data:{user:null,session:null},error:null};r=t}else{let{data:i,error:n}=await this._getUser(e.access_token);if(n)throw n;r={access_token:e.access_token,refresh_token:e.refresh_token,user:i.user,token_type:"bearer",expires_in:s-t,expires_at:s},await this._saveSession(r),await this._notifyAllSubscribers("SIGNED_IN",r)}return{data:{user:r.user,session:r},error:null}}catch(e){if(eF(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var s;if(!e){let{data:i,error:r}=t;if(r)throw r;e=null!==(s=i.session)&&void 0!==s?s:void 0}if(!(null==e?void 0:e.refresh_token))throw new eW;let{session:i,error:r}=await this._callRefreshToken(e.refresh_token);return r?{data:{user:null,session:null},error:r}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(eF(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!e5())throw new eV("No browser detected.");if(e.error||e.error_description||e.error_code)throw new eV(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new eY("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new eV("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new eY("No code detected.");let{data:t,error:s}=await this._exchangeCodeForSession(e.code);if(s)throw s;let i=new URL(window.location.href);return i.searchParams.delete("code"),window.history.replaceState(window.history.state,"",i.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:s,provider_refresh_token:i,access_token:r,refresh_token:n,expires_in:a,expires_at:o,token_type:l}=e;if(!r||!a||!n||!l)throw new eV("No session defined in URL");let h=Math.round(Date.now()/1e3),c=parseInt(a),u=h+c;o&&(u=parseInt(o));let d=u-h;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${c}s`);let f=u-c;h-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,u,h):h-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,u,h);let{data:p,error:g}=await this._getUser(r);if(g)throw g;let y={provider_token:s,provider_refresh_token:i,access_token:r,expires_in:c,expires_at:u,refresh_token:n,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:y,redirectType:e.type},error:null}}catch(e){if(eF(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await ti(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var s;let{data:i,error:r}=t;if(r)return{error:r};let n=null===(s=i.session)||void 0===s?void 0:s.access_token;if(n){let{error:t}=await this.admin.signOut(n,e);if(t&&!(eF(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await tr(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:s}}}async _emitInitialSession(e){return await this._useSession(async t=>{var s,i;try{let{data:{session:i},error:r}=t;if(r)throw r;await (null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(t){await (null===(i=this.stateChangeEmitters.get(e))||void 0===i?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let s=null,i=null;"pkce"===this.flowType&&([s,i]=await tu(this.storage,this.storageKey,!0));try{return await t_(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:s,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(eF(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:s}=await this.getUser();if(s)throw s;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(e){if(eF(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:s,error:i}=await this._useSession(async t=>{var s,i,r,n,a;let{data:o,error:l}=t;if(l)throw l;let h=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(s=e.options)||void 0===s?void 0:s.redirectTo,scopes:null===(i=e.options)||void 0===i?void 0:i.scopes,queryParams:null===(r=e.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:!0});return await t_(this.fetch,"GET",h,{headers:this.headers,jwt:null!==(a=null===(n=o.session)||void 0===n?void 0:n.access_token)&&void 0!==a?a:void 0})});if(i)throw i;return!e5()||(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)||window.location.assign(null==s?void 0:s.url),{data:{provider:e.provider,url:null==s?void 0:s.url},error:null}}catch(t){if(eF(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var s,i;let{data:r,error:n}=t;if(n)throw n;return await t_(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(i=null===(s=r.session)||void 0===s?void 0:s.access_token)&&void 0!==i?i:void 0})})}catch(e){if(eF(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var s,i;let r=Date.now();return await (s=async s=>(s>0&&await to(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await t_(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:tk})),i=(e,t)=>t&&eQ(t)&&Date.now()+200*Math.pow(2,e)-r<3e4,new Promise((e,t)=>{(async()=>{for(let r=0;r<1/0;r++)try{let t=await s(r);if(!i(r,null,t)){e(t);return}}catch(e){if(!i(r,e)){t(e);return}}})()}))}catch(e){if(this._debug(t,"error",e),eF(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let s=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),e5()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let s=await ti(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s)){this._debug(t,"session is not valid"),null!==s&&await this._removeSession();return}let i=(null!==(e=s.expires_at)&&void 0!==e?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&s.refresh_token){let{error:e}=await this._callRefreshToken(s.refresh_token);e&&(console.error(e),eQ(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,s;if(!e)throw new eW;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let i=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new tn;let{data:t,error:s}=await this._refreshAccessToken(e);if(s)throw s;if(!t.session)throw new eW;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let i={session:t.session,error:null};return this.refreshingDeferred.resolve(i),i}catch(e){if(this._debug(i,"error",e),eF(e)){let s={session:null,error:e};return eQ(e)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(s),s}throw null===(s=this.refreshingDeferred)||void 0===s||s.reject(e),e}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(e,t,s=!0){let i=`#_notifyAllSubscribers(${e})`;this._debug(i,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});let i=[],r=Array.from(this.stateChangeEmitters.values()).map(async s=>{try{await s.callback(e,t)}catch(e){i.push(e)}});if(await Promise.all(r),i.length>0){for(let e=0;e<i.length;e+=1)console.error(i[e]);throw i[0]}}finally{this._debug(i,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await ts(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await tr(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&e5()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:s}}=t;if(!s||!s.refresh_token||!s.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}let i=Math.floor((1e3*s.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),i<=3&&await this._callRefreshToken(s.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof tx)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!e5()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,s){let i=[`provider=${encodeURIComponent(t)}`];if((null==s?void 0:s.redirectTo)&&i.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&i.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){let[e,t]=await tu(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});i.push(s.toString())}if(null==s?void 0:s.queryParams){let e=new URLSearchParams(s.queryParams);i.push(e.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&i.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${i.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var s;let{data:i,error:r}=t;return r?{data:null,error:r}:await t_(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(s=null==i?void 0:i.session)||void 0===s?void 0:s.access_token})})}catch(e){if(eF(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var s,i;let{data:r,error:n}=t;if(n)return{data:null,error:n};let a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await t_(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(i=null==o?void 0:o.totp)||void 0===i?void 0:i.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(eF(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;let{data:i,error:r}=t;if(r)return{data:null,error:r};let{data:n,error:a}=await t_(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(s=null==i?void 0:i.session)||void 0===s?void 0:s.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:a})})}catch(e){if(eF(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;let{data:i,error:r}=t;return r?{data:null,error:r}:await t_(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(s=null==i?void 0:i.session)||void 0===s?void 0:s.access_token})})}catch(e){if(eF(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:s}=await this._challenge({factorId:e.factorId});return s?{data:null,error:s}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let s=(null==e?void 0:e.factors)||[],i=s.filter(e=>"totp"===e.factor_type&&"verified"===e.status),r=s.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:s,totp:i,phone:r},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,s;let{data:{session:i},error:r}=e;if(r)return{data:null,error:r};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:n}=ta(i.access_token),a=null;n.aal&&(a=n.aal);let o=a;return(null!==(s=null===(t=i.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==s?s:[]).length>0&&(o="aal2"),{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:n.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let s=t.keys.find(t=>t.kid===e);if(s||(s=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return s;let{data:i,error:r}=await t_(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(r)throw r;if(!i.keys||0===i.keys.length)throw new e0("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),!(s=i.keys.find(t=>t.kid===e)))throw new e0("No matching signing key found in JWKS");return s}async getClaims(e,t={keys:[]}){try{let s=e;if(!s){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};s=e.session.access_token}let{header:i,payload:r,signature:n,raw:{header:a,payload:o}}=ta(s);if(!function(e){if(!e)throw Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}(r.exp),!i.kid||"HS256"===i.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(s);if(e)throw e;return{data:{claims:r,header:i,signature:n},error:null}}let l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(i.alg),h=await this.fetchJwk(i.kid,t),c=await crypto.subtle.importKey("jwk",h,l,!0,["verify"]);if(!await crypto.subtle.verify(l,c,n,function(e){let t=[];return!function(e,t){for(let s=0;s<e.length;s+=1){let i=e.charCodeAt(s);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(s+1)-56320&65535|t)+65536,s+=1}!function(e,t){if(e<=127){t(e);return}if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${a}.${o}`)))throw new e0("Invalid JWT signature");return{data:{claims:r,header:i,signature:n},error:null}}catch(e){if(eF(e))return{data:null,error:e};throw e}}}tq.nextInstanceID=0;var tB=tq;class tM extends tB{constructor(e){super(e)}}class tF{constructor(e,t,s){var i,r,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(e.endsWith("/")?e:e+"/");this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o=`sb-${a.hostname.split(".")[0]}-auth-token`,l=function(e,t){var s,i;let{db:r,auth:n,realtime:a,global:o}=e,{db:l,auth:h,realtime:c,global:u}=t,d={db:Object.assign(Object.assign({},l),r),auth:Object.assign(Object.assign({},h),n),realtime:Object.assign(Object.assign({},c),a),global:Object.assign(Object.assign(Object.assign({},u),o),{headers:Object.assign(Object.assign({},null!==(s=null==u?void 0:u.headers)&&void 0!==s?s:{}),null!==(i=null==o?void 0:o.headers)&&void 0!==i?i:{})}),accessToken:()=>{var e,t,s,i;return e=this,t=void 0,i=function*(){return""},new(s=void 0,s=Promise)(function(r,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=s?s:{},{db:e$,realtime:eC,auth:Object.assign(Object.assign({},eA),{storageKey:o}),global:eP});this.storageKey=null!==(i=l.auth.storageKey)&&void 0!==i?i:"",this.headers=null!==(r=l.global.headers)&&void 0!==r?r:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(n=l.auth)&&void 0!==n?n:{},this.headers,l.global.fetch),this.fetch=eU(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new h(new URL("rest/v1",a).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new l(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new eE(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,s,i,r,n;return s=this,i=void 0,r=void 0,n=function*(){if(this.accessToken)return yield this.accessToken();let{data:s}=yield this.auth.getSession();return null!==(t=null===(e=s.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null},new(r||(r=Promise))(function(e,t){function a(e){try{l(n.next(e))}catch(e){t(e)}}function o(e){try{l(n.throw(e))}catch(e){t(e)}}function l(t){var s;t.done?e(t.value):((s=t.value)instanceof r?s:new r(function(e){e(s)})).then(a,o)}l((n=n.apply(s,i||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:i,storageKey:r,flowType:n,lock:a,debug:o},l,h){let c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new tM({url:this.authUrl.href,headers:Object.assign(Object.assign({},c),l),storageKey:r,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:i,flowType:n,lock:a,debug:o,fetch:h,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new et(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,s){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==s?this.changedAccessToken=s:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let tz=(e,t,s)=>new tF(e,t,s)}}]);