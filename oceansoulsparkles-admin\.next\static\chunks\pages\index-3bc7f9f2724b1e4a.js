(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{8312:function(n,r,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return e(1854)}])},1854:function(n,r,e){"use strict";e.r(r),e.d(r,{default:function(){return c}}),function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}(),function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}();var t=e(1163),o=e(9008),i=e.n(o);function c(){let n=(0,t.useRouter)();return Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(()=>{localStorage.getItem("admin-token")?n.replace("/admin/dashboard"):n.replace("/admin/login")},[n]),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}()),{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(i(),{children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("title",{children:"Ocean Soul Sparkles Admin"})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",fontFamily:"system-ui, sans-serif"},children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h2",{children:"Redirecting..."}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("p",{children:"Please wait while we redirect you to the admin portal."})]})})]})}}},function(n){n.O(0,[736,888,179],function(){return n(n.s=8312)}),_N_E=n.O()}]);