"use strict";(()=>{var e={};e.id=8264,e.ids=[8264,660],e.modules={9002:(e,i,r)=>{r.a(e,async(e,t)=>{try{r.r(i),r.d(i,{config:()=>b,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>u,getStaticProps:()=>h,reportWebVitals:()=>m,routeModule:()=>P,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>j});var s=r(7093),d=r(5244),n=r(1323),o=r(2899),a=r.n(o),l=r(6814),p=r(3508),x=e([l,p]);[l,p]=x.then?(await x)():x;let c=(0,n.l)(p,"default"),h=(0,n.l)(p,"getStaticProps"),u=(0,n.l)(p,"getStaticPaths"),g=(0,n.l)(p,"getServerSideProps"),b=(0,n.l)(p,"config"),m=(0,n.l)(p,"reportWebVitals"),j=(0,n.l)(p,"unstable_getStaticProps"),v=(0,n.l)(p,"unstable_getStaticPaths"),f=(0,n.l)(p,"unstable_getStaticParams"),S=(0,n.l)(p,"unstable_getServerProps"),y=(0,n.l)(p,"unstable_getServerSideProps"),P=new s.PagesRouteModule({definition:{kind:d.x.PAGES,page:"/admin/mobile-test",pathname:"/admin/mobile-test",bundlePath:"",filename:""},components:{App:l.default,Document:a()},userland:p});t()}catch(e){t(e)}})},3508:(e,i,r)=>{r.a(e,async(e,t)=>{try{r.r(i),r.d(i,{default:()=>x});var s=r(997),d=r(6689),n=r(968),o=r.n(n),a=r(4845),l=r(8568),p=e([a]);function x(){let{user:e}=(0,l.a)(),[i,r]=(0,d.useState)(!1),[t,n]=(0,d.useState)({width:0,height:0});return(0,s.jsxs)(a.Z,{children:[s.jsx(o(),{children:s.jsx("title",{children:"Mobile Test - Ocean Soul Sparkles Admin"})}),(0,s.jsxs)("div",{style:{padding:"20px"},children:[s.jsx("h1",{children:"Mobile Responsiveness Test"}),(0,s.jsxs)("div",{style:{background:"#f8f9fa",padding:"20px",borderRadius:"8px",marginBottom:"20px"},children:[s.jsx("h2",{children:"Screen Information"}),(0,s.jsxs)("p",{children:[s.jsx("strong",{children:"Screen Size:"})," ",t.width," x ",t.height]}),(0,s.jsxs)("p",{children:[s.jsx("strong",{children:"Is Mobile:"})," ",i?"Yes":"No"]}),(0,s.jsxs)("p",{children:[s.jsx("strong",{children:"User Agent:"})," ","N/A"]})]}),(0,s.jsxs)("div",{style:{background:"#e3f2fd",padding:"20px",borderRadius:"8px",marginBottom:"20px"},children:[s.jsx("h2",{children:"Mobile Components Status"}),(0,s.jsxs)("div",{style:{display:"grid",gap:"10px"},children:[s.jsx("div",{children:"✅ PWA Manager: Integrated"}),(0,s.jsxs)("div",{children:["✅ Mobile Bottom Navigation: ",i?"Visible":"Hidden (Desktop)"]}),s.jsx("div",{children:"✅ Mobile Hamburger Menu: Available"}),s.jsx("div",{children:"✅ Responsive Layout: Active"}),s.jsx("div",{children:"✅ Touch Gestures: Enabled"}),s.jsx("div",{children:"✅ Service Worker: Registered"})]})]}),(0,s.jsxs)("div",{style:{background:"#f3e5f5",padding:"20px",borderRadius:"8px",marginBottom:"20px"},children:[s.jsx("h2",{children:"Test Elements"}),(0,s.jsxs)("div",{style:{marginBottom:"15px"},children:[s.jsx("h3",{children:"Touch-Friendly Buttons"}),(0,s.jsxs)("div",{style:{display:"flex",gap:"10px",flexWrap:"wrap"},children:[s.jsx("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",minWidth:"44px",background:"#16213e",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},children:"Primary"}),s.jsx("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",minWidth:"44px",background:"#4CAF50",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},children:"Success"}),s.jsx("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",minWidth:"44px",background:"#f44336",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},children:"Danger"})]})]}),(0,s.jsxs)("div",{style:{marginBottom:"15px"},children:[s.jsx("h3",{children:"Responsive Grid"}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:i?"1fr":"repeat(auto-fit, minmax(200px, 1fr))",gap:"15px"},children:[(0,s.jsxs)("div",{style:{background:"#fff",padding:"15px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[s.jsx("h4",{children:"Card 1"}),s.jsx("p",{children:"This card adapts to mobile screens"})]}),(0,s.jsxs)("div",{style:{background:"#fff",padding:"15px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[s.jsx("h4",{children:"Card 2"}),s.jsx("p",{children:"Responsive design in action"})]}),(0,s.jsxs)("div",{style:{background:"#fff",padding:"15px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[s.jsx("h4",{children:"Card 3"}),s.jsx("p",{children:"Mobile-first approach"})]})]})]}),(0,s.jsxs)("div",{style:{marginBottom:"15px"},children:[s.jsx("h3",{children:"Form Elements"}),(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"10px"},children:[s.jsx("input",{type:"text",placeholder:"Touch-friendly input",style:{padding:"12px",fontSize:"16px",border:"2px solid #e0e0e0",borderRadius:"8px",minHeight:"44px"}}),(0,s.jsxs)("select",{style:{padding:"12px",fontSize:"16px",border:"2px solid #e0e0e0",borderRadius:"8px",minHeight:"44px"},children:[s.jsx("option",{children:"Select an option"}),s.jsx("option",{children:"Option 1"}),s.jsx("option",{children:"Option 2"})]}),s.jsx("textarea",{placeholder:"Touch-friendly textarea",style:{padding:"12px",fontSize:"16px",border:"2px solid #e0e0e0",borderRadius:"8px",minHeight:"100px",resize:"vertical"}})]})]})]}),(0,s.jsxs)("div",{style:{background:"#fff3e0",padding:"20px",borderRadius:"8px",marginBottom:"20px"},children:[s.jsx("h2",{children:"Mobile Navigation Test"}),s.jsx("p",{children:i?"✅ Mobile bottom navigation should be visible at the bottom of the screen":"⚠️ Switch to mobile view (width ≤ 768px) to see mobile navigation"}),s.jsx("p",{children:"The hamburger menu in the header should open the mobile menu when tapped."})]}),(0,s.jsxs)("div",{style:{background:"#e8f5e8",padding:"20px",borderRadius:"8px",marginBottom:"80px"},children:[s.jsx("h2",{children:"PWA Features Test"}),(0,s.jsxs)("div",{style:{display:"grid",gap:"10px"},children:[s.jsx("div",{children:"\uD83D\uDCF1 Install prompt should appear after 5 seconds"}),s.jsx("div",{children:"\uD83D\uDD04 Service worker caching active"}),s.jsx("div",{children:"\uD83D\uDCF6 Offline mode detection"}),s.jsx("div",{children:"\uD83D\uDD14 Push notifications available"}),s.jsx("div",{children:"\uD83D\uDCBE Background sync enabled"})]})]})]})]})}a=(p.then?(await p)():p)[0],t()}catch(e){t(e)}})},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},2048:e=>{e.exports=require("fs")},5315:e=>{e.exports=require("path")},6162:e=>{e.exports=require("stream")},1568:e=>{e.exports=require("zlib")},3590:e=>{e.exports=import("react-toastify")}};var i=require("../../webpack-runtime.js");i.C(e);var r=e=>i(i.s=e),t=i.X(0,[2899,6212,1664,7441],()=>r(9002));module.exports=t})();