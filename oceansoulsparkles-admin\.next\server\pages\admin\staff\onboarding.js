(()=>{var e={};e.id=8177,e.ids=[8177,660],e.modules={5382:e=>{e.exports={onboardingContainer:"StaffOnboarding_onboardingContainer__0gEsE",header:"StaffOnboarding_header__FQ6mN",headerLeft:"StaffOnboarding_headerLeft__D3Nfs",title:"StaffOnboarding_title__cCfXg",subtitle:"StaffOnboarding_subtitle__kPkuq",headerActions:"StaffOnboarding_headerActions__kllUR",backBtn:"StaffOnboarding_backBtn__lldWS",errorMessage:"StaffOnboarding_errorMessage__735pT",closeError:"StaffOnboarding_closeError__dFzBF",emptyState:"StaffOnboarding_emptyState__r_EgB",emptyIcon:"StaffOnboarding_emptyIcon__gK0RX",initializeBtn:"StaffOnboarding_initializeBtn__xFU4E",progressSection:"StaffOnboarding_progressSection__hVcbY",progressCard:"StaffOnboarding_progressCard__utfM0",progressBar:"StaffOnboarding_progressBar__8gPjH",progressFill:"StaffOnboarding_progressFill__qytRt",progressStats:"StaffOnboarding_progressStats__Nd_0L",filters:"StaffOnboarding_filters__dN3ph",filterGroup:"StaffOnboarding_filterGroup__Yvzux",filterSelect:"StaffOnboarding_filterSelect__dBGww",checklistContent:"StaffOnboarding_checklistContent__qPdhX",categorySection:"StaffOnboarding_categorySection__eV_fV",categoryHeader:"StaffOnboarding_categoryHeader__GlG0q",categoryIcon:"StaffOnboarding_categoryIcon__XlZGI",categoryCount:"StaffOnboarding_categoryCount__kLba9",checklistItems:"StaffOnboarding_checklistItems__TEc3i",checklistItem:"StaffOnboarding_checklistItem__zGzeh",completed:"StaffOnboarding_completed__mLyk9",itemHeader:"StaffOnboarding_itemHeader__RMK4f",checkboxLabel:"StaffOnboarding_checkboxLabel__sXZ9N",checkbox:"StaffOnboarding_checkbox__GJbK_",itemTitle:"StaffOnboarding_itemTitle__CYlBk",requiredBadge:"StaffOnboarding_requiredBadge__xQoAO",dueDate:"StaffOnboarding_dueDate__07K_M",itemDescription:"StaffOnboarding_itemDescription__OXGcF",completionInfo:"StaffOnboarding_completionInfo__FIDvn",itemNotes:"StaffOnboarding_itemNotes__tKkjM",loadingContainer:"StaffOnboarding_loadingContainer__ejKCF",loadingSpinner:"StaffOnboarding_loadingSpinner__APxxH",spin:"StaffOnboarding_spin__fxKq7"}},4423:(e,t,a)=>{"use strict";a.a(e,async(e,i)=>{try{a.r(t),a.d(t,{config:()=>h,default:()=>m,getServerSideProps:()=>p,getStaticPaths:()=>f,getStaticProps:()=>_,reportWebVitals:()=>u,routeModule:()=>k,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>x,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>b});var r=a(7093),s=a(5244),n=a(1323),o=a(2899),c=a.n(o),l=a(6814),d=a(5560),g=e([l,d]);[l,d]=g.then?(await g)():g;let m=(0,n.l)(d,"default"),_=(0,n.l)(d,"getStaticProps"),f=(0,n.l)(d,"getStaticPaths"),p=(0,n.l)(d,"getServerSideProps"),h=(0,n.l)(d,"config"),u=(0,n.l)(d,"reportWebVitals"),b=(0,n.l)(d,"unstable_getStaticProps"),S=(0,n.l)(d,"unstable_getStaticPaths"),x=(0,n.l)(d,"unstable_getStaticParams"),j=(0,n.l)(d,"unstable_getServerProps"),N=(0,n.l)(d,"unstable_getServerSideProps"),k=new r.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/admin/staff/onboarding",pathname:"/admin/staff/onboarding",bundlePath:"",filename:""},components:{App:l.default,Document:c()},userland:d});i()}catch(e){i(e)}})},5560:(e,t,a)=>{"use strict";a.a(e,async(e,i)=>{try{a.r(t),a.d(t,{default:()=>f});var r=a(997),s=a(6689),n=a(968),o=a.n(n),c=a(1163),l=a(8568),d=a(4845),g=a(5382),m=a.n(g),_=e([d]);d=(_.then?(await _)():_)[0];let p={documentation:"Documentation",training:"Training",equipment:"Equipment",access:"Access & Setup"},h={documentation:"\uD83D\uDCC4",training:"\uD83C\uDF93",equipment:"\uD83D\uDEE0️",access:"\uD83D\uDD11"};function f(){let{user:e}=(0,l.a)(),t=(0,c.useRouter)(),{staff_id:a}=t.query,[i,n]=(0,s.useState)(!0),[g,_]=(0,s.useState)(null),[f,u]=(0,s.useState)([]),[b,S]=(0,s.useState)({total:0,completed:0,required:0,completedRequired:0,completionPercentage:0,requiredCompletionPercentage:0}),[x,j]=(0,s.useState)(null),[N,k]=(0,s.useState)("all"),O=async()=>{try{n(!0);let e=await fetch(`/api/admin/staff/onboarding?staff_id=${a}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}});if(e.ok){let t=await e.json();u(t.checklist||[]),S(t.statistics||{})}else 404===e.status?u([]):_("Failed to load onboarding data")}catch(e){console.error("Error loading onboarding data:",e),_("Failed to load onboarding data")}finally{n(!1)}},y=async()=>{try{(await fetch("/api/admin/staff/onboarding",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({staff_id:a,action:"initialize"})})).ok?await O():_("Failed to initialize onboarding checklist")}catch(e){console.error("Error initializing onboarding:",e),_("Failed to initialize onboarding checklist")}},v=async(e,t)=>{try{(await fetch("/api/admin/staff/onboarding",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({staff_id:a,action:t?"uncomplete_item":"complete_item",checklist_item_id:e})})).ok?await O():_("Failed to update checklist item")}catch(e){console.error("Error updating checklist item:",e),_("Failed to update checklist item")}},C=e=>new Date(e).toLocaleDateString("en-AU",{year:"numeric",month:"short",day:"numeric"}),P=e=>e>=90?"#10b981":e>=70?"#f59e0b":"#ef4444",q=("all"===N?f:f.filter(e=>e.category===N)).reduce((e,t)=>{let a=t.category;return e[a]||(e[a]=[]),e[a].push(t),e},{});return i?r.jsx(d.Z,{children:(0,r.jsxs)("div",{className:m().loadingContainer,children:[r.jsx("div",{className:m().loadingSpinner}),r.jsx("p",{children:"Loading onboarding data..."})]})}):(0,r.jsxs)(d.Z,{children:[(0,r.jsxs)(o(),{children:[r.jsx("title",{children:"Staff Onboarding | Ocean Soul Sparkles Admin"}),r.jsx("meta",{name:"description",content:"Manage staff onboarding process and checklist"})]}),(0,r.jsxs)("div",{className:m().onboardingContainer,children:[(0,r.jsxs)("header",{className:m().header,children:[(0,r.jsxs)("div",{className:m().headerLeft,children:[r.jsx("h1",{className:m().title,children:"Staff Onboarding"}),x&&(0,r.jsxs)("p",{className:m().subtitle,children:[x.firstName," ",x.lastName," - ",x.role]})]}),r.jsx("div",{className:m().headerActions,children:r.jsx("button",{onClick:()=>t.back(),className:m().backBtn,children:"← Back to Staff"})})]}),g&&(0,r.jsxs)("div",{className:m().errorMessage,children:[g,r.jsx("button",{onClick:()=>_(null),className:m().closeError,children:"\xd7"})]}),0===f.length?(0,r.jsxs)("div",{className:m().emptyState,children:[r.jsx("div",{className:m().emptyIcon,children:"\uD83D\uDCCB"}),r.jsx("h3",{children:"No Onboarding Checklist"}),r.jsx("p",{children:"This staff member doesn't have an onboarding checklist yet."}),r.jsx("button",{onClick:y,className:m().initializeBtn,children:"Initialize Onboarding Checklist"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:m().progressSection,children:[(0,r.jsxs)("div",{className:m().progressCard,children:[r.jsx("h3",{children:"Overall Progress"}),r.jsx("div",{className:m().progressBar,children:r.jsx("div",{className:m().progressFill,style:{width:`${b.completionPercentage}%`,backgroundColor:P(b.completionPercentage)}})}),(0,r.jsxs)("div",{className:m().progressStats,children:[(0,r.jsxs)("span",{children:[b.completed," of ",b.total," completed"]}),(0,r.jsxs)("span",{children:[b.completionPercentage,"%"]})]})]}),(0,r.jsxs)("div",{className:m().progressCard,children:[r.jsx("h3",{children:"Required Items"}),r.jsx("div",{className:m().progressBar,children:r.jsx("div",{className:m().progressFill,style:{width:`${b.requiredCompletionPercentage}%`,backgroundColor:P(b.requiredCompletionPercentage)}})}),(0,r.jsxs)("div",{className:m().progressStats,children:[(0,r.jsxs)("span",{children:[b.completedRequired," of ",b.required," completed"]}),(0,r.jsxs)("span",{children:[b.requiredCompletionPercentage,"%"]})]})]})]}),r.jsx("div",{className:m().filters,children:(0,r.jsxs)("div",{className:m().filterGroup,children:[r.jsx("label",{htmlFor:"categoryFilter",children:"Filter by Category:"}),(0,r.jsxs)("select",{id:"categoryFilter",value:N,onChange:e=>k(e.target.value),className:m().filterSelect,children:[r.jsx("option",{value:"all",children:"All Categories"}),Object.entries(p).map(([e,t])=>r.jsx("option",{value:e,children:t},e))]})]})}),r.jsx("div",{className:m().checklistContent,children:Object.entries(q).map(([e,t])=>(0,r.jsxs)("div",{className:m().categorySection,children:[(0,r.jsxs)("h3",{className:m().categoryHeader,children:[r.jsx("span",{className:m().categoryIcon,children:h[e]}),p[e],(0,r.jsxs)("span",{className:m().categoryCount,children:["(",t.filter(e=>e.is_completed).length,"/",t.length,")"]})]}),r.jsx("div",{className:m().checklistItems,children:t.map(e=>(0,r.jsxs)("div",{className:`${m().checklistItem} ${e.is_completed?m().completed:""}`,children:[(0,r.jsxs)("div",{className:m().itemHeader,children:[(0,r.jsxs)("label",{className:m().checkboxLabel,children:[r.jsx("input",{type:"checkbox",checked:e.is_completed,onChange:()=>v(e.id,e.is_completed),className:m().checkbox}),(0,r.jsxs)("span",{className:m().itemTitle,children:[e.checklist_item,e.is_required&&r.jsx("span",{className:m().requiredBadge,children:"Required"})]})]}),e.due_date&&(0,r.jsxs)("span",{className:m().dueDate,children:["Due: ",C(e.due_date)]})]}),e.description&&r.jsx("p",{className:m().itemDescription,children:e.description}),e.is_completed&&e.completed_at&&(0,r.jsxs)("div",{className:m().completionInfo,children:["✅ Completed on ",C(e.completed_at)]}),e.notes&&(0,r.jsxs)("div",{className:m().itemNotes,children:[r.jsx("strong",{children:"Notes:"})," ",e.notes]})]},e.id))})]},e))})]})]})]})}i()}catch(e){i(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[2899,6212,1664,7441],()=>a(4423));module.exports=i})();