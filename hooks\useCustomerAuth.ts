import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

interface CustomerUser {
  id: string;
  customerId: string;
  email: string;
  firstName: string;
  lastName: string;
  emailVerified: boolean;
  isActive: boolean;
}

interface UseCustomerAuthReturn {
  user: CustomerUser | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string; requiresEmailVerification?: boolean }>;
  register: (firstName: string, lastName: string, email: string, password: string, phone?: string) => Promise<{ success: boolean; error?: string; requiresEmailVerification?: boolean }>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

export function useCustomerAuth(): UseCustomerAuthReturn {
  const [user, setUser] = useState<CustomerUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('customer-token');
      if (!token) {
        setLoading(false);
        return;
      }

      const response = await fetch('/api/customer/profile', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUser({
          id: data.profile.id,
          customerId: data.profile.id,
          email: data.profile.email,
          firstName: data.profile.first_name,
          lastName: data.profile.last_name,
          emailVerified: true, // Assuming verified if they can access profile
          isActive: true
        });
      } else {
        // Token is invalid, remove it
        localStorage.removeItem('customer-token');
        document.cookie = 'customer-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      }
    } catch (error) {
      console.error('Auth check error:', error);
      localStorage.removeItem('customer-token');
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/customer/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        localStorage.setItem('customer-token', data.token);
        document.cookie = `customer-token=${data.token}; path=/; secure; samesite=strict`;
        setUser(data.user);
        return { success: true };
      } else {
        return { 
          success: false, 
          error: data.error,
          requiresEmailVerification: data.requiresEmailVerification
        };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed. Please try again.' };
    }
  };

  const register = async (firstName: string, lastName: string, email: string, password: string, phone?: string) => {
    try {
      const response = await fetch('/api/customer/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ firstName, lastName, email, password, phone })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return { 
          success: true,
          requiresEmailVerification: data.requiresEmailVerification
        };
      } else {
        return { success: false, error: data.error };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Registration failed. Please try again.' };
    }
  };

  const logout = async () => {
    try {
      const token = localStorage.getItem('customer-token');
      if (token) {
        await fetch('/api/customer/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ sessionToken: token })
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('customer-token');
      document.cookie = 'customer-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      setUser(null);
      router.push('/customer/login');
    }
  };

  const refreshUser = async () => {
    await checkAuthStatus();
  };

  return {
    user,
    loading,
    login,
    register,
    logout,
    refreshUser
  };
}
