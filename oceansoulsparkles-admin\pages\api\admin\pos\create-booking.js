import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth/admin-auth'

/**
 * API endpoint for creating POS bookings with customer and payment data
 * This endpoint handles the complete POS transaction flow
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] POS booking creation API called`)

  try {
    // Authenticate admin request
    const { user, error: authError } = await authenticateAdminRequest(req)
    if (authError || !user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: authError?.message || 'Authentication failed',
        requestId
      })
    }

    console.log(`[${requestId}] Authentication successful. User: ${user?.email}`)

    const { service, artist, tier, timeSlot, customer, payment } = req.body

    // Validate required data
    if (!service?.id || !artist?.id || !tier?.id || !timeSlot?.time || !customer || !payment) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'Service, artist, tier, timeSlot, customer, and payment information are required',
        requestId
      })
    }

    // Enhanced Input Validation
    if (typeof service.id !== 'string' || service.id.trim() === '' ||
        typeof artist.id !== 'string' || artist.id.trim() === '' ||
        typeof tier.id !== 'string' || tier.id.trim() === '') {
      return res.status(400).json({ 
        error: 'Invalid input', 
        message: 'Service ID, Artist ID, and Tier ID must be non-empty strings.', 
        requestId 
      })
    }

    if (typeof tier.duration !== 'number' || tier.duration <= 0) {
      return res.status(400).json({ 
        error: 'Invalid input', 
        message: 'Tier duration must be a positive number.', 
        requestId 
      })
    }

    if (typeof tier.price !== 'number' || tier.price < 0) {
      return res.status(400).json({ 
        error: 'Invalid input', 
        message: 'Tier price must be a non-negative number.', 
        requestId 
      })
    }

    if (!timeSlot.time || typeof timeSlot.time !== 'string' || isNaN(new Date(timeSlot.time).getTime())) {
      return res.status(400).json({ 
        error: 'Invalid input', 
        message: 'Invalid timeSlot.time format. Expected valid ISO string.', 
        requestId 
      })
    }

    if (customer.email && (typeof customer.email !== 'string' || !customer.email.includes('@') || !customer.email.includes('.'))) {
      return res.status(400).json({ 
        error: 'Invalid input', 
        message: 'Invalid customer email format.', 
        requestId 
      })
    }

    if (!customer.isAnonymous && (!customer.name || typeof customer.name !== 'string' || customer.name.trim() === '')) {
      return res.status(400).json({ 
        error: 'Invalid input', 
        message: 'Customer name is required for non-anonymous customers.', 
        requestId 
      })
    }

    if (typeof payment.amount !== 'number' || payment.amount < 0) {
      return res.status(400).json({ 
        error: 'Invalid input', 
        message: 'Payment amount must be a non-negative number.', 
        requestId 
      })
    }

    if (typeof payment.method !== 'string' || payment.method.trim() === '') {
      return res.status(400).json({ 
        error: 'Invalid input', 
        message: 'Payment method must be a non-empty string.', 
        requestId 
      })
    }

    console.log(`[${requestId}] Creating POS booking for service: ${service.name}, artist: ${artist.name}`)

    // Start transaction-like process
    let customerId = null
    let bookingId = null
    let paymentId = null

    try {
      // Generate unique POS session ID
      const posSessionId = `pos_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`

      // Step 1: Create or find customer
      if (customer.isAnonymous) {
        // Create anonymous customer record
        try {
          const { data: customerData, error: customerError } = await supabaseAdmin
            .from('customers')
            .insert([{
              name: customer.name || `Walk-in Customer #${Date.now()}`,
              email: null,
              phone: customer.phone || null,
              notes: 'POS anonymous customer',
              created_via: 'pos'
            }])
            .select()
            .single()

          if (customerError) {
            console.error(`[${requestId}] Anonymous customer creation failed:`, customerError)
            throw new Error(`Anonymous customer creation failed: ${customerError.message}`)
          }

          customerId = customerData.id
          console.log(`[${requestId}] Created anonymous customer: ${customerId}`)
        } catch (anonError) {
          console.error(`[${requestId}] Anonymous customer creation failed:`, anonError)
          throw new Error(`Anonymous customer creation failed: ${anonError.message}`)
        }
      } else {
        // Create named customer
        try {
          const { data: customerData, error: customerError } = await supabaseAdmin
            .from('customers')
            .insert([{
              name: customer.name,
              email: customer.email || null,
              phone: customer.phone || null,
              notes: customer.notes || 'POS customer',
              created_via: 'pos'
            }])
            .select()
            .single()

          if (customerError) {
            console.error(`[${requestId}] Named customer creation failed:`, customerError)
            throw new Error(`Named customer creation failed: ${customerError.message}`)
          }

          customerId = customerData.id
          console.log(`[${requestId}] Created new customer: ${customerId} (${customer.email || 'no email'})`)
        } catch (namedError) {
          console.error(`[${requestId}] Named customer creation failed:`, namedError)
          throw new Error(`Customer creation failed: ${namedError.message}`)
        }
      }

      // Step 2: Create booking
      const startTime = new Date(timeSlot.time)
      const endTime = new Date(startTime.getTime() + (tier.duration * 60 * 1000))

      try {
        const { data: bookingData, error: bookingError } = await supabaseAdmin
          .from('bookings')
          .insert([{
            customer_id: customerId,
            service_id: service.id,
            artist_id: artist.id,
            assigned_artist_id: artist.id,
            start_time: startTime.toISOString(),
            end_time: endTime.toISOString(),
            status: 'confirmed',
            location: 'POS Terminal',
            notes: `POS booking - ${tier.name} tier - ${payment.method} payment - Artist: ${artist.name}`,
            booking_source: 'pos',
            pos_session_id: posSessionId,
            tier_name: tier.name,
            tier_price: tier.price,
            total_amount: payment.amount
          }])
          .select()
          .single()

        if (bookingError) {
          console.error(`[${requestId}] Error creating booking:`, bookingError)
          throw new Error(`Failed to create booking: ${bookingError.message}`)
        }

        bookingId = bookingData.id
        console.log(`[${requestId}] Created POS booking: ${bookingId} for session: ${posSessionId}`)
      } catch (bookingCreationError) {
        console.error(`[${requestId}] Booking creation failed:`, bookingCreationError)
        throw new Error(`Booking creation failed: ${bookingCreationError.message}`)
      }

      // Step 3: Create payment record
      try {
        const { data: paymentData, error: paymentError } = await supabaseAdmin
          .from('payments')
          .insert([{
            booking_id: bookingId,
            amount: payment.amount,
            currency: payment.currency || 'AUD',
            method: payment.method,
            status: 'completed',
            transaction_id: payment.details?.transactionId || null,
            cash_received: payment.details?.cashReceived || null,
            change_amount: payment.details?.changeAmount || null,
            tip_amount: payment.tip_amount || 0,
            tip_method: payment.tip_method || 'none',
            tip_percentage: payment.tip_percentage || 0,
            processing_fee: payment.details?.processingFee || 0,
            payment_time: new Date().toISOString(),
            receipt_data: payment.details ? JSON.stringify(payment.details) : null
          }])
          .select()
          .single()

        if (paymentError) {
          console.error(`[${requestId}] Error creating payment:`, paymentError)
          throw new Error(`Failed to create payment record: ${paymentError.message}`)
        }

        paymentId = paymentData.id
        console.log(`[${requestId}] Created POS payment: ${paymentId} for session: ${posSessionId}`)

        // Step 4: Create tip record if there's a tip
        if (payment.tip_amount && payment.tip_amount > 0) {
          try {
            const { data: tipData, error: tipError } = await supabaseAdmin
              .from('tips')
              .insert([{
                payment_id: paymentId,
                booking_id: bookingId,
                artist_id: artist.id,
                amount: payment.tip_amount,
                currency: payment.currency || 'AUD',
                tip_method: payment.tip_method,
                percentage: payment.tip_percentage,
                distribution_status: 'pending',
                notes: `POS tip for ${service.name} - ${artist.name}`,
                created_by: user.id
              }])
              .select()
              .single()

            if (tipError) {
              console.error(`[${requestId}] Error creating tip record:`, tipError)
              // Don't fail the whole transaction for tip record issues
            } else {
              console.log(`[${requestId}] Tip record created:`, tipData)
            }
          } catch (tipError) {
            console.error(`[${requestId}] Error creating tip record:`, tipError)
            // Continue without failing the transaction
          }
        }

      } catch (paymentCreationError) {
        console.error(`[${requestId}] Payment creation failed:`, paymentCreationError)
        throw new Error(`Payment creation failed: ${paymentCreationError.message}`)
      }

      // Return success response
      return res.status(201).json({
        success: true,
        booking: {
          id: bookingId,
          customer_id: customerId,
          service_id: service.id,
          start_time: startTime.toISOString(),
          end_time: endTime.toISOString(),
          status: 'confirmed'
        },
        payment: {
          id: paymentId,
          amount: payment.amount,
          currency: payment.currency || 'AUD',
          method: payment.method,
          status: 'completed'
        },
        customer: {
          id: customerId,
          name: customer.name,
          email: customer.email || null
        },
        receipt: {
          service: service.name,
          tier: tier.name,
          duration: tier.duration,
          amount: payment.amount,
          currency: payment.currency || 'AUD',
          payment_method: payment.method,
          booking_time: startTime.toISOString(),
          booking_id: bookingId
        }
      })

    } catch (transactionError) {
      console.error(`[${requestId}] Transaction error:`, transactionError)

      // Attempt cleanup if we created partial records
      if (paymentId) {
        await supabaseAdmin.from('payments').delete().eq('id', paymentId)
      }
      if (bookingId) {
        await supabaseAdmin.from('bookings').delete().eq('id', bookingId)
      }
      if (customerId && customer.isAnonymous) {
        await supabaseAdmin.from('customers').delete().eq('id', customerId)
      }

      throw transactionError
    }

  } catch (error) {
    console.error(`[${requestId}] POS Booking Creation Error:`, error)

    // Determine appropriate status code
    let statusCode = 500
    let errorMessage = 'Failed to create booking'

    if (error.message && error.message.includes('validation')) {
      statusCode = 400
      errorMessage = error.message
    } else if (error.message && error.message.includes('not found')) {
      statusCode = 404
      errorMessage = error.message
    } else if (error.message) {
      errorMessage = error.message
    }

    return res.status(statusCode).json({
      error: 'Booking creation failed',
      message: errorMessage,
      requestId
    })
  }
}
