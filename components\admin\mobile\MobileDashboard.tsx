/**
 * Ocean Soul Sparkles - Mobile Dashboard Component
 * Touch-optimized dashboard with mobile-specific widgets and navigation
 */

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import styles from '../../../styles/admin/mobile/MobileDashboard.module.css';

interface DashboardStats {
  todayBookings: number;
  todayRevenue: number;
  pendingBookings: number;
  totalCustomers: number;
  monthlyRevenue: number;
  monthlyBookings: number;
}

interface QuickAction {
  id: string;
  label: string;
  icon: string;
  href: string;
  color: string;
  description: string;
}

interface MobileDashboardProps {
  stats: DashboardStats;
  userRole: string;
  userName: string;
}

const QUICK_ACTIONS: QuickAction[] = [
  {
    id: 'new-booking',
    label: 'New Booking',
    icon: '📅',
    href: '/admin/bookings/new',
    color: '#667eea',
    description: 'Create a new appointment'
  },
  {
    id: 'pos',
    label: 'POS Terminal',
    icon: '💳',
    href: '/admin/pos',
    color: '#38a169',
    description: 'Process transactions'
  },
  {
    id: 'add-customer',
    label: 'Add Customer',
    icon: '👤',
    href: '/admin/customers/new',
    color: '#ed8936',
    description: 'Register new customer'
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: '📦',
    href: '/admin/inventory',
    color: '#9f7aea',
    description: 'Check stock levels'
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: '📊',
    href: '/admin/reports',
    color: '#e53e3e',
    description: 'View analytics'
  },
  {
    id: 'communications',
    label: 'Messages',
    icon: '📧',
    href: '/admin/communications',
    color: '#3182ce',
    description: 'Send notifications'
  }
];

export default function MobileDashboard({ stats, userRole, userName }: MobileDashboardProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const hour = currentTime.getHours();
    if (hour < 12) {
      setGreeting('Good Morning');
    } else if (hour < 17) {
      setGreeting('Good Afternoon');
    } else {
      setGreeting('Good Evening');
    }
  }, [currentTime]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const filteredActions = QUICK_ACTIONS.filter(action => {
    // Filter actions based on user role
    if (userRole === 'Artist' || userRole === 'Braider') {
      return ['new-booking', 'pos', 'add-customer'].includes(action.id);
    }
    return true; // Admin and DEV see all actions
  });

  return (
    <div className={styles.mobileDashboard}>
      {/* Header Section */}
      <div className={styles.header}>
        <div className={styles.greeting}>
          <h1>{greeting}, {userName}!</h1>
          <p className={styles.dateTime}>
            <span className={styles.date}>{formatDate(currentTime)}</span>
            <span className={styles.time}>{formatTime(currentTime)}</span>
          </p>
        </div>
        <div className={styles.headerIcon}>
          <span className={styles.waveEmoji}>👋</span>
        </div>
      </div>

      {/* Stats Overview */}
      <div className={styles.statsSection}>
        <h2>Today's Overview</h2>
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statIcon}>📅</div>
            <div className={styles.statContent}>
              <span className={styles.statValue}>{stats.todayBookings}</span>
              <span className={styles.statLabel}>Bookings</span>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>💰</div>
            <div className={styles.statContent}>
              <span className={styles.statValue}>{formatCurrency(stats.todayRevenue)}</span>
              <span className={styles.statLabel}>Revenue</span>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>⏳</div>
            <div className={styles.statContent}>
              <span className={styles.statValue}>{stats.pendingBookings}</span>
              <span className={styles.statLabel}>Pending</span>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👥</div>
            <div className={styles.statContent}>
              <span className={styles.statValue}>{stats.totalCustomers}</span>
              <span className={styles.statLabel}>Customers</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className={styles.actionsSection}>
        <h2>Quick Actions</h2>
        <div className={styles.actionsGrid}>
          {filteredActions.map((action) => (
            <Link
              key={action.id}
              href={action.href}
              className={styles.actionCard}
              style={{ '--action-color': action.color } as React.CSSProperties}
            >
              <div className={styles.actionIcon}>{action.icon}</div>
              <div className={styles.actionContent}>
                <span className={styles.actionLabel}>{action.label}</span>
                <span className={styles.actionDescription}>{action.description}</span>
              </div>
              <div className={styles.actionArrow}>→</div>
            </Link>
          ))}
        </div>
      </div>

      {/* Monthly Summary */}
      <div className={styles.summarySection}>
        <h2>This Month</h2>
        <div className={styles.summaryCards}>
          <div className={styles.summaryCard}>
            <div className={styles.summaryHeader}>
              <span className={styles.summaryIcon}>📈</span>
              <span className={styles.summaryTitle}>Revenue</span>
            </div>
            <div className={styles.summaryValue}>
              {formatCurrency(stats.monthlyRevenue)}
            </div>
            <div className={styles.summaryChange}>
              <span className={styles.changePositive}>+12.5%</span>
              <span className={styles.changeLabel}>vs last month</span>
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryHeader}>
              <span className={styles.summaryIcon}>📊</span>
              <span className={styles.summaryTitle}>Bookings</span>
            </div>
            <div className={styles.summaryValue}>
              {stats.monthlyBookings}
            </div>
            <div className={styles.summaryChange}>
              <span className={styles.changePositive}>+8.3%</span>
              <span className={styles.changeLabel}>vs last month</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className={styles.activitySection}>
        <div className={styles.activityHeader}>
          <h2>Recent Activity</h2>
          <Link href="/admin/bookings" className={styles.viewAllLink}>
            View All →
          </Link>
        </div>
        <div className={styles.activityList}>
          <div className={styles.activityItem}>
            <div className={styles.activityIcon}>✅</div>
            <div className={styles.activityContent}>
              <span className={styles.activityText}>Booking confirmed for Sarah Johnson</span>
              <span className={styles.activityTime}>2 minutes ago</span>
            </div>
          </div>
          <div className={styles.activityItem}>
            <div className={styles.activityIcon}>💳</div>
            <div className={styles.activityContent}>
              <span className={styles.activityText}>Payment received - $150.00</span>
              <span className={styles.activityTime}>15 minutes ago</span>
            </div>
          </div>
          <div className={styles.activityItem}>
            <div className={styles.activityIcon}>👤</div>
            <div className={styles.activityContent}>
              <span className={styles.activityText}>New customer registered</span>
              <span className={styles.activityTime}>1 hour ago</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Links */}
      <div className={styles.linksSection}>
        <h2>Quick Links</h2>
        <div className={styles.linksList}>
          <Link href="/admin/bookings" className={styles.quickLink}>
            <span className={styles.linkIcon}>📅</span>
            <span className={styles.linkText}>All Bookings</span>
            <span className={styles.linkArrow}>→</span>
          </Link>
          <Link href="/admin/customers" className={styles.quickLink}>
            <span className={styles.linkIcon}>👥</span>
            <span className={styles.linkText}>Customer List</span>
            <span className={styles.linkArrow}>→</span>
          </Link>
          <Link href="/admin/services" className={styles.quickLink}>
            <span className={styles.linkIcon}>✨</span>
            <span className={styles.linkText}>Services</span>
            <span className={styles.linkArrow}>→</span>
          </Link>
          <Link href="/admin/settings" className={styles.quickLink}>
            <span className={styles.linkIcon}>⚙️</span>
            <span className={styles.linkText}>Settings</span>
            <span className={styles.linkArrow}>→</span>
          </Link>
        </div>
      </div>
    </div>
  );
}
