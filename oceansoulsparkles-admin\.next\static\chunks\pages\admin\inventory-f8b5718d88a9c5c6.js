(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[371],{7719:function(t,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/inventory",function(){return n(1339)}])},1339:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return _}}),function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();var r=n(9008),o=n.n(r),c=n(1664),a=n.n(c),i=n(6026),d=n(99),l=n(8935),u=n(4363),s=n.n(u);function _(){let{user:t,loading:e}=(0,i.a)(),[n,r]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(!0),[c,u]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())([]),[_,O]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())([]),[m,f]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(""),[N,v]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())("all"),[h,E]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())("all"),[D,U]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())("name"),j=async()=>{try{r(!0);let t=localStorage.getItem("admin-token"),e=await fetch("/api/admin/inventory",{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to fetch inventory");let n=await e.json();u(n.inventory||[]),O(n.inventory||[])}catch(t){console.error("Error loading inventory:",t),u([]),O([])}finally{r(!1)}};Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{!e&&t&&j()},[e,t]),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{let t=c;m&&(t=t.filter(t=>t.name.toLowerCase().includes(m.toLowerCase())||t.category.toLowerCase().includes(m.toLowerCase())||t.sku&&t.sku.toLowerCase().includes(m.toLowerCase()))),"all"!==N&&(t=t.filter(t=>t.category===N)),"all"!==h&&(t=t.filter(t=>"in_stock"===h?"in_stock"===t.status:"low_stock"===h?"low_stock"===t.status:"out_of_stock"!==h||"out_of_stock"===t.status)),t.sort((t,e)=>{switch(D){case"name":return t.name.localeCompare(e.name);case"category":return t.category.localeCompare(e.category);case"stock":return(e.stock_quantity||0)-(t.stock_quantity||0);case"price":return(e.sale_price||0)-(t.sale_price||0);default:return 0}}),O(t)},[c,m,N,h,D]);let y=t=>{switch(t){case"in_stock":return"#22c55e";case"low_stock":return"#f59e0b";case"out_of_stock":return"#ef4444";default:return"#6b7280"}},C=t=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(t);return e||n?Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(d.Z,{children:Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().loadingContainer,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().loadingSpinner}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("p",{children:"Loading inventory..."})]})}):t?Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(d.Z,{children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(o(),{children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("title",{children:"Inventory Management | Ocean Soul Sparkles Admin"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("meta",{name:"description",content:"Manage product inventory and stock levels"})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().inventoryContainer,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("header",{className:s().header,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h1",{className:s().title,children:"Inventory Management"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().headerActions,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(l.F,{data:_,type:"inventory",className:s().exportBtn}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(a(),{href:"/admin/inventory/new",className:s().newItemBtn,children:"+ Add Product"})]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().controlsPanel,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().searchSection,children:Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("input",{type:"text",placeholder:"Search products by name, category, or SKU...",value:m,onChange:t=>f(t.target.value),className:s().searchInput})}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().filtersSection,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().filterGroup,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("label",{children:"Category:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("select",{value:N,onChange:t=>v(t.target.value),className:s().filterSelect,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"all",children:"All Categories"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"Braiding Hair",children:"Braiding Hair"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"Marley Hair",children:"Marley Hair"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"Hair Products",children:"Hair Products"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"Accessories",children:"Accessories"})]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().filterGroup,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("label",{children:"Stock Status:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("select",{value:h,onChange:t=>E(t.target.value),className:s().filterSelect,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"all",children:"All Stock Levels"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"in_stock",children:"In Stock"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"low_stock",children:"Low Stock"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"out_of_stock",children:"Out of Stock"})]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().filterGroup,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("label",{children:"Sort by:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("select",{value:D,onChange:t=>U(t.target.value),className:s().filterSelect,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"name",children:"Name"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"category",children:"Category"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"stock",children:"Stock Level"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"price",children:"Price"})]})]})]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().inventoryContent,children:0===_.length?Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().emptyState,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h3",{children:"No inventory items found"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("p",{children:0===c.length?"Get started by adding your first product to the inventory.":"Try adjusting your search or filter criteria."}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(a(),{href:"/admin/inventory/new",className:s().addFirstBtn,children:"Add First Product"})]}):Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().inventoryGrid,children:_.map(t=>Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().inventoryCard,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().cardHeader,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h3",{className:s().itemName,children:t.name}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:s().statusBadge,style:{backgroundColor:y(t.status)},children:t.status.replace("_"," ").toUpperCase()})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().cardBody,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().itemInfo,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("p",{className:s().category,children:t.category}),t.sku&&Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("p",{className:s().sku,children:["SKU: ",t.sku]}),t.description&&Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("p",{className:s().description,children:t.description})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().stockInfo,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().stockLevel,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:s().label,children:"Stock:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:s().value,children:[t.stock_quantity||0,t.min_stock_level&&" (Min: ".concat(t.min_stock_level,")")]})]}),t.sale_price&&Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().priceInfo,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:s().label,children:"Price:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:s().value,children:C(t.sale_price)})]}),t.supplier&&Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().supplierInfo,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:s().label,children:"Supplier:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:s().value,children:t.supplier})]})]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:s().cardActions,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(a(),{href:"/admin/inventory/".concat(t.id),className:s().viewBtn,children:"View Details"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("button",{className:s().editBtn,children:"Edit"}),(t.stock_quantity||0)<=(t.min_stock_level||0)&&Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("button",{className:s().restockBtn,children:"Restock"})]})]},t.id))})})]})]}):null}},4363:function(t){t.exports={inventoryContainer:"Inventory_inventoryContainer__GcfkW",header:"Inventory_header__YsGK_",title:"Inventory_title__Y0smo",headerActions:"Inventory_headerActions__sakeA",newItemBtn:"Inventory_newItemBtn__L0yQN",backButton:"Inventory_backButton__j1uiL",controlsPanel:"Inventory_controlsPanel__ptM_3",searchSection:"Inventory_searchSection__Y9Hgr",searchInput:"Inventory_searchInput__MExRL",filters:"Inventory_filters__LHQyS",categoryFilter:"Inventory_categoryFilter__0FZb_",stockFilter:"Inventory_stockFilter__Fq41i",sortSelect:"Inventory_sortSelect__w6Bd6",inventoryContent:"Inventory_inventoryContent__MZtM5",statsCards:"Inventory_statsCards__zQ0vN",statCard:"Inventory_statCard__281a_",statValue:"Inventory_statValue__fmJux",emptyState:"Inventory_emptyState__JwMRE",inventoryGrid:"Inventory_inventoryGrid__6nI_9",inventoryCard:"Inventory_inventoryCard__h1i83",itemHeader:"Inventory_itemHeader__6a78v",itemInfo:"Inventory_itemInfo__6vyyD",category:"Inventory_category__uUYPL",statusBadge:"Inventory_statusBadge__293g1",statusInStock:"Inventory_statusInStock__4rFKW",statusLowStock:"Inventory_statusLowStock__mOjky",statusCritical:"Inventory_statusCritical__iI7Nz",statusOutOfStock:"Inventory_statusOutOfStock__8GWFB",statusDefault:"Inventory_statusDefault__bCVl8",itemDetails:"Inventory_itemDetails__Jlyif",stockInfo:"Inventory_stockInfo__dQ4ll",stockLevel:"Inventory_stockLevel__0YpyD",currentStock:"Inventory_currentStock__0ZyIE",stockRange:"Inventory_stockRange__tajLE",stockBar:"Inventory_stockBar__R6ZDA",stockFill:"Inventory_stockFill__IbWUp",itemStats:"Inventory_itemStats__orFkd",statItem:"Inventory_statItem__Q085T",statLabel:"Inventory_statLabel__yHJ_o",itemActions:"Inventory_itemActions__F5Ub9",editBtn:"Inventory_editBtn__Gdnet",restockBtn:"Inventory_restockBtn__yaupq",loadingContainer:"Inventory_loadingContainer__28CuE",loadingSpinner:"Inventory_loadingSpinner__DNa6M",spin:"Inventory_spin__kif5C"}}},function(t){t.O(0,[736,592,888,179],function(){return t(t.s=7719)}),_N_E=t.O()}]);