/**
 * Ocean Soul Sparkles Admin Dashboard - API Error Middleware
 * Centralized error handling middleware for Next.js API routes
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { AppError, handleError, ErrorC<PERSON>, asyncHandler } from './error-handler';
import { verifyAdminToken } from '../auth/admin-auth';
import { auditLog } from '../security/audit-logging';

// Request context interface
export interface RequestContext {
  requestId: string;
  startTime: number;
  user?: {
    id: string;
    email: string;
    role: string;
  };
  ip?: string;
  userAgent?: string;
}

// Enhanced API request with context
export interface ApiRequestWithContext extends NextApiRequest {
  context: RequestContext;
}

// Middleware function type
export type ApiMiddleware = (
  req: ApiRequestWithContext,
  res: NextApiResponse,
  next: () => void
) => void | Promise<void>;

// Error handling middleware
export const errorMiddleware: ApiMiddleware = async (req, res, next) => {
  try {
    next();
  } catch (error) {
    // Log error with context
    await auditLog({
      action: 'API_ERROR',
      path: req.url || '',
      ip: req.context.ip,
      error: error instanceof Error ? error.message : 'Unknown error',
      severity: 'high',
      metadata: {
        requestId: req.context.requestId,
        method: req.method,
        userAgent: req.context.userAgent,
        userId: req.context.user?.id
      }
    });

    handleError(error as Error, res);
  }
};

// Request context middleware
export const contextMiddleware: ApiMiddleware = (req, res, next) => {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const startTime = Date.now();
  
  // Extract client information
  const ip = req.headers['x-forwarded-for'] as string || 
             req.headers['x-real-ip'] as string ||
             req.connection.remoteAddress ||
             'unknown';
  
  const userAgent = req.headers['user-agent'] || 'unknown';

  req.context = {
    requestId,
    startTime,
    ip,
    userAgent
  };

  // Add request ID to response headers
  res.setHeader('X-Request-ID', requestId);

  next();
};

// Authentication middleware
export const authMiddleware: ApiMiddleware = async (req, res, next) => {
  try {
    const authResult = await verifyAdminToken(req);
    
    if (!authResult.valid || !authResult.user) {
      throw new AppError(ErrorCode.UNAUTHORIZED, 'Authentication required', 401);
    }

    req.context.user = {
      id: authResult.user.id,
      email: authResult.user.email,
      role: authResult.user.role
    };

    next();
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(ErrorCode.UNAUTHORIZED, 'Invalid authentication token', 401);
  }
};

// Role-based authorization middleware
export const requireRole = (allowedRoles: string[]): ApiMiddleware => {
  return (req, res, next) => {
    if (!req.context.user) {
      throw new AppError(ErrorCode.UNAUTHORIZED, 'Authentication required', 401);
    }

    if (!allowedRoles.includes(req.context.user.role)) {
      throw new AppError(
        ErrorCode.FORBIDDEN, 
        `Access denied. Required roles: ${allowedRoles.join(', ')}`,
        403
      );
    }

    next();
  };
};

// Method validation middleware
export const requireMethod = (allowedMethods: string[]): ApiMiddleware => {
  return (req, res, next) => {
    if (!req.method || !allowedMethods.includes(req.method)) {
      throw new AppError(
        ErrorCode.INVALID_INPUT,
        `Method ${req.method} not allowed. Allowed methods: ${allowedMethods.join(', ')}`,
        405
      );
    }

    next();
  };
};

// Request logging middleware
export const loggingMiddleware: ApiMiddleware = async (req, res, next) => {
  const startTime = Date.now();

  // Log request
  console.log(`[${req.context.requestId}] ${req.method} ${req.url} - Started`, {
    ip: req.context.ip,
    userAgent: req.context.userAgent,
    userId: req.context.user?.id
  });

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(data: any) {
    const duration = Date.now() - startTime;
    const statusCode = res.statusCode;

    console.log(`[${req.context.requestId}] ${req.method} ${req.url} - ${statusCode} (${duration}ms)`);

    // Log to audit system for important operations
    if (req.method !== 'GET' || statusCode >= 400) {
      auditLog({
        action: `API_${req.method}`,
        path: req.url || '',
        userId: req.context.user?.id,
        userRole: req.context.user?.role,
        ip: req.context.ip,
        metadata: {
          requestId: req.context.requestId,
          statusCode,
          duration,
          success: statusCode < 400
        },
        severity: statusCode >= 500 ? 'high' : statusCode >= 400 ? 'medium' : 'low'
      });
    }

    return originalJson.call(this, data);
  };

  next();
};

// Rate limiting middleware (basic implementation)
const requestCounts = new Map<string, { count: number; resetTime: number }>();

export const rateLimitMiddleware = (
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): ApiMiddleware => {
  return (req, res, next) => {
    const key = req.context.ip || 'unknown';
    const now = Date.now();
    
    const record = requestCounts.get(key);
    
    if (!record || now > record.resetTime) {
      requestCounts.set(key, { count: 1, resetTime: now + windowMs });
      next();
      return;
    }
    
    if (record.count >= maxRequests) {
      throw new AppError(
        ErrorCode.RATE_LIMIT_EXCEEDED,
        'Too many requests. Please try again later.',
        429
      );
    }
    
    record.count++;
    next();
  };
};

// Compose multiple middlewares
export function composeMiddleware(...middlewares: ApiMiddleware[]) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const enhancedReq = req as ApiRequestWithContext;
    let index = 0;

    const next = async () => {
      if (index < middlewares.length) {
        const middleware = middlewares[index++];
        await middleware(enhancedReq, res, next);
      }
    };

    try {
      await next();
    } catch (error) {
      handleError(error as Error, res);
    }
  };
}

// Common middleware combinations
export const standardMiddleware = composeMiddleware(
  contextMiddleware,
  loggingMiddleware,
  errorMiddleware
);

export const authenticatedMiddleware = composeMiddleware(
  contextMiddleware,
  loggingMiddleware,
  authMiddleware,
  errorMiddleware
);

export const adminOnlyMiddleware = composeMiddleware(
  contextMiddleware,
  loggingMiddleware,
  authMiddleware,
  requireRole(['Admin', 'DEV']),
  errorMiddleware
);

// Wrapper for API handlers with middleware
export function withMiddleware(
  handler: (req: ApiRequestWithContext, res: NextApiResponse) => Promise<void> | void,
  ...middlewares: ApiMiddleware[]
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const enhancedReq = req as ApiRequestWithContext;
    
    // Apply middlewares
    const middleware = composeMiddleware(...middlewares);
    
    try {
      await middleware(enhancedReq, res);
      
      // Execute the actual handler
      await handler(enhancedReq, res);
    } catch (error) {
      handleError(error as Error, res);
    }
  };
}

// Validation middleware factory
export const validateBody = (schema: any): ApiMiddleware => {
  return (req, res, next) => {
    // Basic validation - in production you'd use a library like Joi or Yup
    if (!req.body) {
      throw new AppError(ErrorCode.VALIDATION_ERROR, 'Request body is required', 400);
    }
    
    // Add your validation logic here
    next();
  };
};

// CORS middleware
export const corsMiddleware: ApiMiddleware = (req, res, next) => {
  const origin = req.headers.origin;
  const allowedOrigins = [
    'https://admin.oceansoulsparkles.com.au',
    'https://oceansoulsparkles.com.au'
  ];
  
  if (process.env.NODE_ENV === 'development') {
    allowedOrigins.push('http://localhost:3002', 'http://localhost:3000');
  }
  
  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }
  
  next();
};
