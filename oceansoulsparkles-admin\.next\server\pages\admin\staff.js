(()=>{var e={};e.id=5261,e.ids=[5261,660],e.modules={5786:e=>{e.exports={staffContainer:"Staff_staffContainer__Kyycp",header:"Staff_header__hV6GO",title:"Staff_title__gX7SX",headerActions:"Staff_headerActions__hgBm7",newStaffBtn:"Staff_newStaffBtn__avc2A",controlsPanel:"Staff_controlsPanel__2aAX9",searchSection:"Staff_searchSection__Yif0n",searchInput:"Staff_searchInput__KoHPz",filtersSection:"Staff_filtersSection__QfHqB",filterSelect:"Staff_filterSelect__pkcN_",sortSelect:"Staff_sortSelect__DitCT",staffContent:"Staff_staffContent__Gap4y",emptyState:"Staff_emptyState__PWk8z",emptyIcon:"Staff_emptyIcon__DLiuz",addFirstBtn:"Staff_addFirstBtn__4fAFQ",staffGrid:"Staff_staffGrid__KZOSB",staffCard:"Staff_staffCard__uiBW7",cardHeader:"Staff_cardHeader__ZBGA5",memberInfo:"Staff_memberInfo__ph3eL",memberName:"Staff_memberName__1XVzi",memberEmail:"Staff_memberEmail__vFTwL",badges:"Staff_badges__6wd6L",roleBadge:"Staff_roleBadge__s1osi",statusBadge:"Staff_statusBadge__E3gsY",cardBody:"Staff_cardBody__SeT9c",memberDetails:"Staff_memberDetails__K4xVH",lastLogin:"Staff_lastLogin__PGNfe",joinDate:"Staff_joinDate__uWi33",cardActions:"Staff_cardActions__i3pFb",viewBtn:"Staff_viewBtn__dRAa5",editBtn:"Staff_editBtn__GqoHX",onboardingBtn:"Staff_onboardingBtn__dLd32",trainingBtn:"Staff_trainingBtn__XZ2k4",performanceBtn:"Staff_performanceBtn__m2NVq",loadingContainer:"Staff_loadingContainer__6ywa2",loadingSpinner:"Staff_loadingSpinner__Lo18d",spin:"Staff_spin__Tq92n",accessDenied:"Staff_accessDenied__INizo"}},1294:(e,a,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(a),t.d(a,{config:()=>S,default:()=>m,getServerSideProps:()=>h,getStaticPaths:()=>u,getStaticProps:()=>_,reportWebVitals:()=>p,routeModule:()=>N,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>x});var r=t(7093),n=t(5244),i=t(1323),l=t(2899),c=t.n(l),d=t(6814),f=t(145),o=e([d,f]);[d,f]=o.then?(await o)():o;let m=(0,i.l)(f,"default"),_=(0,i.l)(f,"getStaticProps"),u=(0,i.l)(f,"getStaticPaths"),h=(0,i.l)(f,"getServerSideProps"),S=(0,i.l)(f,"config"),p=(0,i.l)(f,"reportWebVitals"),x=(0,i.l)(f,"unstable_getStaticProps"),g=(0,i.l)(f,"unstable_getStaticPaths"),j=(0,i.l)(f,"unstable_getStaticParams"),v=(0,i.l)(f,"unstable_getServerProps"),b=(0,i.l)(f,"unstable_getServerSideProps"),N=new r.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/staff",pathname:"/admin/staff",bundlePath:"",filename:""},components:{App:d.default,Document:c()},userland:f});s()}catch(e){s(e)}})},145:(e,a,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(a),t.d(a,{default:()=>h});var r=t(997),n=t(6689),i=t(968),l=t.n(i),c=t(1664),d=t.n(c),f=t(8568),o=t(4845),m=t(5786),_=t.n(m),u=e([o]);function h(){let{user:e,loading:a}=(0,f.a)(),[t,s]=(0,n.useState)(!0),[i,c]=(0,n.useState)([]),[m,u]=(0,n.useState)([]),[h,S]=(0,n.useState)(""),[p,x]=(0,n.useState)("all"),[g,j]=(0,n.useState)("all"),[v,b]=(0,n.useState)("name"),N=e=>{switch(e){case"DEV":return"#8b5cf6";case"Admin":return"#ef4444";case"Artist":return"#10b981";case"Braider":return"#f59e0b";default:return"#6b7280"}},B=e=>{switch(e){case"active":return"#10b981";case"inactive":default:return"#6b7280";case"suspended":return"#ef4444"}},D=e=>{if(!e)return"Never";let a=new Date(e),t=new Date,s=Math.floor((t-a)/864e5);return 0===s?"Today":1===s?"Yesterday":s<7?`${s} days ago`:a.toLocaleDateString()};return a||t?r.jsx(o.Z,{children:(0,r.jsxs)("div",{className:_().loadingContainer,children:[r.jsx("div",{className:_().loadingSpinner}),r.jsx("p",{children:"Loading staff..."})]})}):e?["DEV","Admin"].includes(e.role)?(0,r.jsxs)(o.Z,{children:[(0,r.jsxs)(l(),{children:[r.jsx("title",{children:"Staff Management | Ocean Soul Sparkles Admin"}),r.jsx("meta",{name:"description",content:"Manage staff members and permissions"})]}),(0,r.jsxs)("div",{className:_().staffContainer,children:[(0,r.jsxs)("header",{className:_().header,children:[r.jsx("h1",{className:_().title,children:"Staff Management"}),r.jsx("div",{className:_().headerActions,children:r.jsx(d(),{href:"/admin/staff/new",className:_().newStaffBtn,children:"+ Add Staff Member"})})]}),(0,r.jsxs)("div",{className:_().controlsPanel,children:[r.jsx("div",{className:_().searchSection,children:r.jsx("input",{type:"text",placeholder:"Search staff by name or email...",value:h,onChange:e=>S(e.target.value),className:_().searchInput})}),(0,r.jsxs)("div",{className:_().filtersSection,children:[(0,r.jsxs)("select",{value:p,onChange:e=>x(e.target.value),className:_().filterSelect,children:[r.jsx("option",{value:"all",children:"All Roles"}),r.jsx("option",{value:"DEV",children:"Developer"}),r.jsx("option",{value:"Admin",children:"Admin"}),r.jsx("option",{value:"Artist",children:"Artist"}),r.jsx("option",{value:"Braider",children:"Braider"})]}),(0,r.jsxs)("select",{value:g,onChange:e=>j(e.target.value),className:_().filterSelect,children:[r.jsx("option",{value:"all",children:"All Status"}),r.jsx("option",{value:"active",children:"Active"}),r.jsx("option",{value:"inactive",children:"Inactive"}),r.jsx("option",{value:"suspended",children:"Suspended"})]}),(0,r.jsxs)("select",{value:v,onChange:e=>b(e.target.value),className:_().sortSelect,children:[r.jsx("option",{value:"name",children:"Sort by Name"}),r.jsx("option",{value:"role",children:"Sort by Role"}),r.jsx("option",{value:"email",children:"Sort by Email"}),r.jsx("option",{value:"lastLogin",children:"Sort by Last Login"})]})]})]}),r.jsx("div",{className:_().staffContent,children:0===m.length?(0,r.jsxs)("div",{className:_().emptyState,children:[r.jsx("div",{className:_().emptyIcon,children:"\uD83D\uDC65"}),r.jsx("h3",{children:"No Staff Members Found"}),r.jsx("p",{children:0===i.length?"No staff members have been added yet.":"No staff members match your current filters."}),0===i.length&&r.jsx(d(),{href:"/admin/staff/new",className:_().addFirstBtn,children:"Add First Staff Member"})]}):r.jsx("div",{className:_().staffGrid,children:m.map(e=>(0,r.jsxs)("div",{className:_().staffCard,children:[(0,r.jsxs)("div",{className:_().cardHeader,children:[(0,r.jsxs)("div",{className:_().memberInfo,children:[(0,r.jsxs)("h3",{className:_().memberName,children:[e.firstName," ",e.lastName]}),r.jsx("p",{className:_().memberEmail,children:e.email})]}),(0,r.jsxs)("div",{className:_().badges,children:[r.jsx("span",{className:_().roleBadge,style:{backgroundColor:N(e.role)},children:e.role}),r.jsx("span",{className:_().statusBadge,style:{backgroundColor:B(e.status)},children:e.status})]})]}),(0,r.jsxs)("div",{className:_().cardBody,children:[(0,r.jsxs)("div",{className:_().memberDetails,children:[(0,r.jsxs)("p",{className:_().lastLogin,children:["Last login: ",D(e.lastLogin)]}),(0,r.jsxs)("p",{className:_().joinDate,children:["Joined: ",new Date(e.createdAt).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:_().cardActions,children:[r.jsx(d(),{href:`/admin/staff/${e.id}`,className:_().viewBtn,children:"View Details"}),r.jsx(d(),{href:`/admin/staff/onboarding?staff_id=${e.id}`,className:_().onboardingBtn,children:"\uD83D\uDCCB Onboarding"}),r.jsx(d(),{href:`/admin/staff/training?staff_id=${e.id}`,className:_().trainingBtn,children:"\uD83C\uDF93 Training"}),r.jsx(d(),{href:`/admin/staff/performance?staff_id=${e.id}`,className:_().performanceBtn,children:"\uD83D\uDCCA Performance"}),r.jsx(d(),{href:`/admin/staff/${e.id}/edit`,className:_().editBtn,children:"Edit"})]})]})]},e.id))})})]})]}):r.jsx(o.Z,{children:(0,r.jsxs)("div",{className:_().accessDenied,children:[r.jsx("h2",{children:"Access Denied"}),r.jsx("p",{children:"You don't have permission to access staff management."})]})}):null}o=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[2899,6212,1664,7441],()=>t(1294));module.exports=s})();