(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[349],{9014:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/settings",function(){return t(6464)}])},6464:function(n,e,t){"use strict";t.r(e),t.d(e,{default:function(){return s}}),function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}(),function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}();var o=t(9008),r=t.n(o),i=t(6026),a=t(99),c=t(9505),d=t.n(c);function s(){let{user:n,loading:e}=(0,i.a)(),[t,o]=Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(!0),[c,s]=Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())("general"),[u,l]=Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())({general:{businessName:"Ocean Soul Sparkles",businessEmail:"<EMAIL>",businessPhone:"+61 XXX XXX XXX",businessAddress:"Australia",timezone:"Australia/Sydney",currency:"AUD"},booking:{defaultBookingDuration:60,advanceBookingDays:30,cancellationHours:24,autoConfirmBookings:!0,requireDeposit:!1,depositPercentage:20},payment:{squareEnabled:!0,squareEnvironment:"sandbox",cashEnabled:!0,cardEnabled:!0,allowPartialPayments:!0,autoProcessRefunds:!1},notifications:{emailNotifications:!0,smsNotifications:!1,pushNotifications:!1,emailBookingConfirmation:!0,emailBookingReminder:!0,emailBookingCancellation:!0,emailPaymentReceipt:!0,emailStaffNotification:!0,emailLowInventoryAlert:!0,emailPromotional:!0,smsBookingConfirmation:!1,smsBookingReminder:!1,smsBookingCancellation:!1,smsPaymentReceipt:!1,smsStaffNotification:!1,smsPromotional:!1,pushBookingConfirmation:!1,pushBookingReminder:!1,pushBookingCancellation:!1,pushStaffNotification:!1,bookingReminders:!0,reminderHours:24,adminNotifications:!0,customerNotifications:!0,emailFallbackWhenSMSFails:!0,smsFallbackWhenEmailFails:!1},security:{sessionTimeout:1800,adminSessionTimeout:1800,maxLoginAttempts:5,lockoutDuration:900,requireMFA:!1,ipRestrictions:!1}}),[O,m]=Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(!1),[f,N]=Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(!1);Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(()=>{n&&h()},[n]);let h=async()=>{try{o(!0);let n=await fetch("/api/admin/settings",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}});if(n.ok){let e=await n.json();l(e.settings||u)}else console.log("Using default settings - API not available")}catch(n){console.error("Error loading settings:",n)}finally{o(!1)}},_=(n,e,t)=>{l(o=>({...o,[n]:{...o[n],[e]:t}})),m(!0)},E=async()=>{try{N(!0),(await fetch("/api/admin/settings",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))},body:JSON.stringify({settings:u})})).ok?(m(!1),alert("Settings saved successfully!")):alert("Failed to save settings. Please try again.")}catch(n){console.error("Error saving settings:",n),alert("Error saving settings. Please try again.")}finally{N(!1)}};return e||t?Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(a.Z,{children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().loadingContainer,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().loadingSpinner}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("p",{children:"Loading settings..."})]})}):n?["DEV","Admin"].includes(n.role)?Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(a.Z,{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(r(),{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("title",{children:"Settings | Ocean Soul Sparkles Admin"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{name:"description",content:"Manage system settings and configuration"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsContainer,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("header",{className:d().header,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h1",{className:d().title,children:"Settings"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().headerActions,children:O&&Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}()),{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("button",{onClick:()=>{confirm("Are you sure you want to reset all settings to defaults?")&&(h(),m(!1))},className:d().resetBtn,disabled:f,children:"Reset"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("button",{onClick:E,className:d().saveBtn,disabled:f,children:f?"Saving...":"Save Changes"})]})})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsContent,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("nav",{className:d().tabNavigation,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("button",{className:"".concat(d().tabButton," ").concat("general"===c?d().active:""),onClick:()=>s("general"),children:"General"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("button",{className:"".concat(d().tabButton," ").concat("booking"===c?d().active:""),onClick:()=>s("booking"),children:"Booking"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("button",{className:"".concat(d().tabButton," ").concat("payment"===c?d().active:""),onClick:()=>s("payment"),children:"Payment"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("button",{className:"".concat(d().tabButton," ").concat("notifications"===c?d().active:""),onClick:()=>s("notifications"),children:"Notifications"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("button",{className:"".concat(d().tabButton," ").concat("security"===c?d().active:""),onClick:()=>s("security"),children:"Security"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().tabContent,children:["general"===c&&Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsSection,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h2",{children:"General Settings"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGrid,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Business Name"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"text",value:u.general.businessName,onChange:n=>_("general","businessName",n.target.value)})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Business Email"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"email",value:u.general.businessEmail,onChange:n=>_("general","businessEmail",n.target.value)})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Business Phone"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"tel",value:u.general.businessPhone,onChange:n=>_("general","businessPhone",n.target.value)})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Business Address"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("textarea",{value:u.general.businessAddress,onChange:n=>_("general","businessAddress",n.target.value),rows:3})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Timezone"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("select",{value:u.general.timezone,onChange:n=>_("general","timezone",n.target.value),children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("option",{value:"Australia/Sydney",children:"Australia/Sydney"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("option",{value:"Australia/Melbourne",children:"Australia/Melbourne"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("option",{value:"Australia/Brisbane",children:"Australia/Brisbane"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("option",{value:"Australia/Perth",children:"Australia/Perth"})]})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Currency"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("select",{value:u.general.currency,onChange:n=>_("general","currency",n.target.value),children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("option",{value:"AUD",children:"AUD - Australian Dollar"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("option",{value:"USD",children:"USD - US Dollar"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("option",{value:"EUR",children:"EUR - Euro"})]})]})]})]}),"booking"===c&&Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsSection,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h2",{children:"Booking Settings"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGrid,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Default Booking Duration (minutes)"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"number",value:u.booking.defaultBookingDuration,onChange:n=>_("booking","defaultBookingDuration",parseInt(n.target.value)),min:"15",max:"480"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Advance Booking Days"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"number",value:u.booking.advanceBookingDays,onChange:n=>_("booking","advanceBookingDays",parseInt(n.target.value)),min:"1",max:"365"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Cancellation Notice (hours)"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"number",value:u.booking.cancellationHours,onChange:n=>_("booking","cancellationHours",parseInt(n.target.value)),min:"1",max:"168"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.booking.autoConfirmBookings,onChange:n=>_("booking","autoConfirmBookings",n.target.checked)}),"Auto-confirm bookings"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.booking.requireDeposit,onChange:n=>_("booking","requireDeposit",n.target.checked)}),"Require deposit"]})}),u.booking.requireDeposit&&Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Deposit Percentage"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"number",value:u.booking.depositPercentage,onChange:n=>_("booking","depositPercentage",parseInt(n.target.value)),min:"5",max:"100"})]})]})]}),"payment"===c&&Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsSection,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h2",{children:"Payment Settings"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("p",{className:d().comingSoon,children:"Payment settings configuration coming soon..."})]}),"notifications"===c&&Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsSection,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h2",{children:"Notification Settings"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGroup,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h3",{children:"Communication Channels"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGrid,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.emailNotifications,onChange:n=>_("notifications","emailNotifications",n.target.checked)}),"Enable Email Notifications"]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("small",{children:"Master toggle for all email communications"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.smsNotifications,onChange:n=>_("notifications","smsNotifications",n.target.checked)}),"Enable SMS Notifications"]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("small",{children:"Master toggle for all SMS communications"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.pushNotifications,onChange:n=>_("notifications","pushNotifications",n.target.checked)}),"Enable Push Notifications"]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("small",{children:"Master toggle for push notifications (future feature)"})]})]})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGroup,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h3",{children:"Email Notifications"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGrid,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.emailBookingConfirmation,onChange:n=>_("notifications","emailBookingConfirmation",n.target.checked),disabled:!u.notifications.emailNotifications}),"Booking Confirmations"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.emailBookingReminder,onChange:n=>_("notifications","emailBookingReminder",n.target.checked),disabled:!u.notifications.emailNotifications}),"Booking Reminders"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.emailBookingCancellation,onChange:n=>_("notifications","emailBookingCancellation",n.target.checked),disabled:!u.notifications.emailNotifications}),"Booking Cancellations"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.emailPaymentReceipt,onChange:n=>_("notifications","emailPaymentReceipt",n.target.checked),disabled:!u.notifications.emailNotifications}),"Payment Receipts"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.emailStaffNotification,onChange:n=>_("notifications","emailStaffNotification",n.target.checked),disabled:!u.notifications.emailNotifications}),"Staff Notifications"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.emailPromotional,onChange:n=>_("notifications","emailPromotional",n.target.checked),disabled:!u.notifications.emailNotifications}),"Promotional Emails"]})})]})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGroup,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h3",{children:"SMS Notifications"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGrid,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.smsBookingConfirmation,onChange:n=>_("notifications","smsBookingConfirmation",n.target.checked),disabled:!u.notifications.smsNotifications}),"Booking Confirmations"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.smsBookingReminder,onChange:n=>_("notifications","smsBookingReminder",n.target.checked),disabled:!u.notifications.smsNotifications}),"Booking Reminders"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.smsBookingCancellation,onChange:n=>_("notifications","smsBookingCancellation",n.target.checked),disabled:!u.notifications.smsNotifications}),"Booking Cancellations"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.smsPaymentReceipt,onChange:n=>_("notifications","smsPaymentReceipt",n.target.checked),disabled:!u.notifications.smsNotifications}),"Payment Receipts"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.smsStaffNotification,onChange:n=>_("notifications","smsStaffNotification",n.target.checked),disabled:!u.notifications.smsNotifications}),"Staff Notifications"]})}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.smsPromotional,onChange:n=>_("notifications","smsPromotional",n.target.checked),disabled:!u.notifications.smsNotifications}),"Promotional SMS"]})})]})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGroup,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h3",{children:"General Settings"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGrid,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:"Reminder Hours Before Appointment"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"number",value:u.notifications.reminderHours,onChange:n=>_("notifications","reminderHours",parseInt(n.target.value)),min:"1",max:"168"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("small",{children:"How many hours before appointment to send reminders"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.adminNotifications,onChange:n=>_("notifications","adminNotifications",n.target.checked)}),"Admin Notifications"]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("small",{children:"Receive notifications for admin events"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.customerNotifications,onChange:n=>_("notifications","customerNotifications",n.target.checked)}),"Customer Notifications"]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("small",{children:"Send notifications to customers"})]})]})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGroup,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h3",{children:"Fallback Behavior"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsGrid,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.emailFallbackWhenSMSFails,onChange:n=>_("notifications","emailFallbackWhenSMSFails",n.target.checked)}),"Send Email When SMS Fails"]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("small",{children:"Automatically send email if SMS delivery fails"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingItem,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("label",{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("input",{type:"checkbox",checked:u.notifications.smsFallbackWhenEmailFails,onChange:n=>_("notifications","smsFallbackWhenEmailFails",n.target.checked)}),"Send SMS When Email Fails"]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("small",{children:"Automatically send SMS if email delivery fails"})]})]})]})]}),"security"===c&&Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().settingsSection,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h2",{children:"Security Settings"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("p",{className:d().comingSoon,children:"Security settings configuration coming soon..."})]})]})]})]})]}):Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(a.Z,{children:Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{className:d().accessDenied,children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("h2",{children:"Access Denied"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("p",{children:"You don't have permission to access system settings."})]})}):null}},9505:function(n){n.exports={settingsContainer:"Settings_settingsContainer__E9ikM",header:"Settings_header__iprwI",title:"Settings_title__UPQFQ",headerActions:"Settings_headerActions__RuDIB",resetBtn:"Settings_resetBtn__Ibi0t",saveBtn:"Settings_saveBtn__8_bRU",settingsContent:"Settings_settingsContent__nNeed",tabNavigation:"Settings_tabNavigation__IKynR",tabButton:"Settings_tabButton__jz8Cs",active:"Settings_active__0_8Bo",tabContent:"Settings_tabContent__cAICo",settingsSection:"Settings_settingsSection__GC4In",settingsGrid:"Settings_settingsGrid__wLtMu",settingItem:"Settings_settingItem__KxWbr",comingSoon:"Settings_comingSoon__Szu72",loadingContainer:"Settings_loadingContainer__uPTPv",loadingSpinner:"Settings_loadingSpinner__6_Tk_",spin:"Settings_spin__3eCCm",accessDenied:"Settings_accessDenied___aepr"}}},function(n){n.O(0,[736,592,888,179],function(){return n(n.s=9014)}),_N_E=n.O()}]);