/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Form Component
 * Complete mobile-optimized form wrapper with validation and submission handling
 */

import React, { useState, useRef, useEffect } from 'react';
import { ValidationError } from '@/types';
import { useMobileFormValidation, ValidationRule } from './MobileValidation';
import MobileValidation, { MobileSuccess, MobileWarning } from './MobileValidation';
import styles from './MobileForm.module.css';

export interface MobileFormProps {
  children: React.ReactNode;
  onSubmit: (data: Record<string, any>) => Promise<void> | void;
  initialValues?: Record<string, any>;
  validationRules?: Record<string, ValidationRule>;
  className?: string;
  title?: string;
  subtitle?: string;
  submitText?: string;
  cancelText?: string;
  onCancel?: () => void;
  disabled?: boolean;
  loading?: boolean;
  showProgress?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
  successMessage?: string;
  warningMessage?: string;
  'data-testid'?: string;
}

export const MobileForm: React.FC<MobileFormProps> = ({
  children,
  onSubmit,
  initialValues = {},
  validationRules = {},
  className = '',
  title,
  subtitle,
  submitText = 'Save',
  cancelText = 'Cancel',
  onCancel,
  disabled = false,
  loading = false,
  showProgress = false,
  autoSave = false,
  autoSaveDelay = 2000,
  successMessage,
  warningMessage,
  'data-testid': testId,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const formRef = useRef<HTMLFormElement>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  const {
    formState,
    updateField,
    touchField,
    validateForm,
    resetForm,
    getFieldProps,
    getAllErrors,
    isFormValid,
    hasErrors
  } = useMobileFormValidation(initialValues, validationRules);

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave) return;

    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(async () => {
      if (isFormValid && !isSubmitting) {
        try {
          setAutoSaveStatus('saving');
          const formData = Object.fromEntries(
            Object.entries(formState).map(([key, field]) => [key, field.value])
          );
          await onSubmit(formData);
          setAutoSaveStatus('saved');
          setTimeout(() => setAutoSaveStatus('idle'), 2000);
        } catch (error) {
          setAutoSaveStatus('error');
          setTimeout(() => setAutoSaveStatus('idle'), 3000);
        }
      }
    }, autoSaveDelay);

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [formState, autoSave, autoSaveDelay, isFormValid, isSubmitting, onSubmit]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (disabled || isSubmitting) return;

    // Validate all fields
    const isValid = validateForm();
    
    if (!isValid) {
      // Focus first error field
      const firstErrorField = Object.keys(formState).find(
        field => formState[field].errors.length > 0
      );
      
      if (firstErrorField) {
        const errorElement = formRef.current?.querySelector(`[name="${firstErrorField}"]`) as HTMLElement;
        errorElement?.focus();
        errorElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      
      return;
    }

    try {
      setIsSubmitting(true);
      setSubmitError(null);
      setSubmitSuccess(false);

      const formData = Object.fromEntries(
        Object.entries(formState).map(([key, field]) => [key, field.value])
      );

      await onSubmit(formData);
      
      setSubmitSuccess(true);
      setSubmitError(null);
      
      // Auto-hide success message after 3 seconds
      setTimeout(() => setSubmitSuccess(false), 3000);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while saving';
      setSubmitError(errorMessage);
      setSubmitSuccess(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    resetForm();
    setSubmitSuccess(false);
    setSubmitError(null);
    setAutoSaveStatus('idle');
  };

  const formClasses = [
    styles.form,
    loading && styles.loading,
    disabled && styles.disabled,
    className
  ].filter(Boolean).join(' ');

  const allErrors = getAllErrors();
  const totalFields = Object.keys(validationRules).length;
  const validFields = Object.values(formState).filter(field => field.valid && field.touched).length;
  const progressPercentage = totalFields > 0 ? (validFields / totalFields) * 100 : 0;

  return (
    <div className={styles.container} data-testid={testId}>
      {title && (
        <div className={styles.header}>
          <h2 className={styles.title}>{title}</h2>
          {subtitle && <p className={styles.subtitle}>{subtitle}</p>}
        </div>
      )}

      {showProgress && totalFields > 0 && (
        <div className={styles.progressContainer}>
          <div className={styles.progressLabel}>
            Form Progress: {validFields} of {totalFields} fields completed
          </div>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill}
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      )}

      {autoSave && (
        <div className={styles.autoSaveStatus}>
          {autoSaveStatus === 'saving' && (
            <span className={styles.autoSaveSaving}>💾 Auto-saving...</span>
          )}
          {autoSaveStatus === 'saved' && (
            <span className={styles.autoSaveSaved}>✅ Auto-saved</span>
          )}
          {autoSaveStatus === 'error' && (
            <span className={styles.autoSaveError}>❌ Auto-save failed</span>
          )}
        </div>
      )}

      {submitSuccess && (successMessage || 'Form submitted successfully!') && (
        <MobileSuccess message={successMessage || 'Form submitted successfully!'} />
      )}

      {warningMessage && (
        <MobileWarning message={warningMessage} />
      )}

      {submitError && (
        <MobileValidation errors={[{ field: '', message: submitError }]} />
      )}

      {hasErrors && allErrors.length > 0 && (
        <MobileValidation errors={allErrors} />
      )}

      <form 
        ref={formRef}
        className={formClasses}
        onSubmit={handleSubmit}
        noValidate
      >
        <div className={styles.fields}>
          {React.Children.map(children, (child) => {
            if (React.isValidElement(child) && child.props.name) {
              const fieldName = child.props.name;
              const fieldProps = getFieldProps(fieldName);
              
              return React.cloneElement(child, {
                ...fieldProps,
                ...child.props, // Allow props to override
              });
            }
            return child;
          })}
        </div>

        <div className={styles.actions}>
          {onCancel && (
            <button
              type="button"
              className={styles.cancelButton}
              onClick={onCancel}
              disabled={isSubmitting}
            >
              {cancelText}
            </button>
          )}
          
          <button
            type="button"
            className={styles.resetButton}
            onClick={handleReset}
            disabled={isSubmitting}
          >
            Reset
          </button>
          
          <button
            type="submit"
            className={styles.submitButton}
            disabled={disabled || isSubmitting || !isFormValid}
          >
            {isSubmitting ? (
              <>
                <span className={styles.spinner} />
                Saving...
              </>
            ) : (
              submitText
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

// Form field wrapper for easier integration
export const MobileFormField: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <div className={[styles.field, className].filter(Boolean).join(' ')}>
      {children}
    </div>
  );
};

// Form section wrapper
export const MobileFormSection: React.FC<{
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  className?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}> = ({ 
  title, 
  subtitle, 
  children, 
  className = '', 
  collapsible = false,
  defaultExpanded = true 
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <div className={[styles.section, className].filter(Boolean).join(' ')}>
      {title && (
        <div 
          className={styles.sectionHeader}
          onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}
        >
          <h3 className={styles.sectionTitle}>{title}</h3>
          {subtitle && <p className={styles.sectionSubtitle}>{subtitle}</p>}
          {collapsible && (
            <span className={styles.sectionToggle}>
              {isExpanded ? '▲' : '▼'}
            </span>
          )}
        </div>
      )}
      
      {(!collapsible || isExpanded) && (
        <div className={styles.sectionContent}>
          {children}
        </div>
      )}
    </div>
  );
};

export default MobileForm;
