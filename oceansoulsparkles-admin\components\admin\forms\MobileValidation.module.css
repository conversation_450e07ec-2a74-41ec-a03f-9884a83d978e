/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Validation Styles
 * Mobile-friendly validation feedback styling
 */

.container {
  margin: 1rem 0;
  padding: 1rem;
  background: #fef2f2;
  border: 2px solid #fecaca;
  border-radius: 12px;
  animation: slideIn 0.3s ease;
}

.summary {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #fecaca;
}

.summaryIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.summaryText {
  font-size: 1rem;
  font-weight: 600;
  color: #dc2626;
  line-height: 1.4;
}

.errorList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.errorItem {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #ffffff;
  border: 1px solid #fecaca;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.errorItem:hover {
  border-color: #f87171;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.1);
}

.errorIcon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.errorContent {
  flex: 1;
  min-width: 0;
}

.errorField {
  font-size: 0.875rem;
  font-weight: 600;
  color: #7f1d1d;
  margin-bottom: 0.25rem;
  text-transform: capitalize;
}

.errorMessage {
  font-size: 1rem;
  color: #dc2626;
  line-height: 1.4;
  word-wrap: break-word;
}

/* Success feedback */
.success {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 1rem 0;
  padding: 1rem;
  background: #f0fdf4;
  border: 2px solid #bbf7d0;
  border-radius: 12px;
  animation: slideIn 0.3s ease;
}

.successIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.successMessage {
  font-size: 1rem;
  font-weight: 500;
  color: #166534;
  line-height: 1.4;
}

/* Warning feedback */
.warning {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 1rem 0;
  padding: 1rem;
  background: #fffbeb;
  border: 2px solid #fed7aa;
  border-radius: 12px;
  animation: slideIn 0.3s ease;
}

.warningIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.warningMessage {
  font-size: 1rem;
  font-weight: 500;
  color: #92400e;
  line-height: 1.4;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .container,
  .success,
  .warning {
    margin: 1.25rem 0;
    padding: 1.25rem;
    border-radius: 16px;
  }
  
  .summary {
    gap: 1rem;
    margin-bottom: 1.25rem;
    padding-bottom: 1.25rem;
  }
  
  .summaryIcon {
    font-size: 1.75rem;
  }
  
  .summaryText {
    font-size: 1.125rem;
  }
  
  .errorList {
    gap: 1rem;
  }
  
  .errorItem {
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
  }
  
  .errorIcon {
    font-size: 1.5rem;
  }
  
  .errorField {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .errorMessage {
    font-size: 1.125rem;
  }
  
  .successIcon,
  .warningIcon {
    font-size: 1.75rem;
  }
  
  .successMessage,
  .warningMessage {
    font-size: 1.125rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .container,
  .success,
  .warning {
    padding: 1.5rem;
  }
  
  .summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    text-align: left;
  }
  
  .summaryIcon {
    align-self: center;
  }
  
  .errorItem {
    padding: 1.25rem;
  }
  
  .errorContent {
    min-width: 0;
  }
  
  .errorMessage {
    font-size: 1rem;
    line-height: 1.5;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .container {
    background: #ffffff;
    border-color: #dc2626;
    border-width: 3px;
  }
  
  .errorItem {
    border-color: #dc2626;
    border-width: 2px;
  }
  
  .success {
    background: #ffffff;
    border-color: #166534;
    border-width: 3px;
  }
  
  .warning {
    background: #ffffff;
    border-color: #92400e;
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .container,
  .success,
  .warning,
  .errorItem {
    animation: none;
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .container {
    background: #1f1f1f;
    border-color: #dc2626;
  }
  
  .summary {
    border-color: #dc2626;
  }
  
  .summaryText {
    color: #fca5a5;
  }
  
  .errorItem {
    background: #2d2d2d;
    border-color: #dc2626;
  }
  
  .errorField {
    color: #fca5a5;
  }
  
  .errorMessage {
    color: #fca5a5;
  }
  
  .success {
    background: #1f1f1f;
    border-color: #166534;
  }
  
  .successMessage {
    color: #86efac;
  }
  
  .warning {
    background: #1f1f1f;
    border-color: #92400e;
  }
  
  .warningMessage {
    color: #fbbf24;
  }
}

/* Focus management for accessibility */
.container:focus-within,
.success:focus-within,
.warning:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .container,
  .success,
  .warning {
    background: transparent !important;
    border: 2px solid #000000 !important;
    color: #000000 !important;
    box-shadow: none !important;
  }
  
  .summaryText,
  .errorMessage,
  .successMessage,
  .warningMessage {
    color: #000000 !important;
  }
  
  .summaryIcon,
  .errorIcon,
  .successIcon,
  .warningIcon {
    display: none;
  }
}
