(()=>{var e={};e.id=8843,e.ids=[8843,660],e.modules={8852:e=>{e.exports={customerDetailsContainer:"CustomerDetails_customerDetailsContainer__PUgb1",header:"CustomerDetails_header__Tqbwb",breadcrumb:"CustomerDetails_breadcrumb__QOnUv",headerActions:"CustomerDetails_headerActions__X5n7c",editButton:"CustomerDetails_editButton__UMugZ",bookButton:"CustomerDetails_bookButton__mW9yX",deleteButton:"CustomerDetails_deleteButton__depNI",backButton:"CustomerDetails_backButton__cgv_v",customerContent:"CustomerDetails_customerContent__A1sJQ",mainInfo:"CustomerDetails_mainInfo__cOw52",customerHeader:"CustomerDetails_customerHeader__PQm8b",customerAvatar:"CustomerDetails_customerAvatar__TQtvG",customerName:"CustomerDetails_customerName__1V0bD",customerEmail:"CustomerDetails_customerEmail__B2RAj",detailsGrid:"CustomerDetails_detailsGrid__DDIUl",detailCard:"CustomerDetails_detailCard__o97Uz",contactInfo:"CustomerDetails_contactInfo__zk__s",personalInfo:"CustomerDetails_personalInfo__CjMUK",contactItem:"CustomerDetails_contactItem__BwMx1",infoItem:"CustomerDetails_infoItem__pdLK9",notes:"CustomerDetails_notes__W4S_v",metaInfo:"CustomerDetails_metaInfo__XO5o5",metaItem:"CustomerDetails_metaItem__J9QyJ",sidebar:"CustomerDetails_sidebar__XOBgH",quickActions:"CustomerDetails_quickActions__ZQ4Wx",bookingHistory:"CustomerDetails_bookingHistory__QqM_o",actionButton:"CustomerDetails_actionButton__lmh_7",noBookings:"CustomerDetails_noBookings__88MEN",bookingsList:"CustomerDetails_bookingsList__obGLp",bookingItem:"CustomerDetails_bookingItem__mVc7c",bookingHeader:"CustomerDetails_bookingHeader__pFW_Y",serviceName:"CustomerDetails_serviceName__MSURv",bookingStatus:"CustomerDetails_bookingStatus__Xt8k9",bookingDetails:"CustomerDetails_bookingDetails__5zW5A",viewAllBookings:"CustomerDetails_viewAllBookings__FFhu8",loadingContainer:"CustomerDetails_loadingContainer__5RODO",errorContainer:"CustomerDetails_errorContainer__lXYBZ",notFoundContainer:"CustomerDetails_notFoundContainer__TYYVz",loadingSpinner:"CustomerDetails_loadingSpinner__WvOjv",spin:"CustomerDetails_spin__oRuUX"}},4406:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{config:()=>j,default:()=>u,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>_,reportWebVitals:()=>g,routeModule:()=>b,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>D,unstable_getStaticParams:()=>N,unstable_getStaticPaths:()=>p,unstable_getStaticProps:()=>C});var r=t(7093),i=t(5244),o=t(1323),n=t(2899),l=t.n(n),c=t(6814),d=t(5615),m=e([c,d]);[c,d]=m.then?(await m)():m;let u=(0,o.l)(d,"default"),_=(0,o.l)(d,"getStaticProps"),h=(0,o.l)(d,"getStaticPaths"),x=(0,o.l)(d,"getServerSideProps"),j=(0,o.l)(d,"config"),g=(0,o.l)(d,"reportWebVitals"),C=(0,o.l)(d,"unstable_getStaticProps"),p=(0,o.l)(d,"unstable_getStaticPaths"),N=(0,o.l)(d,"unstable_getStaticParams"),v=(0,o.l)(d,"unstable_getServerProps"),D=(0,o.l)(d,"unstable_getServerSideProps"),b=new r.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/admin/customers/[id]",pathname:"/admin/customers/[id]",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});a()}catch(e){a(e)}})},5615:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{default:()=>j});var r=t(997),i=t(6689),o=t(1163),n=t(968),l=t.n(n),c=t(1664),d=t.n(c),m=t(8568),u=t(4845),_=t(8852),h=t.n(_),x=e([u]);function j(){let e=(0,o.useRouter)(),{id:s}=e.query,{user:t,loading:a}=(0,m.a)(),[n,c]=(0,i.useState)(!0),[_,x]=(0,i.useState)(null),[j,g]=(0,i.useState)([]),[C,p]=(0,i.useState)(null),N=async()=>{if(confirm("Are you sure you want to delete this customer? This action cannot be undone."))try{let t=localStorage.getItem("admin-token");if(!(await fetch(`/api/admin/customers/${s}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).ok)throw Error("Failed to delete customer");e.push("/admin/customers")}catch(e){console.error("Error deleting customer:",e),alert("Failed to delete customer: "+e.message)}},v=e=>e?new Date(e).toLocaleDateString("en-AU"):"N/A",D=e=>e?new Date(e).toLocaleString("en-AU"):"N/A",b=e=>{switch(e?.toLowerCase()){case"confirmed":return"#28a745";case"pending":return"#ffc107";case"cancelled":return"#dc3545";case"completed":return"#17a2b8";default:return"#6c757d"}};return a||n?r.jsx(u.Z,{children:(0,r.jsxs)("div",{className:h().loadingContainer,children:[r.jsx("div",{className:h().loadingSpinner}),r.jsx("p",{children:"Loading customer details..."})]})}):C?r.jsx(u.Z,{children:(0,r.jsxs)("div",{className:h().errorContainer,children:[r.jsx("h2",{children:"Error Loading Customer"}),r.jsx("p",{children:C}),r.jsx(d(),{href:"/admin/customers",className:h().backButton,children:"← Back to Customers"})]})}):_?(0,r.jsxs)(u.Z,{children:[(0,r.jsxs)(l(),{children:[(0,r.jsxs)("title",{children:[_.first_name," ",_.last_name," - Customer Details | Ocean Soul Sparkles Admin"]}),r.jsx("meta",{name:"description",content:`Details for customer ${_.first_name} ${_.last_name}`})]}),(0,r.jsxs)("div",{className:h().customerDetailsContainer,children:[(0,r.jsxs)("header",{className:h().header,children:[(0,r.jsxs)("div",{className:h().breadcrumb,children:[r.jsx(d(),{href:"/admin/customers",children:"Customers"}),r.jsx("span",{children:"/"}),(0,r.jsxs)("span",{children:[_.first_name," ",_.last_name]})]}),(0,r.jsxs)("div",{className:h().headerActions,children:[r.jsx(d(),{href:`/admin/customers/${_.id}/edit`,className:h().editButton,children:"✏️ Edit Customer"}),r.jsx(d(),{href:`/admin/bookings/new?customer=${_.id}`,className:h().bookButton,children:"\uD83D\uDCC5 New Booking"}),r.jsx("button",{onClick:N,className:h().deleteButton,children:"\uD83D\uDDD1️ Delete"}),r.jsx(d(),{href:"/admin/customers",className:h().backButton,children:"← Back to Customers"})]})]}),(0,r.jsxs)("div",{className:h().customerContent,children:[(0,r.jsxs)("div",{className:h().mainInfo,children:[(0,r.jsxs)("div",{className:h().customerHeader,children:[(0,r.jsxs)("div",{className:h().customerAvatar,children:[_.first_name?.[0],_.last_name?.[0]]}),(0,r.jsxs)("div",{className:h().customerName,children:[(0,r.jsxs)("h1",{children:[_.first_name," ",_.last_name]}),r.jsx("p",{className:h().customerEmail,children:_.email})]})]}),(0,r.jsxs)("div",{className:h().detailsGrid,children:[(0,r.jsxs)("div",{className:h().detailCard,children:[r.jsx("h4",{children:"Contact Information"}),(0,r.jsxs)("div",{className:h().contactInfo,children:[(0,r.jsxs)("div",{className:h().contactItem,children:[r.jsx("strong",{children:"Email:"})," ",_.email||"N/A"]}),(0,r.jsxs)("div",{className:h().contactItem,children:[r.jsx("strong",{children:"Phone:"})," ",_.phone||"N/A"]}),_.phone_secondary&&(0,r.jsxs)("div",{className:h().contactItem,children:[r.jsx("strong",{children:"Secondary Phone:"})," ",_.phone_secondary]}),(0,r.jsxs)("div",{className:h().contactItem,children:[r.jsx("strong",{children:"Address:"})," ",_.address||"N/A"]})]})]}),(0,r.jsxs)("div",{className:h().detailCard,children:[r.jsx("h4",{children:"Personal Information"}),(0,r.jsxs)("div",{className:h().personalInfo,children:[(0,r.jsxs)("div",{className:h().infoItem,children:[r.jsx("strong",{children:"Date of Birth:"})," ",v(_.date_of_birth)]}),(0,r.jsxs)("div",{className:h().infoItem,children:[r.jsx("strong",{children:"Customer Since:"})," ",v(_.created_at)]}),(0,r.jsxs)("div",{className:h().infoItem,children:[r.jsx("strong",{children:"Total Bookings:"})," ",_.total_bookings||0]})]})]}),_.notes&&(0,r.jsxs)("div",{className:h().detailCard,children:[r.jsx("h4",{children:"Notes"}),r.jsx("p",{className:h().notes,children:_.notes})]})]}),(0,r.jsxs)("div",{className:h().metaInfo,children:[(0,r.jsxs)("div",{className:h().metaItem,children:[r.jsx("strong",{children:"Created:"})," ",D(_.created_at)]}),(0,r.jsxs)("div",{className:h().metaItem,children:[r.jsx("strong",{children:"Last Updated:"})," ",D(_.updated_at)]}),(0,r.jsxs)("div",{className:h().metaItem,children:[r.jsx("strong",{children:"Customer ID:"})," ",_.id]})]})]}),(0,r.jsxs)("div",{className:h().sidebar,children:[(0,r.jsxs)("div",{className:h().quickActions,children:[r.jsx("h3",{children:"Quick Actions"}),r.jsx(d(),{href:`/admin/customers/${_.id}/edit`,className:h().actionButton,children:"Edit Customer Details"}),r.jsx(d(),{href:`/admin/bookings/new?customer=${_.id}`,className:h().actionButton,children:"Create New Booking"}),r.jsx(d(),{href:`/admin/customers/${_.id}/history`,className:h().actionButton,children:"View Full History"})]}),(0,r.jsxs)("div",{className:h().bookingHistory,children:[r.jsx("h3",{children:"Recent Bookings"}),0===j.length?r.jsx("p",{className:h().noBookings,children:"No bookings found"}):(0,r.jsxs)("div",{className:h().bookingsList,children:[j.slice(0,5).map(e=>(0,r.jsxs)("div",{className:h().bookingItem,children:[(0,r.jsxs)("div",{className:h().bookingHeader,children:[r.jsx("span",{className:h().serviceName,children:e.service_name}),r.jsx("span",{className:h().bookingStatus,style:{backgroundColor:b(e.status)},children:e.status})]}),(0,r.jsxs)("div",{className:h().bookingDetails,children:[r.jsx("div",{children:v(e.booking_date)}),r.jsx("div",{children:e.booking_time}),(0,r.jsxs)("div",{children:["$",e.total_amount]})]})]},e.id)),j.length>5&&(0,r.jsxs)(d(),{href:`/admin/customers/${_.id}/history`,className:h().viewAllBookings,children:["View All ",j.length," Bookings →"]})]})]})]})]})]})]}):r.jsx(u.Z,{children:(0,r.jsxs)("div",{className:h().notFoundContainer,children:[r.jsx("h2",{children:"Customer Not Found"}),r.jsx("p",{children:"The customer you're looking for doesn't exist."}),r.jsx(d(),{href:"/admin/customers",className:h().backButton,children:"← Back to Customers"})]})})}u=(x.then?(await x)():x)[0],a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[2899,6212,1664,7441],()=>t(4406));module.exports=a})();