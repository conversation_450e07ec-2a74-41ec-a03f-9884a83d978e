/**
 * Ocean Soul Sparkles Admin Dashboard - Error Handler Tests
 * Comprehensive tests for the error handling system
 */

import { NextApiResponse } from 'next';
import {
  AppError,
  ErrorCode,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  BusinessLogicError,
  ExternalServiceError,
  handleError,
  sendSuccess,
  validateRequired,
  validateEmail,
  validatePhone,
  validateDate,
  mapDatabaseError
} from '../../../lib/errors/error-handler';
import { createMockApiRequest } from '../../utils/test-helpers';

describe('Error Handler', () => {
  let mockRes: NextApiResponse;

  beforeEach(() => {
    const { res } = createMockApiRequest();
    mockRes = res;
    mockRes.status = jest.fn().mockReturnThis();
    mockRes.json = jest.fn().mockReturnThis();
  });

  describe('AppError', () => {
    it('should create an AppError with correct properties', () => {
      const error = new AppError(
        ErrorCode.VALIDATION_ERROR,
        'Test error message',
        400,
        true,
        { field: 'email' },
        'email'
      );

      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.message).toBe('Test error message');
      expect(error.statusCode).toBe(400);
      expect(error.isOperational).toBe(true);
      expect(error.details).toEqual({ field: 'email' });
      expect(error.field).toBe('email');
      expect(error.requestId).toBeDefined();
      expect(error.timestamp).toBeDefined();
    });

    it('should have default values', () => {
      const error = new AppError(ErrorCode.INTERNAL_ERROR, 'Test error');

      expect(error.statusCode).toBe(500);
      expect(error.isOperational).toBe(true);
      expect(error.details).toBeUndefined();
      expect(error.field).toBeUndefined();
    });
  });

  describe('Specific Error Classes', () => {
    it('should create ValidationError correctly', () => {
      const error = new ValidationError('Invalid email', 'email', { format: 'email' });

      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.statusCode).toBe(400);
      expect(error.field).toBe('email');
      expect(error.details).toEqual({ format: 'email' });
    });

    it('should create AuthenticationError correctly', () => {
      const error = new AuthenticationError();

      expect(error.code).toBe(ErrorCode.UNAUTHORIZED);
      expect(error.statusCode).toBe(401);
      expect(error.message).toBe('Authentication required');
    });

    it('should create AuthorizationError correctly', () => {
      const error = new AuthorizationError();

      expect(error.code).toBe(ErrorCode.FORBIDDEN);
      expect(error.statusCode).toBe(403);
      expect(error.message).toBe('Insufficient permissions');
    });

    it('should create NotFoundError correctly', () => {
      const error = new NotFoundError('Customer', 'cust_123');

      expect(error.code).toBe(ErrorCode.NOT_FOUND);
      expect(error.statusCode).toBe(404);
      expect(error.message).toBe('Customer with ID cust_123 not found');
      expect(error.details).toEqual({ resource: 'Customer', id: 'cust_123' });
    });

    it('should create ConflictError correctly', () => {
      const error = new ConflictError('Email already exists', { email: '<EMAIL>' });

      expect(error.code).toBe(ErrorCode.CONFLICT);
      expect(error.statusCode).toBe(409);
      expect(error.details).toEqual({ email: '<EMAIL>' });
    });

    it('should create BusinessLogicError correctly', () => {
      const error = new BusinessLogicError(
        ErrorCode.BOOKING_CONFLICT,
        'Artist not available',
        { artistId: 'art_123' }
      );

      expect(error.code).toBe(ErrorCode.BOOKING_CONFLICT);
      expect(error.statusCode).toBe(422);
      expect(error.details).toEqual({ artistId: 'art_123' });
    });

    it('should create ExternalServiceError correctly', () => {
      const error = new ExternalServiceError('Square', 'Payment failed', { transactionId: 'tx_123' });

      expect(error.code).toBe(ErrorCode.EXTERNAL_SERVICE_ERROR);
      expect(error.statusCode).toBe(503);
      expect(error.message).toBe('External service error: Square - Payment failed');
      expect(error.details).toEqual({ service: 'Square', transactionId: 'tx_123' });
    });
  });

  describe('handleError', () => {
    it('should handle AppError correctly', () => {
      const error = new ValidationError('Invalid email', 'email');
      handleError(error, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: ErrorCode.VALIDATION_ERROR,
          message: 'Invalid email',
          details: undefined,
          field: 'email'
        },
        meta: {
          requestId: error.requestId,
          timestamp: error.timestamp,
          version: '1.0.0'
        }
      });
    });

    it('should handle unknown errors', () => {
      const error = new Error('Unknown error');
      handleError(error, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: {
            code: ErrorCode.INTERNAL_ERROR,
            message: 'An unexpected error occurred',
            details: undefined,
            field: undefined
          }
        })
      );
    });
  });

  describe('sendSuccess', () => {
    it('should send success response', () => {
      const data = { id: 'test_123', name: 'Test' };
      sendSuccess(mockRes, data, 201, { custom: 'meta' });

      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data,
        meta: expect.objectContaining({
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0.0',
          custom: 'meta'
        })
      });
    });

    it('should use default status code', () => {
      sendSuccess(mockRes, { test: true });

      expect(mockRes.status).toHaveBeenCalledWith(200);
    });
  });

  describe('Validation Functions', () => {
    describe('validateRequired', () => {
      it('should pass for valid data', () => {
        const data = { name: 'John', email: '<EMAIL>' };
        expect(() => validateRequired(data, ['name', 'email'])).not.toThrow();
      });

      it('should throw for missing fields', () => {
        const data = { name: 'John' };
        expect(() => validateRequired(data, ['name', 'email'])).toThrow(ValidationError);
      });

      it('should throw for empty string fields', () => {
        const data = { name: '', email: '<EMAIL>' };
        expect(() => validateRequired(data, ['name', 'email'])).toThrow(ValidationError);
      });

      it('should throw for null fields', () => {
        const data = { name: null, email: '<EMAIL>' };
        expect(() => validateRequired(data, ['name', 'email'])).toThrow(ValidationError);
      });
    });

    describe('validateEmail', () => {
      it('should pass for valid emails', () => {
        expect(() => validateEmail('<EMAIL>')).not.toThrow();
        expect(() => validateEmail('<EMAIL>')).not.toThrow();
      });

      it('should throw for invalid emails', () => {
        expect(() => validateEmail('invalid-email')).toThrow(ValidationError);
        expect(() => validateEmail('test@')).toThrow(ValidationError);
        expect(() => validateEmail('@example.com')).toThrow(ValidationError);
        expect(() => validateEmail('test.example.com')).toThrow(ValidationError);
      });
    });

    describe('validatePhone', () => {
      it('should pass for valid phone numbers', () => {
        expect(() => validatePhone('+61412345678')).not.toThrow();
        expect(() => validatePhone('0412 345 678')).not.toThrow();
        expect(() => validatePhone('(02) 1234-5678')).not.toThrow();
      });

      it('should throw for invalid phone numbers', () => {
        expect(() => validatePhone('123')).toThrow(ValidationError);
        expect(() => validatePhone('abc123def')).toThrow(ValidationError);
        expect(() => validatePhone('')).toThrow(ValidationError);
      });
    });

    describe('validateDate', () => {
      it('should pass for valid dates', () => {
        expect(() => validateDate('2024-06-16')).not.toThrow();
        expect(() => validateDate('2024-06-16T10:00:00Z')).not.toThrow();
      });

      it('should throw for invalid dates', () => {
        expect(() => validateDate('invalid-date')).toThrow(ValidationError);
        expect(() => validateDate('2024-13-01')).toThrow(ValidationError);
        expect(() => validateDate('')).toThrow(ValidationError);
      });
    });
  });

  describe('mapDatabaseError', () => {
    it('should map unique constraint violation', () => {
      const dbError = { code: '23505', constraint: 'customers_email_key' };
      const error = mapDatabaseError(dbError);

      expect(error).toBeInstanceOf(ConflictError);
      expect(error.details).toEqual({ constraint: 'customers_email_key' });
    });

    it('should map foreign key constraint violation', () => {
      const dbError = { code: '23503', constraint: 'bookings_customer_id_fkey' };
      const error = mapDatabaseError(dbError);

      expect(error).toBeInstanceOf(ValidationError);
      expect(error.details).toEqual({ constraint: 'bookings_customer_id_fkey' });
    });

    it('should map not null constraint violation', () => {
      const dbError = { code: '23502', column: 'email' };
      const error = mapDatabaseError(dbError);

      expect(error).toBeInstanceOf(ValidationError);
      expect(error.details).toEqual({ column: 'email' });
    });

    it('should map unknown database errors', () => {
      const dbError = { code: 'UNKNOWN', message: 'Unknown database error' };
      const error = mapDatabaseError(dbError);

      expect(error.code).toBe(ErrorCode.DATABASE_ERROR);
      expect(error.statusCode).toBe(500);
    });
  });
});
