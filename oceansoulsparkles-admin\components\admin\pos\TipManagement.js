import { useState, useEffect } from 'react'
import styles from '@/styles/admin/POS.module.css'

/**
 * TipManagement component - Handles tip calculation and collection in POS
 * Integrates with payment flow to add tips to transactions
 */
export default function TipManagement({ 
  baseAmount, 
  paymentMethod, 
  onTipCalculated, 
  onCancel,
  customerName = 'Customer'
}) {
  const [tipSettings, setTipSettings] = useState(null)
  const [selectedTipType, setSelectedTipType] = useState('percentage') // 'percentage', 'amount', 'none'
  const [selectedPercentage, setSelectedPercentage] = useState(null)
  const [customAmount, setCustomAmount] = useState('')
  const [customPercentage, setCustomPercentage] = useState('')
  const [tipAmount, setTipAmount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load tip settings from system settings
  useEffect(() => {
    loadTipSettings()
  }, [])

  // Calculate tip amount when selection changes
  useEffect(() => {
    calculateTipAmount()
  }, [selectedTipType, selectedPercentage, customAmount, customPercentage, baseAmount])

  const loadTipSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings?category=tips')
      if (response.ok) {
        const data = await response.json()
        const settings = {}
        data.settings?.forEach(setting => {
          settings[setting.key] = setting.value
        })
        setTipSettings(settings)
      }
    } catch (error) {
      console.error('Error loading tip settings:', error)
      // Use default settings if API fails
      setTipSettings({
        enableTips: 'true',
        defaultTipPercentages: '15,18,20,25',
        customTipAllowed: 'true',
        minimumTipAmount: '1.00',
        maximumTipPercentage: '50'
      })
    } finally {
      setLoading(false)
    }
  }

  const calculateTipAmount = () => {
    let amount = 0

    if (selectedTipType === 'percentage') {
      if (selectedPercentage) {
        amount = (baseAmount * selectedPercentage) / 100
      } else if (customPercentage) {
        const percentage = parseFloat(customPercentage)
        if (!isNaN(percentage) && percentage >= 0) {
          amount = (baseAmount * percentage) / 100
        }
      }
    } else if (selectedTipType === 'amount' && customAmount) {
      amount = parseFloat(customAmount)
      if (isNaN(amount) || amount < 0) {
        amount = 0
      }
    }

    // Apply minimum tip amount
    const minTip = parseFloat(tipSettings?.minimumTipAmount || '1.00')
    if (amount > 0 && amount < minTip) {
      amount = minTip
    }

    // Apply maximum tip percentage
    const maxPercentage = parseFloat(tipSettings?.maximumTipPercentage || '50')
    const maxAmount = (baseAmount * maxPercentage) / 100
    if (amount > maxAmount) {
      amount = maxAmount
    }

    setTipAmount(amount)
  }

  const handleConfirmTip = () => {
    const tipData = {
      tipAmount: tipAmount,
      tipMethod: paymentMethod === 'cash' ? 'cash' : 'card',
      tipPercentage: selectedTipType === 'percentage' ? 
        (selectedPercentage || parseFloat(customPercentage)) : 
        ((tipAmount / baseAmount) * 100),
      baseAmount: baseAmount,
      totalAmount: baseAmount + tipAmount
    }

    onTipCalculated(tipData)
  }

  const handleNoTip = () => {
    onTipCalculated({
      tipAmount: 0,
      tipMethod: 'none',
      tipPercentage: 0,
      baseAmount: baseAmount,
      totalAmount: baseAmount
    })
  }

  const getDefaultPercentages = () => {
    if (!tipSettings?.defaultTipPercentages) return [15, 18, 20, 25]
    return tipSettings.defaultTipPercentages.split(',').map(p => parseInt(p.trim()))
  }

  const formatCurrency = (amount) => {
    return `$${amount.toFixed(2)}`
  }

  if (loading) {
    return (
      <div className={styles.tipContainer}>
        <div className={styles.tipHeader}>
          <h3>Loading tip options...</h3>
        </div>
      </div>
    )
  }

  if (tipSettings?.enableTips !== 'true') {
    // Tips disabled, proceed without tip
    handleNoTip()
    return null
  }

  return (
    <div className={styles.tipContainer}>
      <div className={styles.tipHeader}>
        <h3>💰 Add Tip for {customerName}</h3>
        <p>Service Total: {formatCurrency(baseAmount)}</p>
        <button 
          className={styles.tipCancelButton}
          onClick={onCancel}
        >
          ✕
        </button>
      </div>

      {error && (
        <div className={styles.tipError}>
          {error}
        </div>
      )}

      <div className={styles.tipContent}>
        {/* Tip Type Selection */}
        <div className={styles.tipTypeSelector}>
          <button
            className={`${styles.tipTypeButton} ${selectedTipType === 'none' ? styles.active : ''}`}
            onClick={() => setSelectedTipType('none')}
          >
            No Tip
          </button>
          <button
            className={`${styles.tipTypeButton} ${selectedTipType === 'percentage' ? styles.active : ''}`}
            onClick={() => setSelectedTipType('percentage')}
          >
            Percentage
          </button>
          {tipSettings?.customTipAllowed === 'true' && (
            <button
              className={`${styles.tipTypeButton} ${selectedTipType === 'amount' ? styles.active : ''}`}
              onClick={() => setSelectedTipType('amount')}
            >
              Custom Amount
            </button>
          )}
        </div>

        {/* Percentage Options */}
        {selectedTipType === 'percentage' && (
          <div className={styles.tipPercentageOptions}>
            <h4>Select Tip Percentage</h4>
            <div className={styles.tipPercentageGrid}>
              {getDefaultPercentages().map(percentage => (
                <button
                  key={percentage}
                  className={`${styles.tipPercentageButton} ${selectedPercentage === percentage ? styles.active : ''}`}
                  onClick={() => {
                    setSelectedPercentage(percentage)
                    setCustomPercentage('')
                  }}
                >
                  {percentage}%
                  <span className={styles.tipAmount}>
                    {formatCurrency((baseAmount * percentage) / 100)}
                  </span>
                </button>
              ))}
            </div>
            
            {tipSettings?.customTipAllowed === 'true' && (
              <div className={styles.customPercentageInput}>
                <label>Custom Percentage:</label>
                <input
                  type="number"
                  min="0"
                  max={tipSettings?.maximumTipPercentage || '50'}
                  step="0.1"
                  value={customPercentage}
                  onChange={(e) => {
                    setCustomPercentage(e.target.value)
                    setSelectedPercentage(null)
                  }}
                  placeholder="Enter %"
                />
                <span>%</span>
              </div>
            )}
          </div>
        )}

        {/* Custom Amount */}
        {selectedTipType === 'amount' && (
          <div className={styles.tipCustomAmount}>
            <h4>Enter Tip Amount</h4>
            <div className={styles.customAmountInput}>
              <span className={styles.currencySymbol}>$</span>
              <input
                type="number"
                min={tipSettings?.minimumTipAmount || '1.00'}
                step="0.01"
                value={customAmount}
                onChange={(e) => setCustomAmount(e.target.value)}
                placeholder="0.00"
              />
            </div>
          </div>
        )}

        {/* Tip Summary */}
        {selectedTipType !== 'none' && tipAmount > 0 && (
          <div className={styles.tipSummary}>
            <div className={styles.tipSummaryRow}>
              <span>Service Total:</span>
              <span>{formatCurrency(baseAmount)}</span>
            </div>
            <div className={styles.tipSummaryRow}>
              <span>Tip Amount:</span>
              <span>{formatCurrency(tipAmount)}</span>
            </div>
            <div className={`${styles.tipSummaryRow} ${styles.total}`}>
              <span>Total with Tip:</span>
              <span>{formatCurrency(baseAmount + tipAmount)}</span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className={styles.tipActions}>
          {selectedTipType === 'none' ? (
            <button
              className={styles.tipConfirmButton}
              onClick={handleNoTip}
            >
              Continue Without Tip
            </button>
          ) : (
            <button
              className={styles.tipConfirmButton}
              onClick={handleConfirmTip}
              disabled={tipAmount <= 0}
            >
              Add {formatCurrency(tipAmount)} Tip
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
