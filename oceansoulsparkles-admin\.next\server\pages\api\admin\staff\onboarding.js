"use strict";(()=>{var e={};e.id=8146,e.ids=[8146],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},2353:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>u,routeModule:()=>g});var i={};r.r(i),r.d(i,{default:()=>m});var s=r(1802),o=r(7153),n=r(8781),a=r(7474),c=r(2885);let d=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,c.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",d);async function m(e,t){let r=Math.random().toString(36).substring(2,8);console.log(`[${r}] Staff onboarding API called - ${e.method}`);try{let i=await (0,a.SA)(e);if(!i.success)return t.status(401).json({error:"Authentication required",message:i.message||"Authentication failed",requestId:r});let{user:s}=i;if(!s)return t.status(401).json({error:"User not found",requestId:r});if("Admin"!==s.role&&"DEV"!==s.role)return t.status(403).json({error:"Insufficient permissions",message:"Only admins can manage staff onboarding",requestId:r});if("GET"===e.method){let{staff_id:i}=e.query;if(!i)return t.status(400).json({error:"Staff ID required",message:"Staff ID parameter is required",requestId:r});let{data:s,error:o}=await l.from("staff_onboarding_checklist").select(`
          id,
          checklist_item,
          category,
          description,
          is_required,
          is_completed,
          completed_at,
          due_date,
          notes,
          created_at
        `).eq("staff_id",i).order("category",{ascending:!0}).order("checklist_item",{ascending:!0});if(o)return console.error(`[${r}] Database error:`,o),t.status(500).json({error:"Failed to fetch onboarding checklist",message:o.message,requestId:r});let n=s?.length||0,a=s?.filter(e=>e.is_completed).length||0,c=s?.filter(e=>e.is_required).length||0,d=s?.filter(e=>e.is_required&&e.is_completed).length||0;return t.status(200).json({checklist:s||[],statistics:{total:n,completed:a,required:c,completedRequired:d,completionPercentage:n>0?Math.round(a/n*100):0,requiredCompletionPercentage:c>0?Math.round(d/c*100):0},requestId:r})}if("POST"===e.method){let{staff_id:i,action:o,checklist_item_id:n,notes:a}=e.body;if(!i||!o)return t.status(400).json({error:"Missing required fields",message:"Staff ID and action are required",requestId:r});if("initialize"===o){let e=[{item:"Complete personal information form",category:"documentation",required:!0,description:"Fill out all personal details and emergency contacts"},{item:"Provide identification documents",category:"documentation",required:!0,description:"Driver's license, passport, or other valid ID"},{item:"Submit tax file number (TFN)",category:"documentation",required:!0,description:"Required for payroll processing"},{item:"Complete bank details form",category:"documentation",required:!0,description:"For salary and tip payments"},{item:"Sign employment contract",category:"documentation",required:!0,description:"Review and sign employment agreement"},{item:"Complete Health & Safety training",category:"training",required:!0,description:"Basic health and safety protocols"},{item:"Complete Customer Service training",category:"training",required:!0,description:"Professional customer service standards"},{item:"Complete POS System training",category:"training",required:!0,description:"Learn to use the point-of-sale system"},{item:"Receive workspace tour",category:"access",required:!0,description:"Tour of facilities and workspace assignment"},{item:"Receive equipment and supplies",category:"equipment",required:!0,description:"Basic tools and supplies for role"},{item:"Set up system access accounts",category:"access",required:!0,description:"Admin system login and permissions"},{item:"Meet team members",category:"access",required:!1,description:"Introduction to colleagues and team"},{item:"Review policies and procedures",category:"documentation",required:!0,description:"Company policies and standard procedures"},{item:"Complete emergency procedures training",category:"training",required:!0,description:"Emergency response and first aid basics"}].map(e=>({staff_id:i,checklist_item:e.item,category:e.category,description:e.description,is_required:e.required,due_date:new Date(Date.now()+6048e5).toISOString().split("T")[0]})),{data:s,error:o}=await l.from("staff_onboarding_checklist").insert(e).select();if(o)return console.error(`[${r}] Error creating checklist:`,o),t.status(500).json({error:"Failed to initialize onboarding checklist",message:o.message,requestId:r});return t.status(201).json({checklist:s,message:"Onboarding checklist initialized successfully",requestId:r})}if("complete_item"===o){if(!n)return t.status(400).json({error:"Checklist item ID required",message:"Checklist item ID is required for completion",requestId:r});let{data:e,error:o}=await l.from("staff_onboarding_checklist").update({is_completed:!0,completed_at:new Date().toISOString(),completed_by:s.id,notes:a||null}).eq("id",n).eq("staff_id",i).select().single();if(o)return console.error(`[${r}] Error completing item:`,o),t.status(500).json({error:"Failed to complete checklist item",message:o.message,requestId:r});return t.status(200).json({item:e,message:"Checklist item completed successfully",requestId:r})}if("uncomplete_item"===o){if(!n)return t.status(400).json({error:"Checklist item ID required",message:"Checklist item ID is required for uncompletion",requestId:r});let{data:e,error:s}=await l.from("staff_onboarding_checklist").update({is_completed:!1,completed_at:null,completed_by:null,notes:a||null}).eq("id",n).eq("staff_id",i).select().single();if(s)return console.error(`[${r}] Error uncompleting item:`,s),t.status(500).json({error:"Failed to uncomplete checklist item",message:s.message,requestId:r});return t.status(200).json({item:e,message:"Checklist item uncompleted successfully",requestId:r})}return t.status(400).json({error:"Invalid action",message:"Action must be initialize, complete_item, or uncomplete_item",requestId:r})}return t.status(405).json({error:"Method not allowed",requestId:r})}catch(e){return console.error(`[${r}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:e instanceof Error?e.message:"Unknown error",requestId:r})}}let u=(0,n.l)(i,"default"),p=(0,n.l)(i,"config"),g=new s.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/staff/onboarding",pathname:"/api/admin/staff/onboarding",bundlePath:"",filename:""},userland:i})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[2805],()=>r(2353));module.exports=i})();