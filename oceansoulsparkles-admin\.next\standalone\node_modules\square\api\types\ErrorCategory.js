"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorCategory = void 0;
exports.ErrorCategory = {
    ApiError: "API_ERROR",
    AuthenticationError: "AUTHENTICATION_ERROR",
    InvalidRequestError: "INVALID_REQUEST_ERROR",
    RateLimitError: "RATE_LIMIT_ERROR",
    PaymentMethodError: "PAYMENT_METHOD_ERROR",
    RefundError: "REFUND_ERROR",
    MerchantSubscriptionError: "MERCHANT_SUBSCRIPTION_ERROR",
    ExternalVendorError: "EXTERNAL_VENDOR_ERROR",
};
