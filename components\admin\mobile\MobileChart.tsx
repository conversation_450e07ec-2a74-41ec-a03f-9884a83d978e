/**
 * Ocean Soul Sparkles - Mobile Chart Component
 * Touch-optimized charts with mobile-friendly interactions
 */

import React, { useRef, useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  ChartOptions,
  ChartData
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import styles from '../../../styles/admin/mobile/MobileChart.module.css';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface MobileChartProps {
  type: 'line' | 'bar' | 'doughnut';
  data: ChartData<any>;
  title: string;
  height?: number;
  showLegend?: boolean;
  showTooltips?: boolean;
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  className?: string;
}

export default function MobileChart({
  type,
  data,
  title,
  height = 300,
  showLegend = true,
  showTooltips = true,
  responsive = true,
  maintainAspectRatio = false,
  className = ''
}: MobileChartProps) {
  const chartRef = useRef<any>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [chartSize, setChartSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const updateChartSize = () => {
      if (chartRef.current) {
        const container = chartRef.current.canvas?.parentElement;
        if (container) {
          setChartSize({
            width: container.clientWidth,
            height: container.clientHeight
          });
        }
      }
    };

    updateChartSize();
    window.addEventListener('resize', updateChartSize);
    return () => window.removeEventListener('resize', updateChartSize);
  }, []);

  const getMobileOptions = (): ChartOptions<any> => {
    const baseOptions: ChartOptions<any> = {
      responsive,
      maintainAspectRatio,
      devicePixelRatio: window.devicePixelRatio || 1,
      interaction: {
        mode: 'nearest',
        intersect: false,
      },
      plugins: {
        title: {
          display: true,
          text: title,
          font: {
            size: isMobile ? 14 : 16,
            weight: '600'
          },
          color: '#2d3748',
          padding: {
            top: 10,
            bottom: 20
          }
        },
        legend: {
          display: showLegend,
          position: isMobile ? 'bottom' : 'top',
          labels: {
            font: {
              size: isMobile ? 11 : 12
            },
            color: '#4a5568',
            padding: isMobile ? 15 : 20,
            usePointStyle: true,
            pointStyle: 'circle'
          }
        },
        tooltip: {
          enabled: showTooltips,
          backgroundColor: 'rgba(45, 55, 72, 0.95)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#667eea',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          titleFont: {
            size: isMobile ? 12 : 14,
            weight: '600'
          },
          bodyFont: {
            size: isMobile ? 11 : 12
          },
          padding: isMobile ? 8 : 12,
          caretSize: isMobile ? 4 : 6,
          mode: 'nearest',
          intersect: false
        }
      },
      scales: type !== 'doughnut' ? {
        x: {
          grid: {
            display: !isMobile,
            color: 'rgba(226, 232, 240, 0.5)'
          },
          ticks: {
            font: {
              size: isMobile ? 10 : 11
            },
            color: '#718096',
            maxRotation: isMobile ? 45 : 0,
            minRotation: 0
          }
        },
        y: {
          grid: {
            color: 'rgba(226, 232, 240, 0.5)'
          },
          ticks: {
            font: {
              size: isMobile ? 10 : 11
            },
            color: '#718096'
          }
        }
      } : undefined
    };

    // Mobile-specific optimizations
    if (isMobile) {
      baseOptions.elements = {
        point: {
          radius: type === 'line' ? 4 : 0,
          hoverRadius: 6,
          hitRadius: 10
        },
        line: {
          borderWidth: 2,
          tension: 0.1
        },
        bar: {
          borderRadius: 4
        }
      };

      // Increase touch targets for mobile
      if (baseOptions.interaction) {
        baseOptions.interaction.mode = 'index';
        baseOptions.interaction.intersect = false;
      }
    }

    return baseOptions;
  };

  const renderChart = () => {
    const options = getMobileOptions();

    switch (type) {
      case 'line':
        return <Line ref={chartRef} data={data} options={options} height={height} />;
      case 'bar':
        return <Bar ref={chartRef} data={data} options={options} height={height} />;
      case 'doughnut':
        return <Doughnut ref={chartRef} data={data} options={options} height={height} />;
      default:
        return null;
    }
  };

  return (
    <div className={`${styles.mobileChart} ${className}`}>
      <div className={styles.chartContainer}>
        {renderChart()}
      </div>
      
      {/* Mobile Chart Controls */}
      {isMobile && (
        <div className={styles.chartControls}>
          <div className={styles.chartInfo}>
            <span className={styles.chartType}>
              {type.charAt(0).toUpperCase() + type.slice(1)} Chart
            </span>
            <span className={styles.chartSize}>
              {chartSize.width}×{chartSize.height}
            </span>
          </div>
          
          <div className={styles.chartActions}>
            <button
              className={styles.chartAction}
              onClick={() => {
                if (chartRef.current) {
                  const chart = chartRef.current;
                  chart.resetZoom?.();
                }
              }}
              title="Reset Zoom"
            >
              🔍
            </button>
            
            <button
              className={styles.chartAction}
              onClick={() => {
                if (chartRef.current) {
                  const chart = chartRef.current;
                  const url = chart.toBase64Image?.();
                  if (url) {
                    const link = document.createElement('a');
                    link.download = `${title.replace(/\s+/g, '_')}_chart.png`;
                    link.href = url;
                    link.click();
                  }
                }
              }}
              title="Download Chart"
            >
              📥
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
