(()=>{var e={};e.id=2133,e.ids=[2133,660],e.modules={3781:e=>{e.exports={chartsContainer:"Charts_chartsContainer__Z3GOI",chartCard:"Charts_chartCard__M2YHl",chartWrapper:"Charts_chartWrapper__EcBP0",summaryTable:"Charts_summaryTable__sgTuW",statsGrid:"Charts_statsGrid__uJA_q",statCard:"Charts_statCard__tjCSi",statValue:"Charts_statValue__BpX_X",statLabel:"Charts_statLabel__WzqYN",tableWrapper:"Charts_tableWrapper__a26ts",statusDot:"Charts_statusDot__XIdWc",completed:"Charts_completed__rTGXJ",confirmed:"Charts_confirmed__AJ6nZ",cancelled:"Charts_cancelled__GVhrs",pending:"Charts_pending__9GvEX",rescheduled:"Charts_rescheduled__KiNRp",exportControls:"Charts_exportControls__dfk4h",exportBtn:"Charts_exportBtn__BTLeV",chartLoading:"Charts_chartLoading__T6Bed",loadingSpinner:"Charts_loadingSpinner__se_nt",spin:"Charts_spin__i79T8",chartError:"Charts_chartError__93t8b",errorIcon:"Charts_errorIcon__U84PV"}},4007:e=>{e.exports={reportsContainer:"Reports_reportsContainer__jxvjM",header:"Reports_header__fP_EO",title:"Reports_title__5vYcA",headerActions:"Reports_headerActions__tmZ92",dateRangeSelect:"Reports_dateRangeSelect__x4eJU",exportBtn:"Reports_exportBtn__FaWeK",reportsContent:"Reports_reportsContent__XPw2q",tabNavigation:"Reports_tabNavigation__NTIN7",tabButton:"Reports_tabButton__9xCN6",active:"Reports_active__nqimh",tabContent:"Reports_tabContent__Zhb5q",overviewSection:"Reports_overviewSection___0TH8",metricsGrid:"Reports_metricsGrid__ZKmdF",metricCard:"Reports_metricCard__jKS9w",metricValue:"Reports_metricValue__kUym3",metricChange:"Reports_metricChange__GNlLj",revenueSection:"Reports_revenueSection__LatZ8",bookingsSection:"Reports_bookingsSection___cGFc",customersSection:"Reports_customersSection__ov6C9",chartPlaceholder:"Reports_chartPlaceholder__6brrL",serviceRevenueList:"Reports_serviceRevenueList__xVcjj",serviceRevenueItem:"Reports_serviceRevenueItem__1qsU4",serviceName:"Reports_serviceName__wNXiq",serviceAmount:"Reports_serviceAmount__tLjrD",servicePercentage:"Reports_servicePercentage__SSY4q",bookingStats:"Reports_bookingStats__5l7d_",customerStats:"Reports_customerStats__r_U0J",statCard:"Reports_statCard__pQEyX",statusItem:"Reports_statusItem__uXKuN",statusName:"Reports_statusName___5Fbp",statusCount:"Reports_statusCount__dfiZL",statusPercentage:"Reports_statusPercentage__FRRRB",cancellationRate:"Reports_cancellationRate__OG0h2",clvValue:"Reports_clvValue__0w8WS",loadingContainer:"Reports_loadingContainer__VsUKj",loadingSpinner:"Reports_loadingSpinner___mn7n",spin:"Reports_spin__fjQm8",accessDenied:"Reports_accessDenied__AqwdO"}},3888:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>g,default:()=>p,getServerSideProps:()=>b,getStaticPaths:()=>m,getStaticProps:()=>h,reportWebVitals:()=>v,routeModule:()=>y,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>C,unstable_getStaticPaths:()=>_,unstable_getStaticProps:()=>x});var a=r(7093),i=r(5244),o=r(1323),n=r(2899),l=r.n(n),c=r(6814),d=r(541),u=e([c,d]);[c,d]=u.then?(await u)():u;let p=(0,o.l)(d,"default"),h=(0,o.l)(d,"getStaticProps"),m=(0,o.l)(d,"getStaticPaths"),b=(0,o.l)(d,"getServerSideProps"),g=(0,o.l)(d,"config"),v=(0,o.l)(d,"reportWebVitals"),x=(0,o.l)(d,"unstable_getStaticProps"),_=(0,o.l)(d,"unstable_getStaticPaths"),C=(0,o.l)(d,"unstable_getStaticParams"),f=(0,o.l)(d,"unstable_getServerProps"),j=(0,o.l)(d,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/admin/reports",pathname:"/admin/reports",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});s()}catch(e){s(e)}})},4544:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Z:()=>d});var a=r(997);r(6689);var i=r(3767),o=r(2189),n=r(3781),l=r.n(n),c=e([i,o]);function d({data:e,dateRange:t}){let r={labels:e.statusBreakdown.map(e=>e.status),datasets:[{label:"Booking Status",data:e.statusBreakdown.map(e=>e.count),backgroundColor:["rgba(34, 197, 94, 0.8)","rgba(59, 130, 246, 0.8)","rgba(239, 68, 68, 0.8)","rgba(245, 158, 11, 0.8)","rgba(168, 85, 247, 0.8)"],borderColor:["rgb(34, 197, 94)","rgb(59, 130, 246)","rgb(239, 68, 68)","rgb(245, 158, 11)","rgb(168, 85, 247)"],borderWidth:2,hoverOffset:10}]},s=e.daily?{labels:e.daily.map(e=>new Date(e.date).toLocaleDateString("en-AU",{month:"short",day:"numeric"})),datasets:[{label:"Daily Bookings",data:e.daily.map(e=>e.bookings),backgroundColor:"rgba(102, 126, 234, 0.8)",borderColor:"rgb(102, 126, 234)",borderWidth:2,borderRadius:6,borderSkipped:!1}]}:null,i={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right",labels:{font:{family:"Inter, sans-serif",size:12},color:"#374151",padding:15,usePointStyle:!0,pointStyle:"circle"}},title:{display:!0,text:`Booking Status Distribution - ${t}`,font:{family:"Inter, sans-serif",size:16,weight:"bold"},color:"#1f2937",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"rgba(102, 126, 234, 0.8)",borderWidth:1,cornerRadius:8,callbacks:{label:function(e){let t=(e.parsed/e.dataset.data.reduce((e,t)=>e+t,0)*100).toFixed(1);return`${e.label}: ${e.parsed} bookings (${t}%)`}}}}},n={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{font:{family:"Inter, sans-serif",size:12},color:"#374151"}},title:{display:!0,text:`Daily Booking Trends - ${t}`,font:{family:"Inter, sans-serif",size:16,weight:"bold"},color:"#1f2937",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"rgba(102, 126, 234, 0.8)",borderWidth:1,cornerRadius:8,displayColors:!1,callbacks:{label:function(e){return`Bookings: ${e.parsed.y}`}}}},scales:{x:{grid:{color:"rgba(0, 0, 0, 0.05)"},ticks:{font:{family:"Inter, sans-serif",size:11},color:"#6b7280"}},y:{grid:{color:"rgba(0, 0, 0, 0.05)"},ticks:{font:{family:"Inter, sans-serif",size:11},color:"#6b7280",stepSize:1}}}};return(0,a.jsxs)("div",{className:l().chartsContainer,children:[a.jsx("div",{className:l().chartCard,children:a.jsx("div",{className:l().chartWrapper,children:a.jsx(o.Pie,{data:r,options:i})})}),s&&a.jsx("div",{className:l().chartCard,children:a.jsx("div",{className:l().chartWrapper,children:a.jsx(o.Bar,{data:s,options:n})})}),(0,a.jsxs)("div",{className:l().summaryTable,children:[a.jsx("h3",{children:"Booking Statistics"}),(0,a.jsxs)("div",{className:l().statsGrid,children:[(0,a.jsxs)("div",{className:l().statCard,children:[a.jsx("div",{className:l().statValue,children:e.statusBreakdown.reduce((e,t)=>e+t.count,0)}),a.jsx("div",{className:l().statLabel,children:"Total Bookings"})]}),(0,a.jsxs)("div",{className:l().statCard,children:[a.jsx("div",{className:l().statValue,children:e.statusBreakdown.find(e=>"Completed"===e.status)?.count||0}),a.jsx("div",{className:l().statLabel,children:"Completed"})]}),(0,a.jsxs)("div",{className:l().statCard,children:[(0,a.jsxs)("div",{className:l().statValue,children:[e.cancellationRate.toFixed(1),"%"]}),a.jsx("div",{className:l().statLabel,children:"Cancellation Rate"})]}),(0,a.jsxs)("div",{className:l().statCard,children:[(0,a.jsxs)("div",{className:l().statValue,children:[(100-e.cancellationRate).toFixed(1),"%"]}),a.jsx("div",{className:l().statLabel,children:"Success Rate"})]})]}),a.jsx("div",{className:l().tableWrapper,children:(0,a.jsxs)("table",{children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{children:[a.jsx("th",{children:"Status"}),a.jsx("th",{children:"Count"}),a.jsx("th",{children:"Percentage"})]})}),a.jsx("tbody",{children:e.statusBreakdown.map((e,t)=>(0,a.jsxs)("tr",{children:[(0,a.jsxs)("td",{children:[a.jsx("span",{className:`${l().statusDot} ${l()[e.status.toLowerCase()]}`}),e.status]}),a.jsx("td",{children:e.count}),(0,a.jsxs)("td",{children:[e.percentage.toFixed(1),"%"]})]},t))})]})})]})]})}[i,o]=c.then?(await c)():c,i.Chart.register(i.CategoryScale,i.LinearScale,i.BarElement,i.Title,i.Tooltip,i.Legend,i.ArcElement),s()}catch(e){s(e)}})},2174:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Z:()=>d});var a=r(997);r(6689);var i=r(3767),o=r(2189),n=r(3781),l=r.n(n),c=e([i,o]);function d({data:e,dateRange:t}){let r={labels:["New Customers","Returning Customers"],datasets:[{label:"Customer Type",data:[e.newCustomers,e.returningCustomers],backgroundColor:["rgba(34, 197, 94, 0.8)","rgba(59, 130, 246, 0.8)"],borderColor:["rgb(34, 197, 94)","rgb(59, 130, 246)"],borderWidth:2,hoverOffset:10}]},s=e.growth?{labels:e.growth.map(e=>e.month),datasets:[{label:"New Customers",data:e.growth.map(e=>e.customers),borderColor:"rgb(102, 126, 234)",backgroundColor:"rgba(102, 126, 234, 0.1)",borderWidth:3,fill:!0,tension:.4,pointBackgroundColor:"rgb(102, 126, 234)",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:6,pointHoverRadius:8}]}:null,i=e.demographics?{labels:e.demographics.map(e=>e.ageGroup),datasets:[{label:"Customer Count",data:e.demographics.map(e=>e.count),backgroundColor:["rgba(239, 68, 68, 0.8)","rgba(245, 158, 11, 0.8)","rgba(34, 197, 94, 0.8)","rgba(59, 130, 246, 0.8)","rgba(168, 85, 247, 0.8)"],borderColor:["rgb(239, 68, 68)","rgb(245, 158, 11)","rgb(34, 197, 94)","rgb(59, 130, 246)","rgb(168, 85, 247)"],borderWidth:2,borderRadius:6,borderSkipped:!1}]}:null,n={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right",labels:{font:{family:"Inter, sans-serif",size:12},color:"#374151",padding:15,usePointStyle:!0,pointStyle:"circle"}},title:{display:!0,text:`Customer Type Distribution - ${t}`,font:{family:"Inter, sans-serif",size:16,weight:"bold"},color:"#1f2937",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"rgba(102, 126, 234, 0.8)",borderWidth:1,cornerRadius:8,callbacks:{label:function(e){let t=e.dataset.data.reduce((e,t)=>e+t,0),r=(e.parsed/t*100).toFixed(1);return`${e.label}: ${e.parsed} customers (${r}%)`}}}}};return(0,a.jsxs)("div",{className:l().chartsContainer,children:[a.jsx("div",{className:l().chartCard,children:a.jsx("div",{className:l().chartWrapper,children:a.jsx(o.Doughnut,{data:r,options:n})})}),s&&a.jsx("div",{className:l().chartCard,children:a.jsx("div",{className:l().chartWrapper,children:a.jsx(o.Line,{data:s,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{font:{family:"Inter, sans-serif",size:12},color:"#374151"}},title:{display:!0,text:"Customer Growth Trend",font:{family:"Inter, sans-serif",size:16,weight:"bold"},color:"#1f2937",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"rgba(102, 126, 234, 0.8)",borderWidth:1,cornerRadius:8,displayColors:!1,callbacks:{label:function(e){return`New Customers: ${e.parsed.y}`}}}},scales:{x:{grid:{color:"rgba(0, 0, 0, 0.05)"},ticks:{font:{family:"Inter, sans-serif",size:11},color:"#6b7280"}},y:{grid:{color:"rgba(0, 0, 0, 0.05)"},ticks:{font:{family:"Inter, sans-serif",size:11},color:"#6b7280",stepSize:1}}}}})})}),i&&a.jsx("div",{className:l().chartCard,children:a.jsx("div",{className:l().chartWrapper,children:a.jsx(o.Bar,{data:i,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},title:{display:!0,text:"Customer Demographics",font:{family:"Inter, sans-serif",size:16,weight:"bold"},color:"#1f2937",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"rgba(102, 126, 234, 0.8)",borderWidth:1,cornerRadius:8,displayColors:!1,callbacks:{label:function(e){return`Customers: ${e.parsed.y}`}}}},scales:{x:{grid:{color:"rgba(0, 0, 0, 0.05)"},ticks:{font:{family:"Inter, sans-serif",size:11},color:"#6b7280"}},y:{grid:{color:"rgba(0, 0, 0, 0.05)"},ticks:{font:{family:"Inter, sans-serif",size:11},color:"#6b7280",stepSize:1}}}}})})}),(0,a.jsxs)("div",{className:l().summaryTable,children:[a.jsx("h3",{children:"Customer Metrics"}),(0,a.jsxs)("div",{className:l().statsGrid,children:[(0,a.jsxs)("div",{className:l().statCard,children:[a.jsx("div",{className:l().statValue,children:e.newCustomers}),a.jsx("div",{className:l().statLabel,children:"New Customers"})]}),(0,a.jsxs)("div",{className:l().statCard,children:[a.jsx("div",{className:l().statValue,children:e.returningCustomers}),a.jsx("div",{className:l().statLabel,children:"Returning Customers"})]}),(0,a.jsxs)("div",{className:l().statCard,children:[(0,a.jsxs)("div",{className:l().statValue,children:["$",e.customerLifetimeValue.toFixed(2)]}),a.jsx("div",{className:l().statLabel,children:"Avg. Lifetime Value"})]}),(0,a.jsxs)("div",{className:l().statCard,children:[(0,a.jsxs)("div",{className:l().statValue,children:[(e.returningCustomers/(e.newCustomers+e.returningCustomers)*100).toFixed(1),"%"]}),a.jsx("div",{className:l().statLabel,children:"Retention Rate"})]})]})]})]})}[i,o]=c.then?(await c)():c,i.Chart.register(i.CategoryScale,i.LinearScale,i.PointElement,i.LineElement,i.BarElement,i.Title,i.Tooltip,i.Legend,i.ArcElement),s()}catch(e){s(e)}})},9867:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Z:()=>d});var a=r(997);r(6689);var i=r(3767),o=r(2189),n=r(3781),l=r.n(n),c=e([i,o]);function d({data:e,dateRange:t}){let r={labels:e.daily.map(e=>new Date(e.date).toLocaleDateString("en-AU",{month:"short",day:"numeric"})),datasets:[{label:"Daily Revenue",data:e.daily.map(e=>e.amount),borderColor:"rgb(102, 126, 234)",backgroundColor:"rgba(102, 126, 234, 0.1)",borderWidth:3,fill:!0,tension:.4,pointBackgroundColor:"rgb(102, 126, 234)",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:6,pointHoverRadius:8}]},s={labels:e.byService.map(e=>e.service),datasets:[{label:"Revenue by Service",data:e.byService.map(e=>e.amount),backgroundColor:["rgba(102, 126, 234, 0.8)","rgba(118, 75, 162, 0.8)","rgba(255, 99, 132, 0.8)","rgba(54, 162, 235, 0.8)","rgba(255, 205, 86, 0.8)","rgba(75, 192, 192, 0.8)"],borderColor:["rgb(102, 126, 234)","rgb(118, 75, 162)","rgb(255, 99, 132)","rgb(54, 162, 235)","rgb(255, 205, 86)","rgb(75, 192, 192)"],borderWidth:2,hoverOffset:10}]},i={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{font:{family:"Inter, sans-serif",size:12},color:"#374151"}},title:{display:!0,text:`Daily Revenue Trend - ${t}`,font:{family:"Inter, sans-serif",size:16,weight:"bold"},color:"#1f2937",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"rgba(102, 126, 234, 0.8)",borderWidth:1,cornerRadius:8,displayColors:!1,callbacks:{label:function(e){return`Revenue: $${e.parsed.y.toFixed(2)}`}}}},scales:{x:{grid:{color:"rgba(0, 0, 0, 0.05)"},ticks:{font:{family:"Inter, sans-serif",size:11},color:"#6b7280"}},y:{grid:{color:"rgba(0, 0, 0, 0.05)"},ticks:{font:{family:"Inter, sans-serif",size:11},color:"#6b7280",callback:function(e){return"$"+e.toFixed(0)}}}}};return(0,a.jsxs)("div",{className:l().chartsContainer,children:[a.jsx("div",{className:l().chartCard,children:a.jsx("div",{className:l().chartWrapper,children:a.jsx(o.Line,{data:r,options:i})})}),a.jsx("div",{className:l().chartCard,children:a.jsx("div",{className:l().chartWrapper,children:a.jsx(o.Doughnut,{data:s,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right",labels:{font:{family:"Inter, sans-serif",size:12},color:"#374151",padding:15,usePointStyle:!0,pointStyle:"circle"}},title:{display:!0,text:"Revenue by Service",font:{family:"Inter, sans-serif",size:16,weight:"bold"},color:"#1f2937",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"rgba(102, 126, 234, 0.8)",borderWidth:1,cornerRadius:8,callbacks:{label:function(e){let t=(e.parsed/e.dataset.data.reduce((e,t)=>e+t,0)*100).toFixed(1);return`${e.label}: $${e.parsed.toFixed(2)} (${t}%)`}}}}}})})}),(0,a.jsxs)("div",{className:l().summaryTable,children:[a.jsx("h3",{children:"Service Revenue Breakdown"}),a.jsx("div",{className:l().tableWrapper,children:(0,a.jsxs)("table",{children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{children:[a.jsx("th",{children:"Service"}),a.jsx("th",{children:"Revenue"}),a.jsx("th",{children:"Percentage"})]})}),a.jsx("tbody",{children:e.byService.map((e,t)=>(0,a.jsxs)("tr",{children:[a.jsx("td",{children:e.service}),(0,a.jsxs)("td",{children:["$",e.amount.toFixed(2)]}),(0,a.jsxs)("td",{children:[e.percentage.toFixed(1),"%"]})]},t))})]})})]})]})}[i,o]=c.then?(await c)():c,i.Chart.register(i.CategoryScale,i.LinearScale,i.PointElement,i.LineElement,i.BarElement,i.Title,i.Tooltip,i.Legend,i.ArcElement),s()}catch(e){s(e)}})},541:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>g});var a=r(997),i=r(6689),o=r(968),n=r.n(o),l=r(8568),c=r(4845),d=r(9867),u=r(4544),p=r(2174),h=r(4007),m=r.n(h),b=e([c,d,u,p]);function g(){let{user:e,loading:t}=(0,l.a)(),[r,s]=(0,i.useState)(!0),[o,h]=(0,i.useState)("overview"),[b,g]=(0,i.useState)("last30days"),[v,x]=(0,i.useState)({overview:{totalRevenue:0,totalBookings:0,totalCustomers:0,averageBookingValue:0,revenueGrowth:0,bookingGrowth:0},revenue:{daily:[],monthly:[],byService:[],byArtist:[]},bookings:{statusBreakdown:[],servicePopularity:[],timeSlotAnalysis:[],cancellationRate:0},customers:{newCustomers:0,returningCustomers:0,customerLifetimeValue:0,topCustomers:[]}}),_=async e=>{try{let t=await fetch(`/api/admin/reports/export?format=${e}&range=${b}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}});if(t.ok){let r=await t.blob(),s=window.URL.createObjectURL(r),a=document.createElement("a");a.href=s,a.download=`ocean-soul-sparkles-report-${b}.${e}`,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(s),document.body.removeChild(a)}else alert("Export feature not available yet")}catch(e){console.error("Error exporting report:",e),alert("Export feature not available yet")}},C=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),f=e=>`${e>=0?"+":""}${e.toFixed(1)}%`;return t||r?a.jsx(c.Z,{children:(0,a.jsxs)("div",{className:m().loadingContainer,children:[a.jsx("div",{className:m().loadingSpinner}),a.jsx("p",{children:"Loading reports..."})]})}):e?["DEV","Admin"].includes(e.role)?(0,a.jsxs)(c.Z,{children:[(0,a.jsxs)(n(),{children:[a.jsx("title",{children:"Reports & Analytics | Ocean Soul Sparkles Admin"}),a.jsx("meta",{name:"description",content:"Business analytics and reporting dashboard"})]}),(0,a.jsxs)("div",{className:m().reportsContainer,children:[(0,a.jsxs)("header",{className:m().header,children:[a.jsx("h1",{className:m().title,children:"Reports & Analytics"}),(0,a.jsxs)("div",{className:m().headerActions,children:[(0,a.jsxs)("select",{value:b,onChange:e=>g(e.target.value),className:m().dateRangeSelect,children:[a.jsx("option",{value:"last7days",children:"Last 7 Days"}),a.jsx("option",{value:"last30days",children:"Last 30 Days"}),a.jsx("option",{value:"last90days",children:"Last 90 Days"}),a.jsx("option",{value:"thisyear",children:"This Year"}),a.jsx("option",{value:"custom",children:"Custom Range"})]}),a.jsx("button",{onClick:()=>_("pdf"),className:m().exportBtn,children:"Export PDF"}),a.jsx("button",{onClick:()=>_("csv"),className:m().exportBtn,children:"Export CSV"})]})]}),(0,a.jsxs)("div",{className:m().reportsContent,children:[(0,a.jsxs)("nav",{className:m().tabNavigation,children:[a.jsx("button",{className:`${m().tabButton} ${"overview"===o?m().active:""}`,onClick:()=>h("overview"),children:"Overview"}),a.jsx("button",{className:`${m().tabButton} ${"revenue"===o?m().active:""}`,onClick:()=>h("revenue"),children:"Revenue"}),a.jsx("button",{className:`${m().tabButton} ${"bookings"===o?m().active:""}`,onClick:()=>h("bookings"),children:"Bookings"}),a.jsx("button",{className:`${m().tabButton} ${"customers"===o?m().active:""}`,onClick:()=>h("customers"),children:"Customers"})]}),(0,a.jsxs)("div",{className:m().tabContent,children:["overview"===o&&a.jsx("div",{className:m().overviewSection,children:(0,a.jsxs)("div",{className:m().metricsGrid,children:[(0,a.jsxs)("div",{className:m().metricCard,children:[a.jsx("h3",{children:"Total Revenue"}),a.jsx("div",{className:m().metricValue,children:C(v.overview.totalRevenue)}),(0,a.jsxs)("div",{className:m().metricChange,children:[f(v.overview.revenueGrowth)," vs previous period"]})]}),(0,a.jsxs)("div",{className:m().metricCard,children:[a.jsx("h3",{children:"Total Bookings"}),a.jsx("div",{className:m().metricValue,children:v.overview.totalBookings}),(0,a.jsxs)("div",{className:m().metricChange,children:[f(v.overview.bookingGrowth)," vs previous period"]})]}),(0,a.jsxs)("div",{className:m().metricCard,children:[a.jsx("h3",{children:"Total Customers"}),a.jsx("div",{className:m().metricValue,children:v.overview.totalCustomers})]}),(0,a.jsxs)("div",{className:m().metricCard,children:[a.jsx("h3",{children:"Average Booking Value"}),a.jsx("div",{className:m().metricValue,children:C(v.overview.averageBookingValue)})]})]})}),"revenue"===o&&(0,a.jsxs)("div",{className:m().revenueSection,children:[a.jsx("h2",{children:"Revenue Analysis"}),a.jsx(d.Z,{data:v.revenue,dateRange:b.replace(/([a-z])([A-Z])/g,"$1 $2").replace(/^./,e=>e.toUpperCase())})]}),"bookings"===o&&(0,a.jsxs)("div",{className:m().bookingsSection,children:[a.jsx("h2",{children:"Booking Analysis"}),a.jsx(u.Z,{data:v.bookings,dateRange:b.replace(/([a-z])([A-Z])/g,"$1 $2").replace(/^./,e=>e.toUpperCase())})]}),"customers"===o&&(0,a.jsxs)("div",{className:m().customersSection,children:[a.jsx("h2",{children:"Customer Analysis"}),a.jsx(p.Z,{data:v.customers,dateRange:b.replace(/([a-z])([A-Z])/g,"$1 $2").replace(/^./,e=>e.toUpperCase())})]})]})]})]})]}):a.jsx(c.Z,{children:(0,a.jsxs)("div",{className:m().accessDenied,children:[a.jsx("h2",{children:"Access Denied"}),a.jsx("p",{children:"You don't have permission to access reports."})]})}):null}[c,d,u,p]=b.then?(await b)():b,s()}catch(e){s(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3767:e=>{"use strict";e.exports=import("chart.js")},2189:e=>{"use strict";e.exports=import("react-chartjs-2")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2899,6212,1664,7441],()=>r(3888));module.exports=s})();