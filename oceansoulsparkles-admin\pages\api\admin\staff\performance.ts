import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuth } from '../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Staff performance API called - ${req.method}`);

  try {
    // Authenticate admin request
    const authResult = await verifyAdminAuth(req);
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Authentication required',
        message: authResult.message || 'Authentication failed',
        requestId
      });
    }

    const { user } = authResult;

    if (req.method === 'GET') {
      const { 
        staff_id, 
        start_date, 
        end_date, 
        period = 'month' // 'day', 'week', 'month', 'year'
      } = req.query;

      if (staff_id) {
        // Get performance metrics for specific staff member
        let query = supabase
          .from('staff_performance_metrics')
          .select(`
            id,
            metric_date,
            total_bookings,
            completed_bookings,
            cancelled_bookings,
            total_revenue,
            total_tips,
            average_rating,
            customer_feedback_count,
            hours_worked,
            punctuality_score,
            created_at
          `)
          .eq('staff_id', staff_id)
          .order('metric_date', { ascending: false });

        if (start_date) {
          query = query.gte('metric_date', start_date);
        }
        if (end_date) {
          query = query.lte('metric_date', end_date);
        }

        const { data: metrics, error } = await query;

        if (error) {
          console.error(`[${requestId}] Database error:`, error);
          return res.status(500).json({
            error: 'Failed to fetch performance metrics',
            message: error.message,
            requestId
          });
        }

        // Calculate summary statistics
        const totalMetrics = metrics?.length || 0;
        const summary = metrics?.reduce((acc, metric) => ({
          totalBookings: acc.totalBookings + (metric.total_bookings || 0),
          completedBookings: acc.completedBookings + (metric.completed_bookings || 0),
          cancelledBookings: acc.cancelledBookings + (metric.cancelled_bookings || 0),
          totalRevenue: acc.totalRevenue + parseFloat(metric.total_revenue || '0'),
          totalTips: acc.totalTips + parseFloat(metric.total_tips || '0'),
          totalHours: acc.totalHours + parseFloat(metric.hours_worked || '0'),
          ratingSum: acc.ratingSum + (metric.average_rating || 0),
          ratingCount: acc.ratingCount + (metric.average_rating ? 1 : 0),
          punctualitySum: acc.punctualitySum + (metric.punctuality_score || 0),
          punctualityCount: acc.punctualityCount + (metric.punctuality_score ? 1 : 0)
        }), {
          totalBookings: 0,
          completedBookings: 0,
          cancelledBookings: 0,
          totalRevenue: 0,
          totalTips: 0,
          totalHours: 0,
          ratingSum: 0,
          ratingCount: 0,
          punctualitySum: 0,
          punctualityCount: 0
        }) || {
          totalBookings: 0,
          completedBookings: 0,
          cancelledBookings: 0,
          totalRevenue: 0,
          totalTips: 0,
          totalHours: 0,
          ratingSum: 0,
          ratingCount: 0,
          punctualitySum: 0,
          punctualityCount: 0
        };

        const averageRating = summary.ratingCount > 0 ? summary.ratingSum / summary.ratingCount : 0;
        const averagePunctuality = summary.punctualityCount > 0 ? summary.punctualitySum / summary.punctualityCount : 0;
        const completionRate = summary.totalBookings > 0 ? (summary.completedBookings / summary.totalBookings) * 100 : 0;
        const cancellationRate = summary.totalBookings > 0 ? (summary.cancelledBookings / summary.totalBookings) * 100 : 0;

        return res.status(200).json({
          metrics: metrics || [],
          summary: {
            ...summary,
            averageRating: Math.round(averageRating * 100) / 100,
            averagePunctuality: Math.round(averagePunctuality * 100) / 100,
            completionRate: Math.round(completionRate * 100) / 100,
            cancellationRate: Math.round(cancellationRate * 100) / 100,
            totalPeriods: totalMetrics
          },
          requestId
        });
      } else {
        // Get performance overview for all staff
        const { data: allMetrics, error } = await supabase
          .from('staff_performance_metrics')
          .select(`
            staff_id,
            metric_date,
            total_bookings,
            completed_bookings,
            total_revenue,
            total_tips,
            average_rating,
            admin_users!inner(
              id,
              first_name,
              last_name,
              role
            )
          `)
          .order('metric_date', { ascending: false });

        if (error) {
          console.error(`[${requestId}] Database error:`, error);
          return res.status(500).json({
            error: 'Failed to fetch staff performance overview',
            message: error.message,
            requestId
          });
        }

        // Group by staff member and calculate totals
        interface StaffPerformanceData {
          staff: any;
          totalBookings: number;
          completedBookings: number;
          totalRevenue: number;
          totalTips: number;
          ratingSum: number;
          ratingCount: number;
          periods: number;
        }

        const staffPerformance: Record<string, StaffPerformanceData> = {};
        allMetrics?.forEach((metric: any) => {
          const staffId = metric.staff_id;
          if (!staffPerformance[staffId]) {
            staffPerformance[staffId] = {
              staff: metric.admin_users,
              totalBookings: 0,
              completedBookings: 0,
              totalRevenue: 0,
              totalTips: 0,
              ratingSum: 0,
              ratingCount: 0,
              periods: 0
            };
          }

          const staff = staffPerformance[staffId];
          staff.totalBookings += metric.total_bookings || 0;
          staff.completedBookings += metric.completed_bookings || 0;
          staff.totalRevenue += parseFloat(metric.total_revenue || '0');
          staff.totalTips += parseFloat(metric.total_tips || '0');
          if (metric.average_rating) {
            staff.ratingSum += metric.average_rating;
            staff.ratingCount += 1;
          }
          staff.periods += 1;
        });

        // Convert to array and calculate averages
        const performanceArray = Object.values(staffPerformance).map((staff: StaffPerformanceData) => ({
          ...staff,
          averageRating: staff.ratingCount > 0 ? Math.round((staff.ratingSum / staff.ratingCount) * 100) / 100 : 0,
          completionRate: staff.totalBookings > 0 ? Math.round((staff.completedBookings / staff.totalBookings) * 100) : 0
        }));

        return res.status(200).json({
          staffPerformance: performanceArray,
          requestId
        });
      }
    }

    if (req.method === 'POST') {
      const { action, staff_id, metric_date, metrics } = req.body;

      if (!action || !staff_id) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Action and staff ID are required',
          requestId
        });
      }

      if (action === 'update_metrics') {
        if (!metric_date || !metrics) {
          return res.status(400).json({
            error: 'Missing required fields',
            message: 'Metric date and metrics data are required',
            requestId
          });
        }

        const { data: updatedMetrics, error } = await supabase
          .from('staff_performance_metrics')
          .upsert([
            {
              staff_id,
              metric_date,
              ...metrics,
              updated_at: new Date().toISOString()
            }
          ])
          .select()
          .single();

        if (error) {
          console.error(`[${requestId}] Error updating metrics:`, error);
          return res.status(500).json({
            error: 'Failed to update performance metrics',
            message: error.message,
            requestId
          });
        }

        return res.status(200).json({
          metrics: updatedMetrics,
          message: 'Performance metrics updated successfully',
          requestId
        });
      }

      if (action === 'calculate_daily_metrics') {
        const targetDate = metric_date || new Date().toISOString().split('T')[0];

        // Calculate metrics from actual booking and payment data
        const { data: bookingMetrics, error: bookingError } = await supabase
          .rpc('calculate_staff_daily_metrics', {
            p_staff_id: staff_id,
            p_date: targetDate
          });

        if (bookingError) {
          console.error(`[${requestId}] Error calculating metrics:`, bookingError);
          return res.status(500).json({
            error: 'Failed to calculate daily metrics',
            message: bookingError.message,
            requestId
          });
        }

        return res.status(200).json({
          calculatedMetrics: bookingMetrics,
          message: 'Daily metrics calculated successfully',
          requestId
        });
      }

      return res.status(400).json({
        error: 'Invalid action',
        message: 'Action must be update_metrics or calculate_daily_metrics',
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
}
