import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyCustomerToken, customerLogout } from '../../../../lib/auth/customer-auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get token from Authorization header or cookie
    const authHeader = req.headers.authorization;
    const token = authHeader?.startsWith('Bearer ') 
      ? authHeader.substring(7)
      : req.cookies['customer-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token provided' });
    }

    // Verify token
    const authResult = await verifyCustomerToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    const { sessionToken } = req.body;

    // Logout customer
    await customerLogout(sessionToken, authResult.user.customerId);

    return res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    console.error('Customer logout API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
