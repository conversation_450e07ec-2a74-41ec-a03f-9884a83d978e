import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyCustomerToken, logCustomerActivity } from '../../../lib/auth/customer-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Get token from Authorization header or cookie
  const authHeader = req.headers.authorization;
  const token = authHeader?.startsWith('Bearer ') 
    ? authHeader.substring(7)
    : req.cookies['customer-token'];

  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // Verify customer authentication
  const authResult = await verifyCustomerToken(token);
  if (!authResult.valid || !authResult.user) {
    return res.status(401).json({ error: 'Invalid authentication token' });
  }

  const customer = authResult.user;

  try {
    if (req.method === 'GET') {
      return await handleGetBookingRequests(req, res, customer);
    } else if (req.method === 'POST') {
      return await handleCreateBookingRequest(req, res, customer);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Customer booking requests API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetBookingRequests(req: NextApiRequest, res: NextApiResponse, customer: any) {
  const { data: bookingRequests, error } = await supabase
    .from('customer_booking_requests')
    .select(`
      id,
      requested_date,
      requested_time_start,
      requested_time_end,
      special_requests,
      status,
      admin_notes,
      created_at,
      expires_at,
      services (
        id,
        name,
        category
      ),
      service_pricing_tiers (
        id,
        name,
        price,
        duration
      ),
      artist_profiles (
        id,
        name
      )
    `)
    .eq('customer_id', customer.customerId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Booking requests query error:', error);
    return res.status(500).json({ error: 'Failed to fetch booking requests' });
  }

  const transformedRequests = bookingRequests?.map(request => ({
    id: request.id,
    service_name: request.services?.name || 'Unknown Service',
    service_category: request.services?.category,
    tier_name: request.service_pricing_tiers?.name,
    tier_price: request.service_pricing_tiers?.price,
    tier_duration: request.service_pricing_tiers?.duration,
    artist_name: request.artist_profiles?.name || 'Any Artist',
    requested_date: request.requested_date,
    requested_time_start: request.requested_time_start,
    requested_time_end: request.requested_time_end,
    special_requests: request.special_requests,
    status: request.status,
    admin_notes: request.admin_notes,
    created_at: request.created_at,
    expires_at: request.expires_at
  })) || [];

  return res.status(200).json({
    bookingRequests: transformedRequests,
    total: transformedRequests.length
  });
}

async function handleCreateBookingRequest(req: NextApiRequest, res: NextApiResponse, customer: any) {
  const {
    service_id,
    tier_id,
    preferred_artist_id,
    requested_date,
    requested_time_start,
    requested_time_end,
    alternative_dates,
    alternative_times,
    special_requests
  } = req.body;

  // Validate required fields
  if (!service_id || !tier_id || !requested_date || !requested_time_start || !requested_time_end) {
    return res.status(400).json({
      error: 'Service, tier, date, and time are required'
    });
  }

  // Check if customer has reached daily limit
  const today = new Date().toISOString().split('T')[0];
  const { count: todayRequestsCount } = await supabase
    .from('customer_booking_requests')
    .select('*', { count: 'exact', head: true })
    .eq('customer_id', customer.customerId)
    .gte('created_at', `${today}T00:00:00Z`)
    .lt('created_at', `${today}T23:59:59Z`);

  const maxRequestsPerDay = 3; // This could be configurable
  if (todayRequestsCount && todayRequestsCount >= maxRequestsPerDay) {
    return res.status(400).json({
      error: `You can only make ${maxRequestsPerDay} booking requests per day`
    });
  }

  // Validate requested date is in the future
  const requestedDateTime = new Date(`${requested_date}T${requested_time_start}`);
  const minAdvanceDays = 2; // This could be configurable
  const minDateTime = new Date();
  minDateTime.setDate(minDateTime.getDate() + minAdvanceDays);

  if (requestedDateTime < minDateTime) {
    return res.status(400).json({
      error: `Booking requests must be made at least ${minAdvanceDays} days in advance`
    });
  }

  // Verify service and tier exist
  const { data: service, error: serviceError } = await supabase
    .from('services')
    .select('id, name, is_active')
    .eq('id', service_id)
    .single();

  if (serviceError || !service || !service.is_active) {
    return res.status(400).json({ error: 'Invalid service selected' });
  }

  const { data: tier, error: tierError } = await supabase
    .from('service_pricing_tiers')
    .select('id, name, price, duration')
    .eq('id', tier_id)
    .eq('service_id', service_id)
    .single();

  if (tierError || !tier) {
    return res.status(400).json({ error: 'Invalid service tier selected' });
  }

  // Create booking request
  const { data: bookingRequest, error: createError } = await supabase
    .from('customer_booking_requests')
    .insert({
      customer_id: customer.customerId,
      service_id,
      tier_id,
      preferred_artist_id: preferred_artist_id || null,
      requested_date,
      requested_time_start,
      requested_time_end,
      alternative_dates: alternative_dates || [],
      alternative_times: alternative_times || [],
      special_requests: special_requests || null,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
    })
    .select(`
      id,
      requested_date,
      requested_time_start,
      requested_time_end,
      status,
      created_at
    `)
    .single();

  if (createError || !bookingRequest) {
    console.error('Booking request creation error:', createError);
    return res.status(500).json({ error: 'Failed to create booking request' });
  }

  // Log activity
  await logCustomerActivity(
    customer.customerId,
    'booking_request',
    `Created booking request for ${service.name}`,
    req.headers['x-forwarded-for'] as string,
    { booking_request_id: bookingRequest.id, service_name: service.name }
  );

  // TODO: Send notification to admin about new booking request
  // TODO: Send confirmation email to customer

  return res.status(201).json({
    success: true,
    bookingRequest: {
      id: bookingRequest.id,
      service_name: service.name,
      tier_name: tier.name,
      tier_price: tier.price,
      requested_date: bookingRequest.requested_date,
      requested_time_start: bookingRequest.requested_time_start,
      requested_time_end: bookingRequest.requested_time_end,
      status: bookingRequest.status,
      created_at: bookingRequest.created_at
    },
    message: 'Booking request submitted successfully. We will review and respond within 24 hours.'
  });
}
