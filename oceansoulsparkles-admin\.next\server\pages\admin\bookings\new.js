(()=>{var e={};e.id=9570,e.ids=[9570,660],e.modules={33:e=>{e.exports={newBookingContainer:"NewBooking_newBookingContainer__D1FAU",header:"NewBooking_header__Tb9K5",headerContent:"NewBooking_headerContent__dF28c",headerActions:"NewBooking_headerActions__dMENv",backButton:"NewBooking_backButton__g2itb",bookingForm:"NewBooking_bookingForm__U3c_O",formGrid:"NewBooking_formGrid__Cak5U",formSection:"NewBooking_formSection__X0n4P",formGroup:"NewBooking_formGroup__kk_6_",formControl:"NewBooking_formControl__2ENPF",formRow:"NewBooking_formRow__4D_fo",timeInfo:"NewBooking_timeInfo__4OdA7",linkButton:"NewBooking_linkButton__fsbtM",formActions:"NewBooking_formActions__xz_I7",cancelButton:"NewBooking_cancelButton__p80yq",submitButton:"NewBooking_submitButton__g18z_",content:"NewBooking_content__Yic_y",comingSoon:"NewBooking_comingSoon__au8HZ",actions:"NewBooking_actions__iMfSi",backBtn:"NewBooking_backBtn__bXS2C",loadingContainer:"NewBooking_loadingContainer__B0B7u",loadingSpinner:"NewBooking_loadingSpinner__y0PeA",spin:"NewBooking_spin__zwaRl"}},3209:(e,t,i)=>{"use strict";i.a(e,async(e,o)=>{try{i.r(t),i.d(t,{config:()=>p,default:()=>u,getServerSideProps:()=>g,getStaticPaths:()=>h,getStaticProps:()=>_,reportWebVitals:()=>x,routeModule:()=>N,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>k,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>j});var n=i(7093),s=i(5244),r=i(1323),a=i(2899),l=i.n(a),c=i(6814),d=i(4220),m=e([c,d]);[c,d]=m.then?(await m)():m;let u=(0,r.l)(d,"default"),_=(0,r.l)(d,"getStaticProps"),h=(0,r.l)(d,"getStaticPaths"),g=(0,r.l)(d,"getServerSideProps"),p=(0,r.l)(d,"config"),x=(0,r.l)(d,"reportWebVitals"),j=(0,r.l)(d,"unstable_getStaticProps"),f=(0,r.l)(d,"unstable_getStaticPaths"),k=(0,r.l)(d,"unstable_getStaticParams"),v=(0,r.l)(d,"unstable_getServerProps"),b=(0,r.l)(d,"unstable_getServerSideProps"),N=new n.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/admin/bookings/new",pathname:"/admin/bookings/new",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});o()}catch(e){o(e)}})},4220:(e,t,i)=>{"use strict";i.a(e,async(e,o)=>{try{i.r(t),i.d(t,{default:()=>x});var n=i(997),s=i(6689),r=i(968),a=i.n(r),l=i(1163),c=i(1664),d=i.n(c),m=i(8568),u=i(4845),_=i(3590),h=i(33),g=i.n(h),p=e([u,_]);function x(){let e=(0,l.useRouter)(),{user:t,loading:i}=(0,m.a)(),[o,r]=(0,s.useState)(!1),[c,h]=(0,s.useState)([]),[p,x]=(0,s.useState)([]),[j,f]=(0,s.useState)([]),[k,v]=(0,s.useState)([]),[b,N]=(0,s.useState)({customer_id:"",service_id:"",tier_id:"",artist_id:"",booking_date:"",start_time:"",duration:60,notes:"",location:"Studio"}),S=e=>{let{name:t,value:i}=e.target;N(e=>({...e,[t]:i}))},w=()=>{if(b.start_time&&b.duration){let[e,t]=b.start_time.split(":"),i=new Date;return i.setHours(parseInt(e),parseInt(t),0,0),new Date(i.getTime()+6e4*b.duration).toTimeString().slice(0,5)}return""},B=()=>{let e=["customer_id","service_id","tier_id","artist_id","booking_date","start_time"].filter(e=>!b[e]);return e.length>0?(_.toast.error(`Please fill in all required fields: ${e.join(", ")}`),!1):!(new Date(`${b.booking_date}T${b.start_time}`)<new Date)||(_.toast.error("Booking date and time cannot be in the past"),!1)},C=async t=>{if(t.preventDefault(),B()){r(!0);try{let t=k.find(e=>e.id===b.tier_id),i=w(),o={customer_id:b.customer_id,service_id:b.service_id,assigned_artist_id:b.artist_id,start_time:`${b.booking_date}T${b.start_time}:00`,end_time:`${b.booking_date}T${i}:00`,status:"confirmed",total_amount:t?.price||0,notes:b.notes,location:b.location,tier_name:t?.name,tier_price:t?.price,booking_source:"admin"},n=await fetch("/api/admin/bookings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(n.ok){let t=await n.json();_.toast.success("Booking created successfully!"),e.push(`/admin/bookings/${t.booking.id}`)}else{let e=await n.json();_.toast.error(e.message||"Failed to create booking")}}catch(e){console.error("Error creating booking:",e),_.toast.error("Failed to create booking")}finally{r(!1)}}};return i?n.jsx(u.Z,{children:(0,n.jsxs)("div",{className:g().loadingContainer,children:[n.jsx("div",{className:g().loadingSpinner}),n.jsx("p",{children:"Loading..."})]})}):(0,n.jsxs)(u.Z,{children:[(0,n.jsxs)(a(),{children:[n.jsx("title",{children:"New Booking | Ocean Soul Sparkles Admin"}),n.jsx("meta",{name:"description",content:"Create a new customer booking"})]}),(0,n.jsxs)("div",{className:g().newBookingContainer,children:[(0,n.jsxs)("header",{className:g().header,children:[(0,n.jsxs)("div",{className:g().headerContent,children:[n.jsx("h1",{children:"Create New Booking"}),n.jsx("p",{children:"Schedule a new appointment for a customer"})]}),n.jsx("div",{className:g().headerActions,children:n.jsx(d(),{href:"/admin/bookings",className:g().backButton,children:"← Back to Bookings"})})]}),(0,n.jsxs)("form",{onSubmit:C,className:g().bookingForm,children:[(0,n.jsxs)("div",{className:g().formGrid,children:[(0,n.jsxs)("div",{className:g().formSection,children:[n.jsx("h3",{children:"Customer Information"}),(0,n.jsxs)("div",{className:g().formGroup,children:[n.jsx("label",{htmlFor:"customer_id",children:"Customer *"}),(0,n.jsxs)("select",{id:"customer_id",name:"customer_id",value:b.customer_id,onChange:S,required:!0,className:g().formControl,children:[n.jsx("option",{value:"",children:"Select a customer..."}),c.map(e=>(0,n.jsxs)("option",{value:e.id,children:[e.first_name," ",e.last_name," - ",e.email]},e.id))]})]}),n.jsx("div",{className:g().formActions,children:n.jsx(d(),{href:"/admin/customers/new",className:g().linkButton,children:"+ Add New Customer"})})]}),(0,n.jsxs)("div",{className:g().formSection,children:[n.jsx("h3",{children:"Service Details"}),(0,n.jsxs)("div",{className:g().formGroup,children:[n.jsx("label",{htmlFor:"service_id",children:"Service *"}),(0,n.jsxs)("select",{id:"service_id",name:"service_id",value:b.service_id,onChange:S,required:!0,className:g().formControl,children:[n.jsx("option",{value:"",children:"Select a service..."}),p.map(e=>(0,n.jsxs)("option",{value:e.id,children:[e.name," - $",e.base_price]},e.id))]})]}),k.length>0&&(0,n.jsxs)("div",{className:g().formGroup,children:[n.jsx("label",{htmlFor:"tier_id",children:"Service Tier *"}),(0,n.jsxs)("select",{id:"tier_id",name:"tier_id",value:b.tier_id,onChange:e=>{let t=e.target.value,i=k.find(e=>e.id===t);N(e=>({...e,tier_id:t,duration:i?.duration||60}))},required:!0,className:g().formControl,children:[n.jsx("option",{value:"",children:"Select a tier..."}),k.map(e=>(0,n.jsxs)("option",{value:e.id,children:[e.name," - $",e.price," (",e.duration," min)"]},e.id))]})]})]}),(0,n.jsxs)("div",{className:g().formSection,children:[n.jsx("h3",{children:"Artist Assignment"}),(0,n.jsxs)("div",{className:g().formGroup,children:[n.jsx("label",{htmlFor:"artist_id",children:"Artist *"}),(0,n.jsxs)("select",{id:"artist_id",name:"artist_id",value:b.artist_id,onChange:S,required:!0,className:g().formControl,children:[n.jsx("option",{value:"",children:"Select an artist..."}),j.map(e=>(0,n.jsxs)("option",{value:e.id,children:[e.name||e.artist_name," - ",e.specializations?.join(", ")]},e.id))]})]})]}),(0,n.jsxs)("div",{className:g().formSection,children:[n.jsx("h3",{children:"Schedule"}),(0,n.jsxs)("div",{className:g().formRow,children:[(0,n.jsxs)("div",{className:g().formGroup,children:[n.jsx("label",{htmlFor:"booking_date",children:"Date *"}),n.jsx("input",{type:"date",id:"booking_date",name:"booking_date",value:b.booking_date,onChange:S,min:new Date().toISOString().split("T")[0],required:!0,className:g().formControl})]}),(0,n.jsxs)("div",{className:g().formGroup,children:[n.jsx("label",{htmlFor:"start_time",children:"Start Time *"}),n.jsx("input",{type:"time",id:"start_time",name:"start_time",value:b.start_time,onChange:S,required:!0,className:g().formControl})]})]}),b.start_time&&b.duration&&(0,n.jsxs)("div",{className:g().timeInfo,children:[(0,n.jsxs)("p",{children:["End Time: ",w()]}),(0,n.jsxs)("p",{children:["Duration: ",b.duration," minutes"]})]})]}),(0,n.jsxs)("div",{className:g().formSection,children:[n.jsx("h3",{children:"Additional Details"}),(0,n.jsxs)("div",{className:g().formGroup,children:[n.jsx("label",{htmlFor:"location",children:"Location"}),(0,n.jsxs)("select",{id:"location",name:"location",value:b.location,onChange:S,className:g().formControl,children:[n.jsx("option",{value:"Studio",children:"Studio"}),n.jsx("option",{value:"Client Location",children:"Client Location"}),n.jsx("option",{value:"Event Venue",children:"Event Venue"}),n.jsx("option",{value:"Mobile Service",children:"Mobile Service"})]})]}),(0,n.jsxs)("div",{className:g().formGroup,children:[n.jsx("label",{htmlFor:"notes",children:"Notes"}),n.jsx("textarea",{id:"notes",name:"notes",value:b.notes,onChange:S,rows:4,placeholder:"Any special requirements or notes for this booking...",className:g().formControl})]})]})]}),(0,n.jsxs)("div",{className:g().formActions,children:[n.jsx("button",{type:"button",onClick:()=>e.push("/admin/bookings"),className:g().cancelButton,disabled:o,children:"Cancel"}),n.jsx("button",{type:"submit",className:g().submitButton,disabled:o,children:o?"Creating...":"Create Booking"})]})]})]})]})}[u,_]=p.then?(await p)():p,o()}catch(e){o(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),o=t.X(0,[2899,6212,1664,7441],()=>i(3209));module.exports=o})();