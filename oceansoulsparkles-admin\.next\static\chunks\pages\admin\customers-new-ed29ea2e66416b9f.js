(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[228],{9275:function(r,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/customers-new",function(){return n(768)}])},768:function(r,e,n){"use strict";n.r(e),n.d(e,{default:function(){return u}}),function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}(),function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}();var t=n(9008),o=n.n(t),c=n(99),a=n(6026),i=n(5165),d=n.n(i);function u(){let{user:r,loading:e}=(0,a.a)(),[n,t]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())([]),[i,u]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())([]),[O,s]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(!0),[m,l]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(""),N=async()=>{try{s(!0);let r=localStorage.getItem("admin-token"),e=await fetch("/api/admin/customers",{headers:{Authorization:"Bearer ".concat(r)}});if(!e.ok)throw Error("Failed to load customers");let n=await e.json();t(n.customers||[]),u(n.customers||[])}catch(r){console.error("Error loading customers:",r),t([]),u([])}finally{s(!1)}};return(Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(()=>{!e&&r&&N()},[r,e]),Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(()=>{let r=n;m&&(r=r.filter(r=>r.name.toLowerCase().includes(m.toLowerCase())||r.email.toLowerCase().includes(m.toLowerCase()))),u(r)},[n,m]),e||O)?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(c.Z,{children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().loadingContainer,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().loadingSpinner}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Loading customers..."})]})}):Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(c.Z,{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(o(),{children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("title",{children:"Customers Management - Ocean Soul Sparkles Admin"})}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().customersContainer,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("header",{className:d().header,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h1",{children:"Customers Management"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().headerActions,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{className:d().newCustomerBtn,children:"+ New Customer"})})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().controlsPanel,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().searchContainer,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("input",{type:"text",placeholder:"Search customers...",value:m,onChange:r=>l(r.target.value),className:d().searchInput})})}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().customersGrid,children:0===i.length?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().emptyState,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"No customers found."})}):i.map(r=>Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().customerCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().customerHeader,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:r.name}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:d().bookingCount,children:[r.total_bookings," bookings"]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().customerDetails,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{className:d().customerEmail,children:r.email}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{className:d().customerPhone,children:r.phone}),r.notes&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{className:d().customerNotes,children:r.notes})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:d().customerActions,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{className:d().viewBtn,children:"View Details"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{className:d().editBtn,children:"Edit"})]})]},r.id))})]})]})}}},function(r){r.O(0,[736,592,888,179],function(){return r(r.s=9275)}),_N_E=r.O()}]);