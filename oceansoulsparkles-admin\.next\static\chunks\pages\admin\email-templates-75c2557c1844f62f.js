(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[152],{17:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/email-templates",function(){return a(7254)}])},7254:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return m}}),function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var r=a(9008),n=a.n(r),o=a(6026),i=a(99),c=a(3118),l=a.n(c);let d=[{value:"booking_confirmation",label:"Booking Confirmation"},{value:"booking_reminder",label:"Booking Reminder"},{value:"booking_cancellation",label:"Booking Cancellation"},{value:"payment_receipt",label:"Payment Receipt"},{value:"staff_notification",label:"Staff Notification"},{value:"custom",label:"Custom"}];function m(){let{user:e}=(0,o.a)(),[t,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[r,c]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[m,s]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[u,_]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("all"),[O,f]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[E,N]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[p,h]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[D,v]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{U()},[u,O]);let U=async()=>{try{c(!0);let e=new URLSearchParams;"all"!==u&&e.append("type",u),O&&e.append("active_only","true");let t=await fetch("/api/admin/email-templates?".concat(e),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}});if(t.ok){let e=await t.json();a(e.templates||[])}else s("Failed to load email templates")}catch(e){console.error("Error loading templates:",e),s("Failed to load email templates")}finally{c(!1)}},j=async(e,a)=>{try{let r=t.find(t=>t.id===e);if(!r)return;(await fetch("/api/admin/email-templates/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))},body:JSON.stringify({...r,is_active:!a})})).ok?await U():s("Failed to update template status")}catch(e){console.error("Error updating template:",e),s("Failed to update template status")}},T=async e=>{try{let a=t.find(t=>t.id===e);if(!a)return;(await fetch("/api/admin/email-templates/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))},body:JSON.stringify({...a,is_default:!0})})).ok?await U():s("Failed to set default template")}catch(e){console.error("Error setting default:",e),s("Failed to set default template")}},b=async e=>{if(confirm("Are you sure you want to delete this template? This action cannot be undone."))try{let t=await fetch("/api/admin/email-templates/".concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}});if(t.ok)await U();else{let e=await t.json();s(e.message||"Failed to delete template")}}catch(e){console.error("Error deleting template:",e),s("Failed to delete template")}},C=e=>{let t=d.find(t=>t.value===e);return t?t.label:e},w=t.filter(e=>("all"===u||e.type===u)&&(!O||!!e.is_active));return r?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(i.Z,{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().loadingContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().loadingSpinner}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Loading email templates..."})]})}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(i.Z,{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(n(),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("title",{children:"Email Templates | Ocean Soul Sparkles Admin"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("meta",{name:"description",content:"Manage email templates for customer communications"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templatesContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("header",{className:l().header,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().headerLeft,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:l().title,children:"Email Templates"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:l().subtitle,children:"Manage email templates for customer communications"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().headerActions,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>h(!0),className:l().createBtn,children:"+ Create Template"})})]}),m&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().errorMessage,children:[m,Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>s(null),className:l().closeError,children:"\xd7"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().filters,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().filterGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"typeFilter",children:"Filter by Type:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"typeFilter",value:u,onChange:e=>_(e.target.value),className:l().filterSelect,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"all",children:"All Types"}),d.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.value,children:e.label},e.value))]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().filterGroup,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{className:l().checkboxLabel,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"checkbox",checked:O,onChange:e=>f(e.target.checked)}),"Show active only"]})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templatesGrid,children:0===w.length?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().emptyState,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"No email templates found"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Create your first email template to get started with customer communications."}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>h(!0),className:l().createBtn,children:"Create Template"})]}):w.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().cardHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:l().templateName,children:e.name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:l().templateType,children:C(e.type)}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:l().templateSubject,children:e.subject})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateBadges,children:[e.is_default&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().defaultBadge,children:"Default"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"".concat(l().statusBadge," ").concat(e.is_active?l().active:l().inactive),children:e.is_active?"Active":"Inactive"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().templateVariables,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Variables:"})," ",e.variables.join(", ")||"None"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().cardActions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>{N(e),v(!0)},className:l().previewBtn,children:"Preview"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>N(e),className:l().editBtn,children:"Edit"}),!e.is_default&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>T(e.id),className:l().defaultBtn,children:"Set Default"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>j(e.id,e.is_active),className:"".concat(l().toggleBtn," ").concat(e.is_active?l().deactivate:l().activate),children:e.is_active?"Deactivate":"Activate"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>b(e.id),className:l().deleteBtn,children:"Delete"})]})]},e.id))})]})]})}},3118:function(e){e.exports={templatesContainer:"EmailTemplates_templatesContainer__Dy9ae",header:"EmailTemplates_header__A1Hic",headerLeft:"EmailTemplates_headerLeft__iUCn_",title:"EmailTemplates_title__IjAyS",subtitle:"EmailTemplates_subtitle__MlCVx",headerActions:"EmailTemplates_headerActions__aIzMz",createBtn:"EmailTemplates_createBtn__gQLwH",errorMessage:"EmailTemplates_errorMessage__psel5",closeError:"EmailTemplates_closeError__Ff19A",filters:"EmailTemplates_filters__fh2xV",filterGroup:"EmailTemplates_filterGroup__xvqrm",filterSelect:"EmailTemplates_filterSelect__cnN3W",checkboxLabel:"EmailTemplates_checkboxLabel__pHOkL",templatesGrid:"EmailTemplates_templatesGrid__HxTMU",templateCard:"EmailTemplates_templateCard__5CIzM",cardHeader:"EmailTemplates_cardHeader__qWwb1",templateInfo:"EmailTemplates_templateInfo__vnn2X",templateName:"EmailTemplates_templateName__L7h8E",templateType:"EmailTemplates_templateType__FangX",templateSubject:"EmailTemplates_templateSubject__CL9JD",templateBadges:"EmailTemplates_templateBadges__oztO3",defaultBadge:"EmailTemplates_defaultBadge__KIYD3",statusBadge:"EmailTemplates_statusBadge__vXsS0",active:"EmailTemplates_active__FpG58",inactive:"EmailTemplates_inactive__LLRxn",templateVariables:"EmailTemplates_templateVariables__5zAeB",cardActions:"EmailTemplates_cardActions__CEDGT",previewBtn:"EmailTemplates_previewBtn__c8qr3",editBtn:"EmailTemplates_editBtn___7tet",defaultBtn:"EmailTemplates_defaultBtn__jrlCj",toggleBtn:"EmailTemplates_toggleBtn__32VWV",activate:"EmailTemplates_activate__6Mtp7",deactivate:"EmailTemplates_deactivate__HX2PX",deleteBtn:"EmailTemplates_deleteBtn__2w_pP",emptyState:"EmailTemplates_emptyState__q3okq",loadingContainer:"EmailTemplates_loadingContainer__Cd8Rs",loadingSpinner:"EmailTemplates_loadingSpinner__6lYmD",spin:"EmailTemplates_spin__srCDH"}}},function(e){e.O(0,[736,592,888,179],function(){return e(e.s=17)}),_N_E=e.O()}]);