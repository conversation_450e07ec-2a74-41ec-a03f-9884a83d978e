(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[826],{67:e=>{"use strict";e.exports=require("node:async_hooks")},195:e=>{"use strict";e.exports=require("node:buffer")},102:(e,t,r)=>{"use strict";let i,s;r.r(t),r.d(t,{default:()=>sr});var n,a,o,l,c,u,h,d,p,f,g,m,w,y,v,b,_,S,E,k,T,P,O,x={};async function R(){let e="_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&(await _ENTRIES.middleware_instrumentation).register;if(e)try{await e()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}r.r(x),r.d(x,{config:()=>i9,middleware:()=>i8});let A=null;function C(){return A||(A=R()),A}function I(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Error(I(e))},construct(){throw Error(I(e))},apply(r,i,s){if("function"==typeof s[0])return s[0](t);throw Error(I(e))}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),C();class j extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class N extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class L extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let $={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};function M(e){var t,r,i,s,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=s,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function D(e){let t={},r=[];if(e)for(let[i,s]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...M(s)),t[i]=1===r.length?r[0]:r):t[i]=s;return t}function U(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}({...$,GROUP:{serverOnly:[$.reactServerComponents,$.actionBrowser,$.appMetadataRoute,$.appRouteHandler,$.instrument],clientOnly:[$.serverSideRendering,$.appPagesBrowser],nonClientServerTarget:[$.middleware,$.api],app:[$.reactServerComponents,$.actionBrowser,$.appMetadataRoute,$.appRouteHandler,$.serverSideRendering,$.appPagesBrowser,$.shared,$.instrument]}});let q=Symbol("response"),B=Symbol("passThrough"),H=Symbol("waitUntil");class W{constructor(e){this[H]=[],this[B]=!1}respondWith(e){this[q]||(this[q]=Promise.resolve(e))}passThroughOnException(){this[B]=!0}waitUntil(e){this[H].push(e)}}class K extends W{constructor(e){super(e.request),this.sourcePage=e.page}get request(){throw new j({page:this.sourcePage})}respondWith(){throw new j({page:this.sourcePage})}}function V(e){return e.replace(/\/$/,"")||"/"}function F(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function G(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=F(e);return""+t+r+i+s}function J(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=F(e);return""+r+t+i+s}function z(e,t){if("string"!=typeof e)return!1;let{pathname:r}=F(e);return r===t||r.startsWith(t+"/")}function X(e,t){let r;let i=e.split("/");return(t||[]).some(t=>!!i[1]&&i[1].toLowerCase()===t.toLowerCase()&&(r=t,i.splice(1,1),e=i.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}let Y=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function Z(e,t){return new URL(String(e).replace(Y,"localhost"),t&&String(t).replace(Y,"localhost"))}let Q=Symbol("NextURLInternal");class ee{constructor(e,t,r){let i,s;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,s=r||{}):s=r||t||{},this[Q]={url:Z(e,i??s.base),options:s,basePath:""},this.analyze()}analyze(){var e,t,r,i,s;let n=function(e,t){var r,i;let{basePath:s,i18n:n,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};s&&z(o.pathname,s)&&(o.pathname=function(e,t){if(!z(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,s),o.basePath=s);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];o.buildId=r,l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(n){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):X(o.pathname,n.locales);o.locale=e.detectedLocale,o.pathname=null!=(i=e.pathname)?i:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):X(l,n.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[Q].url.pathname,{nextConfig:this[Q].options.nextConfig,parseData:!0,i18nProvider:this[Q].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[Q].url,this[Q].options.headers);this[Q].domainLocale=this[Q].options.i18nProvider?this[Q].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let n of(r&&(r=r.toLowerCase()),e)){var i,s;if(t===(null==(i=n.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===n.defaultLocale.toLowerCase()||(null==(s=n.locales)?void 0:s.some(e=>e.toLowerCase()===r)))return n}}(null==(t=this[Q].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[Q].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[Q].options.nextConfig)?void 0:null==(i=s.i18n)?void 0:i.defaultLocale);this[Q].url.pathname=n.pathname,this[Q].defaultLocale=o,this[Q].basePath=n.basePath??"",this[Q].buildId=n.buildId,this[Q].locale=n.locale??o,this[Q].trailingSlash=n.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,i){if(!t||t===r)return e;let s=e.toLowerCase();return!i&&(z(s,"/api")||z(s,"/"+t.toLowerCase()))?e:G(e,"/"+t)}((e={basePath:this[Q].basePath,buildId:this[Q].buildId,defaultLocale:this[Q].options.forceLocale?void 0:this[Q].defaultLocale,locale:this[Q].locale,pathname:this[Q].url.pathname,trailingSlash:this[Q].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=V(t)),e.buildId&&(t=J(G(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=G(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:J(t,"/"):V(t)}formatSearch(){return this[Q].url.search}get buildId(){return this[Q].buildId}set buildId(e){this[Q].buildId=e}get locale(){return this[Q].locale??""}set locale(e){var t,r;if(!this[Q].locale||!(null==(r=this[Q].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[Q].locale=e}get defaultLocale(){return this[Q].defaultLocale}get domainLocale(){return this[Q].domainLocale}get searchParams(){return this[Q].url.searchParams}get host(){return this[Q].url.host}set host(e){this[Q].url.host=e}get hostname(){return this[Q].url.hostname}set hostname(e){this[Q].url.hostname=e}get port(){return this[Q].url.port}set port(e){this[Q].url.port=e}get protocol(){return this[Q].url.protocol}set protocol(e){this[Q].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[Q].url=Z(e),this.analyze()}get origin(){return this[Q].url.origin}get pathname(){return this[Q].url.pathname}set pathname(e){this[Q].url.pathname=e}get hash(){return this[Q].url.hash}set hash(e){this[Q].url.hash=e}get search(){return this[Q].url.search}set search(e){this[Q].url.search=e}get password(){return this[Q].url.password}set password(e){this[Q].url.password=e}get username(){return this[Q].url.username}set username(e){this[Q].url.username=e}get basePath(){return this[Q].basePath}set basePath(e){this[Q].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new ee(String(this),this[Q].options)}}var et=r(675);let er=Symbol("internal request");class ei extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);U(r),e instanceof Request?super(e,t):super(r,t);let i=new ee(r,{headers:D(this.headers),nextConfig:t.nextConfig});this[er]={cookies:new et.RequestCookies(this.headers),geo:t.geo||{},ip:t.ip,nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[er].cookies}get geo(){return this[er].geo}get ip(){return this[er].ip}get nextUrl(){return this[er].nextUrl}get page(){throw new N}get ua(){throw new L}get url(){return this[er].url}}class es{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let en=Symbol("internal response"),ea=new Set([301,302,303,307,308]);function eo(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[i,s]of e.request.headers)t.set("x-middleware-request-"+i,s),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class el extends Response{constructor(e,t={}){super(e,t);let r=this.headers,i=new Proxy(new et.ResponseCookies(r),{get(e,i,s){switch(i){case"delete":case"set":return(...s)=>{let n=Reflect.apply(e[i],e,s),a=new Headers(r);return n instanceof et.ResponseCookies&&r.set("x-middleware-set-cookie",n.getAll().map(e=>(0,et.stringifyCookie)(e)).join(",")),eo(t,a),n};default:return es.get(e,i,s)}}});this[en]={cookies:i,url:t.url?new ee(t.url,{headers:D(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[en].cookies}static json(e,t){let r=Response.json(e,t);return new el(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!ea.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let i="object"==typeof t?t:{},s=new Headers(null==i?void 0:i.headers);return s.set("Location",U(e)),new el(null,{...i,headers:s,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",U(e)),eo(t,r),new el(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),eo(e,t),new el(null,{...e,headers:t})}}function ec(e,t){let r="string"==typeof t?new URL(t):t,i=new URL(e,t),s=r.protocol+"//"+r.host;return i.protocol+"//"+i.host===s?i.toString().replace(s,""):i.toString()}let eu=[["RSC"],["Next-Router-State-Tree"],["Next-Router-Prefetch"]],eh=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],ed=["__nextDataReq"];class ep extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ep}}class ef extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return es.get(t,r,i);let s=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==n)return es.get(t,n,i)},set(t,r,i,s){if("symbol"==typeof r)return es.set(t,r,i,s);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return es.set(t,a??r,i,s)},has(t,r){if("symbol"==typeof r)return es.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&es.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return es.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||es.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return ep.callable;default:return es.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new ef(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let eg=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class em{disable(){throw eg}getStore(){}run(){throw eg}exit(){throw eg}enterWith(){throw eg}}let ew=globalThis.AsyncLocalStorage;function ey(){return ew?new ew:new em}let ev=ey();class eb extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new eb}}class e_{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return eb.callable;default:return es.get(e,t,r)}}})}}let eS=Symbol.for("next.mutated.cookies");class eE{static wrap(e,t){let r=new et.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],s=new Set,n=()=>{let e=ev.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of i){let r=new et.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case eS:return i;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{n()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{n()}};default:return es.get(e,t,r)}}})}}!function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(n||(n={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(a||(a={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(o||(o={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(l||(l={})),(c||(c={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(u||(u={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(h||(h={})),(d||(d={})).executeRoute="Router.executeRoute",(p||(p={})).runHandler="Node.runHandler",(f||(f={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(g||(g={})),(m||(m={})).execute="Middleware.execute";let ek=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],eT=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"],{context:eP,propagation:eO,trace:ex,SpanStatusCode:eR,SpanKind:eA,ROOT_CONTEXT:eC}=i=r(486),eI=e=>null!==e&&"object"==typeof e&&"function"==typeof e.then,ej=(e,t)=>{(null==t?void 0:t.bubble)===!0?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eR.ERROR,message:null==t?void 0:t.message})),e.end()},eN=new Map,eL=i.createContextKey("next.rootSpanId"),e$=0,eM=()=>e$++;class eD{getTracerInstance(){return ex.getTracer("next.js","0.0.1")}getContext(){return eP}getActiveScopeSpan(){return ex.getSpan(null==eP?void 0:eP.active())}withPropagatedContext(e,t,r){let i=eP.active();if(ex.getSpanContext(i))return t();let s=eO.extract(i,e,r);return eP.with(s,t)}trace(...e){var t;let[r,i,s]=e,{fn:n,options:a}="function"==typeof i?{fn:i,options:{}}:{fn:s,options:{...i}},o=a.spanName??r;if(!ek.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return n();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=ex.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==eP?void 0:eP.active())??eC,c=!0);let u=eM();return a.attributes={"next.span_name":o,"next.span_type":r,...a.attributes},eP.with(l.setValue(eL,u),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis?globalThis.performance.now():void 0,i=()=>{eN.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&eT.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&eN.set(u,new Map(Object.entries(a.attributes??{})));try{if(n.length>1)return n(e,t=>ej(e,t));let t=n(e);if(eI(t))return t.then(t=>(e.end(),t)).catch(t=>{throw ej(e,t),t}).finally(i);return e.end(),i(),t}catch(t){throw ej(e,t),i(),t}}))}wrap(...e){let t=this,[r,i,s]=3===e.length?e:[e[0],{},e[1]];return ek.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=i;"function"==typeof e&&"function"==typeof s&&(e=e.apply(this,arguments));let n=arguments.length-1,a=arguments[n];if("function"!=typeof a)return t.trace(r,e,()=>s.apply(this,arguments));{let i=t.getContext().bind(eP.active(),a);return t.trace(r,e,(e,t)=>(arguments[n]=function(e){return null==t||t(e),i.apply(this,arguments)},s.apply(this,arguments)))}}:s}startSpan(...e){let[t,r]=e,i=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,i)}getSpanContext(e){return e?ex.setSpan(eP.active(),e):void 0}getRootSpanAttributes(){let e=eP.active().getValue(eL);return eN.get(e)}}let eU=(()=>{let e=new eD;return()=>e})(),eq="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eq);class eB{constructor(e,t,r,i){var s;let n=e&&function(e,t){let r=ef.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(s=r.get(eq))?void 0:s.value;this.isEnabled=!!(!n&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=i}enable(){if(!this._previewModeId)throw Error("Invariant: previewProps missing previewModeId this should never happen");this._mutableCookies.set({name:eq,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"})}disable(){this._mutableCookies.set({name:eq,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)})}}function eH(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],i=new Headers;for(let e of M(r))i.append("set-cookie",e);for(let e of new et.ResponseCookies(i).getAll())t.set(e)}}let eW={wrap(e,{req:t,res:r,renderOpts:i},s){let n;function a(e){r&&r.setHeader("Set-Cookie",e)}i&&"previewProps"in i&&(n=i.previewProps);let o={},l={get headers(){return o.headers||(o.headers=function(e){let t=ef.from(e);for(let e of eu)t.delete(e.toString().toLowerCase());return ef.seal(t)}(t.headers)),o.headers},get cookies(){if(!o.cookies){let e=new et.RequestCookies(ef.from(t.headers));eH(t,e),o.cookies=e_.seal(e)}return o.cookies},get mutableCookies(){if(!o.mutableCookies){let e=function(e,t){let r=new et.RequestCookies(ef.from(e));return eE.wrap(r,t)}(t.headers,(null==i?void 0:i.onUpdateCookies)||(r?a:void 0));eH(t,e),o.mutableCookies=e}return o.mutableCookies},get draftMode(){return o.draftMode||(o.draftMode=new eB(n,t,this.cookies,this.mutableCookies)),o.draftMode},reactLoadableManifest:(null==i?void 0:i.reactLoadableManifest)||{},assetPrefix:(null==i?void 0:i.assetPrefix)||""};return e.run(l,s,l)}},eK=ey();function eV(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}class eF extends ei{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw new j({page:this.sourcePage})}respondWith(){throw new j({page:this.sourcePage})}waitUntil(){throw new j({page:this.sourcePage})}}let eG={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},eJ=(e,t)=>eU().withPropagatedContext(e.headers,t,eG),ez=!1;async function eX(e){let t,i;!function(){if(!ez&&(ez=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(74);e(),eJ=t(eJ)}}(),await C();let s=void 0!==self.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let n=new ee(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...n.searchParams.keys()]){let t=n.searchParams.getAll(e);!function(e,t){for(let r of["nxtP","nxtI"])e!==r&&e.startsWith(r)&&t(e.substring(r.length))}(e,r=>{for(let e of(n.searchParams.delete(r),t))n.searchParams.append(r,e);n.searchParams.delete(e)})}let a=n.buildId;n.buildId="";let o=e.request.headers["x-nextjs-data"];o&&"/index"===n.pathname&&(n.pathname="/");let l=function(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),c=new Map;if(!s)for(let e of eu){let t=e.toString().toLowerCase();l.get(t)&&(c.set(t,l.get(t)),l.delete(t))}let u=new eF({page:e.page,input:(function(e,t){let r="string"==typeof e,i=r?new URL(e):e;for(let e of eh)i.searchParams.delete(e);if(t)for(let e of ed)i.searchParams.delete(e);return r?i.toString():i})(n,!0).toString(),init:{body:e.request.body,geo:e.request.geo,headers:l,ip:e.request.ip,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});o&&Object.defineProperty(u,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCacheShared&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:eV()})}));let h=new K({request:u,page:e.page});if((t=await eJ(u,()=>"/middleware"===e.page||"/src/middleware"===e.page?eU().trace(m.execute,{spanName:`middleware ${u.method} ${u.nextUrl.pathname}`,attributes:{"http.target":u.nextUrl.pathname,"http.method":u.method}},()=>eW.wrap(eK,{req:u,renderOpts:{onUpdateCookies:e=>{i=e},previewProps:eV()}},()=>e.handler(u,h))):e.handler(u,h)))&&!(t instanceof Response))throw TypeError("Expected an instance of Response to be returned");t&&i&&t.headers.set("set-cookie",i);let d=null==t?void 0:t.headers.get("x-middleware-rewrite");if(t&&d&&!s){let r=new ee(d,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});r.host===u.nextUrl.host&&(r.buildId=a||r.buildId,t.headers.set("x-middleware-rewrite",String(r)));let i=ec(String(r),String(n));o&&t.headers.set("x-nextjs-rewrite",i)}let p=null==t?void 0:t.headers.get("Location");if(t&&p&&!s){let r=new ee(p,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});t=new Response(t.body,t),r.host===u.nextUrl.host&&(r.buildId=a||r.buildId,t.headers.set("Location",String(r))),o&&(t.headers.delete("Location"),t.headers.set("x-nextjs-redirect",ec(String(r),String(n))))}let f=t||el.next(),g=f.headers.get("x-middleware-override-headers"),w=[];if(g){for(let[e,t]of c)f.headers.set(`x-middleware-request-${e}`,t),w.push(e);w.length>0&&f.headers.set("x-middleware-override-headers",g+","+w.join(","))}return{response:f,waitUntil:Promise.all(h[H]),fetchMetrics:u.fetchMetrics}}r(52),"undefined"==typeof URLPattern||URLPattern;let eY=new TextEncoder,eZ=new TextDecoder;function eQ(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:eZ.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=eZ.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}class e0 extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class e1 extends e0{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.claim=r,this.reason=i,this.payload=t}}class e2 extends e0{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.claim=r,this.reason=i,this.payload=t}}class e3 extends e0{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class e6 extends e0{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class e4 extends e0{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class e5 extends e0{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class e8 extends e0{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}class e9 extends e0{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(e="signature verification failed",t){super(e,t)}}let e7=(e,t)=>{let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:parseInt(e.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new e6(`alg ${e} is not supported either by JOSE or your javascript runtime`)}},te=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}};function tt(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function tr(e,t){return e.name===t}function ti(e){return parseInt(e.name.slice(4),10)}function ts(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let tn=(e,...t)=>ts("Key must be ",e,...t);function ta(e,t,...r){return ts(`Key for the ${e} algorithm must be `,t,...r)}let to=async(e,t,r)=>{if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(tn(t,"CryptoKey","KeyObject","JSON Web Key"));return crypto.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}return!function(e,t,r){switch(t){case"HS256":case"HS384":case"HS512":{if(!tr(e.algorithm,"HMAC"))throw tt("HMAC");let r=parseInt(t.slice(2),10);if(ti(e.algorithm.hash)!==r)throw tt(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!tr(e.algorithm,"RSASSA-PKCS1-v1_5"))throw tt("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(ti(e.algorithm.hash)!==r)throw tt(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!tr(e.algorithm,"RSA-PSS"))throw tt("RSA-PSS");let r=parseInt(t.slice(2),10);if(ti(e.algorithm.hash)!==r)throw tt(`SHA-${r}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":if(!tr(e.algorithm,"Ed25519"))throw tt("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!tr(e.algorithm,"ECDSA"))throw tt("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw tt(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}(function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)})(e,r)}(t,e,r),t},tl=async(e,t,r,i)=>{let s=await to(e,t,"verify");te(e,s);let n=e7(e,s.algorithm);try{return await crypto.subtle.verify(n,s,r,i)}catch{return!1}},tc=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},tu=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function th(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function td(e){return e?.[Symbol.toStringTag]==="KeyObject"}let tp=e=>th(e)||td(e);function tf(e){return tu(e)&&"string"==typeof e.kty}let tg=e=>e?.[Symbol.toStringTag],tm=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let i;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):i=r;break;case e.startsWith("PBES2"):i="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):i=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):i="wrapKey";break;case"decrypt"===r:i=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(i&&t.key_ops?.includes?.(i)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${i}" when present`)}return!0},tw=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(tf(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&tm(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!tp(t))throw TypeError(ta(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${tg(t)} instances for symmetric algorithms must be of type "secret"`)}},ty=(e,t,r)=>{if(tf(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tm(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tm(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!tp(t))throw TypeError(ta(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${tg(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${tg(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${tg(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${tg(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${tg(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},tv=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?tw(e,t,r):ty(e,t,r)},tb=(e,t,r,i,s)=>{let n;if(void 0!==s.crit&&i?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!i||void 0===i.crit)return new Set;if(!Array.isArray(i.crit)||0===i.crit.length||i.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let a of(n=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,i.crit)){if(!n.has(a))throw new e6(`Extension Header Parameter "${a}" is not recognized`);if(void 0===s[a])throw new e(`Extension Header Parameter "${a}" is missing`);if(n.get(a)&&void 0===i[a])throw new e(`Extension Header Parameter "${a}" MUST be integrity protected`)}return new Set(i.crit)},t_=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)},tS=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new e6('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new e6('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new e6('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new e6('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),i={...e};return delete i.alg,delete i.use,crypto.subtle.importKey("jwk",i,t,e.ext??!e.d,e.key_ops??r)},tE=async(e,t,r,i=!1)=>{let n=(s||=new WeakMap).get(e);if(n?.[r])return n[r];let a=await tS({...t,alg:r});return i&&Object.freeze(e),n?n[r]=a:s.set(e,{[r]:a}),a},tk=(e,t)=>{let r;let i=(s||=new WeakMap).get(e);if(i?.[t])return i[t];let n="public"===e.type,a=!!n;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,a,n?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,a,[n?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let i;switch(t){case"RSA-OAEP":i="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":i="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":i="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":i="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:i},a,n?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:i},a,[n?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let i=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!i)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===i&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:i},a,[n?"verify":"sign"])),"ES384"===t&&"P-384"===i&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:i},a,[n?"verify":"sign"])),"ES512"===t&&"P-521"===i&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:i},a,[n?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:i},a,n?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return i?i[t]=r:s.set(e,{[t]:r}),r},tT=async(e,t)=>{if(e instanceof Uint8Array||th(e))return e;if(td(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return tk(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return tE(e,r,t)}if(tf(e))return e.k?eQ(e.k):tE(e,e,t,!0);throw Error("unreachable")};async function tP(e,t,r){let i,s;if(!tu(e))throw new e4("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new e4('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new e4("JWS Protected Header incorrect type");if(void 0===e.payload)throw new e4("JWS Payload missing");if("string"!=typeof e.signature)throw new e4("JWS Signature missing or incorrect type");if(void 0!==e.header&&!tu(e.header))throw new e4("JWS Unprotected Header incorrect type");let n={};if(e.protected)try{let t=eQ(e.protected);n=JSON.parse(eZ.decode(t))}catch{throw new e4("JWS Protected Header is invalid")}if(!tc(n,e.header))throw new e4("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let a={...n,...e.header},o=tb(e4,new Map([["b64",!0]]),r?.crit,n,a),l=!0;if(o.has("b64")&&"boolean"!=typeof(l=n.b64))throw new e4('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:c}=a;if("string"!=typeof c||!c)throw new e4('JWS "alg" (Algorithm) Header Parameter missing or invalid');let u=r&&t_("algorithms",r.algorithms);if(u&&!u.has(c))throw new e3('"alg" (Algorithm) Header Parameter value not allowed');if(l){if("string"!=typeof e.payload)throw new e4("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new e4("JWS Payload must be a string or an Uint8Array instance");let h=!1;"function"==typeof t&&(t=await t(n,e),h=!0),tv(c,t,"verify");let d=function(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let i of e)t.set(i,r),r+=i.length;return t}(eY.encode(e.protected??""),eY.encode("."),"string"==typeof e.payload?eY.encode(e.payload):e.payload);try{i=eQ(e.signature)}catch{throw new e4("Failed to base64url decode the signature")}let p=await tT(t,c);if(!await tl(c,p,i,d))throw new e9;if(l)try{s=eQ(e.payload)}catch{throw new e4("Failed to base64url decode the payload")}else s="string"==typeof e.payload?eY.encode(e.payload):e.payload;let f={payload:s};return(void 0!==e.protected&&(f.protectedHeader=n),void 0!==e.header&&(f.unprotectedHeader=e.header),h)?{...f,key:p}:f}async function tO(e,t,r){if(e instanceof Uint8Array&&(e=eZ.decode(e)),"string"!=typeof e)throw new e4("Compact JWS must be a string or Uint8Array");let{0:i,1:s,2:n,length:a}=e.split(".");if(3!==a)throw new e4("Invalid Compact JWS");let o=await tP({payload:s,protected:i,signature:n},t,r),l={payload:o.payload,protectedHeader:o.protectedHeader};return"function"==typeof t?{...l,key:o.key}:l}let tx=e=>Math.floor(e.getTime()/1e3),tR=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tA=e=>{let t;let r=tR.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let i=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(i);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*i);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*i);break;case"day":case"days":case"d":t=Math.round(86400*i);break;case"week":case"weeks":case"w":t=Math.round(604800*i);break;default:t=Math.round(31557600*i)}return"-"===r[1]||"ago"===r[4]?-t:t},tC=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,tI=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));async function tj(e,t,r){let i=await tO(e,t,r);if(i.protectedHeader.crit?.includes("b64")&&!1===i.protectedHeader.b64)throw new e5("JWTs MUST NOT use unencoded payload");let s={payload:function(e,t,r={}){let i,s;try{i=JSON.parse(eZ.decode(t))}catch{}if(!tu(i))throw new e5("JWT Claims Set must be a top-level JSON object");let{typ:n}=r;if(n&&("string"!=typeof e.typ||tC(e.typ)!==tC(n)))throw new e1('unexpected "typ" JWT header value',i,"typ","check_failed");let{requiredClaims:a=[],issuer:o,subject:l,audience:c,maxTokenAge:u}=r,h=[...a];for(let e of(void 0!==u&&h.push("iat"),void 0!==c&&h.push("aud"),void 0!==l&&h.push("sub"),void 0!==o&&h.push("iss"),new Set(h.reverse())))if(!(e in i))throw new e1(`missing required "${e}" claim`,i,e,"missing");if(o&&!(Array.isArray(o)?o:[o]).includes(i.iss))throw new e1('unexpected "iss" claim value',i,"iss","check_failed");if(l&&i.sub!==l)throw new e1('unexpected "sub" claim value',i,"sub","check_failed");if(c&&!tI(i.aud,"string"==typeof c?[c]:c))throw new e1('unexpected "aud" claim value',i,"aud","check_failed");switch(typeof r.clockTolerance){case"string":s=tA(r.clockTolerance);break;case"number":s=r.clockTolerance;break;case"undefined":s=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:d}=r,p=tx(d||new Date);if((void 0!==i.iat||u)&&"number"!=typeof i.iat)throw new e1('"iat" claim must be a number',i,"iat","invalid");if(void 0!==i.nbf){if("number"!=typeof i.nbf)throw new e1('"nbf" claim must be a number',i,"nbf","invalid");if(i.nbf>p+s)throw new e1('"nbf" claim timestamp check failed',i,"nbf","check_failed")}if(void 0!==i.exp){if("number"!=typeof i.exp)throw new e1('"exp" claim must be a number',i,"exp","invalid");if(i.exp<=p-s)throw new e2('"exp" claim timestamp check failed',i,"exp","check_failed")}if(u){let e=p-i.iat;if(e-s>("number"==typeof u?u:tA(u)))throw new e2('"iat" claim timestamp check failed (too far in the past)',i,"iat","check_failed");if(e<0-s)throw new e1('"iat" claim timestamp check failed (it should be in the past)',i,"iat","check_failed")}return i}(i.protectedHeader,i.payload,r),protectedHeader:i.protectedHeader};return"function"==typeof t?{...s,key:i.key}:s}let tN=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,339)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)};class tL extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class t$ extends tL{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class tM extends tL{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class tD extends tL{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(w||(w={}));class tU{constructor(e,{headers:t={},customFetch:r,region:i=w.Any}={}){this.url=e,this.headers=t,this.region=i,this.fetch=tN(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,i,s,n,a;return i=this,s=void 0,n=void 0,a=function*(){try{let i;let{headers:s,method:n,body:a}=t,o={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(o["x-region"]=l),a&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&a instanceof Blob||a instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",i=a):"string"==typeof a?(o["Content-Type"]="text/plain",i=a):"undefined"!=typeof FormData&&a instanceof FormData?i=a:(o["Content-Type"]="application/json",i=JSON.stringify(a)));let c=yield this.fetch(`${this.url}/${e}`,{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),s),body:i}).catch(e=>{throw new t$(e)}),u=c.headers.get("x-relay-error");if(u&&"true"===u)throw new tM(c);if(!c.ok)throw new tD(c);let h=(null!==(r=c.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return{data:"application/json"===h?yield c.json():"application/octet-stream"===h?yield c.blob():"text/event-stream"===h?c:"multipart/form-data"===h?yield c.formData():yield c.text(),error:null}}catch(e){return{data:null,error:e}}},new(n||(n=Promise))(function(e,t){function r(e){try{l(a.next(e))}catch(e){t(e)}}function o(e){try{l(a.throw(e))}catch(e){t(e)}}function l(t){var i;t.done?e(t.value):((i=t.value)instanceof n?i:new n(function(e){e(i)})).then(r,o)}l((a=a.apply(i,s||[])).next())})}}let{PostgrestClient:tq,PostgrestQueryBuilder:tB,PostgrestFilterBuilder:tH,PostgrestTransformBuilder:tW,PostgrestBuilder:tK,PostgrestError:tV}=r(196),tF="undefined"==typeof window?r(163):window.WebSocket,tG={"X-Client-Info":"realtime-js/2.11.10"};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(y||(y={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(v||(v={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(b||(b={})),(_||(_={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(S||(S={}));class tJ{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let i=t.getUint8(1),s=t.getUint8(2),n=this.HEADER_LENGTH+2,a=r.decode(e.slice(n,n+i));n+=i;let o=r.decode(e.slice(n,n+s));return n+=s,{ref:null,topic:a,event:o,payload:JSON.parse(r.decode(e.slice(n,e.byteLength)))}}}class tz{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(E||(E={}));let tX=(e,t,r={})=>{var i;let s=null!==(i=r.skipTypes)&&void 0!==i?i:[];return Object.keys(t).reduce((r,i)=>(r[i]=tY(i,e,t,s),r),{})},tY=(e,t,r,i)=>{let s=t.find(t=>t.name===e),n=null==s?void 0:s.type,a=r[e];return n&&!i.includes(n)?tZ(n,a):tQ(a)},tZ=(e,t)=>{if("_"===e.charAt(0))return t3(t,e.slice(1,e.length));switch(e){case E.bool:return t0(t);case E.float4:case E.float8:case E.int2:case E.int4:case E.int8:case E.numeric:case E.oid:return t1(t);case E.json:case E.jsonb:return t2(t);case E.timestamp:return t6(t);case E.abstime:case E.date:case E.daterange:case E.int4range:case E.int8range:case E.money:case E.reltime:case E.text:case E.time:case E.timestamptz:case E.timetz:case E.tsrange:case E.tstzrange:default:return tQ(t)}},tQ=e=>e,t0=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},t1=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},t2=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},t3=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,i=e[r];if("{"===e[0]&&"}"===i){let i;let s=e.slice(1,r);try{i=JSON.parse("["+s+"]")}catch(e){i=s?s.split(","):[]}return i.map(e=>tZ(t,e))}return e},t6=e=>"string"==typeof e?e.replace(" ","T"):e,t4=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class t5{constructor(e,t,r={},i=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(k||(k={}));class t8{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=t8.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=t8.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],i()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=t8.syncDiff(this.state,e,t,r),i())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,i){let s=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(s,(e,t)=>{n[e]||(o[e]=t)}),this.map(n,(e,t)=>{let r=s[e];if(r){let i=t.map(e=>e.presence_ref),s=r.map(e=>e.presence_ref),n=t.filter(e=>0>s.indexOf(e.presence_ref)),l=r.filter(e=>0>i.indexOf(e.presence_ref));n.length>0&&(a[e]=n),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(s,{joins:a,leaves:o},r,i)}static syncDiff(e,t,r,i){let{joins:s,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(s,(t,i)=>{var s;let n=null!==(s=e[t])&&void 0!==s?s:[];if(e[t]=this.cloneDeep(i),n.length>0){let r=e[t].map(e=>e.presence_ref),i=n.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...i)}r(t,n,i)}),this.map(n,(t,r)=>{let s=e[t];if(!s)return;let n=r.map(e=>e.presence_ref);s=s.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=s,i(t,s,r),0===s.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let i=e[r];return"metas"in i?t[r]=i.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(T||(T={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(P||(P={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(O||(O={}));class t9{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=v.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new t5(this,b.join,this.params,this.timeout),this.rejoinTimer=new tz(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=v.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=v.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=v.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=v.errored,this.rejoinTimer.scheduleTimeout())}),this._on(b.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new t8(this),this.broadcastEndpointURL=t4(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:s,presence:n,private:a}}=this.params;this._onError(t=>null==e?void 0:e(O.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(O.CLOSED));let o={},l={broadcast:s,presence:n,postgres_changes:null!==(i=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==i?i:[],private:a};this.socket.accessTokenValue&&(o.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},o)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(O.SUBSCRIBED);return}{let i=this.bindings.postgres_changes,s=null!==(r=null==i?void 0:i.length)&&void 0!==r?r:0,n=[];for(let r=0;r<s;r++){let s=i[r],{filter:{event:a,schema:o,table:l,filter:c}}=s,u=t&&t[r];if(u&&u.event===a&&u.schema===o&&u.table===l&&u.filter===c)n.push(Object.assign(Object.assign({},s),{id:u.id}));else{this.unsubscribe(),this.state=v.errored,null==e||e(O.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(O.SUBSCRIBED);return}}).receive("error",t=>{this.state=v.errored,null==e||e(O.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(O.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,i;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var i,s,n;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(n=null===(s=null===(i=this.params)||void 0===i?void 0:i.config)||void 0===s?void 0:s.broadcast)||void 0===n?void 0:n.ack)||r("ok"),a.receive("ok",()=>r("ok")),a.receive("error",()=>r("error")),a.receive("timeout",()=>r("timed out"))});{let{event:s,payload:n}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:s,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await (null===(i=e.body)||void 0===i?void 0:i.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=v.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(b.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(r=>{let i=new t5(this,b.leave,{},e);i.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),i.send(),this._canPush()||i.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){let i=new AbortController,s=setTimeout(()=>i.abort(),r),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:i.signal}));return clearTimeout(s),n}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new t5(this,e,t,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var i,s;let n=e.toLocaleLowerCase(),{close:a,error:o,leave:l,join:c}=b;if(r&&[a,o,l,c].indexOf(n)>=0&&r!==this._joinRef())return;let u=this._onMessage(n,t,r);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null===(i=this.bindings.postgres_changes)||void 0===i||i.filter(e=>{var t,r,i;return(null===(t=e.filter)||void 0===t?void 0:t.event)==="*"||(null===(i=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===i?void 0:i.toLocaleLowerCase())===n}).map(e=>e.callback(u,r)):null===(s=this.bindings[n])||void 0===s||s.filter(e=>{var r,i,s,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,a=null===(r=e.filter)||void 0===r?void 0:r.event;return n&&(null===(i=t.ids)||void 0===i?void 0:i.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null===(s=t.data)||void 0===s?void 0:s.type.toLocaleLowerCase()))}{let r=null===(o=null===(a=null==e?void 0:e.filter)||void 0===a?void 0:a.event)||void 0===o?void 0:o.toLocaleLowerCase();return"*"===r||r===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof u&&"ids"in u){let e=u.data,{schema:t,table:r,commit_timestamp:i,type:s,errors:n}=e;u=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:i,eventType:s,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(u,r)})}_isClosed(){return this.state===v.closed}_isJoined(){return this.state===v.joined}_isJoining(){return this.state===v.joining}_isLeaving(){return this.state===v.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let i=e.toLocaleLowerCase(),s={type:i,filter:t,callback:r};return this.bindings[i]?this.bindings[i].push(s):this.bindings[i]=[s],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var i;return!((null===(i=e.type)||void 0===i?void 0:i.toLocaleLowerCase())===r&&t9.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(b.close,{},e)}_onError(e){this._on(b.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=v.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=tX(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=tX(e.columns,e.old_record)),t}}let t7=()=>{},re=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class rt{constructor(e,t){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=tG,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=t7,this.ref=0,this.logger=t7,this.conn=null,this.sendBuffer=[],this.serializer=new tJ,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,339)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},this.endPoint=`${e}/${_.websocket}`,this.httpEndpoint=t4(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let s=null===(i=null==t?void 0:t.params)||void 0===i?void 0:i.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new tz(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=tF),this.transport){"undefined"!=typeof window&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new rr(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return this.channels=this.channels.filter(t=>t._joinRef!==e._joinRef),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case y.connecting:return S.Connecting;case y.open:return S.Open;case y.closing:return S.Closing;default:return S.Closed}}isConnected(){return this.connectionState()===S.Open}channel(e,t={config:{}}){let r=`realtime:${e}`,i=this.getChannels().find(e=>e.topic===r);if(i)return i;{let r=new t9(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){let{topic:t,event:r,payload:i,ref:s}=e,n=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${s})`,i),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),e.joinedOnce&&e._isJoined()&&e._push(b.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:i,ref:s}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),s&&s===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${t} ${r} ${s&&"("+s+")"||""}`,i),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,i,s)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(b.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${r}${i}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([re],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class rr{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=y.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class ri extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function rs(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class rn extends ri{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class ra extends ri{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let ro=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,339)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},rl=()=>(function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,339))).Response:Response}),rc=e=>{if(Array.isArray(e))return e.map(e=>rc(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=rc(r)}),t};var ru=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let rh=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),rd=(e,t,r)=>ru(void 0,void 0,void 0,function*(){e instanceof(yield rl())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new rn(rh(r),e.status||500))}).catch(e=>{t(new ra(rh(e),e))}):t(new ra(rh(e),e))}),rp=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),i&&(s.body=JSON.stringify(i)),Object.assign(Object.assign({},s),r))};function rf(e,t,r,i,s,n){return ru(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(r,rp(t,i,s,n)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>rd(e,o,i))})})}function rg(e,t,r,i){return ru(this,void 0,void 0,function*(){return rf(e,"GET",t,r,i)})}function rm(e,t,r,i,s){return ru(this,void 0,void 0,function*(){return rf(e,"POST",t,i,s,r)})}function rw(e,t,r,i,s){return ru(this,void 0,void 0,function*(){return rf(e,"DELETE",t,i,s,r)})}var ry=r(195).Buffer,rv=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let rb={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},r_={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class rS{constructor(e,t={},r,i){this.url=e,this.headers=t,this.bucketId=r,this.fetch=ro(i)}uploadOrUpdate(e,t,r,i){return rv(this,void 0,void 0,function*(){try{let s;let n=Object.assign(Object.assign({},r_),i),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),o=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((s=new FormData).append("cacheControl",n.cacheControl),o&&s.append("metadata",this.encodeMetadata(o)),s.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((s=r).append("cacheControl",n.cacheControl),o&&s.append("metadata",this.encodeMetadata(o))):(s=r,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==i?void 0:i.headers)&&(a=Object.assign(Object.assign({},a),i.headers));let l=this._removeEmptyFolders(t),c=this._getFinalPath(l),u=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:s,headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),h=yield u.json();if(u.ok)return{data:{path:l,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}upload(e,t,r){return rv(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,i){return rv(this,void 0,void 0,function*(){let s=this._removeEmptyFolders(e),n=this._getFinalPath(s),a=new URL(this.url+`/object/upload/sign/${n}`);a.searchParams.set("token",t);try{let e;let t=Object.assign({upsert:r_.upsert},i),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),l=yield o.json();if(o.ok)return{data:{path:s,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return rv(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(i["x-upsert"]="true");let s=yield rm(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),n=new URL(this.url+s.url),a=n.searchParams.get("token");if(!a)throw new ri("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:a},error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}update(e,t,r){return rv(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return rv(this,void 0,void 0,function*(){try{return{data:yield rm(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}copy(e,t,r){return rv(this,void 0,void 0,function*(){try{return{data:{path:(yield rm(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return rv(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),s=yield rm(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s={signedUrl:encodeURI(`${this.url}${s.signedURL}${n}`)},error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return rv(this,void 0,void 0,function*(){try{let i=yield rm(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:i.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${s}`):null})),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}download(e,t){return rv(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),s=i?`?${i}`:"";try{let t=this._getFinalPath(e),i=yield rg(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${s}`,{headers:this.headers,noResolveJson:!0});return{data:yield i.blob(),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}info(e){return rv(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield rg(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:rc(e),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}exists(e){return rv(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,i){return ru(this,void 0,void 0,function*(){return rf(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(rs(e)&&e instanceof ra){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),i=[],s=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==s&&i.push(s);let n=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&i.push(a);let o=i.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${r}${o}`)}}}remove(e){return rv(this,void 0,void 0,function*(){try{return{data:yield rw(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}list(e,t,r){return rv(this,void 0,void 0,function*(){try{let i=Object.assign(Object.assign(Object.assign({},rb),t),{prefix:e||""});return{data:yield rm(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==ry?ry.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let rE={"X-Client-Info":"storage-js/2.7.1"};var rk=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};class rT{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},rE),t),this.fetch=ro(r)}listBuckets(){return rk(this,void 0,void 0,function*(){try{return{data:yield rg(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}getBucket(e){return rk(this,void 0,void 0,function*(){try{return{data:yield rg(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return rk(this,void 0,void 0,function*(){try{return{data:yield rm(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return rk(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,i,s){return ru(this,void 0,void 0,function*(){return rf(e,"PUT",t,i,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}emptyBucket(e){return rk(this,void 0,void 0,function*(){try{return{data:yield rm(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}deleteBucket(e){return rk(this,void 0,void 0,function*(){try{return{data:yield rw(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(rs(e))return{data:null,error:e};throw e}})}}class rP extends rT{constructor(e,t={},r){super(e,t,r)}from(e){return new rS(this.url,this.headers,e,this.fetch)}}let rO="";"undefined"!=typeof Deno?rO="deno":"undefined"!=typeof document?rO="web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?rO="react-native":rO="node";let rx={headers:{"X-Client-Info":`supabase-js-${rO}/2.50.0`}},rR={schema:"public"},rA={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},rC={};var rI=r(339);let rj=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=rI.default:t=fetch,(...e)=>t(...e)},rN=()=>"undefined"==typeof Headers?rI.Headers:Headers,rL=(e,t,r)=>{let i=rj(r),s=rN();return(r,n)=>(function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!==(a=yield t())&&void 0!==a?a:e,l=new s(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),i(r,Object.assign(Object.assign({},n),{headers:l}))})},r$="2.70.0",rM={"X-Client-Info":`gotrue-js/${r$}`},rD="X-Supabase-Api-Version",rU={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},rq=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class rB extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function rH(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class rW extends rB{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class rK extends rB{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class rV extends rB{constructor(e,t,r,i){super(e,r,i),this.name=t,this.status=r}}class rF extends rV{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class rG extends rV{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class rJ extends rV{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class rz extends rV{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rX extends rV{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rY extends rV{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function rZ(e){return rH(e)&&"AuthRetryableFetchError"===e.name}class rQ extends rV{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class r0 extends rV{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let r1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),r2=" 	\n\r=".split(""),r3=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<r2.length;t+=1)e[r2[t].charCodeAt(0)]=-2;for(let t=0;t<r1.length;t+=1)e[r1[t].charCodeAt(0)]=t;return e})();function r6(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)r(r1[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)r(r1[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function r4(e,t,r){let i=r3[e];if(i>-1)for(t.queue=t.queue<<6|i,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===i)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function r5(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},n=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127){r(e);return}for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,i,r)};for(let t=0;t<e.length;t+=1)r4(e.charCodeAt(t),s,n);return t.join("")}let r8=()=>"undefined"!=typeof window&&"undefined"!=typeof document,r9={tested:!1,writable:!1},r7=()=>{if(!r8())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(r9.tested)return r9.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),r9.tested=!0,r9.writable=!0}catch(e){r9.tested=!0,r9.writable=!1}return r9.writable},ie=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,339)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},it=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,ir=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},ii=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},is=async(e,t)=>{await e.removeItem(t)};class ia{constructor(){this.promise=new ia.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function io(e){let t=e.split(".");if(3!==t.length)throw new r0("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!rq.test(t[e]))throw new r0("JWT not in base64url format");return{header:JSON.parse(r5(t[0])),payload:JSON.parse(r5(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)r4(e.charCodeAt(t),r,i);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function il(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function ic(e){return("0"+e.toString(16)).substr(-2)}async function iu(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function ih(e){return"undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder?btoa(await iu(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e)}async function id(e,t,r=!1){let i=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let i=0;i<56;i++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,ic).join("")}(),s=i;r&&(s+="/PASSWORD_RECOVERY"),await ir(e,`${t}-code-verifier`,s);let n=await ih(i),a=i===n?"plain":"s256";return[n,a]}ia.promiseConstructor=Promise;let ip=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,ig=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function im(e){if(!ig.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var iw=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};let iy=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),iv=[502,503,504];async function ib(e){var t;let r,i;if(!it(e))throw new rY(iy(e),0);if(iv.includes(e.status))throw new rY(iy(e),e.status);try{r=await e.json()}catch(e){throw new rK(iy(e),e)}let s=function(e){let t=e.headers.get(rD);if(!t||!t.match(ip))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(s&&s.getTime()>=rU["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?i=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(i=r.error_code),i){if("weak_password"===i)throw new rQ(iy(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===i)throw new rF}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new rQ(iy(r),e.status,r.weak_password.reasons);throw new rW(iy(r),e.status||500,i)}let i_=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),s.body=JSON.stringify(i),Object.assign(Object.assign({},s),r))};async function iS(e,t,r,i){var s;let n=Object.assign({},null==i?void 0:i.headers);n[rD]||(n[rD]=rU["2024-01-01"].name),(null==i?void 0:i.jwt)&&(n.Authorization=`Bearer ${i.jwt}`);let a=null!==(s=null==i?void 0:i.query)&&void 0!==s?s:{};(null==i?void 0:i.redirectTo)&&(a.redirect_to=i.redirectTo);let o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await iE(e,t,r+o,{headers:n,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(l):{data:Object.assign({},l),error:null}}async function iE(e,t,r,i,s,n){let a;let o=i_(t,i,s,n);try{a=await e(r,Object.assign({},o))}catch(e){throw console.error(e),new rY(iy(e),0)}if(a.ok||await ib(a),null==i?void 0:i.noResolveJson)return a;try{return await a.json()}catch(e){await ib(e)}}function ik(e){var t,r;let i=null;return e.access_token&&e.refresh_token&&e.expires_in&&(i=Object.assign({},e),!e.expires_at)&&(i.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)),{data:{session:i,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function iT(e){let t=ik(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function iP(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function iO(e){return{data:e,error:null}}function ix(e){let{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n},user:Object.assign({},iw(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function iR(e){return e}let iA=["global","local","others"];var iC=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};class iI{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=ie(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=iA[0]){if(0>iA.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${iA.join(", ")}`);try{return await iS(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(rH(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await iS(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:iP})}catch(e){if(rH(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=iC(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=null==r?void 0:r.newEmail,delete i.newEmail),await iS(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:ix,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(rH(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await iS(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:iP})}catch(e){if(rH(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,i,s,n,a,o;try{let l={nextPage:null,lastPage:0,total:0},c=await iS(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(s=null===(i=null==e?void 0:e.perPage)||void 0===i?void 0:i.toString())&&void 0!==s?s:""},xform:iR});if(c.error)throw c.error;let u=await c.json(),h=null!==(n=c.headers.get("x-total-count"))&&void 0!==n?n:0,d=null!==(o=null===(a=c.headers.get("link"))||void 0===a?void 0:a.split(","))&&void 0!==o?o:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(e){if(rH(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){im(e);try{return await iS(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:iP})}catch(e){if(rH(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){im(e);try{return await iS(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:iP})}catch(e){if(rH(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){im(e);try{return await iS(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:iP})}catch(e){if(rH(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){im(e.userId);try{let{data:t,error:r}=await iS(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(rH(e))return{data:null,error:e};throw e}}async _deleteFactor(e){im(e.userId),im(e.id);try{return{data:await iS(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(rH(e))return{data:null,error:e};throw e}}}let ij={getItem:e=>r7()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{r7()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{r7()&&globalThis.localStorage.removeItem(e)}};function iN(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let iL={debug:!!(globalThis&&r7()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class i$ extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class iM extends i${}async function iD(e,t,r){iL.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let i=new globalThis.AbortController;return t>0&&setTimeout(()=>{i.abort(),iL.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){iL.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await r()}finally{iL.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(0===t)throw iL.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new iM(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(iL.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();let iU={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:rM,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function iq(e,t,r){return await r()}class iB{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=iB.nextInstanceID,iB.nextInstanceID+=1,this.instanceID>0&&r8()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let i=Object.assign(Object.assign({},iU),e);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new iI({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=ie(i.fetch),this.lock=i.lock||iq,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:r8()&&(null===(t=null==globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=iD:this.lock=iq,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:r7()?this.storage=ij:(this.memoryStorage={},this.storage=iN(this.memoryStorage)):(this.memoryStorage={},this.storage=iN(this.memoryStorage)),r8()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${r$}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),r8()&&this.detectSessionInUrl&&"none"!==r){let{data:i,error:s}=await this._getSessionFromURL(t,r);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),rH(s)&&"AuthImplicitGrantRedirectError"===s.name){let t=null===(e=s.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:s}}return await this._removeSession(),{error:s}}let{session:n,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",n,"redirect type",a),await this._saveSession(n),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(rH(e))return{error:e};return{error:new rK("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,i;try{let{data:s,error:n}=await iS(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken}},xform:ik});if(n||!s)return{data:{user:null,session:null},error:n};let a=s.session,o=s.user;return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:o,session:a},error:null}}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,i;try{let s;if("email"in e){let{email:r,password:i,options:n}=e,a=null,o=null;"pkce"===this.flowType&&([a,o]=await id(this.storage,this.storageKey)),s=await iS(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:r,password:i,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:a,code_challenge_method:o},xform:ik})}else if("phone"in e){let{phone:t,password:n,options:a}=e;s=await iS(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!==(r=null==a?void 0:a.data)&&void 0!==r?r:{},channel:null!==(i=null==a?void 0:a.channel)&&void 0!==i?i:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:ik})}else throw new rJ("You must provide either an email or phone number and a password");let{data:n,error:a}=s;if(a||!n)return{data:{user:null,session:null},error:a};let o=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:i,options:s}=e;t=await iS(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:iT})}else if("phone"in e){let{phone:r,password:i,options:s}=e;t=await iS(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:iT})}else throw new rJ("You must provide either an email or phone number and a password");let{data:r,error:i}=t;if(i)return{data:{user:null,session:null},error:i};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new rG};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i}}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,i,s;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(i=e.options)||void 0===i?void 0:i.queryParams,skipBrowserRedirect:null===(s=e.options)||void 0===s?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,i,s,n,a,o,l,c,u,h,d;let p,f;if("message"in e)p=e.message,f=e.signature;else{let h;let{chain:d,wallet:g,statement:m,options:w}=e;if(r8()){if("object"==typeof g)h=g;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))h=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}}else{if("object"!=typeof g||!(null==w?void 0:w.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");h=g}let y=new URL(null!==(t=null==w?void 0:w.url)&&void 0!==t?t:window.location.href);if("signIn"in h&&h.signIn){let e;let t=await h.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==w?void 0:w.signInWithSolana),{version:"1",domain:y.host,uri:y.href}),m?{statement:m}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)p="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),f=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in h)||"function"!=typeof h.signMessage||!("publicKey"in h)||"object"!=typeof h||!h.publicKey||!("toBase58"in h.publicKey)||"function"!=typeof h.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");p=[`${y.host} wants you to sign in with your Solana account:`,h.publicKey.toBase58(),...m?["",m,""]:[""],"Version: 1",`URI: ${y.href}`,`Issued At: ${null!==(i=null===(r=null==w?void 0:w.signInWithSolana)||void 0===r?void 0:r.issuedAt)&&void 0!==i?i:new Date().toISOString()}`,...(null===(s=null==w?void 0:w.signInWithSolana)||void 0===s?void 0:s.notBefore)?[`Not Before: ${w.signInWithSolana.notBefore}`]:[],...(null===(n=null==w?void 0:w.signInWithSolana)||void 0===n?void 0:n.expirationTime)?[`Expiration Time: ${w.signInWithSolana.expirationTime}`]:[],...(null===(a=null==w?void 0:w.signInWithSolana)||void 0===a?void 0:a.chainId)?[`Chain ID: ${w.signInWithSolana.chainId}`]:[],...(null===(o=null==w?void 0:w.signInWithSolana)||void 0===o?void 0:o.nonce)?[`Nonce: ${w.signInWithSolana.nonce}`]:[],...(null===(l=null==w?void 0:w.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${w.signInWithSolana.requestId}`]:[],...(null===(u=null===(c=null==w?void 0:w.signInWithSolana)||void 0===c?void 0:c.resources)||void 0===u?void 0:u.length)?["Resources",...w.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await h.signMessage(new TextEncoder().encode(p),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");f=e}}try{let{data:t,error:r}=await iS(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:p,signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};return e.forEach(e=>r6(e,r,i)),r6(null,r,i),t.join("")}(f)},(null===(h=e.options)||void 0===h?void 0:h.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:ik});if(r)throw r;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new rG};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await ii(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(null!=t?t:"").split("/");try{let{data:t,error:s}=await iS(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:ik});if(await is(this.storage,`${this.storageKey}-code-verifier`),s)throw s;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new rG};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=i?i:null}),error:s}}catch(e){if(rH(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:i,access_token:s,nonce:n}=e,{data:a,error:o}=await iS(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:s,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:ik});if(o)return{data:{user:null,session:null},error:o};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new rG};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:o}}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,i,s,n;try{if("email"in e){let{email:i,options:s}=e,n=null,a=null;"pkce"===this.flowType&&([n,a]=await id(this.storage,this.storageKey));let{error:o}=await iS(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:i,data:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:{},create_user:null===(r=null==s?void 0:s.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:n,code_challenge_method:a},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:r}=e,{data:a,error:o}=await iS(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(i=null==r?void 0:r.data)&&void 0!==i?i:{},create_user:null===(s=null==r?void 0:r.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(n=null==r?void 0:r.channel)&&void 0!==n?n:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new rJ("You must provide either an email or phone number.")}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let i,s;"options"in e&&(i=null===(t=e.options)||void 0===t?void 0:t.redirectTo,s=null===(r=e.options)||void 0===r?void 0:r.captchaToken);let{data:n,error:a}=await iS(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:s}}),redirectTo:i,xform:ik});if(a)throw a;if(!n)throw Error("An error occurred on token verification.");let o=n.session,l=n.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,i;try{let s=null,n=null;return"pkce"===this.flowType&&([s,n]=await id(this.storage,this.storageKey)),await iS(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:n}),headers:this.headers,xform:iO})}catch(e){if(rH(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new rF;let{error:i}=await iS(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:i}})}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:i,options:s}=e,{error:n}=await iS(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){let{phone:r,type:i,options:s}=e,{data:n,error:a}=await iS(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:a}}throw new rJ("You must provide either an email or phone number and a type")}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await ii(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,i)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,i))})}return{data:{session:e},error:null}}let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{session:null},error:s};return{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await iS(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:iP});return await this._useSession(async e=>{var t,r,i;let{data:s,error:n}=e;if(n)throw n;return(null===(t=s.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await iS(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(i=null===(r=s.session)||void 0===r?void 0:r.access_token)&&void 0!==i?i:void 0,xform:iP}):{data:{user:null},error:new rF}})}catch(e){if(rH(e))return rH(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await is(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:i,error:s}=r;if(s)throw s;if(!i.session)throw new rF;let n=i.session,a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await id(this.storage,this.storageKey));let{data:l,error:c}=await iS(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:n.access_token,xform:iP});if(c)throw c;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}})}catch(e){if(rH(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new rF;let t=Date.now()/1e3,r=t,i=!0,s=null,{payload:n}=io(e.access_token);if(n.exp&&(i=(r=n.exp)<=t),i){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};s=t}else{let{data:i,error:n}=await this._getUser(e.access_token);if(n)throw n;s={access_token:e.access_token,refresh_token:e.refresh_token,user:i.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(e){if(rH(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:i,error:s}=t;if(s)throw s;e=null!==(r=i.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new rF;let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);return s?{data:{user:null,session:null},error:s}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(rH(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!r8())throw new rz("No browser detected.");if(e.error||e.error_description||e.error_code)throw new rz(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new rX("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new rz("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new rX("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let i=new URL(window.location.href);return i.searchParams.delete("code"),window.history.replaceState(window.history.state,"",i.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:i,access_token:s,refresh_token:n,expires_in:a,expires_at:o,token_type:l}=e;if(!s||!a||!n||!l)throw new rz("No session defined in URL");let c=Math.round(Date.now()/1e3),u=parseInt(a),h=c+u;o&&(h=parseInt(o));let d=h-c;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${u}s`);let p=h-u;c-p>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",p,h,c):c-p<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",p,h,c);let{data:f,error:g}=await this._getUser(s);if(g)throw g;let m={provider_token:r,provider_refresh_token:i,access_token:s,expires_in:u,expires_at:h,refresh_token:n,token_type:l,user:f.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:e.type},error:null}}catch(e){if(rH(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await ii(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{error:s};let n=null===(r=i.session)||void 0===r?void 0:r.access_token;if(n){let{error:t}=await this.admin.signOut(n,e);if(t&&!(rH(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await is(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,i;try{let{data:{session:i},error:s}=t;if(s)throw s;await (null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(t){await (null===(i=this.stateChangeEmitters.get(e))||void 0===i?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,i=null;"pkce"===this.flowType&&([r,i]=await id(this.storage,this.storageKey,!0));try{return await iS(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(rH(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(e){if(rH(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:i}=await this._useSession(async t=>{var r,i,s,n,a;let{data:o,error:l}=t;if(l)throw l;let c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(i=e.options)||void 0===i?void 0:i.scopes,queryParams:null===(s=e.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:!0});return await iS(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(a=null===(n=o.session)||void 0===n?void 0:n.access_token)&&void 0!==a?a:void 0})});if(i)throw i;return!r8()||(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(rH(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)throw n;return await iS(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(i=null===(r=s.session)||void 0===r?void 0:r.access_token)&&void 0!==i?i:void 0})})}catch(e){if(rH(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,i;let s=Date.now();return await (r=async r=>(r>0&&await il(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await iS(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:ik})),i=(e,t)=>t&&rZ(t)&&Date.now()+200*Math.pow(2,e)-s<3e4,new Promise((e,t)=>{(async()=>{for(let s=0;s<1/0;s++)try{let t=await r(s);if(!i(s,null,t)){e(t);return}}catch(e){if(!i(s,e)){t(e);return}}})()}))}catch(e){if(this._debug(t,"error",e),rH(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),r8()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await ii(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let i=(null!==(e=r.expires_at)&&void 0!==e?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),rZ(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new rF;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let i=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new ia;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new rF;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let i={session:t.session,error:null};return this.refreshingDeferred.resolve(i),i}catch(e){if(this._debug(i,"error",e),rH(e)){let r={session:null,error:e};return rZ(e)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(r),r}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(e,t,r=!0){let i=`#_notifyAllSubscribers(${e})`;this._debug(i,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let i=[],s=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){i.push(e)}});if(await Promise.all(s),i.length>0){for(let e=0;e<i.length;e+=1)console.error(i[e]);throw i[0]}}finally{this._debug(i,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await ir(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await is(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&r8()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}let i=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),i<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof i$)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!r8()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let i=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await id(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});i.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);i.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${i.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await iS(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token})})}catch(e){if(rH(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)return{data:null,error:n};let a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await iS(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(i=null==o?void 0:o.totp)||void 0===i?void 0:i.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(rH(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{data:null,error:s};let{data:n,error:a}=await iS(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:a})})}catch(e){if(rH(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await iS(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token})})}catch(e){if(rH(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],i=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),s=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:i,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:i},error:s}=e;if(s)return{data:null,error:s};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:n}=io(i.access_token),a=null;n.aal&&(a=n.aal);let o=a;return(null!==(r=null===(t=i.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(o="aal2"),{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:n.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:i,error:s}=await iS(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(s)throw s;if(!i.keys||0===i.keys.length)throw new r0("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),!(r=i.keys.find(t=>t.kid===e)))throw new r0("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}let{header:i,payload:s,signature:n,raw:{header:a,payload:o}}=io(r);if(!function(e){if(!e)throw Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}(s.exp),!i.kid||"HS256"===i.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:s,header:i,signature:n},error:null}}let l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(i.alg),c=await this.fetchJwk(i.kid,t),u=await crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!await crypto.subtle.verify(l,u,n,function(e){let t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127){t(e);return}if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${a}.${o}`)))throw new r0("Invalid JWT signature");return{data:{claims:s,header:i,signature:n},error:null}}catch(e){if(rH(e))return{data:null,error:e};throw e}}}iB.nextInstanceID=0;let iH=iB;class iW extends iH{constructor(e){super(e)}}class iK{constructor(e,t,r){var i,s,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o=`sb-${a.hostname.split(".")[0]}-auth-token`,l=function(e,t){var r,i;let{db:s,auth:n,realtime:a,global:o}=e,{db:l,auth:c,realtime:u,global:h}=t,d={db:Object.assign(Object.assign({},l),s),auth:Object.assign(Object.assign({},c),n),realtime:Object.assign(Object.assign({},u),a),global:Object.assign(Object.assign(Object.assign({},h),o),{headers:Object.assign(Object.assign({},null!==(r=null==h?void 0:h.headers)&&void 0!==r?r:{}),null!==(i=null==o?void 0:o.headers)&&void 0!==i?i:{})}),accessToken:()=>{var e,t,r,i;return e=this,t=void 0,i=function*(){return""},new(r=void 0,r=Promise)(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:rR,realtime:rC,auth:Object.assign(Object.assign({},rA),{storageKey:o}),global:rx});this.storageKey=null!==(i=l.auth.storageKey)&&void 0!==i?i:"",this.headers=null!==(s=l.global.headers)&&void 0!==s?s:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(n=l.auth)&&void 0!==n?n:{},this.headers,l.global.fetch),this.fetch=rL(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new tq(new URL("rest/v1",a).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new tU(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new rP(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,i,s,n;return r=this,i=void 0,s=void 0,n=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null},new(s||(s=Promise))(function(e,t){function a(e){try{l(n.next(e))}catch(e){t(e)}}function o(e){try{l(n.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof s?r:new s(function(e){e(r)})).then(a,o)}l((n=n.apply(r,i||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,storageKey:s,flowType:n,lock:a,debug:o},l,c){let u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new iW({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),l),storageKey:s,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,flowType:n,lock:a,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new rt(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let iV=(e,t,r)=>new iK(e,t,r),iF=iV("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder");async function iG(e){try{let t=process.env.JWT_SECRET||"placeholder-secret",r=new TextEncoder().encode(t),{payload:i}=await tj(e,r),{data:s,error:n}=await iF.from("admin_users").select(`
        id,
        email,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        last_activity,
        permissions
      `).eq("id",i.userId).eq("is_active",!0).single();if(n||!s)return{valid:!1,error:"User not found or inactive"};if(!s.is_active)return{valid:!1,error:"User account is deactivated"};let a={id:s.id,email:s.email,role:s.role,firstName:s.first_name,lastName:s.last_name,isActive:s.is_active,mfaEnabled:s.mfa_enabled,lastActivity:s.last_activity||Date.now(),permissions:s.permissions||[]};return{valid:!0,user:a}}catch(e){return console.error("Token verification error:",e),{valid:!1,error:"Invalid token"}}}async function iJ(e){let t=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return e.headers.get("cf-connecting-ip")||(t?t.split(",")[0].trim():r||e.ip||"unknown")}(e);if("true"!==process.env.ENABLE_IP_RESTRICTIONS)return{allowed:!0,ip:t};let r=[...process.env.ALLOWED_IP_RANGES?.split(",")||[],...process.env.ADMIN_IP_WHITELIST?.split(",")||[]];if(0===r.length)return{allowed:!0,ip:t};for(let e of r)if(function(e,t){if(e===t)return!0;if(t.includes("/"))return function(e,t){try{let[r,i]=t.split("/"),s=parseInt(i,10);if(iz(e)&&iz(r))return function(e,t,r){let i=iX(e),s=iX(t),n=**********<<32-r>>>0;return(i&n)==(s&n)}(e,r,s);return!1}catch(e){return console.error("CIDR check error:",e),!1}}(e,t);if(t.includes("*")){let r=t.replace(/\*/g,".*");return RegExp(`^${r}$`).test(e)}return!1}(t,e.trim()))return{allowed:!0,ip:t};return{allowed:!1,ip:t,reason:"IP address not in whitelist"}}function iz(e){let t=e.split(".");return 4===t.length&&t.every(e=>{let t=parseInt(e,10);return t>=0&&t<=255&&e===t.toString()})}function iX(e){return e.split(".").reduce((e,t)=>(e<<8)+parseInt(t,10),0)>>>0}let iY=new Map,iZ={"/api/auth/login":{windowMs:9e5,maxRequests:5},"/api/auth/mfa":{windowMs:3e5,maxRequests:10},"/api/auth/forgot-password":{windowMs:36e5,maxRequests:3},"/api/admin":{windowMs:6e4,maxRequests:100},default:{windowMs:6e4,maxRequests:60}};async function iQ(e){let t=function(e){if(e.headers&&"function"==typeof e.headers.get){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return e.headers.get("cf-connecting-ip")||(t?t.split(",")[0].trim():r||e.ip||"unknown")}if(e.headers&&"object"==typeof e.headers){let t=e.headers["x-forwarded-for"],r=e.headers["x-real-ip"],i=e.headers["cf-connecting-ip"];if(i)return Array.isArray(i)?i[0]:i;if(t)return Array.isArray(t)?t[0]:t.split(",")[0].trim();if(r)return Array.isArray(r)?r[0]:r}return"unknown"}(e),r=e.nextUrl?.pathname||e.url||"/unknown",i=function(e){if(iZ[e])return iZ[e];for(let[t,r]of Object.entries(iZ))if("default"!==t&&e.startsWith(t))return r;return iZ.default}(r),s=`${t}:${r}`,n=Date.now();i.windowMs;let a=iY.get(s);return(a&&a.resetTime<n&&(iY.delete(s),a=void 0),a||(a={count:0,resetTime:n+i.windowMs}),a.count>=i.maxRequests)?{allowed:!1,ip:t,remaining:0,resetTime:a.resetTime,reason:"Rate limit exceeded"}:(a.count++,iY.set(s,a),.01>Math.random()&&function(){let e=Date.now(),t=[];iY.forEach((r,i)=>{r.resetTime<e&&t.push(i)}),t.forEach(e=>iY.delete(e))}(),{allowed:!0,ip:t,remaining:i.maxRequests-a.count,resetTime:a.resetTime})}let i0=iV("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder");async function i1(e){try{let t={action:e.action,user_id:e.userId,user_role:e.userRole,email:e.email,ip_address:e.ip,path:e.path,resource:e.resource,resource_id:e.resourceId,old_values:e.oldValues,new_values:e.newValues,reason:e.reason,error:e.error,metadata:e.metadata,severity:e.severity||"medium",created_at:new Date().toISOString()},{error:r}=await i0.from("audit_logs").insert(t);r&&console.error("Failed to write audit log to database:",r);let i=function(e){switch(e){case"low":case"medium":default:return"log";case"high":return"warn";case"critical":return"error"}}(e.severity||"medium"),s=function(e){let t=new Date().toISOString(),r=e.userId?`[User: ${e.userId}]`:"",i=e.ip?`[IP: ${e.ip}]`:"",s=e.path?`[Path: ${e.path}]`:"";return`[AUDIT] ${t} ${e.action} ${r} ${i} ${s} ${e.reason||""}`.trim()}(e);console[i](s),"critical"===e.severity&&await i2(e)}catch(t){console.error("Audit logging failed:",t),console.error("AUDIT_LOG_FAILURE:",JSON.stringify(e,null,2))}}async function i2(e){try{console.error("\uD83D\uDEA8 CRITICAL SECURITY EVENT:",{action:e.action,userId:e.userId,ip:e.ip,reason:e.reason,timestamp:new Date().toISOString()}),"true"===process.env.ENABLE_CRITICAL_ALERTS&&await i3(e)}catch(e){console.error("Failed to send critical alert:",e)}}async function i3(e){console.log("Email alert would be sent for:",e.action)}let i6=["/admin/dashboard","/admin/bookings","/admin/customers","/admin/services","/admin/products","/admin/staff","/admin/artists","/admin/tips","/admin/receipts","/admin/reports","/admin/settings","/admin/pos","/admin/inventory","/api/admin"],i4=["/admin/login","/admin/forgot-password","/admin/reset-password","/admin/mfa-setup","/api/auth"],i5={"/admin/staff":["DEV","Admin"],"/admin/artists":["DEV","Admin"],"/admin/tips":["DEV","Admin"],"/admin/receipts":["DEV","Admin"],"/admin/settings":["DEV","Admin"],"/admin/reports/financial":["DEV","Admin"],"/api/admin/staff":["DEV","Admin"],"/api/admin/tips":["DEV","Admin"],"/api/admin/receipts":["DEV","Admin"],"/api/admin/settings":["DEV","Admin"]};async function i8(e){let{pathname:t}=e.nextUrl,r=el.next();r.headers.set("X-Frame-Options","DENY"),r.headers.set("X-Content-Type-Options","nosniff"),r.headers.set("Referrer-Policy","strict-origin-when-cross-origin"),r.headers.set("X-Admin-Portal","true");try{if("true"===process.env.ENABLE_IP_RESTRICTIONS){let r=await iJ(e);if(!r.allowed)return await i1({action:"IP_BLOCKED",ip:r.ip,path:t,reason:"IP not in whitelist"}),new el("Access Denied",{status:403})}let i=await iQ(e);if(!i.allowed)return await i1({action:"RATE_LIMITED",ip:i.ip,path:t,reason:"Rate limit exceeded"}),new el("Too Many Requests",{status:429});let s=i6.some(e=>t.startsWith(e));if(i4.some(e=>t.startsWith(e))||"/"===t)return r;if(s){let i=e.cookies.get("admin-token")?.value;if(!i)return await i1({action:"UNAUTHORIZED_ACCESS",path:t,reason:"No authentication token"}),el.redirect(new URL("/admin/login",e.url));let s=await iG(i);if(!s.valid||!s.user){await i1({action:"INVALID_TOKEN",path:t,reason:"Invalid or expired token"});let r=el.redirect(new URL("/admin/login",e.url));return r.cookies.delete("admin-token"),r}let n=Object.entries(i5).find(([e])=>t.startsWith(e));if(n){let[,e]=n;if(!e.includes(s.user.role))return await i1({action:"INSUFFICIENT_PERMISSIONS",userId:s.user.id,userRole:s.user.role,path:t,reason:`Role ${s.user.role} not in ${e.join(", ")}`}),new el("Insufficient Permissions",{status:403})}let a="DEV"===s.user.role?parseInt(process.env.ADMIN_SESSION_TIMEOUT||"1800"):parseInt(process.env.SESSION_TIMEOUT||"3600");if(Date.now()-s.user.lastActivity>1e3*a){await i1({action:"SESSION_TIMEOUT",userId:s.user.id,path:t,reason:"Session expired"});let r=el.redirect(new URL("/admin/login?reason=timeout",e.url));return r.cookies.delete("admin-token"),r}t.startsWith("/api/")&&(r.headers.set("X-User-ID",s.user.id),r.headers.set("X-User-Role",s.user.role),r.headers.set("X-User-Email",s.user.email)),await i1({action:"ACCESS_GRANTED",userId:s.user.id,userRole:s.user.role,path:t,ip:e.ip})}if(["/shop","/book-online","/services","/gallery","/contact"].some(e=>t.startsWith(e))){let e=`http://localhost:3000${t}`;return el.redirect(e)}return r}catch(i){if(console.error("Middleware error:",i),await i1({action:"MIDDLEWARE_ERROR",path:t,error:i instanceof Error?i.message:"Unknown error"}),i6.some(e=>t.startsWith(e)))return el.redirect(new URL("/admin/login?error=system",e.url));return r}}let i9={matcher:["/((?!_next/static|_next/image|favicon.ico|public/).*)"]},i7={...x},se=i7.middleware||i7.default,st="/middleware";if("function"!=typeof se)throw Error(`The Middleware "${st}" must export a \`middleware\` or a \`default\` function`);function sr(e){return eX({...e,page:st,handler:se})}},339:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Headers:()=>a,Request:()=>o,Response:()=>l,default:()=>n,fetch:()=>s});var i=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let s=i.fetch,n=i.fetch.bind(i),a=i.Headers,o=i.Request,l=i.Response},967:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(339)),n=i(r(518));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=s.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,i;let s=null,a=null,o=null,l=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept?t:this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let i=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),n=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");i&&n&&n.length>1&&(o=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(s={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,c="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{s=JSON.parse(t),Array.isArray(s)&&404===e.status&&(a=[],s=null,l=200,c="OK")}catch(r){404===e.status&&""===t?(l=204,c="No Content"):s={message:t}}if(s&&this.isMaybeSingle&&(null===(i=null==s?void 0:s.details)||void 0===i?void 0:i.includes("0 rows"))&&(s=null,l=200,c="OK"),s&&this.shouldThrowOnError)throw new n.default(s)}return{error:s,data:a,count:o,status:l,statusText:c}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,i;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(i=null==e?void 0:e.code)&&void 0!==i?i:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},897:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(674)),n=i(r(709)),a=r(678);class o{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=i}from(e){let t=new URL(`${this.url}/${e}`);return new s.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:i=!1,count:s}={}){let a,o;let l=new URL(`${this.url}/rpc/${e}`);r||i?(a=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let c=Object.assign({},this.headers);return s&&(c.Prefer=`count=${s}`),new n.default({method:a,url:l,headers:c,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},518:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},709:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(587));class n extends s.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:i}={}){let s="";"plain"===i?s="pl":"phrase"===i?s="ph":"websearch"===i&&(s="w");let n=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${s}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=n},674:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(709));class n{constructor(e,{headers:t={},schema:r,fetch:i}){this.url=e,this.headers=t,this.schema=r,this.fetch=i}select(e,{head:t=!1,count:r}={}){let i=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",n),r&&(this.headers.Prefer=`count=${r}`),new s.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),t&&i.push(`count=${t}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:i,defaultToNull:n=!0}={}){let a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new s.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new s.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=n},587:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(967));class n extends s.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:i,referencedTable:s=i}={}){let n=s?`${s}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let i=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:i=r}={}){let s=void 0===i?"offset":`${i}.offset`,n=void 0===i?"limit":`${i}.limit`;return this.url.searchParams.set(s,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:i=!1,wal:s=!1,format:n="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,r?"settings":null,i?"buffers":null,s?"wal":null].filter(Boolean).join("|"),l=null!==(a=this.headers.Accept)&&void 0!==a?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=n},678:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let i=r(623);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${i.version}`}},196:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let s=i(r(897));t.PostgrestClient=s.default;let n=i(r(674));t.PostgrestQueryBuilder=n.default;let a=i(r(709));t.PostgrestFilterBuilder=a.default;let o=i(r(587));t.PostgrestTransformBuilder=o.default;let l=i(r(967));t.PostgrestBuilder=l.default;let c=i(r(518));t.PostgrestError=c.default,t.default={PostgrestClient:s.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:c.default}},623:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},675:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,n={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,s]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=s?s:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[i,s],...n]=o(e),{domain:a,expires:l,httponly:h,maxage:d,path:p,samesite:f,secure:g,partitioned:m,priority:w}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:i,value:decodeURIComponent(s),domain:a,...l&&{expires:new Date(l)},...h&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:p,...f&&{sameSite:c.includes(t=(t=f).toLowerCase())?t:void 0},...g&&{secure:!0},...w&&{priority:u.includes(r=(r=w).toLowerCase())?r:void 0},...m&&{partitioned:!0}})}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(n,{RequestCookies:()=>h,ResponseCookies:()=>d,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,n,a,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let l of i(n))s.call(e,l)||l===a||t(e,l,{get:()=>n[l],enumerable:!(o=r(n,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var c=["strict","lax","none"],u=["low","medium","high"],h=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let s=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(s)?s:function(e){if(!e)return[];var t,r,i,s,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=s,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(s)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,s=this._parsed;return s.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(s,this._headers),this}delete(...e){let[t,r,i]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},486:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let i=r(223),s=r(172),n=r(930),a="context",o=new i.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,s.registerGlobal)(a,e,n.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...i){return this._getContextManager().with(e,t,r,...i)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,s.getGlobal)(a)||o}disable(){this._getContextManager().disable(),(0,s.unregisterGlobal)(a,n.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let i=r(56),s=r(912),n=r(957),a=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,a.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:n.DiagLogLevel.INFO})=>{var i,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(i=e.stack)&&void 0!==i?i:e.message),!1}"number"==typeof r&&(r={logLevel:r});let c=(0,a.getGlobal)("diag"),u=(0,s.createLogLevelDiagLogger)(null!==(o=r.logLevel)&&void 0!==o?o:n.DiagLogLevel.INFO,e);if(c&&!r.suppressOverrideMessage){let e=null!==(l=Error().stack)&&void 0!==l?l:"<failed to generate stacktrace>";c.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new i.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let i=r(660),s=r(172),n=r(930),a="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,s.registerGlobal)(a,e,n.DiagAPI.instance())}getMeterProvider(){return(0,s.getGlobal)(a)||i.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,s.unregisterGlobal)(a,n.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let i=r(172),s=r(874),n=r(194),a=r(277),o=r(369),l=r(930),c="propagation",u=new s.NoopTextMapPropagator;class h{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new h),this._instance}setGlobalPropagator(e){return(0,i.registerGlobal)(c,e,l.DiagAPI.instance())}inject(e,t,r=n.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=n.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,i.unregisterGlobal)(c,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,i.getGlobal)(c)||u}}t.PropagationAPI=h},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let i=r(172),s=r(846),n=r(139),a=r(607),o=r(930),l="trace";class c{constructor(){this._proxyTracerProvider=new s.ProxyTracerProvider,this.wrapSpanContext=n.wrapSpanContext,this.isSpanContextValid=n.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalTracerProvider(e){let t=(0,i.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,i.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,i.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new s.ProxyTracerProvider}}t.TraceAPI=c},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let i=r(491),s=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function n(e){return e.getValue(s)||void 0}t.getBaggage=n,t.getActiveBaggage=function(){return n(i.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(s,t)},t.deleteBaggage=function(e){return e.deleteValue(s)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let i=new r(this._entries);return i._entries.set(e,t),i}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let i=r(930),s=r(993),n=r(830),a=i.DiagAPI.instance();t.createBaggage=function(e={}){return new s.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:n.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0;let i=r(491);t.context=i.ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let i=r(780);class s{active(){return i.ROOT_CONTEXT}with(e,t,r,...i){return t.call(r,...i)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=s},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,i)=>{let s=new r(t._currentContext);return s._currentContext.set(e,i),s},t.deleteValue=e=>{let i=new r(t._currentContext);return i._currentContext.delete(e),i}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0;let i=r(930);t.diag=i.DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let i=r(172);class s{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return n("debug",this._namespace,e)}error(...e){return n("error",this._namespace,e)}info(...e){return n("info",this._namespace,e)}warn(...e){return n("warn",this._namespace,e)}verbose(...e){return n("verbose",this._namespace,e)}}function n(e,t,r){let s=(0,i.getGlobal)("diag");if(s)return r.unshift(t),s[e](...r)}t.DiagComponentLogger=s},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class i{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=i},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let i=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,i){let s=t[r];return"function"==typeof s&&e>=i?s.bind(t):function(){}}return e<i.DiagLogLevel.NONE?e=i.DiagLogLevel.NONE:e>i.DiagLogLevel.ALL&&(e=i.DiagLogLevel.ALL),t=t||{},{error:r("error",i.DiagLogLevel.ERROR),warn:r("warn",i.DiagLogLevel.WARN),info:r("info",i.DiagLogLevel.INFO),debug:r("debug",i.DiagLogLevel.DEBUG),verbose:r("verbose",i.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let i=r(200),s=r(521),n=r(130),a=s.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${a}`),l=i._globalThis;t.registerGlobal=function(e,t,r,i=!1){var n;let a=l[o]=null!==(n=l[o])&&void 0!==n?n:{version:s.VERSION};if(!i&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(a.version!==s.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${s.VERSION}`);return r.error(t.stack||t.message),!1}return a[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${s.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let i=null===(t=l[o])||void 0===t?void 0:t.version;if(i&&(0,n.isCompatible)(i))return null===(r=l[o])||void 0===r?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${s.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let i=r(521),s=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function n(e){let t=new Set([e]),r=new Set,i=e.match(s);if(!i)return()=>!1;let n={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=n.prerelease)return function(t){return t===e};function a(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let i=e.match(s);if(!i)return a(e);let o={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};return null!=o.prerelease||n.major!==o.major?a(e):0===n.major?n.minor===o.minor&&n.patch<=o.patch?(t.add(e),!0):a(e):n.minor<=o.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=n,t.isCompatible=n(i.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0;let i=r(653);t.metrics=i.MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class i{}t.NoopMetric=i;class s extends i{add(e,t){}}t.NoopCounterMetric=s;class n extends i{add(e,t){}}t.NoopUpDownCounterMetric=n;class a extends i{record(e,t){}}t.NoopHistogramMetric=a;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class c extends o{}t.NoopObservableGaugeMetric=c;class u extends o{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new s,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new n,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new c,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let i=r(102);class s{getMeter(e,t,r){return i.NOOP_METER}}t.NoopMeterProvider=s,t.NOOP_METER_PROVIDER=new s},200:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0;let i=r(181);t.propagation=i.PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0;let i=r(997);t.trace=i.TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let i=r(476);class s{constructor(e=i.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=s},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let i=r(491),s=r(607),n=r(403),a=r(139),o=i.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){if(null==t?void 0:t.root)return new n.NonRecordingSpan;let i=r&&(0,s.getSpanContext)(r);return"object"==typeof i&&"string"==typeof i.spanId&&"string"==typeof i.traceId&&"number"==typeof i.traceFlags&&(0,a.isSpanContextValid)(i)?new n.NonRecordingSpan(i):new n.NonRecordingSpan}startActiveSpan(e,t,r,i){let n,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(n=t,l=r):(n=t,a=r,l=i);let c=null!=a?a:o.active(),u=this.startSpan(e,n,c),h=(0,s.setSpan)(c,u);return o.with(h,l,void 0,u)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let i=r(614);class s{getTracer(e,t,r){return new i.NoopTracer}}t.NoopTracerProvider=s},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let i=new(r(614)).NoopTracer;class s{constructor(e,t,r,i){this._provider=e,this.name=t,this.version=r,this.options=i}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,i){let s=this._getTracer();return Reflect.apply(s.startActiveSpan,s,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):i}}t.ProxyTracer=s},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let i=r(125),s=new(r(124)).NoopTracerProvider;class n{getTracer(e,t,r){var s;return null!==(s=this.getDelegateTracer(e,t,r))&&void 0!==s?s:new i.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!==(e=this._delegate)&&void 0!==e?e:s}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var i;return null===(i=this._delegate)||void 0===i?void 0:i.getTracer(e,t,r)}}t.ProxyTracerProvider=n},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let i=r(780),s=r(403),n=r(491),a=(0,i.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=o,t.getActiveSpan=function(){return o(n.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new s.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null===(t=o(e))||void 0===t?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let i=r(564);class s{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),s=r.indexOf("=");if(-1!==s){let n=r.slice(0,s),a=r.slice(s+1,t.length);(0,i.validateKey)(n)&&(0,i.validateValue)(a)&&e.set(n,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new s;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=s},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",i=`[a-z]${r}{0,255}`,s=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,n=RegExp(`^(?:${i}|${s})$`),a=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return n.test(e)},t.validateValue=function(e){return a.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let i=r(325);t.createTraceState=function(e){return new i.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let i=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:i.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let i=r(476),s=r(403),n=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function o(e){return n.test(e)&&e!==i.INVALID_TRACEID}function l(e){return a.test(e)&&e!==i.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new s.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},i={};function s(e){var r=i[e];if(void 0!==r)return r.exports;var n=i[e]={exports:{}},a=!0;try{t[e].call(n.exports,n,n.exports,s),a=!1}finally{a&&delete i[e]}return n.exports}s.ab="//";var n={};(()=>{Object.defineProperty(n,"__esModule",{value:!0}),n.trace=n.propagation=n.metrics=n.diag=n.context=n.INVALID_SPAN_CONTEXT=n.INVALID_TRACEID=n.INVALID_SPANID=n.isValidSpanId=n.isValidTraceId=n.isSpanContextValid=n.createTraceState=n.TraceFlags=n.SpanStatusCode=n.SpanKind=n.SamplingDecision=n.ProxyTracerProvider=n.ProxyTracer=n.defaultTextMapSetter=n.defaultTextMapGetter=n.ValueType=n.createNoopMeter=n.DiagLogLevel=n.DiagConsoleLogger=n.ROOT_CONTEXT=n.createContextKey=n.baggageEntryMetadataFromString=void 0;var e=s(369);Object.defineProperty(n,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=s(780);Object.defineProperty(n,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(n,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=s(972);Object.defineProperty(n,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var i=s(957);Object.defineProperty(n,"DiagLogLevel",{enumerable:!0,get:function(){return i.DiagLogLevel}});var a=s(102);Object.defineProperty(n,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var o=s(901);Object.defineProperty(n,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=s(194);Object.defineProperty(n,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(n,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var c=s(125);Object.defineProperty(n,"ProxyTracer",{enumerable:!0,get:function(){return c.ProxyTracer}});var u=s(846);Object.defineProperty(n,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var h=s(996);Object.defineProperty(n,"SamplingDecision",{enumerable:!0,get:function(){return h.SamplingDecision}});var d=s(357);Object.defineProperty(n,"SpanKind",{enumerable:!0,get:function(){return d.SpanKind}});var p=s(847);Object.defineProperty(n,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var f=s(475);Object.defineProperty(n,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=s(98);Object.defineProperty(n,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var m=s(139);Object.defineProperty(n,"isSpanContextValid",{enumerable:!0,get:function(){return m.isSpanContextValid}}),Object.defineProperty(n,"isValidTraceId",{enumerable:!0,get:function(){return m.isValidTraceId}}),Object.defineProperty(n,"isValidSpanId",{enumerable:!0,get:function(){return m.isValidSpanId}});var w=s(476);Object.defineProperty(n,"INVALID_SPANID",{enumerable:!0,get:function(){return w.INVALID_SPANID}}),Object.defineProperty(n,"INVALID_TRACEID",{enumerable:!0,get:function(){return w.INVALID_TRACEID}}),Object.defineProperty(n,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return w.INVALID_SPAN_CONTEXT}});let y=s(67);Object.defineProperty(n,"context",{enumerable:!0,get:function(){return y.context}});let v=s(506);Object.defineProperty(n,"diag",{enumerable:!0,get:function(){return v.diag}});let b=s(886);Object.defineProperty(n,"metrics",{enumerable:!0,get:function(){return b.metrics}});let _=s(939);Object.defineProperty(n,"propagation",{enumerable:!0,get:function(){return _.propagation}});let S=s(845);Object.defineProperty(n,"trace",{enumerable:!0,get:function(){return S.trace}}),n.default={context:y.context,diag:v.diag,metrics:b.metrics,propagation:_.propagation,trace:S.trace}})(),e.exports=n})()},642:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var s={},n=t.split(i),a=(r||{}).decode||e,o=0;o<n.length;o++){var l=n[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),h=l.substr(++c,l.length).trim();'"'==h[0]&&(h=h.slice(1,-1)),void 0==s[u]&&(s[u]=function(e,t){try{return t(e)}catch(t){return e}}(h,a))}}return s},t.serialize=function(e,t,i){var n=i||{},a=n.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!s.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!s.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=n.maxAge){var c=n.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(n.domain){if(!s.test(n.domain))throw TypeError("option domain is invalid");l+="; Domain="+n.domain}if(n.path){if(!s.test(n.path))throw TypeError("option path is invalid");l+="; Path="+n.path}if(n.expires){if("function"!=typeof n.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,i=/; */,s=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},52:(e,t,r)=>{var i;(()=>{var s={226:function(s,n){!function(a,o){"use strict";var l="function",c="undefined",u="object",h="string",d="major",p="model",f="name",g="type",m="vendor",w="version",y="architecture",v="console",b="mobile",_="tablet",S="smarttv",E="wearable",k="embedded",T="Amazon",P="Apple",O="ASUS",x="BlackBerry",R="Browser",A="Chrome",C="Firefox",I="Google",j="Huawei",N="Microsoft",L="Motorola",$="Opera",M="Samsung",D="Sharp",U="Sony",q="Xiaomi",B="Zebra",H="Facebook",W="Chromium OS",K="Mac OS",V=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},F=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},G=function(e,t){return typeof e===h&&-1!==J(t).indexOf(J(e))},J=function(e){return e.toLowerCase()},z=function(e,t){if(typeof e===h)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},X=function(e,t){for(var r,i,s,n,a,c,h=0;h<t.length&&!a;){var d=t[h],p=t[h+1];for(r=i=0;r<d.length&&!a&&d[r];)if(a=d[r++].exec(e))for(s=0;s<p.length;s++)c=a[++i],typeof(n=p[s])===u&&n.length>0?2===n.length?typeof n[1]==l?this[n[0]]=n[1].call(this,c):this[n[0]]=n[1]:3===n.length?typeof n[1]!==l||n[1].exec&&n[1].test?this[n[0]]=c?c.replace(n[1],n[2]):void 0:this[n[0]]=c?n[1].call(this,c,n[2]):void 0:4===n.length&&(this[n[0]]=c?n[3].call(this,c.replace(n[1],n[2])):void 0):this[n]=c||o;h+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var i=0;i<t[r].length;i++)if(G(t[r][i],e))return"?"===r?o:r}else if(G(t[r],e))return"?"===r?o:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[w,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[w,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,w],[/opios[\/ ]+([\w\.]+)/i],[w,[f,$+" Mini"]],[/\bopr\/([\w\.]+)/i],[w,[f,$]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,w],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[w,[f,"UC"+R]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[w,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[w,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[w,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[w,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[w,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+R],w],[/\bfocus\/([\w\.]+)/i],[w,[f,C+" Focus"]],[/\bopt\/([\w\.]+)/i],[w,[f,$+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[w,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[w,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[w,[f,$+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[w,[f,"MIUI "+R]],[/fxios\/([-\w\.]+)/i],[w,[f,C]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+R]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+R],w],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],w],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,w],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,H],w],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,w],[/\bgsa\/([\w\.]+) .*safari\//i],[w,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[w,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[w,[f,A+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,A+" WebView"],w],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[w,[f,"Android "+R]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,w],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[w,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[w,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[w,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,w],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],w],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[w,[f,C+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,w],[/(cobalt)\/([\w\.]+)/i],[f,[w,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[y,"amd64"]],[/(ia32(?=;))/i],[[y,J]],[/((?:i[346]|x)86)[;\)]/i],[[y,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[y,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[y,"armhf"]],[/windows (ce|mobile); ppc;/i],[[y,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[y,/ower/,"",J]],[/(sun4\w)[;\)]/i],[[y,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[y,J]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[m,M],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[m,M],[g,b]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[m,P],[g,b]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[m,P],[g,_]],[/(macintosh);/i],[p,[m,P]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[m,D],[g,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[m,j],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[m,j],[g,b]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[m,q],[g,b]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[m,q],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[m,"OPPO"],[g,b]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[m,"Vivo"],[g,b]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[m,"Realme"],[g,b]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[m,L],[g,b]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[m,L],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[m,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[m,"LG"],[g,b]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[m,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[m,"Nokia"],[g,b]],[/(pixel c)\b/i],[p,[m,I],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[m,I],[g,b]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[m,U],[g,b]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[m,U],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[m,"OnePlus"],[g,b]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[m,T],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[m,T],[g,b]],[/(playbook);[-\w\),; ]+(rim)/i],[p,m,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[m,x],[g,b]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[m,O],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[m,O],[g,b]],[/(nexus 9)/i],[p,[m,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[p,/_/g," "],[g,b]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[m,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[m,"Meizu"],[g,b]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,p,[g,b]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,p,[g,_]],[/(surface duo)/i],[p,[m,N],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[m,"Fairphone"],[g,b]],[/(u304aa)/i],[p,[m,"AT&T"],[g,b]],[/\bsie-(\w*)/i],[p,[m,"Siemens"],[g,b]],[/\b(rct\w+) b/i],[p,[m,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[p,[m,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[p,[m,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[m,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[p,[m,"NuVision"],[g,_]],[/\b(k88) b/i],[p,[m,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[p,[m,"ZTE"],[g,b]],[/\b(gen\d{3}) b.+49h/i],[p,[m,"Swiss"],[g,b]],[/\b(zur\d{3}) b/i],[p,[m,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[p,[m,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],p,[g,_]],[/\b(ns-?\w{0,9}) b/i],[p,[m,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[m,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],p,[g,b]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],p,[g,b]],[/\b(ph-1) /i],[p,[m,"Essential"],[g,b]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[m,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[p,[m,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[p,[m,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[p,[m,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[m,p,[g,b]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[m,N],[g,b]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[m,B],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[m,B],[g,b]],[/smart-tv.+(samsung)/i],[m,[g,S]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[m,M],[g,S]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,S]],[/(apple) ?tv/i],[m,[p,P+" TV"],[g,S]],[/crkey/i],[[p,A+"cast"],[m,I],[g,S]],[/droid.+aft(\w)( bui|\))/i],[p,[m,T],[g,S]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[m,D],[g,S]],[/(bravia[\w ]+)( bui|\))/i],[p,[m,U],[g,S]],[/(mitv-\w{5}) bui/i],[p,[m,q],[g,S]],[/Hbbtv.*(technisat) (.*);/i],[m,p,[g,S]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,z],[p,z],[g,S]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,S]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,p,[g,v]],[/droid.+; (shield) bui/i],[p,[m,"Nvidia"],[g,v]],[/(playstation [345portablevi]+)/i],[p,[m,U],[g,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[m,N],[g,v]],[/((pebble))app/i],[m,p,[g,E]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[m,P],[g,E]],[/droid.+; (glass) \d/i],[p,[m,I],[g,E]],[/droid.+; (wt63?0{2,3})\)/i],[p,[m,B],[g,E]],[/(quest( 2| pro)?)/i],[p,[m,H],[g,E]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,k]],[/(aeobc)\b/i],[p,[m,T],[g,k]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[g,b]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,b]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[w,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[w,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,w],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[w,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,w],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[w,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[w,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[w,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,K],[w,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[w,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,w],[/\(bb(10);/i],[w,[f,x]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[w,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[w,[f,C+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[w,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[w,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[w,[f,A+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,W],w],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,w],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],w],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,w]]},ee=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==c&&a.navigator?a.navigator:o,i=e||(r&&r.userAgent?r.userAgent:""),s=r&&r.userAgentData?r.userAgentData:o,n=t?V(Q,t):Q,v=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[f]=o,t[w]=o,X.call(t,i,n.browser),t[d]=typeof(e=t[w])===h?e.replace(/[^\d\.]/g,"").split(".")[0]:o,v&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[y]=o,X.call(e,i,n.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[p]=o,e[g]=o,X.call(e,i,n.device),v&&!e[g]&&s&&s.mobile&&(e[g]=b),v&&"Macintosh"==e[p]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[f]=o,e[w]=o,X.call(e,i,n.engine),e},this.getOS=function(){var e={};return e[f]=o,e[w]=o,X.call(e,i,n.os),v&&!e[f]&&s&&"Unknown"!=s.platform&&(e[f]=s.platform.replace(/chrome os/i,W).replace(/macos/i,K)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===h&&e.length>350?z(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=F([f,w,d]),ee.CPU=F([y]),ee.DEVICE=F([p,m,g,v,b,S,_,E,k]),ee.ENGINE=ee.OS=F([f,w]),typeof n!==c?(s.exports&&(n=s.exports=ee),n.UAParser=ee):r.amdO?void 0!==(i=(function(){return ee}).call(t,r,t,e))&&(e.exports=i):typeof a!==c&&(a.UAParser=ee);var et=typeof a!==c&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;var r=n[e]={exports:{}},i=!0;try{s[e].call(r.exports,r,r.exports,a),i=!1}finally{i&&delete n[e]}return r.exports}a.ab="//";var o=a(226);e.exports=o})()},844:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return n}});let i=new(r(67)).AsyncLocalStorage;function s(e,t){let r=t.header(e,"next-test-proxy-port");if(r)return{url:t.url(e),proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function n(e,t,r){let n=s(e,t);return n?i.run(n,r):r()}function a(e,t){return i.getStore()||(e&&t?s(e,t):void 0)}},349:(e,t,r)=>{"use strict";var i=r(195).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return n}});let s=r(844),n={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:s,headers:n,body:a,cache:o,credentials:l,integrity:c,mode:u,redirect:h,referrer:d,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:r,method:s,headers:[...Array.from(n),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?i.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:c,mode:u,redirect:h,referrer:d,referrerPolicy:p}}}async function o(e,t){let r=(0,s.getTestReqInfo)(t,n);if(!r)return e(t);let{testData:o,proxyPort:l}=r,c=await a(o,t),u=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!u.ok)throw Error(`Proxy request failed: ${u.status}`);let h=await u.json(),{api:d}=h;switch(d){case"continue":return e(t);case"abort":case"unhandled":throw Error(`Proxy request aborted [${t.method} ${t.url}]`)}return function(e){let{status:t,headers:r,body:s}=e.response;return new Response(s?i.from(s,"base64"):null,{status:t,headers:new Headers(r)})}(h)}function l(e){return r.g.fetch=function(t,r){var i;return(null==r?void 0:null==(i=r.next)?void 0:i.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},74:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return n},wrapRequestHandler:function(){return a}});let i=r(844),s=r(349);function n(){return(0,s.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,i.withRequest)(t,s.reader,()=>e(t,r))}},163:e=>{"use strict";e.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}}},e=>{var t=e(e.s=102);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map