/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Select Component
 * Touch-friendly select component optimized for mobile devices
 */

import React, { useState, useRef, useEffect } from 'react';
import { ValidationError } from '@/types';
import styles from './MobileSelect.module.css';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  group?: string;
}

export interface MobileSelectProps {
  id?: string;
  name: string;
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  options: SelectOption[];
  placeholder?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  error?: ValidationError | string;
  success?: boolean;
  helpText?: string;
  className?: string;
  maxHeight?: number;
  'data-testid'?: string;
}

export const MobileSelect: React.FC<MobileSelectProps> = ({
  id,
  name,
  value,
  onChange,
  onBlur,
  onFocus,
  options,
  placeholder = 'Select an option...',
  label,
  required = false,
  disabled = false,
  multiple = false,
  searchable = false,
  clearable = false,
  error,
  success = false,
  helpText,
  className = '',
  maxHeight = 300,
  'data-testid': testId,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const optionsRef = useRef<HTMLDivElement>(null);

  const selectId = id || `mobile-select-${name}`;
  const hasError = !!error;
  const errorMessage = typeof error === 'string' ? error : error?.message;

  // Filter options based on search term
  const filteredOptions = searchable && searchTerm
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  // Group options if they have groups
  const groupedOptions = filteredOptions.reduce((groups, option) => {
    const group = option.group || 'default';
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(option);
    return groups;
  }, {} as Record<string, SelectOption[]>);

  const selectedOption = options.find(opt => opt.value === value);

  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        setFocusedIndex(-1);
      }
    };

    const handleTouchOutside = (event: TouchEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        setFocusedIndex(-1);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleTouchOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleTouchOutside);
    };
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  const handleToggle = () => {
    if (disabled) return;
    
    setIsOpen(!isOpen);
    if (!isOpen) {
      onFocus?.();
    } else {
      onBlur?.();
    }
  };

  const handleOptionSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setSearchTerm('');
    setFocusedIndex(-1);
    onBlur?.();
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange('');
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        if (!isOpen) {
          e.preventDefault();
          setIsOpen(true);
        } else if (focusedIndex >= 0) {
          e.preventDefault();
          handleOptionSelect(filteredOptions[focusedIndex].value);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        setFocusedIndex(-1);
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          setFocusedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          );
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (isOpen) {
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          );
        }
        break;
    }
  };

  const containerClasses = [
    styles.container,
    isOpen && styles.open,
    hasError && styles.error,
    success && styles.success,
    disabled && styles.disabled,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses} ref={selectRef} data-testid={testId}>
      {label && (
        <label htmlFor={selectId} className={styles.label}>
          {label}
          {required && <span className={styles.required} aria-label="required">*</span>}
        </label>
      )}

      <div
        id={selectId}
        className={styles.selectWrapper}
        onClick={handleToggle}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-invalid={hasError}
        aria-describedby={
          [
            hasError && `${selectId}-error`,
            helpText && `${selectId}-help`
          ].filter(Boolean).join(' ') || undefined
        }
      >
        <div className={styles.selectedValue}>
          {selectedOption ? selectedOption.label : placeholder}
        </div>

        <div className={styles.actions}>
          {clearable && value && !disabled && (
            <button
              type="button"
              className={styles.clearButton}
              onClick={handleClear}
              aria-label="Clear selection"
            >
              ✕
            </button>
          )}
          <div className={styles.chevron}>
            {isOpen ? '▲' : '▼'}
          </div>
        </div>
      </div>

      {isOpen && (
        <div className={styles.dropdown} style={{ maxHeight }}>
          {searchable && (
            <div className={styles.searchWrapper}>
              <input
                ref={searchInputRef}
                type="text"
                className={styles.searchInput}
                placeholder="Search options..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          )}

          <div className={styles.optionsList} ref={optionsRef} role="listbox">
            {Object.keys(groupedOptions).length === 0 ? (
              <div className={styles.noOptions}>No options found</div>
            ) : (
              Object.entries(groupedOptions).map(([groupName, groupOptions]) => (
                <div key={groupName}>
                  {groupName !== 'default' && (
                    <div className={styles.groupLabel}>{groupName}</div>
                  )}
                  {groupOptions.map((option, index) => {
                    const globalIndex = filteredOptions.indexOf(option);
                    return (
                      <div
                        key={option.value}
                        className={[
                          styles.option,
                          option.value === value && styles.selected,
                          globalIndex === focusedIndex && styles.focused,
                          option.disabled && styles.optionDisabled
                        ].filter(Boolean).join(' ')}
                        onClick={() => !option.disabled && handleOptionSelect(option.value)}
                        role="option"
                        aria-selected={option.value === value}
                        aria-disabled={option.disabled}
                      >
                        {option.label}
                        {option.value === value && (
                          <span className={styles.checkmark}>✓</span>
                        )}
                      </div>
                    );
                  })}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {hasError && (
        <div 
          id={`${selectId}-error`}
          className={styles.errorMessage}
          role="alert"
          aria-live="polite"
        >
          {errorMessage}
        </div>
      )}

      {helpText && !hasError && (
        <div 
          id={`${selectId}-help`}
          className={styles.helpText}
        >
          {helpText}
        </div>
      )}
    </div>
  );
};

export default MobileSelect;
