{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/login", "destination": "/admin/login", "statusCode": 307, "regex": "^(?!/_next)/login(?:/)?$"}, {"source": "/shop", "destination": "/", "statusCode": 307, "regex": "^(?!/_next)/shop(?:/)?$"}, {"source": "/book-online", "destination": "/admin/bookings", "statusCode": 307, "regex": "^(?!/_next)/book-online(?:/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(self), microphone=(self), geolocation=(self), payment=(self), notifications=(self), vibrate=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.squareup.com https://cdn.onesignal.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co https://js.squareup.com https://api.onesignal.com wss://*.supabase.co; frame-src 'self' https://js.squareup.com; worker-src 'self'; manifest-src 'self';"}, {"key": "X-Admin-Portal", "value": "true"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/manifest\\.json(?:/)?$"}, {"source": "/sw.js", "headers": [{"key": "Content-Type", "value": "application/javascript"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}], "regex": "^/sw\\.js(?:/)?$"}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, private"}, {"key": "X-Admin-API", "value": "true"}], "regex": "^/api(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/admin/artists/[id]/portfolio", "regex": "^/admin/artists/([^/]+?)/portfolio(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/artists/(?<nxtPid>[^/]+?)/portfolio(?:/)?$"}, {"page": "/admin/bookings/[id]", "regex": "^/admin/bookings/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/bookings/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/customers/[id]", "regex": "^/admin/customers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/customers/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/services/[id]", "regex": "^/admin/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/services/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/services/[id]/edit", "regex": "^/admin/services/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/services/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/api/admin/artists/[id]/commissions", "regex": "^/api/admin/artists/([^/]+?)/commissions(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/artists/(?<nxtPid>[^/]+?)/commissions(?:/)?$"}, {"page": "/api/admin/artists/[id]/portfolio", "regex": "^/api/admin/artists/([^/]+?)/portfolio(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/artists/(?<nxtPid>[^/]+?)/portfolio(?:/)?$"}, {"page": "/api/admin/bookings/[id]", "regex": "^/api/admin/bookings/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/bookings/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/customers/[id]", "regex": "^/api/admin/customers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/customers/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/customers/[id]/bookings", "regex": "^/api/admin/customers/([^/]+?)/bookings(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/customers/(?<nxtPid>[^/]+?)/bookings(?:/)?$"}, {"page": "/api/admin/email-templates/[id]", "regex": "^/api/admin/email\\-templates/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/email\\-templates/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/purchase-orders/[id]", "regex": "^/api/admin/purchase\\-orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/purchase\\-orders/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/purchase-orders/[id]/receive", "regex": "^/api/admin/purchase\\-orders/([^/]+?)/receive(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/purchase\\-orders/(?<nxtPid>[^/]+?)/receive(?:/)?$"}, {"page": "/api/admin/services/[id]", "regex": "^/api/admin/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/services/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/services/[id]/tiers", "regex": "^/api/admin/services/([^/]+?)/tiers(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/services/(?<nxtPid>[^/]+?)/tiers(?:/)?$"}, {"page": "/api/admin/suppliers/[id]", "regex": "^/api/admin/suppliers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/suppliers/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/admin/artists", "regex": "^/admin/artists(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/artists(?:/)?$"}, {"page": "/admin/artists/portfolio", "regex": "^/admin/artists/portfolio(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/artists/portfolio(?:/)?$"}, {"page": "/admin/bookings", "regex": "^/admin/bookings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/bookings(?:/)?$"}, {"page": "/admin/bookings/new", "regex": "^/admin/bookings/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/bookings/new(?:/)?$"}, {"page": "/admin/communications", "regex": "^/admin/communications(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/communications(?:/)?$"}, {"page": "/admin/customers", "regex": "^/admin/customers(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/customers(?:/)?$"}, {"page": "/admin/customers/new", "regex": "^/admin/customers/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/customers/new(?:/)?$"}, {"page": "/admin/customers-new", "regex": "^/admin/customers\\-new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/customers\\-new(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/email-templates", "regex": "^/admin/email\\-templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/email\\-templates(?:/)?$"}, {"page": "/admin/feedback", "regex": "^/admin/feedback(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/feedback(?:/)?$"}, {"page": "/admin/inventory", "regex": "^/admin/inventory(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/inventory(?:/)?$"}, {"page": "/admin/login", "regex": "^/admin/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/login(?:/)?$"}, {"page": "/admin/mobile-debug", "regex": "^/admin/mobile\\-debug(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/mobile\\-debug(?:/)?$"}, {"page": "/admin/mobile-test", "regex": "^/admin/mobile\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/mobile\\-test(?:/)?$"}, {"page": "/admin/notifications", "regex": "^/admin/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/notifications(?:/)?$"}, {"page": "/admin/pos", "regex": "^/admin/pos(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/pos(?:/)?$"}, {"page": "/admin/products", "regex": "^/admin/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/products(?:/)?$"}, {"page": "/admin/purchase-orders", "regex": "^/admin/purchase\\-orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/purchase\\-orders(?:/)?$"}, {"page": "/admin/purchase-orders/new", "regex": "^/admin/purchase\\-orders/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/purchase\\-orders/new(?:/)?$"}, {"page": "/admin/receipts", "regex": "^/admin/receipts(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/receipts(?:/)?$"}, {"page": "/admin/reports", "regex": "^/admin/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/reports(?:/)?$"}, {"page": "/admin/services", "regex": "^/admin/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/services(?:/)?$"}, {"page": "/admin/services/new", "regex": "^/admin/services/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/services/new(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/admin/sms-templates", "regex": "^/admin/sms\\-templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/sms\\-templates(?:/)?$"}, {"page": "/admin/staff", "regex": "^/admin/staff(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/staff(?:/)?$"}, {"page": "/admin/staff/onboarding", "regex": "^/admin/staff/onboarding(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/staff/onboarding(?:/)?$"}, {"page": "/admin/staff/performance", "regex": "^/admin/staff/performance(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/staff/performance(?:/)?$"}, {"page": "/admin/staff/training", "regex": "^/admin/staff/training(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/staff/training(?:/)?$"}, {"page": "/admin/suppliers", "regex": "^/admin/suppliers(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/suppliers(?:/)?$"}, {"page": "/admin/suppliers/new", "regex": "^/admin/suppliers/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/suppliers/new(?:/)?$"}, {"page": "/admin/tips", "regex": "^/admin/tips(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/tips(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}