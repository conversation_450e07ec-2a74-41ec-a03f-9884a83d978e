const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testDatabaseConnection() {
  try {
    console.log('🔗 Testing Supabase database connection...\n');
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Missing Supabase configuration');
      return;
    }
    
    console.log('📍 Supabase URL:', supabaseUrl);
    console.log('🔑 Service key exists:', supabaseKey ? 'Yes' : 'No');
    
    const supabase = createClient(supabaseUrl, supabaseKey);
      // Test basic connection
    console.log('\n1. Testing basic connection...');
    const { data: test, error: testError } = await supabase
      .from('admin_users')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.log('❌ Connection failed:', testError.message);
      console.log('Full error:', testError);
      return;
    }
    
    console.log('✅ Connection successful!');
    
    // Test available tables
    console.log('\n2. Checking available tables...');
    const tables = [
      'admin_users',
      'customers', 
      'bookings',
      'services',
      'artists',
      'inventory',
      'user_profiles'
    ];
    
    for (const table of tables) {
      try {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          console.log(`   ❌ ${table}: ${error.message}`);
        } else {
          console.log(`   ✅ ${table}: ${count || 0} records`);
        }
      } catch (err) {
        console.log(`   ❌ ${table}: ${err.message}`);
      }
    }
    
    // Test admin users specifically
    console.log('\n3. Testing admin users table...');
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('id, email, role, first_name, last_name, is_active')
      .limit(5);
    
    if (adminError) {
      console.log('❌ Admin users query failed:', adminError.message);
    } else {
      console.log('✅ Admin users found:', adminUsers.length);
      adminUsers.forEach(user => {
        console.log(`   - ${user.email} (${user.role}) - ${user.is_active ? 'Active' : 'Inactive'}`);
      });
    }
    
    // Test bookings table
    console.log('\n4. Testing bookings table...');
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('id, status, created_at, total_amount')
      .limit(5);
    
    if (bookingsError) {
      console.log('❌ Bookings query failed:', bookingsError.message);
    } else {
      console.log('✅ Recent bookings found:', bookings.length);
      if (bookings.length === 0) {
        console.log('   📝 No bookings in database (expected for new setup)');
      }
    }
    
    // Test customers table
    console.log('\n5. Testing customers table...');
    const { data: customers, error: customersError } = await supabase
      .from('customers')
      .select('id, first_name, last_name, email')
      .limit(5);
    
    if (customersError) {
      console.log('❌ Customers query failed:', customersError.message);
    } else {
      console.log('✅ Customers found:', customers.length);
      if (customers.length === 0) {
        console.log('   📝 No customers in database (expected for new setup)');
      }
    }
    
    console.log('\n🎉 Database connection test complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testDatabaseConnection();
