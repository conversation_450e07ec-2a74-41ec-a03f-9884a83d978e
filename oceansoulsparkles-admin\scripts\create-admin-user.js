const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createAdminUser() {
  try {
    console.log('Creating admin user...');
    
    const email = '<EMAIL>';
    const password = 'admin123';
    
    // Hash the password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('admin_users')
      .select('id, email')
      .eq('email', email)
      .single();
    
    if (existingUser) {
      console.log('Admin user already exists:', existingUser.email);
      
      // Update password if needed
      const { error: updateError } = await supabase
        .from('admin_users')
        .update({ 
          password_hash: passwordHash,
          is_active: true 
        })
        .eq('email', email);
      
      if (updateError) {
        console.error('Error updating user:', updateError);
      } else {
        console.log('Admin user password updated successfully');
      }
      return;
    }
    
    // Create new admin user
    const { data, error } = await supabase
      .from('admin_users')
      .insert([
        {
          email: email,
          password_hash: passwordHash,
          role: 'Admin',
          first_name: 'Admin',
          last_name: 'User',
          is_active: true,
          mfa_enabled: false,
          permissions: ['all'],
          created_at: new Date().toISOString(),
          last_activity: Date.now()
        }
      ])
      .select();
    
    if (error) {
      console.error('Error creating admin user:', error);
    } else {
      console.log('Admin user created successfully:', data[0]);
    }
    
  } catch (error) {
    console.error('Script error:', error);
  }
}

createAdminUser();
