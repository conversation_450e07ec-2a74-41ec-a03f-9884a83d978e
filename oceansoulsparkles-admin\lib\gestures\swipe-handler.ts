/**
 * Ocean Soul Sparkles Admin - Touch Gesture Handler
 * Provides swipe gestures and mobile interactions for admin interface
 */

import { useRef, useEffect } from 'react';

interface SwipeConfig {
  threshold: number;
  restraint: number;
  allowedTime: number;
  direction: 'left' | 'right' | 'up' | 'down' | 'all';
}

interface TouchPoint {
  x: number;
  y: number;
  time: number;
}

interface SwipeEvent {
  direction: 'left' | 'right' | 'up' | 'down';
  distance: number;
  duration: number;
  startPoint: TouchPoint;
  endPoint: TouchPoint;
  element: HTMLElement;
}

type SwipeCallback = (event: SwipeEvent) => void;

class SwipeHandler {
  private element: HTMLElement;
  private config: SwipeConfig;
  private callback: SwipeCallback;
  private startPoint: TouchPoint | null = null;
  private isTracking: boolean = false;

  constructor(
    element: HTMLElement,
    callback: SwipeCallback,
    config: Partial<SwipeConfig> = {}
  ) {
    this.element = element;
    this.callback = callback;
    this.config = {
      threshold: 50, // Minimum distance for swipe
      restraint: 100, // Maximum perpendicular distance
      allowedTime: 300, // Maximum time for swipe
      direction: 'all',
      ...config
    };

    this.init();
  }

  private init(): void {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
    this.element.addEventListener('touchcancel', this.handleTouchCancel.bind(this), { passive: true });
  }

  private handleTouchStart(event: TouchEvent): void {
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.startPoint = {
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now()
      };
      this.isTracking = true;
    }
  }

  private handleTouchEnd(event: TouchEvent): void {
    if (!this.isTracking || !this.startPoint || event.changedTouches.length !== 1) {
      return;
    }

    const touch = event.changedTouches[0];
    const endPoint: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };

    this.processSwipe(this.startPoint, endPoint);
    this.reset();
  }

  private handleTouchCancel(): void {
    this.reset();
  }

  private processSwipe(start: TouchPoint, end: TouchPoint): void {
    const duration = end.time - start.time;
    const distanceX = end.x - start.x;
    const distanceY = end.y - start.y;
    const absDistanceX = Math.abs(distanceX);
    const absDistanceY = Math.abs(distanceY);

    // Check if swipe is within allowed time
    if (duration > this.config.allowedTime) {
      return;
    }

    // Determine primary direction and check thresholds
    let direction: 'left' | 'right' | 'up' | 'down' | null = null;
    let distance = 0;

    if (absDistanceX >= this.config.threshold && absDistanceX > absDistanceY) {
      // Horizontal swipe
      if (absDistanceY <= this.config.restraint) {
        direction = distanceX > 0 ? 'right' : 'left';
        distance = absDistanceX;
      }
    } else if (absDistanceY >= this.config.threshold && absDistanceY > absDistanceX) {
      // Vertical swipe
      if (absDistanceX <= this.config.restraint) {
        direction = distanceY > 0 ? 'down' : 'up';
        distance = absDistanceY;
      }
    }

    // Check if direction is allowed
    if (direction && (this.config.direction === 'all' || this.config.direction === direction)) {
      const swipeEvent: SwipeEvent = {
        direction,
        distance,
        duration,
        startPoint: start,
        endPoint: end,
        element: this.element
      };

      this.callback(swipeEvent);
    }
  }

  private reset(): void {
    this.startPoint = null;
    this.isTracking = false;
  }

  public destroy(): void {
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    this.element.removeEventListener('touchcancel', this.handleTouchCancel.bind(this));
  }
}

/**
 * React hook for swipe gestures
 */
export function useSwipeGesture(
  callback: SwipeCallback,
  config?: Partial<SwipeConfig>
) {
  const elementRef = useRef<HTMLElement>(null);
  const handlerRef = useRef<SwipeHandler | null>(null);

  useEffect(() => {
    if (elementRef.current) {
      handlerRef.current = new SwipeHandler(elementRef.current, callback, config);
    }

    return () => {
      if (handlerRef.current) {
        handlerRef.current.destroy();
      }
    };
  }, [callback, config]);

  return elementRef;
}

/**
 * Utility functions for common swipe actions
 */
export class SwipeActions {
  /**
   * Swipe to delete functionality
   */
  static swipeToDelete(
    element: HTMLElement,
    onDelete: () => void,
    options: {
      threshold?: number;
      confirmationRequired?: boolean;
      animationDuration?: number;
    } = {}
  ): SwipeHandler {
    const { threshold = 100, confirmationRequired = true, animationDuration = 300 } = options;

    return new SwipeHandler(
      element,
      (event) => {
        if (event.direction === 'left' && event.distance >= threshold) {
          if (confirmationRequired) {
            this.showDeleteConfirmation(element, onDelete, animationDuration);
          } else {
            this.animateDelete(element, onDelete, animationDuration);
          }
        }
      },
      { direction: 'left', threshold }
    );
  }

  /**
   * Swipe navigation functionality
   */
  static swipeNavigation(
    element: HTMLElement,
    onSwipeLeft: () => void,
    onSwipeRight: () => void,
    threshold: number = 80
  ): SwipeHandler {
    return new SwipeHandler(
      element,
      (event) => {
        if (event.direction === 'left') {
          onSwipeLeft();
        } else if (event.direction === 'right') {
          onSwipeRight();
        }
      },
      { direction: 'all', threshold }
    );
  }

  /**
   * Pull to refresh functionality
   */
  static pullToRefresh(
    element: HTMLElement,
    onRefresh: () => Promise<void>,
    threshold: number = 100
  ): SwipeHandler {
    let isRefreshing = false;

    return new SwipeHandler(
      element,
      async (event) => {
        if (event.direction === 'down' && event.distance >= threshold && !isRefreshing) {
          isRefreshing = true;
          this.showRefreshIndicator(element);
          
          try {
            await onRefresh();
          } finally {
            this.hideRefreshIndicator(element);
            isRefreshing = false;
          }
        }
      },
      { direction: 'down', threshold }
    );
  }

  private static showDeleteConfirmation(
    element: HTMLElement,
    onDelete: () => void,
    animationDuration: number
  ): void {
    // Add delete confirmation overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background: #f44336;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      z-index: 10;
      animation: slideInLeft ${animationDuration}ms ease-out;
    `;
    overlay.textContent = 'Tap to Delete';
    overlay.onclick = () => {
      this.animateDelete(element, onDelete, animationDuration);
    };

    element.style.position = 'relative';
    element.appendChild(overlay);

    // Auto-hide after 3 seconds
    setTimeout(() => {
      if (overlay.parentNode) {
        overlay.remove();
      }
    }, 3000);
  }

  private static animateDelete(
    element: HTMLElement,
    onDelete: () => void,
    animationDuration: number
  ): void {
    element.style.transition = `transform ${animationDuration}ms ease-out, opacity ${animationDuration}ms ease-out`;
    element.style.transform = 'translateX(-100%)';
    element.style.opacity = '0';

    setTimeout(() => {
      onDelete();
    }, animationDuration);
  }

  private static showRefreshIndicator(element: HTMLElement): void {
    const indicator = document.createElement('div');
    indicator.className = 'refresh-indicator';
    indicator.style.cssText = `
      position: absolute;
      top: -40px;
      left: 50%;
      transform: translateX(-50%);
      background: #4CAF50;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      z-index: 1000;
      animation: fadeIn 300ms ease-out;
    `;
    indicator.textContent = '🔄 Refreshing...';

    element.style.position = 'relative';
    element.appendChild(indicator);
  }

  private static hideRefreshIndicator(element: HTMLElement): void {
    const indicator = element.querySelector('.refresh-indicator');
    if (indicator) {
      indicator.remove();
    }
  }
}

/**
 * Haptic feedback utility
 */
export class HapticFeedback {
  /**
   * Trigger haptic feedback if supported
   */
  static vibrate(pattern: number | number[] = 100): void {
    if ('vibrate' in navigator) {
      navigator.vibrate(pattern);
    }
  }

  /**
   * Light haptic feedback for button taps
   */
  static light(): void {
    this.vibrate(50);
  }

  /**
   * Medium haptic feedback for selections
   */
  static medium(): void {
    this.vibrate(100);
  }

  /**
   * Heavy haptic feedback for confirmations
   */
  static heavy(): void {
    this.vibrate([100, 50, 100]);
  }

  /**
   * Error haptic feedback
   */
  static error(): void {
    this.vibrate([100, 50, 100, 50, 100]);
  }

  /**
   * Success haptic feedback
   */
  static success(): void {
    this.vibrate([50, 25, 50]);
  }
}

export default SwipeHandler;
