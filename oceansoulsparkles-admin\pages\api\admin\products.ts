import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET' && req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;

    // Only admin and dev can manage products
    if (user.role !== 'Admin' && user.role !== 'DEV') {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    if (req.method === 'POST') {
      // Create new product
      const {
        name,
        description,
        short_description,
        sku,
        price,
        sale_price,
        cost_price,
        category_name,
        image_url,
        gallery_images,
        is_active,
        stock,
        low_stock_threshold,
        reorder_point,
        reorder_quantity,
        max_stock_level,
        barcode,
        weight,
        dimensions,
        meta_title,
        meta_description,
        featured,
        abc_classification,
        seasonal_item,
        perishable,
        shelf_life_days,
        lead_time_days,
        minimum_order_quantity
      } = req.body;

      // Validate required fields
      if (!name) {
        return res.status(400).json({ error: 'Product name is required' });
      }

      const { data: product, error } = await supabase
        .from('products')
        .insert([{
          name,
          description,
          short_description,
          sku,
          price: price ? parseFloat(price) : null,
          sale_price: sale_price ? parseFloat(sale_price) : null,
          cost_price: cost_price ? parseFloat(cost_price) : null,
          category_name,
          image_url,
          gallery_images: gallery_images || [],
          is_active: is_active !== false,
          stock: stock ? parseInt(stock) : 0,
          low_stock_threshold: low_stock_threshold ? parseInt(low_stock_threshold) : 5,
          reorder_point: reorder_point ? parseInt(reorder_point) : null,
          reorder_quantity: reorder_quantity ? parseInt(reorder_quantity) : null,
          max_stock_level: max_stock_level ? parseInt(max_stock_level) : null,
          barcode,
          weight: weight ? parseFloat(weight) : null,
          dimensions: dimensions || null,
          meta_title,
          meta_description,
          featured: featured || false,
          abc_classification,
          seasonal_item: seasonal_item || false,
          perishable: perishable || false,
          shelf_life_days: shelf_life_days ? parseInt(shelf_life_days) : null,
          lead_time_days: lead_time_days ? parseInt(lead_time_days) : null,
          minimum_order_quantity: minimum_order_quantity ? parseInt(minimum_order_quantity) : null,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        console.error('Product creation error:', error);
        return res.status(500).json({ error: 'Failed to create product' });
      }

      return res.status(201).json({
        product: product
      });
    }

    // GET products from database
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        description,
        short_description,
        sku,
        price,
        sale_price,
        cost_price,
        category_name,
        image_url,
        gallery_images,
        is_active,
        stock,
        low_stock_threshold,
        reorder_point,
        reorder_quantity,
        max_stock_level,
        barcode,
        weight,
        dimensions,
        meta_title,
        meta_description,
        featured,
        abc_classification,
        seasonal_item,
        perishable,
        shelf_life_days,
        lead_time_days,
        minimum_order_quantity,
        status,
        created_at,
        updated_at
      `)
      .order('name', { ascending: true });

    if (error) {
      console.error('Products query error:', error);
      return res.status(500).json({ error: 'Failed to fetch products' });
    }

    // Transform data to include calculated fields
    const transformedProducts = (products || []).map(product => ({
      ...product,
      stock_value: (product.price || 0) * (product.stock || 0),
      profit_margin: product.price && product.cost_price 
        ? ((product.price - product.cost_price) / product.price * 100).toFixed(2)
        : null,
      is_low_stock: (product.stock || 0) <= (product.low_stock_threshold || 5),
      is_out_of_stock: (product.stock || 0) <= 0
    }));

    return res.status(200).json({
      products: transformedProducts,
      total: transformedProducts.length,
      stats: {
        total_products: transformedProducts.length,
        active_products: transformedProducts.filter(p => p.is_active).length,
        low_stock_products: transformedProducts.filter(p => p.is_low_stock).length,
        out_of_stock_products: transformedProducts.filter(p => p.is_out_of_stock).length,
        total_stock_value: transformedProducts.reduce((sum, p) => sum + (p.stock_value || 0), 0),
        featured_products: transformedProducts.filter(p => p.featured).length
      }
    });

  } catch (error) {
    console.error('Products API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
