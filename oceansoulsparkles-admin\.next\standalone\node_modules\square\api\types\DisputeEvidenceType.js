"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DisputeEvidenceType = void 0;
exports.DisputeEvidenceType = {
    GenericEvidence: "GENERIC_EVIDENCE",
    OnlineOrAppAccessLog: "ONLINE_OR_APP_ACCESS_LOG",
    AuthorizationDocumentation: "AUTHORIZATION_DOCUMENTATION",
    CancellationOrRefundDocumentation: "CANCELLATION_OR_REFUND_DOCUMENTATION",
    CardholderCommunication: "CARDHOLDER_COMMUNICATION",
    CardholderInformation: "CARDHOLDER_INFORMATION",
    PurchaseAcknowledgement: "PURCHASE_ACKNOWLEDGEMENT",
    DuplicateChargeDocumentation: "DUPLICATE_CHARGE_DOCUMENTATION",
    ProductOrServiceDescription: "PRODUCT_OR_SERVICE_DESCRIPTION",
    Receipt: "RECEIPT",
    ServiceReceivedDocumentation: "SERVICE_RECEIVED_DOCUMENTATION",
    ProofOfDeliveryDocumentation: "PROOF_OF_DELIVERY_DOCUMENTATION",
    RelatedTransactionDocumentation: "RELATED_TRANSACTION_DOCUMENTATION",
    RebuttalExplanation: "REBUTTAL_EXPLANATION",
    TrackingNumber: "TRACKING_NUMBER",
};
