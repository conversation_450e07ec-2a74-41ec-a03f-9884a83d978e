(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{8090:function(r,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/staff/training",function(){return n(9251)}])},9251:function(r,e,n){"use strict";n.r(e),n.d(e,{default:function(){return f}}),function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}(),function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}();var t=n(9008),o=n.n(t),a=n(1163),i=n(6026),c=n(99),d=n(4288),s=n.n(d);let l={not_started:"#6b7280",in_progress:"#f59e0b",completed:"#10b981",failed:"#ef4444"},u={not_started:"Not Started",in_progress:"In Progress",completed:"Completed",failed:"Failed"},O={safety:"Health & Safety",technical:"Technical Skills",customer_service:"Customer Service",compliance:"Compliance"};function f(){let{user:r}=(0,i.a)(),e=(0,a.useRouter)(),{staff_id:n}=e.query,[t,d]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(!0),[f,_]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(null),[m,N]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())([]),[h,E]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())([]),[D,g]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())({total:0,completed:0,required:0,completedRequired:0,completionPercentage:0,requiredCompletionPercentage:0}),[U,j]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(null),[T,v]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())("all"),[C,p]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(!1);Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(()=>{n&&(b(),w(),F())},[n]);let b=async()=>{try{d(!0);let r=await fetch("/api/admin/staff/training?staff_id=".concat(n),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}});if(r.ok){let e=await r.json();N(e.progress||[]),g(e.statistics||{})}else _("Failed to load training data")}catch(r){console.error("Error loading training data:",r),_("Failed to load training data")}finally{d(!1)}},w=async()=>{try{let r=await fetch("/api/admin/staff?id=".concat(n),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}});if(r.ok){let e=await r.json();j(e.staff)}}catch(r){console.error("Error loading staff info:",r)}},F=async()=>{try{let r=await fetch("/api/admin/staff/training",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}});if(r.ok){let e=await r.json();E(e.modules||[])}}catch(r){console.error("Error loading available modules:",r)}},M=async()=>{try{(await fetch("/api/admin/staff/training",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))},body:JSON.stringify({action:"assign_all_required",staff_id:n})})).ok?await b():_("Failed to assign required modules")}catch(r){console.error("Error assigning required modules:",r),_("Failed to assign required modules")}},L=async r=>{try{(await fetch("/api/admin/staff/training",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))},body:JSON.stringify({action:"assign_module",staff_id:n,module_id:r})})).ok?(await b(),p(!1)):_("Failed to assign module")}catch(r){console.error("Error assigning module:",r),_("Failed to assign module")}},x=async(r,e,t)=>{try{(await fetch("/api/admin/staff/training",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))},body:JSON.stringify({action:e,staff_id:n,module_id:r,score:t})})).ok?await b():_("Failed to ".concat(e.replace("_"," ")))}catch(r){console.error("Error ".concat(e,":"),r),_("Failed to ".concat(e.replace("_"," ")))}},S=r=>new Date(r).toLocaleDateString("en-AU",{year:"numeric",month:"short",day:"numeric"}),y=r=>r>=90?"#10b981":r>=70?"#f59e0b":"#ef4444",B="all"===T?m:m.filter(r=>r.staff_training_modules.category===T),k=m.map(r=>r.staff_training_modules.id),q=h.filter(r=>!k.includes(r.id));return t?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(c.Z,{children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().loadingContainer,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().loadingSpinner}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Loading training data..."})]})}):Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(c.Z,{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(o(),{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("title",{children:"Staff Training | Ocean Soul Sparkles Admin"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("meta",{name:"description",content:"Manage staff training progress and modules"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().trainingContainer,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("header",{className:s().header,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().headerLeft,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h1",{className:s().title,children:"Staff Training"}),U&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{className:s().subtitle,children:[U.firstName," ",U.lastName," - ",U.role]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().headerActions,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:M,className:s().assignBtn,children:"Assign Required Modules"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:()=>p(!0),className:s().assignBtn,children:"+ Assign Module"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:()=>e.back(),className:s().backBtn,children:"← Back to Staff"})]})]}),f&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().errorMessage,children:[f,Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:()=>_(null),className:s().closeError,children:"\xd7"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressSection,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:"Overall Progress"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressBar,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressFill,style:{width:"".concat(D.completionPercentage,"%"),backgroundColor:y(D.completionPercentage)}})}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressStats,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:[D.completed," of ",D.total," completed"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:[D.completionPercentage,"%"]})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:"Required Modules"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressBar,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressFill,style:{width:"".concat(D.requiredCompletionPercentage,"%"),backgroundColor:y(D.requiredCompletionPercentage)}})}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressStats,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:[D.completedRequired," of ",D.required," completed"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:[D.requiredCompletionPercentage,"%"]})]})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().filters,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().filterGroup,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("label",{htmlFor:"categoryFilter",children:"Filter by Category:"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("select",{id:"categoryFilter",value:T,onChange:r=>v(r.target.value),className:s().filterSelect,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:"all",children:"All Categories"}),Object.entries(O).map(r=>{let[e,n]=r;return Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{value:e,children:n},e)})]})]})}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().trainingContent,children:0===B.length?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().emptyState,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().emptyIcon,children:"\uD83C\uDF93"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:"No Training Modules Assigned"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"This staff member doesn't have any training modules assigned yet."}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:M,className:s().assignBtn,children:"Assign Required Modules"})]}):Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().modulesList,children:B.map(r=>Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().moduleCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().cardHeader,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().moduleInfo,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{className:s().moduleName,children:[r.staff_training_modules.name,r.staff_training_modules.is_required&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:s().requiredBadge,children:"Required"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{className:s().moduleDescription,children:r.staff_training_modules.description}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().moduleDetails,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:["Category: ",O[r.staff_training_modules.category]]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:["Duration: ",r.staff_training_modules.duration_minutes," minutes"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:["Passing Score: ",r.staff_training_modules.passing_score,"%"]})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().statusSection,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:s().statusBadge,style:{backgroundColor:l[r.status]},children:u[r.status]}),r.score&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().scoreDisplay,children:["Score: ",r.score,"%"]})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().cardBody,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().progressInfo,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:["Assigned: ",S(r.assigned_at)]}),r.started_at&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:["Started: ",S(r.started_at)]}),r.completed_at&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:["Completed: ",S(r.completed_at)]}),r.attempts>0&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:["Attempts: ",r.attempts]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().cardActions,children:["not_started"===r.status&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:()=>x(r.staff_training_modules.id,"start_training"),className:s().startBtn,children:"Start Training"}),("in_progress"===r.status||"failed"===r.status)&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:()=>{let e=prompt("Enter completion score (0-100):");e&&!isNaN(Number(e))&&x(r.staff_training_modules.id,"complete_training",Number(e))},className:s().completeBtn,children:"Complete Training"})]})]}),r.notes&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().moduleNotes,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{children:"Notes:"})," ",r.notes]})]},r.id))})}),C&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().modalOverlay,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().modal,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().modalHeader,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:"Assign Training Module"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:()=>p(!1),className:s().closeModal,children:"\xd7"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().modalBody,children:0===q.length?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"All available modules are already assigned to this staff member."}):Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().moduleOptions,children:q.map(r=>Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().moduleOption,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().optionInfo,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h4",{children:r.name}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:r.description}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:s().optionDetails,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:O[r.category]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{children:[r.duration_minutes," min"]}),r.is_required&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:s().requiredBadge,children:"Required"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:()=>L(r.id),className:s().assignOptionBtn,children:"Assign"})]},r.id))})})]})})]})]})}},4288:function(r){r.exports={trainingContainer:"StaffTraining_trainingContainer__aK4dZ",header:"StaffTraining_header__v6Eiz",headerLeft:"StaffTraining_headerLeft__3DA82",title:"StaffTraining_title__fF4c9",subtitle:"StaffTraining_subtitle__znx_O",headerActions:"StaffTraining_headerActions__aok8w",assignBtn:"StaffTraining_assignBtn__OEAqW",backBtn:"StaffTraining_backBtn__ioqCz",errorMessage:"StaffTraining_errorMessage__Sun1U",closeError:"StaffTraining_closeError__g_Bs3",progressSection:"StaffTraining_progressSection__nQxw6",progressCard:"StaffTraining_progressCard__Fzccp",progressBar:"StaffTraining_progressBar__CFhqi",progressFill:"StaffTraining_progressFill__YM0Pc",progressStats:"StaffTraining_progressStats__Ge9DG",filters:"StaffTraining_filters__h0KWy",filterGroup:"StaffTraining_filterGroup__qNz3z",filterSelect:"StaffTraining_filterSelect__NrFjw",emptyState:"StaffTraining_emptyState__UnM4B",emptyIcon:"StaffTraining_emptyIcon__8HntE",modulesList:"StaffTraining_modulesList__FlR2b",moduleCard:"StaffTraining_moduleCard__umB_G",cardHeader:"StaffTraining_cardHeader__uOpH0",moduleInfo:"StaffTraining_moduleInfo__TtgBi",moduleName:"StaffTraining_moduleName__T6Vhe",requiredBadge:"StaffTraining_requiredBadge__O7R__",moduleDescription:"StaffTraining_moduleDescription__Q7yGq",moduleDetails:"StaffTraining_moduleDetails__V9L2K",statusSection:"StaffTraining_statusSection__nuYau",statusBadge:"StaffTraining_statusBadge__0QFny",scoreDisplay:"StaffTraining_scoreDisplay__JP9Bc",cardBody:"StaffTraining_cardBody__Tndaa",progressInfo:"StaffTraining_progressInfo__asukL",cardActions:"StaffTraining_cardActions__DgMVa",startBtn:"StaffTraining_startBtn__xUDMg",completeBtn:"StaffTraining_completeBtn__AkIeB",moduleNotes:"StaffTraining_moduleNotes__W4KWr",loadingContainer:"StaffTraining_loadingContainer__hXS41",loadingSpinner:"StaffTraining_loadingSpinner__CeQE3",spin:"StaffTraining_spin__VyBIJ",modalOverlay:"StaffTraining_modalOverlay__vOQfg",modal:"StaffTraining_modal__m1XEa",modalHeader:"StaffTraining_modalHeader__W4_yT",closeModal:"StaffTraining_closeModal__Pzc88",modalBody:"StaffTraining_modalBody__2G5_l"}}},function(r){r.O(0,[736,592,888,179],function(){return r(r.s=8090)}),_N_E=r.O()}]);