(()=>{var e={};e.id=2264,e.ids=[2264,2888,660],e.modules={1323:(e,s)=>{"use strict";Object.defineProperty(s,"l",{enumerable:!0,get:function(){return function e(s,t){return t in s?s[t]:"then"in s&&"function"==typeof s.then?s.then(s=>e(s,t)):"function"==typeof s&&"default"===t?s:void 0}}})},1028:(e,s,t)=>{"use strict";t.a(e,async(e,i)=>{try{t.r(s),t.d(s,{config:()=>g,default:()=>p,getServerSideProps:()=>u,getStaticPaths:()=>m,getStaticProps:()=>h,reportWebVitals:()=>f,routeModule:()=>N,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>j});var d=t(7093),o=t(5244),n=t(1323),r=t(2899),a=t.n(r),c=t(6814),l=t(9378),x=e([c]);c=(x.then?(await x)():x)[0];let p=(0,n.l)(l,"default"),h=(0,n.l)(l,"getStaticProps"),m=(0,n.l)(l,"getStaticPaths"),u=(0,n.l)(l,"getServerSideProps"),g=(0,n.l)(l,"config"),f=(0,n.l)(l,"reportWebVitals"),j=(0,n.l)(l,"unstable_getStaticProps"),b=(0,n.l)(l,"unstable_getStaticPaths"),y=(0,n.l)(l,"unstable_getStaticParams"),v=(0,n.l)(l,"unstable_getServerProps"),S=(0,n.l)(l,"unstable_getServerSideProps"),N=new d.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/admin/mobile-debug",pathname:"/admin/mobile-debug",bundlePath:"",filename:""},components:{App:c.default,Document:a()},userland:l});i()}catch(e){i(e)}})},6814:(e,s,t)=>{"use strict";t.a(e,async(e,i)=>{try{t.r(s),t.d(s,{default:()=>c});var d=t(997),o=t(968),n=t.n(o);t(6689);var r=t(3590);t(8819),t(6764);var a=e([r]);function c({Component:e,pageProps:s}){return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(n(),{children:[d.jsx("meta",{charSet:"utf-8"}),d.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),d.jsx("meta",{httpEquiv:"X-Content-Type-Options",content:"nosniff"}),d.jsx("meta",{httpEquiv:"X-XSS-Protection",content:"1; mode=block"}),d.jsx("meta",{name:"referrer",content:"strict-origin-when-cross-origin"}),d.jsx("meta",{name:"robots",content:"noindex, nofollow, noarchive, nosnippet"}),d.jsx("meta",{name:"googlebot",content:"noindex, nofollow"}),d.jsx("meta",{name:"description",content:"Ocean Soul Sparkles Admin Portal - Secure staff access only"}),d.jsx("link",{rel:"icon",href:"/admin/favicon.ico"}),d.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/admin/apple-touch-icon.png"}),d.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/admin/favicon-32x32.png"}),d.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/admin/favicon-16x16.png"}),d.jsx("meta",{name:"theme-color",content:"#3788d8"}),d.jsx("meta",{name:"msapplication-TileColor",content:"#3788d8"}),d.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),d.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),d.jsx("link",{rel:"preconnect",href:"https://ndlgbcsbidyhxbpqzgqp.supabase.co"}),d.jsx("link",{rel:"dns-prefetch",href:"https://js.squareup.com"}),d.jsx("link",{rel:"dns-prefetch",href:"https://api.onesignal.com"}),d.jsx("title",{children:"Ocean Soul Sparkles Admin Portal"})]}),d.jsx(e,{...s}),d.jsx(r.ToastContainer,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light",toastStyle:{fontFamily:"inherit",fontSize:"14px"}}),d.jsx("div",{style:{position:"fixed",bottom:"10px",right:"10px",background:"rgba(0, 0, 0, 0.1)",color:"rgba(0, 0, 0, 0.3)",padding:"4px 8px",borderRadius:"4px",fontSize:"10px",fontWeight:"bold",pointerEvents:"none",zIndex:9999,userSelect:"none"},children:"ADMIN PORTAL"}),!1]})}r=(a.then?(await a)():a)[0],i()}catch(e){i(e)}})},9378:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var i=t(997),d=t(9816),o=t.n(d),n=t(6689),r=t(968),a=t.n(r);function c(){let[e,s]=(0,n.useState)({width:0,height:0,isMobile:!1,userAgent:""});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(a(),{children:[i.jsx("title",{className:"jsx-5dc5df920014103c",children:"Mobile Debug - Ocean Soul Sparkles Admin"}),i.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0",className:"jsx-5dc5df920014103c"})]}),(0,i.jsxs)("div",{style:{padding:"20px",fontFamily:"system-ui, sans-serif",backgroundColor:"#f8f9fa",minHeight:"100vh"},className:"jsx-5dc5df920014103c",children:[i.jsx("h1",{style:{color:"#16213e",marginBottom:"20px",fontSize:e.isMobile?"1.5rem":"2rem"},className:"jsx-5dc5df920014103c",children:"Mobile Debug Page"}),(0,i.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"20px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[i.jsx("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"Screen Information"}),(0,i.jsxs)("div",{style:{display:"grid",gap:"10px"},className:"jsx-5dc5df920014103c",children:[(0,i.jsxs)("div",{className:"jsx-5dc5df920014103c",children:[i.jsx("strong",{className:"jsx-5dc5df920014103c",children:"Width:"})," ",e.width,"px"]}),(0,i.jsxs)("div",{className:"jsx-5dc5df920014103c",children:[i.jsx("strong",{className:"jsx-5dc5df920014103c",children:"Height:"})," ",e.height,"px"]}),(0,i.jsxs)("div",{className:"jsx-5dc5df920014103c",children:[i.jsx("strong",{className:"jsx-5dc5df920014103c",children:"Is Mobile:"})," ",e.isMobile?"YES":"NO"]}),(0,i.jsxs)("div",{className:"jsx-5dc5df920014103c",children:[i.jsx("strong",{className:"jsx-5dc5df920014103c",children:"Breakpoint:"})," ",e.width<=480?"Small Mobile":e.width<=768?"Mobile":e.width<=1024?"Tablet":"Desktop"]})]})]}),(0,i.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"20px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[i.jsx("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"CSS Media Query Test"}),(0,i.jsxs)("div",{style:{padding:"10px",borderRadius:"4px",marginBottom:"10px",backgroundColor:"#e3f2fd"},className:"jsx-5dc5df920014103c desktop-only",children:[i.jsx("strong",{className:"jsx-5dc5df920014103c",children:"Desktop Only:"})," This should only be visible on desktop (width > 768px)"]}),(0,i.jsxs)("div",{style:{padding:"10px",borderRadius:"4px",marginBottom:"10px",backgroundColor:"#f3e5f5"},className:"jsx-5dc5df920014103c mobile-only",children:[i.jsx("strong",{className:"jsx-5dc5df920014103c",children:"Mobile Only:"})," This should only be visible on mobile (width ≤ 768px)"]}),(0,i.jsxs)("div",{style:{padding:"10px",borderRadius:"4px",backgroundColor:"#e8f5e8"},className:"jsx-5dc5df920014103c always-visible",children:[i.jsx("strong",{className:"jsx-5dc5df920014103c",children:"Always Visible:"})," This should always be visible"]})]}),(0,i.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"20px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[i.jsx("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"Touch-Friendly Elements"}),(0,i.jsxs)("div",{style:{display:"flex",gap:"10px",flexWrap:"wrap",marginBottom:"15px"},className:"jsx-5dc5df920014103c",children:[i.jsx("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",backgroundColor:"#16213e",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},className:"jsx-5dc5df920014103c",children:"Touch Button 1"}),i.jsx("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",backgroundColor:"#4CAF50",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},className:"jsx-5dc5df920014103c",children:"Touch Button 2"})]}),i.jsx("input",{type:"text",placeholder:"Touch-friendly input (44px min height)",style:{width:"100%",padding:"12px",fontSize:"16px",border:"2px solid #e0e0e0",borderRadius:"8px",minHeight:"44px",boxSizing:"border-box"},className:"jsx-5dc5df920014103c"})]}),(0,i.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"20px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[i.jsx("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"Responsive Layout Test"}),(0,i.jsxs)("div",{style:{display:"grid",gridTemplateColumns:e.isMobile?"1fr":"repeat(auto-fit, minmax(200px, 1fr))",gap:"15px"},className:"jsx-5dc5df920014103c",children:[i.jsx("div",{style:{backgroundColor:"#f0f0f0",padding:"15px",borderRadius:"8px",textAlign:"center"},className:"jsx-5dc5df920014103c",children:"Card 1"}),i.jsx("div",{style:{backgroundColor:"#f0f0f0",padding:"15px",borderRadius:"8px",textAlign:"center"},className:"jsx-5dc5df920014103c",children:"Card 2"}),i.jsx("div",{style:{backgroundColor:"#f0f0f0",padding:"15px",borderRadius:"8px",textAlign:"center"},className:"jsx-5dc5df920014103c",children:"Card 3"})]})]}),(0,i.jsxs)("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",marginBottom:"80px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},className:"jsx-5dc5df920014103c",children:[i.jsx("h2",{style:{marginTop:0,color:"#16213e"},className:"jsx-5dc5df920014103c",children:"Navigation Test"}),i.jsx("p",{className:"jsx-5dc5df920014103c",children:i.jsx("strong",{className:"jsx-5dc5df920014103c",children:"Expected behavior:"})}),(0,i.jsxs)("ul",{className:"jsx-5dc5df920014103c",children:[i.jsx("li",{className:"jsx-5dc5df920014103c",children:"On mobile (≤768px): Bottom navigation should be visible"}),i.jsx("li",{className:"jsx-5dc5df920014103c",children:"On desktop (>768px): Sidebar should be visible"}),i.jsx("li",{className:"jsx-5dc5df920014103c",children:"Mobile hamburger menu should work on mobile"})]}),i.jsx("div",{style:{marginTop:"20px"},className:"jsx-5dc5df920014103c",children:i.jsx("a",{href:"/admin/dashboard",style:{display:"inline-block",padding:"12px 24px",backgroundColor:"#16213e",color:"white",textDecoration:"none",borderRadius:"8px",fontSize:"16px"},className:"jsx-5dc5df920014103c",children:"Back to Dashboard"})})]})]}),i.jsx(o(),{id:"5dc5df920014103c",children:".desktop-only.jsx-5dc5df920014103c{display:block}.mobile-only.jsx-5dc5df920014103c{display:none}@media(max-width:768px){.desktop-only.jsx-5dc5df920014103c{display:none}.mobile-only.jsx-5dc5df920014103c{display:block}}"})]})}},8819:()=>{},6764:()=>{},5244:(e,s)=>{"use strict";var t;Object.defineProperty(s,"x",{enumerable:!0,get:function(){return t}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(t||(t={}))},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},9816:e=>{"use strict";e.exports=require("styled-jsx/style")},5315:e=>{"use strict";e.exports=require("path")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),i=s.X(0,[2899],()=>t(1028));module.exports=i})();