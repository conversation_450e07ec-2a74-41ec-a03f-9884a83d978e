/**
 * Ocean Soul Sparkles Admin Dashboard - Test Helpers
 * Utility functions and helpers for testing
 */

import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { ReactElement, ReactNode } from 'react';
import { NextApiRequest, NextApiResponse } from 'next';
import { createMocks } from 'node-mocks-http';
import { Customer, Booking, Service, StaffMember } from '../../types';

// Mock data generators
export const createMockCustomer = (overrides: Partial<Customer> = {}): Customer => ({
  id: 'cust_123',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+61412345678',
  mobile: '+61412345678',
  contactInfo: {
    email: '<EMAIL>',
    phone: '+61412345678',
    preferredContact: 'email'
  },
  notifications: {
    email: true,
    sms: true,
    push: true,
    inApp: true,
    categories: {
      bookings: true,
      payments: true,
      marketing: false,
      system: true
    }
  },
  customerType: 'individual',
  marketingConsent: true,
  totalSpent: { amount: 50000, currency: 'AUD' }, // $500.00
  averageBookingValue: { amount: 12500, currency: 'AUD' }, // $125.00
  outstandingBalance: { amount: 0, currency: 'AUD' },
  status: 'active',
  isVip: false,
  isBlacklisted: false,
  totalVisits: 4,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides
});

export const createMockBooking = (overrides: Partial<Booking> = {}): Booking => ({
  id: 'book_123',
  customerId: 'cust_123',
  serviceId: 'serv_123',
  artistId: 'art_123',
  bookingDate: '2024-06-20',
  startTime: '10:00',
  endTime: '12:00',
  duration: 120,
  timezone: 'Australia/Sydney',
  serviceName: 'Hair Braiding - Box Braids',
  location: 'studio',
  basePrice: { amount: 15000, currency: 'AUD' }, // $150.00
  totalAmount: { amount: 15000, currency: 'AUD' },
  status: 'confirmed',
  paymentStatus: 'pending',
  bookingSource: 'online',
  remindersSent: [],
  rescheduleCount: 0,
  createdAt: '2024-06-15T00:00:00Z',
  updatedAt: '2024-06-15T00:00:00Z',
  ...overrides
});

export const createMockService = (overrides: Partial<Service> = {}): Service => ({
  id: 'serv_123',
  name: 'Hair Braiding - Box Braids',
  slug: 'hair-braiding-box-braids',
  description: 'Professional box braiding service with premium synthetic hair',
  basePrice: { amount: 15000, currency: 'AUD' }, // $150.00
  pricingType: 'tiered',
  duration: 120,
  bufferTime: 15,
  isActive: true,
  isBookable: true,
  isOnlineBookable: true,
  requiresConsultation: false,
  skillLevel: 'intermediate',
  availableLocations: ['studio', 'mobile'],
  advanceBookingDays: 30,
  depositRequired: true,
  depositPercentage: 20,
  isFeatured: false,
  isPopular: true,
  bookingCount: 150,
  revenue: { amount: 2250000, currency: 'AUD' }, // $22,500.00
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides
});

export const createMockStaffMember = (overrides: Partial<StaffMember> = {}): StaffMember => ({
  id: 'staff_123',
  employeeId: 'EMP001',
  firstName: 'Jane',
  lastName: 'Smith',
  displayName: 'Jane',
  email: '<EMAIL>',
  phone: '+61412345679',
  role: 'Artist',
  department: 'Hair Services',
  position: 'Senior Hair Braider',
  permissions: ['view_bookings', 'manage_own_bookings', 'view_customers'],
  employmentType: 'full_time',
  startDate: '2023-01-01',
  status: 'active',
  contactInfo: {
    email: '<EMAIL>',
    phone: '+61412345679',
    preferredContact: 'email'
  },
  skills: ['Box Braids', 'Cornrows', 'Twists', 'Protective Styling'],
  certifications: [],
  experienceLevel: 'senior',
  specialties: ['Box Braids', 'Protective Styling'],
  availability: {
    monday: [{ start: '09:00', end: '17:00', duration: 480 }],
    tuesday: [{ start: '09:00', end: '17:00', duration: 480 }],
    wednesday: [{ start: '09:00', end: '17:00', duration: 480 }],
    thursday: [{ start: '09:00', end: '17:00', duration: 480 }],
    friday: [{ start: '09:00', end: '17:00', duration: 480 }],
    saturday: [{ start: '09:00', end: '15:00', duration: 360 }],
    sunday: []
  },
  hourlyRate: { amount: 5000, currency: 'AUD' }, // $50.00
  commissionRate: 40,
  totalBookings: 75,
  totalRevenue: { amount: 1125000, currency: 'AUD' }, // $11,250.00
  averageRating: 4.8,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides
});

// API testing helpers
export function createMockApiRequest(
  method: string = 'GET',
  url: string = '/api/test',
  body?: any,
  query?: any,
  headers?: any
) {
  const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
    method,
    url,
    body,
    query,
    headers: {
      'content-type': 'application/json',
      ...headers
    }
  });

  return { req, res };
}

export function createAuthenticatedApiRequest(
  method: string = 'GET',
  url: string = '/api/test',
  body?: any,
  query?: any,
  userRole: string = 'Admin'
) {
  const token = 'mock-jwt-token';
  const { req, res } = createMockApiRequest(method, url, body, query, {
    authorization: `Bearer ${token}`
  });

  // Mock the user context
  (req as any).user = {
    id: 'user_123',
    email: '<EMAIL>',
    role: userRole,
    permissions: ['admin:all']
  };

  return { req, res };
}

// Component testing helpers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: any;
  theme?: any;
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult {
  const { initialState, theme, ...renderOptions } = options;

  function Wrapper({ children }: { children: ReactNode }) {
    // Add any providers here (Redux, Theme, etc.)
    return <>{children}</>;
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Form testing helpers
export function fillForm(container: HTMLElement, formData: Record<string, string>) {
  Object.entries(formData).forEach(([name, value]) => {
    const input = container.querySelector(`[name="${name}"]`) as HTMLInputElement;
    if (input) {
      if (input.type === 'checkbox' || input.type === 'radio') {
        input.checked = value === 'true';
      } else {
        input.value = value;
      }
      
      // Trigger change event
      const event = new Event('change', { bubbles: true });
      input.dispatchEvent(event);
    }
  });
}

export function submitForm(container: HTMLElement) {
  const form = container.querySelector('form');
  if (form) {
    const event = new Event('submit', { bubbles: true, cancelable: true });
    form.dispatchEvent(event);
  }
}

// Date testing helpers
export function createMockDate(dateString: string = '2024-06-16T10:00:00Z') {
  const mockDate = new Date(dateString);
  const originalDate = Date;
  
  global.Date = jest.fn(() => mockDate) as any;
  global.Date.UTC = originalDate.UTC;
  global.Date.parse = originalDate.parse;
  global.Date.now = jest.fn(() => mockDate.getTime());
  
  return mockDate;
}

export function restoreDate() {
  global.Date = Date;
}

// Async testing helpers
export function waitForNextTick() {
  return new Promise(resolve => process.nextTick(resolve));
}

export function waitForTimeout(ms: number = 0) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Mock response helpers
export function createMockApiResponse<T>(data: T, success: boolean = true) {
  return {
    success,
    data: success ? data : undefined,
    error: success ? undefined : {
      code: 'TEST_ERROR',
      message: 'Test error message'
    },
    meta: {
      requestId: 'test-request-id',
      timestamp: '2024-06-16T10:00:00Z',
      version: '1.0.0'
    }
  };
}

export function createMockPaginatedResponse<T>(
  data: T[],
  page: number = 1,
  limit: number = 10,
  total: number = data.length
) {
  return {
    success: true,
    data,
    meta: {
      requestId: 'test-request-id',
      timestamp: '2024-06-16T10:00:00Z',
      version: '1.0.0',
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    }
  };
}

// Error testing helpers
export function expectApiError(response: any, code: string, statusCode: number = 400) {
  expect(response.success).toBe(false);
  expect(response.error).toBeDefined();
  expect(response.error.code).toBe(code);
  expect(response.meta).toBeDefined();
  expect(response.meta.requestId).toBeDefined();
}

export function expectApiSuccess(response: any, data?: any) {
  expect(response.success).toBe(true);
  expect(response.error).toBeUndefined();
  expect(response.meta).toBeDefined();
  expect(response.meta.requestId).toBeDefined();
  
  if (data !== undefined) {
    expect(response.data).toEqual(data);
  }
}

// Performance testing helpers
export function measureRenderTime(renderFn: () => void): number {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  return end - start;
}

// Accessibility testing helpers
export function expectAccessibleForm(container: HTMLElement) {
  const inputs = container.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    const label = container.querySelector(`label[for="${input.id}"]`);
    const ariaLabel = input.getAttribute('aria-label');
    const ariaLabelledBy = input.getAttribute('aria-labelledby');
    
    expect(
      label || ariaLabel || ariaLabelledBy
    ).toBeTruthy();
  });
}

// Database testing helpers
export function createMockSupabaseResponse<T>(data: T | null, error: any = null) {
  return Promise.resolve({ data, error });
}

export function createMockSupabaseQuery() {
  const query = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn(),
    maybeSingle: jest.fn(),
  };
  
  return query;
}
