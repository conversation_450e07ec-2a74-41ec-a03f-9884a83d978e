import type { NextApiRequest, NextApiResponse } from 'next';
import { customerRegister } from '../../../../lib/auth/customer-auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { firstName, lastName, email, password, phone } = req.body;

    // Validate input
    if (!firstName || !lastName || !email || !password) {
      return res.status(400).json({ 
        error: 'First name, last name, email, and password are required' 
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }

    // Validate password strength
    if (password.length < 8) {
      return res.status(400).json({ 
        error: 'Password must be at least 8 characters long' 
      });
    }

    // Get client IP for audit logging
    const clientIP = req.headers['x-forwarded-for'] as string || 
                    req.headers['x-real-ip'] as string || 
                    req.connection.remoteAddress || 
                    'unknown';

    // Attempt registration
    const registerResult = await customerRegister(
      firstName,
      lastName,
      email,
      password,
      phone,
      clientIP
    );

    if (!registerResult.success) {
      return res.status(400).json({ error: registerResult.error });
    }

    return res.status(201).json({
      success: true,
      user: registerResult.user,
      requiresEmailVerification: registerResult.requiresEmailVerification,
      message: 'Account created successfully. Please check your email to verify your account.'
    });

  } catch (error) {
    console.error('Customer registration API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
