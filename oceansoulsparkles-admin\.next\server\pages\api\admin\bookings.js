"use strict";(()=>{var e={};e.id=6424,e.ids=[6424],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},5948:(e,t,r)=>{r.r(t),r.d(t,{config:()=>f,default:()=>g,routeModule:()=>p});var s={};r.r(s),r.d(s,{default:()=>c});var i=r(1802),a=r(7153),o=r(8781),n=r(7474),d=r(2885);let l=process.env.SUPABASE_SERVICE_ROLE_KEY,u=(0,d.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",l);async function c(e,t){try{let r=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!r)return t.status(401).json({error:"No authentication token"});let s=await (0,n.Wg)(r);if(!s.valid||!s.user)return t.status(401).json({error:"Invalid authentication"});let i=s.user;if("GET"===e.method)return m(e,t,i);if("POST"===e.method)return _(e,t,i);return t.status(405).json({error:"Method not allowed"})}catch(e){return console.error("Bookings API error:",e),t.status(500).json({error:"Internal server error"})}}async function m(e,t,r){let s=u.from("bookings").select(`
        id,
        booking_date,
        start_time,
        end_time,
        status,
        total_amount,
        notes,
        created_at,
        customer_id,
        service_id,
        assigned_artist_id,
        customers (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        services (
          id,
          name,
          duration,
          price
        ),
        artist_profiles!assigned_artist_id (
          id,
          artist_name,
          display_name
        )
      `).order("booking_date",{ascending:!1}).order("start_time",{ascending:!1});("Artist"===r.role||"Braider"===r.role)&&(s=s.eq("assigned_artist_id",r.id));let{data:i,error:a}=await s;if(a)return console.error("Bookings query error:",a),t.status(500).json({error:"Failed to fetch bookings"});let o=(i||[]).map(e=>{let t=Array.isArray(e.customers)?e.customers[0]:e.customers,r=Array.isArray(e.services)?e.services[0]:e.services,s=Array.isArray(e.artist_profiles)?e.artist_profiles[0]:e.artist_profiles,i=new Date(e.start_time).toTimeString().slice(0,5);return{id:e.id,customer_name:t?`${t.first_name} ${t.last_name}`:"Unknown Customer",customer_email:t?.email||"",customer_phone:t?.phone||"",service_name:r?.name||"Unknown Service",service_duration:r?.duration||0,service_price:r?.price||0,artist_name:s?.artist_name||s?.display_name||"Unassigned",booking_date:e.booking_date,booking_time:i,start_time:e.start_time,end_time:e.end_time,status:e.status,total_amount:e.total_amount,notes:e.notes,created_at:e.created_at,customers:t,services:r,artist_profiles:s}});return t.status(200).json({bookings:o,total:o.length})}async function _(e,t,r){let{customer_id:s,service_id:i,assigned_artist_id:a,start_time:o,end_time:n,status:d="confirmed",total_amount:l,notes:c,location:m="Studio",tier_name:_,tier_price:g,booking_source:f="admin"}=e.body;if(!s||!i||!a||!o||!n)return t.status(400).json({error:"Missing required fields",message:"customer_id, service_id, assigned_artist_id, start_time, and end_time are required"});try{let{data:e,error:r}=await u.from("customers").select("id, first_name, last_name").eq("id",s).single();if(r||!e)return t.status(400).json({error:"Invalid customer ID"});let{data:p,error:k}=await u.from("services").select("id, name, base_price").eq("id",i).single();if(k||!p)return t.status(400).json({error:"Invalid service ID"});let{data:b,error:v}=await u.from("artist_profiles").select("id, artist_name").eq("id",a).single();if(v||!b)return t.status(400).json({error:"Invalid artist ID"});let{data:h,error:y}=await u.from("bookings").select("id, start_time, end_time").eq("assigned_artist_id",a).neq("status","cancelled").or(`and(start_time.lte.${o},end_time.gt.${o}),and(start_time.lt.${n},end_time.gte.${n}),and(start_time.gte.${o},end_time.lte.${n})`);if(y)console.error("Conflict check error:",y);else if(h&&h.length>0)return t.status(400).json({error:"Booking conflict",message:"The selected artist already has a booking during this time slot"});let{data:j,error:q}=await u.from("bookings").insert([{customer_id:s,service_id:i,artist_id:a,assigned_artist_id:a,start_time:o,end_time:n,status:d,total_amount:l,notes:c,location:m,tier_name:_,tier_price:g,booking_source:f}]).select(`
        id,
        booking_date,
        start_time,
        end_time,
        status,
        total_amount,
        notes,
        location,
        tier_name,
        tier_price,
        booking_source,
        created_at
      `).single();if(q)return console.error("Booking creation error:",q),t.status(500).json({error:"Failed to create booking"});return t.status(201).json({message:"Booking created successfully",booking:{...j,customer:e,service:p,artist:b}})}catch(e){return console.error("Create booking error:",e),t.status(500).json({error:"Internal server error"})}}let g=(0,o.l)(s,"default"),f=(0,o.l)(s,"config"),p=new i.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/bookings",pathname:"/api/admin/bookings",bundlePath:"",filename:""},userland:s})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2805],()=>r(5948));module.exports=s})();