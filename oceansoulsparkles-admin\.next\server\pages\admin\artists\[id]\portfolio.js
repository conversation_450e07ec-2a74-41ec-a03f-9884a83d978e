"use strict";(()=>{var e={};e.id=7577,e.ids=[7577,660],e.modules={4419:(e,s,t)=>{t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{config:()=>j,default:()=>m,getServerSideProps:()=>u,getStaticPaths:()=>x,getStaticProps:()=>p,reportWebVitals:()=>g,routeModule:()=>P,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>N,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>v});var i=t(7093),r=t(5244),l=t(1323),n=t(2899),o=t.n(n),c=t(6814),d=t(1297),h=e([c,d]);[c,d]=h.then?(await h)():h;let m=(0,l.l)(d,"default"),p=(0,l.l)(d,"getStaticProps"),x=(0,l.l)(d,"getStaticPaths"),u=(0,l.l)(d,"getServerSideProps"),j=(0,l.l)(d,"config"),g=(0,l.l)(d,"reportWebVitals"),v=(0,l.l)(d,"unstable_getStaticProps"),f=(0,l.l)(d,"unstable_getStaticPaths"),N=(0,l.l)(d,"unstable_getStaticParams"),b=(0,l.l)(d,"unstable_getServerProps"),S=(0,l.l)(d,"unstable_getServerSideProps"),P=new i.PagesRouteModule({definition:{kind:r.x.PAGES,page:"/admin/artists/[id]/portfolio",pathname:"/admin/artists/[id]/portfolio",bundlePath:"",filename:""},components:{App:c.default,Document:o()},userland:d});a()}catch(e){a(e)}})},1297:(e,s,t)=>{t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{default:()=>g});var i=t(997),r=t(6689),l=t(968),n=t.n(l),o=t(1163),c=t(1664),d=t.n(c),h=t(4845),m=t(3194),p=t(8568),x=t(1382),u=t.n(x),j=e([h]);function g(){let{user:e,loading:s}=(0,p.a)(),t=(0,o.useRouter)(),{id:a}=t.query,[l,c]=(0,r.useState)(null),[x,j]=(0,r.useState)({totalItems:0,featuredItems:0,publicItems:0,categories:[],lastUpdated:null}),[g,v]=(0,r.useState)(!0),[f,N]=(0,r.useState)(null),b=async()=>{try{v(!0);let e=localStorage.getItem("adminToken"),s=await fetch(`/api/admin/artists/${a}`,{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!s.ok)throw Error("Artist not found");let t=await s.json();c(t.artist);let i=await fetch(`/api/admin/artists/${a}/portfolio`,{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(i.ok){let e=await i.json();j(e.stats||{totalItems:0,featuredItems:0,publicItems:0,categories:[],lastUpdated:null})}}catch(e){console.error("Error loading artist data:",e),N("Failed to load artist information")}finally{v(!1)}};return s||g?i.jsx(h.Z,{children:(0,i.jsxs)("div",{className:u().loading,children:[i.jsx("div",{className:u().spinner}),i.jsx("p",{children:"Loading artist portfolio..."})]})}):e?["DEV","Admin"].includes(e.role)?f||!l?i.jsx(h.Z,{children:(0,i.jsxs)("div",{className:u().error,children:[i.jsx("h2",{children:"Artist Not Found"}),i.jsx("p",{children:f||"The specified artist could not be found."}),i.jsx(d(),{href:"/admin/artists/portfolio",className:u().backButton,children:"← Back to Portfolio Management"})]})}):(0,i.jsxs)(h.Z,{children:[(0,i.jsxs)(n(),{children:[(0,i.jsxs)("title",{children:[l.name," - Portfolio | Ocean Soul Sparkles Admin"]}),i.jsx("meta",{name:"description",content:`Manage ${l.name}'s portfolio and work samples`})]}),(0,i.jsxs)("div",{className:u().portfolioPage,children:[(0,i.jsxs)("div",{className:u().breadcrumb,children:[i.jsx(d(),{href:"/admin/artists",children:"Artists"}),i.jsx("span",{children:"›"}),i.jsx(d(),{href:"/admin/artists/portfolio",children:"Portfolio Management"}),i.jsx("span",{children:"›"}),i.jsx("span",{children:l.name})]}),(0,i.jsxs)("div",{className:u().artistHeader,children:[(0,i.jsxs)("div",{className:u().artistInfo,children:[(0,i.jsxs)("h1",{children:[l.name,"'s Portfolio"]}),(0,i.jsxs)("div",{className:u().artistDetails,children:[i.jsx("p",{className:u().email,children:l.email}),l.specializations&&l.specializations.length>0&&(0,i.jsxs)("div",{className:u().specializations,children:[i.jsx("strong",{children:"Specializations:"}),i.jsx("div",{className:u().specializationTags,children:l.specializations.map(e=>i.jsx("span",{className:u().specializationTag,children:e.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase())},e))})]}),l.bio&&i.jsx("p",{className:u().bio,children:l.bio})]})]}),(0,i.jsxs)("div",{className:u().artistStats,children:[l.rating&&(0,i.jsxs)("div",{className:u().statItem,children:[(0,i.jsxs)("span",{className:u().statValue,children:["⭐ ",l.rating.toFixed(1)]}),i.jsx("span",{className:u().statLabel,children:"Rating"})]}),l.total_bookings&&(0,i.jsxs)("div",{className:u().statItem,children:[i.jsx("span",{className:u().statValue,children:l.total_bookings}),i.jsx("span",{className:u().statLabel,children:"Total Bookings"})]})]})]}),f&&(0,i.jsxs)("div",{className:u().error,children:[i.jsx("p",{children:f}),i.jsx("button",{onClick:()=>N(null),children:"\xd7"})]}),(0,i.jsxs)("div",{className:u().statsGrid,children:[(0,i.jsxs)("div",{className:u().statCard,children:[i.jsx("div",{className:u().statIcon,children:"\uD83C\uDFA8"}),(0,i.jsxs)("div",{className:u().statContent,children:[i.jsx("h3",{children:x.totalItems}),i.jsx("p",{children:"Portfolio Items"})]})]}),(0,i.jsxs)("div",{className:u().statCard,children:[i.jsx("div",{className:u().statIcon,children:"⭐"}),(0,i.jsxs)("div",{className:u().statContent,children:[i.jsx("h3",{children:x.featuredItems}),i.jsx("p",{children:"Featured Items"})]})]}),(0,i.jsxs)("div",{className:u().statCard,children:[i.jsx("div",{className:u().statIcon,children:"\uD83D\uDC41️"}),(0,i.jsxs)("div",{className:u().statContent,children:[i.jsx("h3",{children:x.publicItems}),i.jsx("p",{children:"Public Items"})]})]}),(0,i.jsxs)("div",{className:u().statCard,children:[i.jsx("div",{className:u().statIcon,children:"\uD83D\uDCC2"}),(0,i.jsxs)("div",{className:u().statContent,children:[i.jsx("h3",{children:x.categories.length}),i.jsx("p",{children:"Categories"})]})]})]}),x.categories.length>0&&(0,i.jsxs)("div",{className:u().categoriesOverview,children:[i.jsx("h3",{children:"Portfolio Categories"}),i.jsx("div",{className:u().categoryTags,children:x.categories.map(e=>i.jsx("span",{className:u().categoryTag,children:e.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase())},e))})]}),i.jsx(m.Z,{artistId:a,onItemAdded:()=>{b()},onItemUpdated:()=>{b()},onItemDeleted:()=>{b()}})]})]}):i.jsx(h.Z,{children:(0,i.jsxs)("div",{className:u().accessDenied,children:[i.jsx("h2",{children:"Access Denied"}),i.jsx("p",{children:"You don't have permission to access portfolio management."})]})}):(t.push("/admin/login"),null)}h=(j.then?(await j)():j)[0],a()}catch(e){a(e)}})},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},2048:e=>{e.exports=require("fs")},5315:e=>{e.exports=require("path")},6162:e=>{e.exports=require("stream")},1568:e=>{e.exports=require("zlib")},3590:e=>{e.exports=import("react-toastify")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[2899,6212,1664,7441,3194],()=>t(4419));module.exports=a})();