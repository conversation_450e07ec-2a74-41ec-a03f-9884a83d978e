/**
 * Ocean Soul Sparkles Admin - Pull to Refresh Styles
 * Mobile-optimized pull-to-refresh component styling
 */

.pullToRefreshContainer {
  position: relative;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background: var(--admin-background);
}

.refreshIndicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #16213e 0%, #1a1a2e 100%);
  color: white;
  z-index: 10;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.refreshContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.refreshIcon {
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.refreshIconReady {
  font-size: 1.5rem;
  transform: rotate(180deg);
  transition: transform 0.3s ease;
  animation: pulse 1s infinite;
}

.refreshIconSpinning {
  font-size: 1.5rem;
  animation: spin 1s linear infinite;
}

.refreshText {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.9;
  text-align: center;
}

.progressBar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: #2196F3;
  transition: width 0.1s ease-out, background-color 0.3s ease;
  border-radius: 0 3px 3px 0;
}

.content {
  position: relative;
  min-height: 100%;
  background: var(--admin-background);
  z-index: 1;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: rotate(180deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .pullToRefreshContainer {
    /* Ensure smooth scrolling on mobile */
    scroll-behavior: smooth;
    overscroll-behavior-y: contain;
  }

  .refreshIndicator {
    height: 50px;
    border-radius: 0 0 8px 8px;
  }

  .refreshIcon,
  .refreshIconReady,
  .refreshIconSpinning {
    font-size: 1.25rem;
  }

  .refreshText {
    font-size: 0.8rem;
  }

  .progressBar {
    height: 2px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .refreshIndicator {
    background: var(--admin-dark);
    border: 2px solid var(--admin-border);
  }

  .progressFill {
    background: var(--admin-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .refreshIcon,
  .refreshIconReady,
  .refreshIconSpinning {
    animation: none;
    transition: none;
  }

  .progressFill {
    transition: width 0.1s ease-out;
  }

  .content {
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .refreshIndicator {
    background: linear-gradient(135deg, #0f1419 0%, #1a1a2e 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .progressBar {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* Touch-friendly improvements */
.pullToRefreshContainer {
  /* Improve touch scrolling performance */
  will-change: transform;
  transform: translateZ(0);
}

.refreshIndicator {
  /* Ensure indicator stays above content */
  will-change: transform, opacity;
  transform: translateZ(0);
}

.content {
  /* Optimize content rendering during pull */
  will-change: transform;
  transform: translateZ(0);
}

/* Loading state improvements */
.refreshIndicator[data-refreshing="true"] {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.refreshIndicator[data-refreshing="true"] .progressFill {
  background: rgba(255, 255, 255, 0.8);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Error state */
.refreshIndicator[data-error="true"] {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Success state */
.refreshIndicator[data-success="true"] {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Accessibility improvements */
.refreshIndicator:focus-within {
  outline: 2px solid var(--admin-primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .refreshIndicator {
    display: none;
  }
  
  .pullToRefreshContainer {
    overflow: visible;
  }
  
  .content {
    transform: none !important;
  }
}
