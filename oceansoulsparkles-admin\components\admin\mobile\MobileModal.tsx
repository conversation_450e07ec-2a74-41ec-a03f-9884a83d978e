/**
 * Ocean Soul Sparkles Admin - Mobile Modal Component
 * Full-screen mobile modals with proper animations and gestures
 */

import React, { useEffect, useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { HapticFeedback } from '../../../lib/gestures/swipe-handler';
import styles from '../../../styles/admin/mobile/MobileModal.module.css';

interface MobileModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  showCloseButton?: boolean;
  closeOnBackdrop?: boolean;
  closeOnSwipeDown?: boolean;
  className?: string;
  headerActions?: React.ReactNode;
}

export default function MobileModal({
  isOpen,
  onClose,
  title,
  children,
  size = 'medium',
  showCloseButton = true,
  closeOnBackdrop = true,
  closeOnSwipeDown = true,
  className = '',
  headerActions
}: MobileModalProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [swipeOffset, setSwipeOffset] = useState(0);
  const modalRef = useRef<HTMLDivElement>(null);
  const startY = useRef(0);
  const currentY = useRef(0);
  const isSwipeTracking = useRef(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setIsAnimating(true);
      
      // Trigger haptic feedback when opening
      HapticFeedback.light();
      
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
      
      // Add escape key listener
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          handleClose();
        }
      };
      
      document.addEventListener('keydown', handleEscape);
      
      return () => {
        document.removeEventListener('keydown', handleEscape);
      };
    } else {
      setIsAnimating(false);
      
      // Restore body scroll
      document.body.style.overflow = '';
      
      // Hide after animation
      const timer = setTimeout(() => {
        setIsVisible(false);
        setSwipeOffset(0);
      }, 300);
      
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Touch event handlers for swipe-to-close
  useEffect(() => {
    if (!closeOnSwipeDown || !modalRef.current) return;

    const modal = modalRef.current;

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 1) {
        startY.current = e.touches[0].clientY;
        currentY.current = e.touches[0].clientY;
        isSwipeTracking.current = true;
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isSwipeTracking.current || e.touches.length !== 1) return;

      currentY.current = e.touches[0].clientY;
      const deltaY = currentY.current - startY.current;

      // Only allow downward swipes
      if (deltaY > 0) {
        const offset = Math.min(deltaY * 0.5, 200); // Add resistance
        setSwipeOffset(offset);
        
        // Prevent default scrolling
        e.preventDefault();
      }
    };

    const handleTouchEnd = () => {
      if (!isSwipeTracking.current) return;

      const deltaY = currentY.current - startY.current;
      
      // Close modal if swiped down enough
      if (deltaY > 100) {
        HapticFeedback.medium();
        handleClose();
      } else {
        // Snap back to original position
        setSwipeOffset(0);
      }

      isSwipeTracking.current = false;
    };

    modal.addEventListener('touchstart', handleTouchStart, { passive: true });
    modal.addEventListener('touchmove', handleTouchMove, { passive: false });
    modal.addEventListener('touchend', handleTouchEnd, { passive: true });
    modal.addEventListener('touchcancel', handleTouchEnd, { passive: true });

    return () => {
      modal.removeEventListener('touchstart', handleTouchStart);
      modal.removeEventListener('touchmove', handleTouchMove);
      modal.removeEventListener('touchend', handleTouchEnd);
      modal.removeEventListener('touchcancel', handleTouchEnd);
    };
  }, [closeOnSwipeDown, isVisible]);

  const handleClose = () => {
    HapticFeedback.light();
    onClose();
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (closeOnBackdrop && e.target === e.currentTarget) {
      handleClose();
    }
  };

  const getModalStyle = (): React.CSSProperties => {
    const baseTransform = isAnimating ? 'translateY(0)' : 'translateY(100%)';
    const swipeTransform = swipeOffset > 0 ? `translateY(${swipeOffset}px)` : '';
    
    return {
      transform: swipeTransform || baseTransform,
      opacity: swipeOffset > 0 ? Math.max(0.5, 1 - (swipeOffset / 200)) : 1
    };
  };

  if (!isVisible) return null;

  const modalContent = (
    <div 
      className={`${styles.modalOverlay} ${isAnimating ? styles.open : ''}`}
      onClick={handleBackdropClick}
    >
      <div 
        ref={modalRef}
        className={`${styles.modal} ${styles[size]} ${className}`}
        style={getModalStyle()}
      >
        {/* Swipe indicator */}
        {closeOnSwipeDown && (
          <div className={styles.swipeIndicator}>
            <div className={styles.swipeHandle} />
          </div>
        )}

        {/* Header */}
        {(title || showCloseButton || headerActions) && (
          <div className={styles.header}>
            <div className={styles.headerLeft}>
              {showCloseButton && (
                <button
                  className={styles.closeButton}
                  onClick={handleClose}
                  aria-label="Close modal"
                >
                  ✕
                </button>
              )}
            </div>
            
            <div className={styles.headerCenter}>
              {title && <h2 className={styles.title}>{title}</h2>}
            </div>
            
            <div className={styles.headerRight}>
              {headerActions}
            </div>
          </div>
        )}

        {/* Content */}
        <div className={styles.content}>
          {children}
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
}

/**
 * Hook for managing mobile modal state
 */
export function useMobileModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<Omit<MobileModalProps, 'isOpen' | 'onClose' | 'children'>>({});

  const show = (
    content: React.ReactNode,
    modalConfig?: Omit<MobileModalProps, 'isOpen' | 'onClose' | 'children'>
  ) => {
    setConfig(modalConfig || {});
    setIsOpen(true);
    return content;
  };

  const hide = () => {
    setIsOpen(false);
  };

  const MobileModalComponent = ({ children }: { children: React.ReactNode }) => (
    <MobileModal
      {...config}
      isOpen={isOpen}
      onClose={hide}
    >
      {children}
    </MobileModal>
  );

  return {
    show,
    hide,
    isOpen,
    MobileModal: MobileModalComponent
  };
}

/**
 * Predefined modal configurations
 */
export const MobileModalPresets = {
  /**
   * Confirmation modal
   */
  confirmation: (
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ) => ({
    title,
    size: 'small' as const,
    children: (
      <div className={styles.confirmationContent}>
        <p className={styles.confirmationMessage}>{message}</p>
        <div className={styles.confirmationActions}>
          <button
            className={`${styles.button} ${styles.buttonSecondary}`}
            onClick={onCancel}
          >
            Cancel
          </button>
          <button
            className={`${styles.button} ${styles.buttonPrimary}`}
            onClick={onConfirm}
          >
            Confirm
          </button>
        </div>
      </div>
    )
  }),

  /**
   * Form modal
   */
  form: (title: string, formContent: React.ReactNode) => ({
    title,
    size: 'medium' as const,
    closeOnSwipeDown: false,
    children: formContent
  }),

  /**
   * Details modal
   */
  details: (title: string, content: React.ReactNode) => ({
    title,
    size: 'large' as const,
    children: content
  }),

  /**
   * Fullscreen modal
   */
  fullscreen: (title: string, content: React.ReactNode, headerActions?: React.ReactNode) => ({
    title,
    size: 'fullscreen' as const,
    headerActions,
    children: content
  })
};
