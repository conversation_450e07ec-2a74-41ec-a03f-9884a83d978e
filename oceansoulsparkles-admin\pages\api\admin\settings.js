import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth/admin-auth'

/**
 * API endpoint for system settings management
 * Handles reading and updating system configuration settings
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Settings API called - ${req.method}`)

  try {
    // Authenticate admin request
    const token = req.headers.authorization?.replace('Bearer ', '') ||
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({
        error: 'No authentication token',
        requestId
      });
    }

    const authResult = await authenticateAdminRequest(token)
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({
        error: 'Invalid authentication',
        requestId
      })
    }

    const user = authResult.user

    // Check permissions - only <PERSON><PERSON> and <PERSON><PERSON> can manage settings
    if (!['DEV', 'Admin'].includes(user.role)) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You do not have permission to manage settings',
        requestId
      })
    }

    if (req.method === 'GET') {
      // Get system settings
      try {
        const { data: settingsData, error } = await supabaseAdmin
          .from('system_settings')
          .select('*')
          .order('category', { ascending: true })

        if (error) {
          console.error(`[${requestId}] Database error:`, error)
          
          // Return default settings if database query fails
          const defaultSettings = {
            general: {
              businessName: 'Ocean Soul Sparkles',
              businessEmail: '<EMAIL>',
              businessPhone: '+61 XXX XXX XXX',
              businessAddress: 'Australia',
              timezone: 'Australia/Sydney',
              currency: 'AUD'
            },
            booking: {
              defaultBookingDuration: 60,
              advanceBookingDays: 30,
              cancellationHours: 24,
              autoConfirmBookings: true,
              requireDeposit: false,
              depositPercentage: 20
            },
            payment: {
              squareEnabled: true,
              squareEnvironment: 'sandbox',
              cashEnabled: true,
              cardEnabled: true,
              allowPartialPayments: true,
              autoProcessRefunds: false
            },
            notifications: {
              // Global toggles
              emailNotifications: true,
              smsNotifications: false,
              pushNotifications: false,

              // Email notification types
              emailBookingConfirmation: true,
              emailBookingReminder: true,
              emailBookingCancellation: true,
              emailPaymentReceipt: true,
              emailStaffNotification: true,
              emailLowInventoryAlert: true,
              emailPromotional: true,

              // SMS notification types
              smsBookingConfirmation: false,
              smsBookingReminder: false,
              smsBookingCancellation: false,
              smsPaymentReceipt: false,
              smsStaffNotification: false,
              smsPromotional: false,

              // Push notification types (for future)
              pushBookingConfirmation: false,
              pushBookingReminder: false,
              pushBookingCancellation: false,
              pushStaffNotification: false,

              // General settings
              bookingReminders: true,
              reminderHours: 24,
              adminNotifications: true,
              customerNotifications: true,

              // Fallback behavior
              emailFallbackWhenSMSFails: true,
              smsFallbackWhenEmailFails: false
            },
            security: {
              sessionTimeout: 1800,
              adminSessionTimeout: 1800,
              maxLoginAttempts: 5,
              lockoutDuration: 900,
              requireMFA: false,
              ipRestrictions: false
            }
          }

          return res.status(200).json({
            settings: defaultSettings,
            source: 'default',
            message: 'Using default settings - database connection issue',
            requestId
          })
        }

        // Transform database data to grouped settings
        const groupedSettings = {}
        settingsData.forEach(setting => {
          if (!groupedSettings[setting.category]) {
            groupedSettings[setting.category] = {}
          }
          
          // Parse JSON values or use raw value
          let value = setting.value
          try {
            value = JSON.parse(setting.value)
          } catch (e) {
            // Keep as string if not valid JSON
          }
          
          groupedSettings[setting.category][setting.key] = value
        })

        return res.status(200).json({
          settings: groupedSettings,
          source: 'database',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error fetching settings:`, error)
        return res.status(500).json({
          error: 'Failed to fetch settings',
          message: error.message,
          requestId
        })
      }

    } else if (req.method === 'PUT') {
      // Update system settings
      const { settings } = req.body

      if (!settings || typeof settings !== 'object') {
        return res.status(400).json({
          error: 'Invalid settings data',
          message: 'Settings object is required',
          requestId
        })
      }

      try {
        // Convert grouped settings to individual records
        const settingsToUpdate = []
        
        Object.keys(settings).forEach(category => {
          Object.keys(settings[category]).forEach(key => {
            const value = settings[category][key]
            settingsToUpdate.push({
              category,
              key,
              value: typeof value === 'object' ? JSON.stringify(value) : String(value),
              updated_at: new Date().toISOString(),
              updated_by: user.id
            })
          })
        })

        // Use upsert to insert or update settings
        const { data: updatedSettings, error } = await supabaseAdmin
          .from('system_settings')
          .upsert(settingsToUpdate, { 
            onConflict: 'category,key',
            ignoreDuplicates: false 
          })
          .select()

        if (error) {
          console.error(`[${requestId}] Error updating settings:`, error)
          
          // If table doesn't exist, just return success with mock response
          if (error.code === '42P01') { // Table doesn't exist
            return res.status(200).json({
              message: 'Settings saved successfully (mock mode)',
              source: 'mock',
              requestId
            })
          }
          
          return res.status(500).json({
            error: 'Failed to update settings',
            message: error.message,
            requestId
          })
        }

        return res.status(200).json({
          message: 'Settings updated successfully',
          updatedCount: updatedSettings?.length || 0,
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error updating settings:`, error)
        return res.status(500).json({
          error: 'Failed to update settings',
          message: error.message,
          requestId
        })
      }

    } else if (req.method === 'POST') {
      // Create new setting
      const { category, key, value, description } = req.body

      if (!category || !key || value === undefined) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'category, key, and value are required',
          requestId
        })
      }

      try {
        const { data: newSetting, error } = await supabaseAdmin
          .from('system_settings')
          .insert([
            {
              category,
              key,
              value: typeof value === 'object' ? JSON.stringify(value) : String(value),
              description: description || null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              created_by: user.id,
              updated_by: user.id
            }
          ])
          .select()
          .single()

        if (error) {
          console.error(`[${requestId}] Error creating setting:`, error)
          return res.status(500).json({
            error: 'Failed to create setting',
            message: error.message,
            requestId
          })
        }

        return res.status(201).json({
          setting: newSetting,
          message: 'Setting created successfully',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error creating setting:`, error)
        return res.status(500).json({
          error: 'Failed to create setting',
          message: error.message,
          requestId
        })
      }

    } else if (req.method === 'DELETE') {
      // Delete setting
      const { category, key } = req.query

      if (!category || !key) {
        return res.status(400).json({
          error: 'Missing parameters',
          message: 'category and key are required',
          requestId
        })
      }

      try {
        const { error } = await supabaseAdmin
          .from('system_settings')
          .delete()
          .eq('category', category)
          .eq('key', key)

        if (error) {
          console.error(`[${requestId}] Error deleting setting:`, error)
          return res.status(500).json({
            error: 'Failed to delete setting',
            message: error.message,
            requestId
          })
        }

        return res.status(200).json({
          message: 'Setting deleted successfully',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error deleting setting:`, error)
        return res.status(500).json({
          error: 'Failed to delete setting',
          message: error.message,
          requestId
        })
      }

    } else {
      return res.status(405).json({ 
        error: 'Method not allowed',
        message: 'Only GET, POST, PUT, and DELETE methods are supported',
        requestId
      })
    }

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      requestId
    })
  }
}
