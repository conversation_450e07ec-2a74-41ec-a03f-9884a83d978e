/* Bookings Management Styles */

.bookingsContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.newBookingBtn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.newBookingBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.backButton {
  background: linear-gradient(135deg, #64748b, #475569);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(100, 116, 139, 0.4);
}

.controlsPanel {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  flex-wrap: wrap;
  gap: 1rem;
}

.viewToggle {
  display: flex;
  background: #f1f5f9;
  border-radius: 8px;
  padding: 0.25rem;
}

.viewBtn {
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}

.viewBtn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.searchInput, .statusFilter, .dateFilter {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.3s ease;
}

.searchInput {
  min-width: 200px;
}

.searchInput:focus, .statusFilter:focus, .dateFilter:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.bookingsContent {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Bookings List */
.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.emptyState {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 3rem;
  border-radius: 12px;
  text-align: center;
  color: #64748b;
  font-size: 1.1rem;
}

.bookingCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.bookingCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.customerInfo h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.customerInfo p {
  color: #64748b;
  margin: 0;
  font-size: 0.875rem;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusPending {
  background: #fef3c7;
  color: #92400e;
}

.statusConfirmed {
  background: #dbeafe;
  color: #1e40af;
}

.statusCompleted {
  background: #d1fae5;
  color: #065f46;
}

.statusCancelled {
  background: #fee2e2;
  color: #991b1b;
}

.statusDefault {
  background: #f1f5f9;
  color: #475569;
}

.bookingDetails {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.serviceInfo strong {
  color: #1e293b;
  font-size: 1rem;
  display: block;
  margin-bottom: 0.5rem;
}

.serviceInfo p {
  color: #64748b;
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
}

.timeInfo {
  text-align: right;
}

.timeInfo p {
  margin: 0 0 0.25rem 0;
  color: #475569;
  font-size: 0.875rem;
}

.timeInfo p:first-child {
  font-weight: 600;
  color: #1e293b;
}

.bookingNotes {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #667eea;
}

.bookingNotes strong {
  color: #475569;
  font-size: 0.875rem;
}

.bookingActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.bookingActions .viewBtn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.bookingActions .viewBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.4);
}

.statusSelect {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

/* Calendar View */
.calendarView {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.comingSoon h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.comingSoon p {
  color: #64748b;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.comingSoon ul {
  text-align: left;
  max-width: 400px;
  margin: 1.5rem auto 0;
  color: #4a5568;
}

.comingSoon li {
  margin-bottom: 0.5rem;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .bookingDetails {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .timeInfo {
    text-align: left;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .controlsPanel {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
  }

  .filters {
    flex-direction: column;
    gap: 0.5rem;
  }

  .searchInput {
    min-width: auto;
  }

  .bookingActions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .bookingActions .viewBtn,
  .statusSelect {
    width: 100%;
    text-align: center;
  }
}
