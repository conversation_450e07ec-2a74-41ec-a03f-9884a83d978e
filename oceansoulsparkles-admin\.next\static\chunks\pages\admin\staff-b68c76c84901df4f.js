(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[261],{1923:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/staff",function(){return r(3612)}])},3612:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return s}}),function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var n=r(9008),a=r.n(n),o=r(1664),c=r.n(o),i=r(6026),d=r(99),f=r(3818),l=r.n(f);function s(){let{user:e,loading:t}=(0,i.a)(),[r,n]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[o,f]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[s,u]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[O,m]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[_,N]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("all"),[h,D]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("all"),[E,U]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("name");Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{e&&j()},[e]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=[...o];O&&(e=e.filter(e=>e.firstName.toLowerCase().includes(O.toLowerCase())||e.lastName.toLowerCase().includes(O.toLowerCase())||e.email.toLowerCase().includes(O.toLowerCase()))),"all"!==_&&(e=e.filter(e=>e.role===_)),"all"!==h&&(e=e.filter(e=>e.status===h)),e.sort((e,t)=>{switch(E){case"name":return"".concat(e.firstName," ").concat(e.lastName).localeCompare("".concat(t.firstName," ").concat(t.lastName));case"role":return e.role.localeCompare(t.role);case"email":return e.email.localeCompare(t.email);case"lastLogin":return new Date(t.lastLogin||0)-new Date(e.lastLogin||0);default:return 0}}),u(e)},[o,O,_,h,E]);let j=async()=>{try{n(!0);let e=await fetch("/api/admin/staff",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))}});if(e.ok){let t=await e.json();f(t.staff||[])}else console.error("Failed to load staff"),f([{id:"1",firstName:"Admin",lastName:"User",email:"<EMAIL>",role:"Admin",status:"active",lastLogin:new Date().toISOString(),createdAt:"2024-01-01T00:00:00Z"}])}catch(e){console.error("Error loading staff:",e),f([])}finally{n(!1)}},v=e=>{switch(e){case"DEV":return"#8b5cf6";case"Admin":return"#ef4444";case"Artist":return"#10b981";case"Braider":return"#f59e0b";default:return"#6b7280"}},b=e=>{switch(e){case"active":return"#10b981";case"inactive":default:return"#6b7280";case"suspended":return"#ef4444"}},C=e=>{if(!e)return"Never";let t=new Date(e),r=Math.floor((new Date-t)/864e5);return 0===r?"Today":1===r?"Yesterday":r<7?"".concat(r," days ago"):t.toLocaleDateString()};return t||r?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(d.Z,{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().loadingContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().loadingSpinner}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Loading staff..."})]})}):e?["DEV","Admin"].includes(e.role)?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(d.Z,{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a(),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("title",{children:"Staff Management | Ocean Soul Sparkles Admin"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("meta",{name:"description",content:"Manage staff members and permissions"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().staffContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("header",{className:l().header,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:l().title,children:"Staff Management"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().headerActions,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/staff/new",className:l().newStaffBtn,children:"+ Add Staff Member"})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().controlsPanel,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().searchSection,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",placeholder:"Search staff by name or email...",value:O,onChange:e=>m(e.target.value),className:l().searchInput})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().filtersSection,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:_,onChange:e=>N(e.target.value),className:l().filterSelect,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"all",children:"All Roles"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"DEV",children:"Developer"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"Admin",children:"Admin"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"Artist",children:"Artist"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"Braider",children:"Braider"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:h,onChange:e=>D(e.target.value),className:l().filterSelect,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"all",children:"All Status"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"active",children:"Active"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"inactive",children:"Inactive"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"suspended",children:"Suspended"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:E,onChange:e=>U(e.target.value),className:l().sortSelect,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"name",children:"Sort by Name"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"role",children:"Sort by Role"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"email",children:"Sort by Email"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"lastLogin",children:"Sort by Last Login"})]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().staffContent,children:0===s.length?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().emptyState,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().emptyIcon,children:"\uD83D\uDC65"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"No Staff Members Found"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:0===o.length?"No staff members have been added yet.":"No staff members match your current filters."}),0===o.length&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/staff/new",className:l().addFirstBtn,children:"Add First Staff Member"})]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().staffGrid,children:s.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().staffCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().cardHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().memberInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:l().memberName,children:[e.firstName," ",e.lastName]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:l().memberEmail,children:e.email})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().badges,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().roleBadge,style:{backgroundColor:v(e.role)},children:e.role}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:l().statusBadge,style:{backgroundColor:b(e.status)},children:e.status})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().cardBody,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().memberDetails,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:l().lastLogin,children:["Last login: ",C(e.lastLogin)]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:l().joinDate,children:["Joined: ",new Date(e.createdAt).toLocaleDateString()]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().cardActions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/staff/".concat(e.id),className:l().viewBtn,children:"View Details"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/staff/onboarding?staff_id=".concat(e.id),className:l().onboardingBtn,children:"\uD83D\uDCCB Onboarding"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/staff/training?staff_id=".concat(e.id),className:l().trainingBtn,children:"\uD83C\uDF93 Training"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/staff/performance?staff_id=".concat(e.id),className:l().performanceBtn,children:"\uD83D\uDCCA Performance"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/staff/".concat(e.id,"/edit"),className:l().editBtn,children:"Edit"})]})]})]},e.id))})})]})]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(d.Z,{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().accessDenied,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{children:"Access Denied"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"You don't have permission to access staff management."})]})}):null}},3818:function(e){e.exports={staffContainer:"Staff_staffContainer__Kyycp",header:"Staff_header__hV6GO",title:"Staff_title__gX7SX",headerActions:"Staff_headerActions__hgBm7",newStaffBtn:"Staff_newStaffBtn__avc2A",controlsPanel:"Staff_controlsPanel__2aAX9",searchSection:"Staff_searchSection__Yif0n",searchInput:"Staff_searchInput__KoHPz",filtersSection:"Staff_filtersSection__QfHqB",filterSelect:"Staff_filterSelect__pkcN_",sortSelect:"Staff_sortSelect__DitCT",staffContent:"Staff_staffContent__Gap4y",emptyState:"Staff_emptyState__PWk8z",emptyIcon:"Staff_emptyIcon__DLiuz",addFirstBtn:"Staff_addFirstBtn__4fAFQ",staffGrid:"Staff_staffGrid__KZOSB",staffCard:"Staff_staffCard__uiBW7",cardHeader:"Staff_cardHeader__ZBGA5",memberInfo:"Staff_memberInfo__ph3eL",memberName:"Staff_memberName__1XVzi",memberEmail:"Staff_memberEmail__vFTwL",badges:"Staff_badges__6wd6L",roleBadge:"Staff_roleBadge__s1osi",statusBadge:"Staff_statusBadge__E3gsY",cardBody:"Staff_cardBody__SeT9c",memberDetails:"Staff_memberDetails__K4xVH",lastLogin:"Staff_lastLogin__PGNfe",joinDate:"Staff_joinDate__uWi33",cardActions:"Staff_cardActions__i3pFb",viewBtn:"Staff_viewBtn__dRAa5",editBtn:"Staff_editBtn__GqoHX",onboardingBtn:"Staff_onboardingBtn__dLd32",trainingBtn:"Staff_trainingBtn__XZ2k4",performanceBtn:"Staff_performanceBtn__m2NVq",loadingContainer:"Staff_loadingContainer__6ywa2",loadingSpinner:"Staff_loadingSpinner__Lo18d",spin:"Staff_spin__Tq92n",accessDenied:"Staff_accessDenied__INizo"}}},function(e){e.O(0,[736,592,888,179],function(){return e(e.s=1923)}),_N_E=e.O()}]);