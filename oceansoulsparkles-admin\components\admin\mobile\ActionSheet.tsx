/**
 * Ocean Soul Sparkles Admin - Mobile Action Sheet Component
 * iOS/Android style action sheets for mobile interactions
 */

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { HapticFeedback } from '../../../lib/gestures/swipe-handler';
import styles from '../../../styles/admin/mobile/ActionSheet.module.css';

interface ActionSheetAction {
  id: string;
  label: string;
  icon?: string;
  variant?: 'default' | 'destructive' | 'primary';
  disabled?: boolean;
  onClick: () => void;
}

interface ActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
  actions: ActionSheetAction[];
  showCancel?: boolean;
  cancelLabel?: string;
  className?: string;
}

export default function ActionSheet({
  isOpen,
  onClose,
  title,
  message,
  actions,
  showCancel = true,
  cancelLabel = 'Cancel',
  className = ''
}: ActionSheetProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setIsAnimating(true);
      
      // Trigger haptic feedback when opening
      HapticFeedback.light();
      
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
      
      // Add escape key listener
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          handleClose();
        }
      };
      
      document.addEventListener('keydown', handleEscape);
      
      return () => {
        document.removeEventListener('keydown', handleEscape);
      };
    } else {
      setIsAnimating(false);
      
      // Restore body scroll
      document.body.style.overflow = '';
      
      // Hide after animation
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 300);
      
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  const handleClose = () => {
    HapticFeedback.light();
    onClose();
  };

  const handleActionClick = (action: ActionSheetAction) => {
    if (action.disabled) return;
    
    // Trigger appropriate haptic feedback
    if (action.variant === 'destructive') {
      HapticFeedback.heavy();
    } else if (action.variant === 'primary') {
      HapticFeedback.medium();
    } else {
      HapticFeedback.light();
    }
    
    action.onClick();
    onClose();
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isVisible) return null;

  const actionSheetContent = (
    <div 
      className={`${styles.actionSheetOverlay} ${isAnimating ? styles.open : ''}`}
      onClick={handleBackdropClick}
    >
      <div className={`${styles.actionSheet} ${className}`}>
        {/* Header */}
        {(title || message) && (
          <div className={styles.header}>
            {title && <h3 className={styles.title}>{title}</h3>}
            {message && <p className={styles.message}>{message}</p>}
          </div>
        )}

        {/* Actions */}
        <div className={styles.actions}>
          {actions.map((action) => (
            <button
              key={action.id}
              className={`${styles.action} ${styles[action.variant || 'default']} ${
                action.disabled ? styles.disabled : ''
              }`}
              onClick={() => handleActionClick(action)}
              disabled={action.disabled}
            >
              {action.icon && (
                <span className={styles.actionIcon}>{action.icon}</span>
              )}
              <span className={styles.actionLabel}>{action.label}</span>
            </button>
          ))}
        </div>

        {/* Cancel Button */}
        {showCancel && (
          <div className={styles.cancelSection}>
            <button
              className={`${styles.action} ${styles.cancel}`}
              onClick={handleClose}
            >
              <span className={styles.actionLabel}>{cancelLabel}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );

  return createPortal(actionSheetContent, document.body);
}

/**
 * Hook for managing action sheet state
 */
export function useActionSheet() {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<Omit<ActionSheetProps, 'isOpen' | 'onClose'>>({
    actions: []
  });

  const show = (newConfig: Omit<ActionSheetProps, 'isOpen' | 'onClose'>) => {
    setConfig(newConfig);
    setIsOpen(true);
  };

  const hide = () => {
    setIsOpen(false);
  };

  const ActionSheetComponent = () => (
    <ActionSheet
      {...config}
      isOpen={isOpen}
      onClose={hide}
    />
  );

  return {
    show,
    hide,
    isOpen,
    ActionSheet: ActionSheetComponent
  };
}

/**
 * Predefined action sheet configurations
 */
export const ActionSheetPresets = {
  /**
   * Delete confirmation action sheet
   */
  deleteConfirmation: (
    itemName: string,
    onConfirm: () => void
  ): Omit<ActionSheetProps, 'isOpen' | 'onClose'> => ({
    title: 'Delete Item',
    message: `Are you sure you want to delete "${itemName}"? This action cannot be undone.`,
    actions: [
      {
        id: 'delete',
        label: 'Delete',
        icon: '🗑️',
        variant: 'destructive',
        onClick: onConfirm
      }
    ]
  }),

  /**
   * Edit options action sheet
   */
  editOptions: (
    onEdit: () => void,
    onDuplicate: () => void,
    onDelete: () => void
  ): Omit<ActionSheetProps, 'isOpen' | 'onClose'> => ({
    title: 'Edit Options',
    actions: [
      {
        id: 'edit',
        label: 'Edit',
        icon: '✏️',
        variant: 'primary',
        onClick: onEdit
      },
      {
        id: 'duplicate',
        label: 'Duplicate',
        icon: '📋',
        variant: 'default',
        onClick: onDuplicate
      },
      {
        id: 'delete',
        label: 'Delete',
        icon: '🗑️',
        variant: 'destructive',
        onClick: onDelete
      }
    ]
  }),

  /**
   * Share options action sheet
   */
  shareOptions: (
    onEmail: () => void,
    onSMS: () => void,
    onCopy: () => void
  ): Omit<ActionSheetProps, 'isOpen' | 'onClose'> => ({
    title: 'Share',
    actions: [
      {
        id: 'email',
        label: 'Email',
        icon: '📧',
        variant: 'default',
        onClick: onEmail
      },
      {
        id: 'sms',
        label: 'SMS',
        icon: '💬',
        variant: 'default',
        onClick: onSMS
      },
      {
        id: 'copy',
        label: 'Copy Link',
        icon: '📋',
        variant: 'default',
        onClick: onCopy
      }
    ]
  }),

  /**
   * Sort options action sheet
   */
  sortOptions: (
    currentSort: string,
    onSortChange: (sortBy: string) => void
  ): Omit<ActionSheetProps, 'isOpen' | 'onClose'> => ({
    title: 'Sort By',
    actions: [
      {
        id: 'name',
        label: 'Name',
        icon: currentSort === 'name' ? '✓' : '',
        variant: currentSort === 'name' ? 'primary' : 'default',
        onClick: () => onSortChange('name')
      },
      {
        id: 'date',
        label: 'Date',
        icon: currentSort === 'date' ? '✓' : '',
        variant: currentSort === 'date' ? 'primary' : 'default',
        onClick: () => onSortChange('date')
      },
      {
        id: 'status',
        label: 'Status',
        icon: currentSort === 'status' ? '✓' : '',
        variant: currentSort === 'status' ? 'primary' : 'default',
        onClick: () => onSortChange('status')
      }
    ]
  }),

  /**
   * Filter options action sheet
   */
  filterOptions: (
    activeFilters: string[],
    onFilterToggle: (filter: string) => void
  ): Omit<ActionSheetProps, 'isOpen' | 'onClose'> => ({
    title: 'Filter',
    actions: [
      {
        id: 'active',
        label: 'Active',
        icon: activeFilters.includes('active') ? '✓' : '',
        variant: activeFilters.includes('active') ? 'primary' : 'default',
        onClick: () => onFilterToggle('active')
      },
      {
        id: 'pending',
        label: 'Pending',
        icon: activeFilters.includes('pending') ? '✓' : '',
        variant: activeFilters.includes('pending') ? 'primary' : 'default',
        onClick: () => onFilterToggle('pending')
      },
      {
        id: 'completed',
        label: 'Completed',
        icon: activeFilters.includes('completed') ? '✓' : '',
        variant: activeFilters.includes('completed') ? 'primary' : 'default',
        onClick: () => onFilterToggle('completed')
      }
    ]
  })
};
