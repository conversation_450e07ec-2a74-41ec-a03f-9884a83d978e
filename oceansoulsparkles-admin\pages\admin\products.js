import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import { QuickExportButton } from '@/components/admin/ExportButton';
import styles from '@/styles/admin/Products.module.css';

export default function ProductsManagement() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!authLoading && user) {
      loadProducts();
    }
  }, [authLoading, user]);

  useEffect(() => {
    filterAndSortProducts();
  }, [products, searchTerm, categoryFilter, statusFilter, sortBy]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/products', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load products');
      }

      const data = await response.json();
      setProducts(data.products || []);
    } catch (error) {
      console.error('Error loading products:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortProducts = () => {
    let filtered = [...products];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (categoryFilter) {
      filtered = filtered.filter(product => product.category_name === categoryFilter);
    }

    // Status filter
    if (statusFilter) {
      if (statusFilter === 'active') {
        filtered = filtered.filter(product => product.is_active);
      } else if (statusFilter === 'inactive') {
        filtered = filtered.filter(product => !product.is_active);
      } else if (statusFilter === 'low_stock') {
        filtered = filtered.filter(product => 
          product.stock <= (product.low_stock_threshold || 5)
        );
      }
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price':
          return (b.price || 0) - (a.price || 0);
        case 'stock':
          return (b.stock || 0) - (a.stock || 0);
        case 'category':
          return (a.category_name || '').localeCompare(b.category_name || '');
        default:
          return 0;
      }
    });

    setFilteredProducts(filtered);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount || 0);
  };

  const getStockStatus = (product) => {
    if (product.stock <= 0) return { status: 'out_of_stock', label: 'Out of Stock', class: 'outOfStock' };
    if (product.stock <= (product.low_stock_threshold || 5)) return { status: 'low_stock', label: 'Low Stock', class: 'lowStock' };
    return { status: 'in_stock', label: 'In Stock', class: 'inStock' };
  };

  const categories = [...new Set(products.map(p => p.category_name).filter(Boolean))];

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading products...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className={styles.errorContainer}>
          <h2>Error Loading Products</h2>
          <p>{error}</p>
          <button onClick={loadProducts} className={styles.retryButton}>
            Try Again
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Products Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage product catalog and inventory" />
      </Head>

      <div className={styles.productsContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Products Management</h1>
          <div className={styles.headerActions}>
            <QuickExportButton
              data={filteredProducts}
              type="products"
              className={styles.exportBtn}
            />
            <Link href="/admin/products/new" className={styles.newProductBtn}>
              + Add Product
            </Link>
          </div>
        </header>

        <div className={styles.filtersSection}>
          <div className={styles.searchBar}>
            <input
              type="text"
              placeholder="Search products by name, SKU, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filters}>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className={styles.filterSelect}
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={styles.filterSelect}
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="low_stock">Low Stock</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className={styles.sortSelect}
            >
              <option value="name">Sort by Name</option>
              <option value="price">Sort by Price</option>
              <option value="stock">Sort by Stock</option>
              <option value="category">Sort by Category</option>
            </select>
          </div>
        </div>

        <div className={styles.statsBar}>
          <div className={styles.stat}>
            <span className={styles.statNumber}>{products.length}</span>
            <span className={styles.statLabel}>Total Products</span>
          </div>
          <div className={styles.stat}>
            <span className={styles.statNumber}>
              {products.filter(p => p.is_active).length}
            </span>
            <span className={styles.statLabel}>Active</span>
          </div>
          <div className={styles.stat}>
            <span className={styles.statNumber}>
              {products.filter(p => p.stock <= (p.low_stock_threshold || 5)).length}
            </span>
            <span className={styles.statLabel}>Low Stock</span>
          </div>
          <div className={styles.stat}>
            <span className={styles.statNumber}>
              {formatCurrency(products.reduce((sum, p) => sum + ((p.price || 0) * (p.stock || 0)), 0))}
            </span>
            <span className={styles.statLabel}>Total Value</span>
          </div>
        </div>

        <div className={styles.productsGrid}>
          {filteredProducts.length === 0 ? (
            <div className={styles.emptyState}>
              <h3>No products found</h3>
              <p>
                {searchTerm || categoryFilter || statusFilter
                  ? 'Try adjusting your filters or search terms.'
                  : 'Start by adding your first product.'}
              </p>
              {!searchTerm && !categoryFilter && !statusFilter && (
                <Link href="/admin/products/new" className={styles.addFirstBtn}>
                  Add Your First Product
                </Link>
              )}
            </div>
          ) : (
            filteredProducts.map(product => {
              const stockStatus = getStockStatus(product);
              return (
                <div key={product.id} className={styles.productCard}>
                  <div className={styles.productImage}>
                    {product.image_url ? (
                      <img src={product.image_url} alt={product.name} />
                    ) : (
                      <div className={styles.placeholderImage}>
                        <span>📦</span>
                      </div>
                    )}
                  </div>

                  <div className={styles.productInfo}>
                    <div className={styles.productHeader}>
                      <h3 className={styles.productName}>{product.name}</h3>
                      <span className={`${styles.statusBadge} ${styles[product.is_active ? 'active' : 'inactive']}`}>
                        {product.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>

                    <div className={styles.productDetails}>
                      <div className={styles.productMeta}>
                        <span className={styles.sku}>SKU: {product.sku || 'N/A'}</span>
                        <span className={styles.category}>{product.category_name || 'Uncategorized'}</span>
                      </div>

                      <div className={styles.priceStock}>
                        <div className={styles.pricing}>
                          <span className={styles.price}>{formatCurrency(product.price)}</span>
                          {product.sale_price && product.sale_price < product.price && (
                            <span className={styles.salePrice}>{formatCurrency(product.sale_price)}</span>
                          )}
                        </div>
                        <div className={styles.stock}>
                          <span className={`${styles.stockStatus} ${styles[stockStatus.class]}`}>
                            {stockStatus.label}
                          </span>
                          <span className={styles.stockCount}>{product.stock || 0} units</span>
                        </div>
                      </div>

                      {product.description && (
                        <p className={styles.description}>
                          {product.description.length > 100
                            ? `${product.description.substring(0, 100)}...`
                            : product.description}
                        </p>
                      )}
                    </div>

                    <div className={styles.productActions}>
                      <Link href={`/admin/products/${product.id}`} className={styles.viewBtn}>
                        View Details
                      </Link>
                      <Link href={`/admin/products/${product.id}/edit`} className={styles.editBtn}>
                        Edit
                      </Link>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
