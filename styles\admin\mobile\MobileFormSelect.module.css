/* Ocean Soul Sparkles - Mobile Form Select Styles */

.mobileSelect {
  position: relative;
  margin-bottom: 1.5rem;
  width: 100%;
}

.selectContainer {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.selectTrigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1rem 0.75rem 1rem;
  min-height: 56px; /* Touch-friendly minimum height */
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  border: none;
  width: 100%;
}

.selectTrigger:focus {
  outline: none;
}

.selectValue {
  flex: 1;
  text-align: left;
  font-size: 1.1rem;
  font-weight: 500;
  color: #2d3748;
}

.selectedOption {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2d3748;
}

.placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.selectArrow {
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.arrow {
  font-size: 0.8rem;
  color: #718096;
  transition: transform 0.3s ease;
  transform-origin: center;
}

.arrow.rotated {
  transform: rotate(180deg);
}

.label {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.1rem;
  font-weight: 500;
  color: #718096;
  pointer-events: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  padding: 0 0.25rem;
  cursor: text;
}

.label.floating {
  top: 0.75rem;
  transform: translateY(0);
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--admin-primary);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
}

.label.required::after {
  content: '*';
  color: #e53e3e;
  margin-left: 0.25rem;
}

.labelIcon {
  font-size: 1rem;
  opacity: 0.7;
}

.selectBorder {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--admin-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.selectBorder.focused {
  transform: scaleX(1);
}

.selectContainer:focus-within {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 0.5rem;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border-radius: 12px;
  border: 2px solid var(--admin-primary);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  max-height: 300px;
  overflow: hidden;
  animation: dropdownSlideIn 0.3s ease-out;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdownContent {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.searchContainer {
  position: relative;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.searchIcon {
  position: absolute;
  left: 1.75rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  color: #a0aec0;
}

.optionsList {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;
  color: #2d3748;
  min-height: 48px; /* Touch-friendly height */
}

.option:hover {
  background: rgba(55, 136, 216, 0.1);
}

.option.selected {
  background: rgba(55, 136, 216, 0.15);
  color: var(--admin-primary);
  font-weight: 600;
}

.option.optionDisabled {
  opacity: 0.5;
  cursor: not-allowed;
  color: #a0aec0;
}

.option.optionDisabled:hover {
  background: transparent;
}

.optionIcon {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.optionLabel {
  flex: 1;
  text-align: left;
}

.selectedIndicator {
  font-size: 1.1rem;
  color: var(--admin-primary);
  font-weight: 700;
  flex-shrink: 0;
}

.noOptions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  color: #718096;
  text-align: center;
  gap: 0.5rem;
}

.noOptionsIcon {
  font-size: 2rem;
  opacity: 0.5;
}

.helperText {
  margin-top: 0.5rem;
  padding: 0 1rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.hintText {
  color: #718096;
  font-weight: 400;
}

.errorText {
  color: #e53e3e;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.errorIcon {
  font-size: 0.9rem;
}

/* Error State */
.mobileSelect.error .selectContainer {
  border-color: #e53e3e;
  background: rgba(229, 62, 62, 0.05);
}

.mobileSelect.error .selectBorder {
  background: #e53e3e;
}

.mobileSelect.error .label.floating {
  color: #e53e3e;
}

/* Disabled State */
.mobileSelect.disabled .selectContainer {
  background: #f7fafc;
  border-color: #e2e8f0;
  opacity: 0.6;
}

.mobileSelect.disabled .selectTrigger {
  cursor: not-allowed;
}

.mobileSelect.disabled .selectValue {
  color: #a0aec0;
}

.mobileSelect.disabled .label {
  color: #a0aec0;
  cursor: not-allowed;
}

.mobileSelect.disabled .arrow {
  color: #a0aec0;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .selectTrigger {
    padding: 1.5rem 1rem 1rem 1rem;
    min-height: 60px; /* Larger touch target on mobile */
  }

  .selectValue {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .label {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .label.floating {
    font-size: 0.8rem;
    top: 0.5rem;
  }

  .selectContainer {
    border-radius: 8px;
  }

  .dropdown {
    border-radius: 8px;
    max-height: 250px; /* Smaller on mobile */
  }

  .option {
    padding: 1.25rem 1rem;
    min-height: 52px; /* Larger touch target */
  }

  .searchInput {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 1rem 1rem 1rem 2.5rem;
  }

  .helperText {
    font-size: 0.85rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .selectContainer {
    border-width: 3px;
  }

  .label {
    font-weight: 600;
  }

  .selectBorder {
    height: 3px;
  }

  .dropdown {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .selectTrigger,
  .label,
  .selectBorder,
  .selectContainer,
  .arrow,
  .dropdown {
    transition: none;
  }

  .dropdown {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .selectContainer {
    background: rgba(45, 55, 72, 0.95);
    border-color: #4a5568;
  }

  .selectValue {
    color: #f7fafc;
  }

  .placeholder {
    color: #718096;
  }

  .label {
    color: #a0aec0;
  }

  .label.floating {
    color: #63b3ed;
    background: rgba(45, 55, 72, 0.9);
  }

  .dropdown {
    background: rgba(45, 55, 72, 0.98);
    border-color: #63b3ed;
  }

  .searchInput {
    background: #2d3748;
    border-color: #4a5568;
    color: #f7fafc;
  }

  .option {
    color: #f7fafc;
  }

  .option:hover {
    background: rgba(99, 179, 237, 0.1);
  }

  .option.selected {
    background: rgba(99, 179, 237, 0.15);
    color: #63b3ed;
  }

  .hintText {
    color: #a0aec0;
  }

  .mobileSelect.disabled .selectContainer {
    background: #2d3748;
  }
}
