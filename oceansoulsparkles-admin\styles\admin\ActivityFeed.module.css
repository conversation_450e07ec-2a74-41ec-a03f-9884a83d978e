/* Activity Feed Component Styles */
.activityFeedContainer {
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-xl);
  border: 1px solid var(--admin-border-light);
  box-shadow: 0 2px 4px var(--admin-shadow-light);
}

.header {
  margin-bottom: var(--admin-spacing-lg);
}

.sectionTitle {
  color: var(--admin-darker);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 var(--admin-spacing-xs) 0;
}

.sectionSubtitle {
  color: var(--admin-gray);
  font-size: 1rem;
  margin: 0;
}

.activityList {
  max-height: 500px;
  overflow-y: auto;
  margin-bottom: var(--admin-spacing-lg);
}

.emptyState {
  text-align: center;
  padding: var(--admin-spacing-xxl);
  color: var(--admin-gray);
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: var(--admin-spacing-md);
  opacity: 0.5;
}

.emptyState h3 {
  color: var(--admin-darker);
  margin-bottom: var(--admin-spacing-sm);
}

.emptyState p {
  margin: 0;
}

.activityItem {
  display: flex;
  gap: var(--admin-spacing-md);
  padding: var(--admin-spacing-md) 0;
  position: relative;
}

.activityItem:not(:last-child) {
  border-bottom: 1px solid var(--admin-border-light);
}

.activityIconContainer {
  position: relative;
  flex-shrink: 0;
}

.activityIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.activityLine {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: calc(100% + var(--admin-spacing-md));
  background: var(--admin-border-light);
  z-index: 1;
}

.activityItem:last-child .activityLine {
  display: none;
}

.activityContent {
  flex: 1;
  min-width: 0;
}

.activityHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-spacing-xs);
}

.activityTitle {
  font-weight: 600;
  color: var(--admin-darker);
  font-size: 0.95rem;
  line-height: 1.3;
}

.activityTime {
  color: var(--admin-gray);
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
  margin-left: var(--admin-spacing-sm);
}

.activityDescription {
  color: var(--admin-gray);
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: var(--admin-spacing-xs);
}

.activityUser {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-xs);
  margin-bottom: var(--admin-spacing-xs);
}

.userIcon {
  font-size: 0.8rem;
  color: var(--admin-gray);
}

.userName {
  font-size: 0.8rem;
  color: var(--admin-gray);
  font-weight: 500;
}

.activityMetadata {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-spacing-sm);
}

.metadataItem {
  display: flex;
  align-items: center;
  gap: 4px;
  background: var(--admin-bg-secondary);
  padding: 4px 8px;
  border-radius: var(--admin-radius-sm);
  font-size: 0.75rem;
  color: var(--admin-gray);
  font-weight: 500;
}

.activitySummary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--admin-spacing-md);
  padding-top: var(--admin-spacing-lg);
  border-top: 1px solid var(--admin-border-light);
}

.summaryItem {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
  padding: var(--admin-spacing-md);
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-md);
  transition: all var(--admin-transition-normal);
}

.summaryItem:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px var(--admin-shadow-light);
}

.summaryIcon {
  font-size: 1.5rem;
  opacity: 0.8;
}

.summaryText {
  flex: 1;
}

.summaryValue {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--admin-darker);
  line-height: 1;
  margin-bottom: 2px;
}

.summaryLabel {
  font-size: 0.8rem;
  color: var(--admin-gray);
  font-weight: 500;
}

/* Scrollbar Styling */
.activityList::-webkit-scrollbar {
  width: 6px;
}

.activityList::-webkit-scrollbar-track {
  background: var(--admin-bg-secondary);
  border-radius: 3px;
}

.activityList::-webkit-scrollbar-thumb {
  background: var(--admin-border-medium);
  border-radius: 3px;
}

.activityList::-webkit-scrollbar-thumb:hover {
  background: var(--admin-gray);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .activityFeedContainer {
    padding: var(--admin-spacing-lg);
  }

  .activityList {
    max-height: 400px;
  }

  .activityHeader {
    flex-direction: column;
    gap: var(--admin-spacing-xs);
  }

  .activityTime {
    margin-left: 0;
  }

  .activityMetadata {
    flex-direction: column;
    gap: var(--admin-spacing-xs);
  }

  .activitySummary {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .activityFeedContainer {
    padding: var(--admin-spacing-md);
  }

  .sectionTitle {
    font-size: 1.25rem;
  }

  .activityItem {
    gap: var(--admin-spacing-sm);
  }

  .activityIcon {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  .activityLine {
    top: 32px;
  }

  .summaryItem {
    padding: var(--admin-spacing-sm);
  }

  .summaryIcon {
    font-size: 1.25rem;
  }

  .summaryValue {
    font-size: 1.1rem;
  }
}
