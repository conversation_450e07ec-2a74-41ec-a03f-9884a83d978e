"use strict";(()=>{var e={};e.id=411,e.ids=[411],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},8277:(e,t,s)=>{s.r(t),s.d(t,{config:()=>d,default:()=>c,routeModule:()=>g});var r={};s.r(r),s.d(r,{default:()=>l});var a=s(1802),o=s(7153),i=s(8781),n=s(8456),u=s(7474);async function l(e,t){let s=Math.random().toString(36).substring(2,8);console.log(`[${s}] Settings API called - ${e.method}`);try{let r=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!r)return t.status(401).json({error:"No authentication token",requestId:s});let a=await (0,u.ZQ)(r);if(!a.valid||!a.user)return t.status(401).json({error:"Invalid authentication",requestId:s});let o=a.user;if(!["DEV","Admin"].includes(o.role))return t.status(403).json({error:"Access denied",message:"You do not have permission to manage settings",requestId:s});if("GET"===e.method)try{let{data:e,error:r}=await n.pR.from("system_settings").select("*").order("category",{ascending:!0});if(r)return console.error(`[${s}] Database error:`,r),t.status(200).json({settings:{general:{businessName:"Ocean Soul Sparkles",businessEmail:"<EMAIL>",businessPhone:"+61 XXX XXX XXX",businessAddress:"Australia",timezone:"Australia/Sydney",currency:"AUD"},booking:{defaultBookingDuration:60,advanceBookingDays:30,cancellationHours:24,autoConfirmBookings:!0,requireDeposit:!1,depositPercentage:20},payment:{squareEnabled:!0,squareEnvironment:"sandbox",cashEnabled:!0,cardEnabled:!0,allowPartialPayments:!0,autoProcessRefunds:!1},notifications:{emailNotifications:!0,smsNotifications:!1,pushNotifications:!1,emailBookingConfirmation:!0,emailBookingReminder:!0,emailBookingCancellation:!0,emailPaymentReceipt:!0,emailStaffNotification:!0,emailLowInventoryAlert:!0,emailPromotional:!0,smsBookingConfirmation:!1,smsBookingReminder:!1,smsBookingCancellation:!1,smsPaymentReceipt:!1,smsStaffNotification:!1,smsPromotional:!1,pushBookingConfirmation:!1,pushBookingReminder:!1,pushBookingCancellation:!1,pushStaffNotification:!1,bookingReminders:!0,reminderHours:24,adminNotifications:!0,customerNotifications:!0,emailFallbackWhenSMSFails:!0,smsFallbackWhenEmailFails:!1},security:{sessionTimeout:1800,adminSessionTimeout:1800,maxLoginAttempts:5,lockoutDuration:900,requireMFA:!1,ipRestrictions:!1}},source:"default",message:"Using default settings - database connection issue",requestId:s});let a={};return e.forEach(e=>{a[e.category]||(a[e.category]={});let t=e.value;try{t=JSON.parse(e.value)}catch(e){}a[e.category][e.key]=t}),t.status(200).json({settings:a,source:"database",requestId:s})}catch(e){return console.error(`[${s}] Error fetching settings:`,e),t.status(500).json({error:"Failed to fetch settings",message:e.message,requestId:s})}else if("PUT"===e.method){let{settings:r}=e.body;if(!r||"object"!=typeof r)return t.status(400).json({error:"Invalid settings data",message:"Settings object is required",requestId:s});try{let e=[];Object.keys(r).forEach(t=>{Object.keys(r[t]).forEach(s=>{let a=r[t][s];e.push({category:t,key:s,value:"object"==typeof a?JSON.stringify(a):String(a),updated_at:new Date().toISOString(),updated_by:o.id})})});let{data:a,error:i}=await n.pR.from("system_settings").upsert(e,{onConflict:"category,key",ignoreDuplicates:!1}).select();if(i){if(console.error(`[${s}] Error updating settings:`,i),"42P01"===i.code)return t.status(200).json({message:"Settings saved successfully (mock mode)",source:"mock",requestId:s});return t.status(500).json({error:"Failed to update settings",message:i.message,requestId:s})}return t.status(200).json({message:"Settings updated successfully",updatedCount:a?.length||0,requestId:s})}catch(e){return console.error(`[${s}] Error updating settings:`,e),t.status(500).json({error:"Failed to update settings",message:e.message,requestId:s})}}else if("POST"===e.method){let{category:r,key:a,value:i,description:u}=e.body;if(!r||!a||void 0===i)return t.status(400).json({error:"Missing required fields",message:"category, key, and value are required",requestId:s});try{let{data:e,error:l}=await n.pR.from("system_settings").insert([{category:r,key:a,value:"object"==typeof i?JSON.stringify(i):String(i),description:u||null,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),created_by:o.id,updated_by:o.id}]).select().single();if(l)return console.error(`[${s}] Error creating setting:`,l),t.status(500).json({error:"Failed to create setting",message:l.message,requestId:s});return t.status(201).json({setting:e,message:"Setting created successfully",requestId:s})}catch(e){return console.error(`[${s}] Error creating setting:`,e),t.status(500).json({error:"Failed to create setting",message:e.message,requestId:s})}}else{if("DELETE"!==e.method)return t.status(405).json({error:"Method not allowed",message:"Only GET, POST, PUT, and DELETE methods are supported",requestId:s});let{category:r,key:a}=e.query;if(!r||!a)return t.status(400).json({error:"Missing parameters",message:"category and key are required",requestId:s});try{let{error:e}=await n.pR.from("system_settings").delete().eq("category",r).eq("key",a);if(e)return console.error(`[${s}] Error deleting setting:`,e),t.status(500).json({error:"Failed to delete setting",message:e.message,requestId:s});return t.status(200).json({message:"Setting deleted successfully",requestId:s})}catch(e){return console.error(`[${s}] Error deleting setting:`,e),t.status(500).json({error:"Failed to delete setting",message:e.message,requestId:s})}}}catch(e){return console.error(`[${s}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:s})}}let c=(0,i.l)(r,"default"),d=(0,i.l)(r,"config"),g=new a.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/settings",pathname:"/api/admin/settings",bundlePath:"",filename:""},userland:r})},8456:(e,t,s)=>{s.d(t,{pR:()=>n});var r=s(2885);let a="https://ndlgbcsbidyhxbpqzgqp.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",i=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!a||!o)throw Error("Missing Supabase environment variables");(0,r.createClient)(a,o,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let n=(0,r.createClient)(a,i||o,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[2805],()=>s(8277));module.exports=r})();