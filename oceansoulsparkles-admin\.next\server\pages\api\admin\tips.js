"use strict";(()=>{var t={};t.id=2443,t.ids=[2443],t.modules={2885:t=>{t.exports=require("@supabase/supabase-js")},8432:t=>{t.exports=require("bcryptjs")},9344:t=>{t.exports=require("jsonwebtoken")},1287:t=>{t.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:t=>{t.exports=require("speakeasy")},812:(t,e,s)=>{s.r(e),s.d(e,{config:()=>p,default:()=>l,routeModule:()=>c});var r={};s.r(r),s.d(r,{default:()=>u});var i=s(1802),a=s(7153),o=s(8781),n=s(8456),d=s(7474);async function u(t,e){let s=Math.random().toString(36).substring(2,8);console.log(`[${s}] Tips API called - ${t.method}`);try{let{user:r,error:i}=await (0,d.ZQ)(t);if(i||!r)return e.status(401).json({error:"Authentication required",message:i?.message||"Authentication failed",requestId:s});if("GET"===t.method){let{status:r="all",artist_id:i,date_from:a,date_to:o,limit:d=50,offset:u=0}=t.query;try{let t=n.pR.from("tips").select(`
            *,
            artist_profiles!tips_artist_id_fkey (
              id,
              name,
              email
            ),
            bookings!tips_booking_id_fkey (
              id,
              service_name,
              start_time
            ),
            payments!tips_payment_id_fkey (
              id,
              method,
              payment_time
            )
          `).order("created_at",{ascending:!1});"all"!==r&&(t=t.eq("distribution_status",r)),i&&(t=t.eq("artist_id",i)),a&&(t=t.gte("created_at",a)),o&&(t=t.lte("created_at",o)),t=t.range(parseInt(u),parseInt(u)+parseInt(d)-1);let{data:l,error:p}=await t;if(p)throw Error(`Failed to fetch tips: ${p.message}`);let{data:c,error:m}=await n.pR.from("tips").select("distribution_status, amount.sum(), artist_id").eq("distribution_status","all"!==r?r:void 0),_={total_tips:l.length,total_amount:l.reduce((t,e)=>t+parseFloat(e.amount),0),pending_amount:l.filter(t=>"pending"===t.distribution_status).reduce((t,e)=>t+parseFloat(e.amount),0),distributed_amount:l.filter(t=>"distributed"===t.distribution_status).reduce((t,e)=>t+parseFloat(e.amount),0)};return e.status(200).json({tips:l,summary:_,pagination:{limit:parseInt(d),offset:parseInt(u),total:l.length},requestId:s})}catch(t){return console.error(`[${s}] Error fetching tips:`,t),e.status(500).json({error:"Failed to fetch tips",message:t.message,requestId:s})}}if("PATCH"===t.method){let{tip_id:r,action:i,distribution_method:a,notes:o}=t.body;if(!r||!i)return e.status(400).json({error:"Missing required fields",message:"tip_id and action are required",requestId:s});try{let t={updated_at:new Date().toISOString()};"distribute"===i?(t.distribution_status="distributed",t.distribution_date=new Date().toISOString(),t.distribution_method=a||"manual",o&&(t.notes=o)):"hold"===i?(t.distribution_status="held",o&&(t.notes=o)):"release"===i&&(t.distribution_status="pending",t.distribution_date=null,t.distribution_method=null);let{data:d,error:u}=await n.pR.from("tips").update(t).eq("id",r).select(`
            *,
            artist_profiles!tips_artist_id_fkey (
              id,
              name,
              email
            ),
            bookings!tips_booking_id_fkey (
              id,
              service_name,
              start_time
            )
          `).single();if(u)throw Error(`Failed to update tip: ${u.message}`);return console.log(`[${s}] Tip ${i}d successfully:`,d.id),e.status(200).json({success:!0,tip:d,message:`Tip ${i}d successfully`,requestId:s})}catch(t){return console.error(`[${s}] Error updating tip:`,t),e.status(500).json({error:"Failed to update tip",message:t.message,requestId:s})}}if("POST"===t.method){let{action:r,tip_ids:i,distribution_method:a,notes:o}=t.body;if(!r||!i||!Array.isArray(i))return e.status(400).json({error:"Missing required fields",message:"action and tip_ids array are required",requestId:s});try{let t={updated_at:new Date().toISOString()};"bulk_distribute"===r?(t.distribution_status="distributed",t.distribution_date=new Date().toISOString(),t.distribution_method=a||"bulk",o&&(t.notes=o)):"bulk_hold"===r&&(t.distribution_status="held",o&&(t.notes=o));let{data:d,error:u}=await n.pR.from("tips").update(t).in("id",i).select();if(u)throw Error(`Failed to bulk update tips: ${u.message}`);return console.log(`[${s}] Bulk ${r} completed for ${d.length} tips`),e.status(200).json({success:!0,updated_count:d.length,tips:d,message:`${d.length} tips ${r}d successfully`,requestId:s})}catch(t){return console.error(`[${s}] Error bulk updating tips:`,t),e.status(500).json({error:"Failed to bulk update tips",message:t.message,requestId:s})}}return e.status(405).json({error:"Method not allowed",requestId:s})}catch(t){return console.error(`[${s}] Tips API error:`,t),e.status(500).json({error:"Internal server error",message:t.message,requestId:s})}}let l=(0,o.l)(r,"default"),p=(0,o.l)(r,"config"),c=new i.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/tips",pathname:"/api/admin/tips",bundlePath:"",filename:""},userland:r})},8456:(t,e,s)=>{s.d(e,{pR:()=>n});var r=s(2885);let i="https://ndlgbcsbidyhxbpqzgqp.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",o=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i||!a)throw Error("Missing Supabase environment variables");(0,r.createClient)(i,a,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let n=(0,r.createClient)(i,o||a,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var e=require("../../../webpack-api-runtime.js");e.C(t);var s=t=>e(e.s=t),r=e.X(0,[2805],()=>s(812));module.exports=r})();