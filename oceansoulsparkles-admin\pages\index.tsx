import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    // Check if user is already authenticated
    const token = localStorage.getItem('admin-token');
    
    if (token) {
      // Redirect authenticated users to dashboard
      router.replace('/admin/dashboard');
    } else {
      // Redirect unauthenticated users to login
      router.replace('/admin/login');
    }
  }, [router]);

  return (
    <>
      <Head>
        <title>Ocean Soul Sparkles Admin</title>
      </Head>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        fontFamily: 'system-ui, sans-serif'
      }}>
        <div>
          <h2>Redirecting...</h2>
          <p>Please wait while we redirect you to the admin portal.</p>
        </div>
      </div>
    </>
  );
}
