/**
 * Ocean Soul Sparkles - Mobile Form Component
 * Touch-optimized form wrapper with mobile-friendly layout and validation
 */

import React, { useState, useEffect } from 'react';
import styles from '../../../styles/admin/mobile/MobileForm.module.css';

interface MobileFormProps {
  title: string;
  subtitle?: string;
  onSubmit: (data: any) => void;
  loading?: boolean;
  children: React.ReactNode;
  submitLabel?: string;
  cancelLabel?: string;
  onCancel?: () => void;
  showProgress?: boolean;
  currentStep?: number;
  totalSteps?: number;
  className?: string;
}

export default function MobileForm({
  title,
  subtitle,
  onSubmit,
  loading = false,
  children,
  submitLabel = 'Submit',
  cancelLabel = 'Cancel',
  onCancel,
  showProgress = false,
  currentStep = 1,
  totalSteps = 1,
  className = ''
}: MobileFormProps) {
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [formHeight, setFormHeight] = useState(window.innerHeight);

  useEffect(() => {
    const handleResize = () => {
      const newHeight = window.innerHeight;
      const heightDifference = formHeight - newHeight;
      
      // Detect virtual keyboard (significant height reduction on mobile)
      if (heightDifference > 150) {
        setIsKeyboardVisible(true);
      } else {
        setIsKeyboardVisible(false);
      }
      
      setFormHeight(newHeight);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [formHeight]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Collect form data
    const formData = new FormData(e.target as HTMLFormElement);
    const data: any = {};
    
    for (const [key, value] of formData.entries()) {
      data[key] = value;
    }
    
    onSubmit(data);
  };

  const progressPercentage = showProgress ? (currentStep / totalSteps) * 100 : 0;

  return (
    <div className={`${styles.mobileForm} ${className} ${isKeyboardVisible ? styles.keyboardVisible : ''}`}>
      {/* Form Header */}
      <div className={styles.formHeader}>
        <div className={styles.headerContent}>
          <h1 className={styles.formTitle}>{title}</h1>
          {subtitle && <p className={styles.formSubtitle}>{subtitle}</p>}
        </div>
        
        {/* Progress Indicator */}
        {showProgress && (
          <div className={styles.progressContainer}>
            <div className={styles.progressBar}>
              <div 
                className={styles.progressFill}
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            <span className={styles.progressText}>
              Step {currentStep} of {totalSteps}
            </span>
          </div>
        )}
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formContent}>
          {children}
        </div>

        {/* Form Actions */}
        <div className={styles.formActions}>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className={styles.cancelButton}
              disabled={loading}
            >
              {cancelLabel}
            </button>
          )}
          
          <button
            type="submit"
            className={styles.submitButton}
            disabled={loading}
          >
            {loading ? (
              <>
                <span className={styles.loadingSpinner}></span>
                <span>Processing...</span>
              </>
            ) : (
              submitLabel
            )}
          </button>
        </div>
      </form>

      {/* Keyboard Spacer */}
      {isKeyboardVisible && <div className={styles.keyboardSpacer}></div>}
    </div>
  );
}
