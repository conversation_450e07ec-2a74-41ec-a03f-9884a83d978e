import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuth } from '../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Staff onboarding API called - ${req.method}`);

  try {
    // Authenticate admin request
    const authResult = await verifyAdminAuth(req);
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Authentication required',
        message: authResult.message || 'Authentication failed',
        requestId
      });
    }

    const { user } = authResult;

    if (!user) {
      return res.status(401).json({
        error: 'User not found',
        requestId
      });
    }

    // Only Admin and DEV can manage staff onboarding
    if (user.role !== 'Admin' && user.role !== 'DEV') {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        message: 'Only admins can manage staff onboarding',
        requestId
      });
    }

    if (req.method === 'GET') {
      const { staff_id } = req.query;

      if (!staff_id) {
        return res.status(400).json({
          error: 'Staff ID required',
          message: 'Staff ID parameter is required',
          requestId
        });
      }

      // Get onboarding checklist for staff member
      const { data: checklist, error } = await supabase
        .from('staff_onboarding_checklist')
        .select(`
          id,
          checklist_item,
          category,
          description,
          is_required,
          is_completed,
          completed_at,
          due_date,
          notes,
          created_at
        `)
        .eq('staff_id', staff_id)
        .order('category', { ascending: true })
        .order('checklist_item', { ascending: true });

      if (error) {
        console.error(`[${requestId}] Database error:`, error);
        return res.status(500).json({
          error: 'Failed to fetch onboarding checklist',
          message: error.message,
          requestId
        });
      }

      // Calculate completion statistics
      const totalItems = checklist?.length || 0;
      const completedItems = checklist?.filter(item => item.is_completed).length || 0;
      const requiredItems = checklist?.filter(item => item.is_required).length || 0;
      const completedRequiredItems = checklist?.filter(item => item.is_required && item.is_completed).length || 0;

      return res.status(200).json({
        checklist: checklist || [],
        statistics: {
          total: totalItems,
          completed: completedItems,
          required: requiredItems,
          completedRequired: completedRequiredItems,
          completionPercentage: totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0,
          requiredCompletionPercentage: requiredItems > 0 ? Math.round((completedRequiredItems / requiredItems) * 100) : 0
        },
        requestId
      });
    }

    if (req.method === 'POST') {
      const { staff_id, action, checklist_item_id, notes } = req.body;

      if (!staff_id || !action) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Staff ID and action are required',
          requestId
        });
      }

      if (action === 'initialize') {
        // Initialize onboarding checklist for new staff member
        const defaultChecklistItems = [
          { item: 'Complete personal information form', category: 'documentation', required: true, description: 'Fill out all personal details and emergency contacts' },
          { item: 'Provide identification documents', category: 'documentation', required: true, description: 'Driver\'s license, passport, or other valid ID' },
          { item: 'Submit tax file number (TFN)', category: 'documentation', required: true, description: 'Required for payroll processing' },
          { item: 'Complete bank details form', category: 'documentation', required: true, description: 'For salary and tip payments' },
          { item: 'Sign employment contract', category: 'documentation', required: true, description: 'Review and sign employment agreement' },
          { item: 'Complete Health & Safety training', category: 'training', required: true, description: 'Basic health and safety protocols' },
          { item: 'Complete Customer Service training', category: 'training', required: true, description: 'Professional customer service standards' },
          { item: 'Complete POS System training', category: 'training', required: true, description: 'Learn to use the point-of-sale system' },
          { item: 'Receive workspace tour', category: 'access', required: true, description: 'Tour of facilities and workspace assignment' },
          { item: 'Receive equipment and supplies', category: 'equipment', required: true, description: 'Basic tools and supplies for role' },
          { item: 'Set up system access accounts', category: 'access', required: true, description: 'Admin system login and permissions' },
          { item: 'Meet team members', category: 'access', required: false, description: 'Introduction to colleagues and team' },
          { item: 'Review policies and procedures', category: 'documentation', required: true, description: 'Company policies and standard procedures' },
          { item: 'Complete emergency procedures training', category: 'training', required: true, description: 'Emergency response and first aid basics' }
        ];

        const checklistData = defaultChecklistItems.map(item => ({
          staff_id,
          checklist_item: item.item,
          category: item.category,
          description: item.description,
          is_required: item.required,
          due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 7 days from now
        }));

        const { data: newChecklist, error } = await supabase
          .from('staff_onboarding_checklist')
          .insert(checklistData)
          .select();

        if (error) {
          console.error(`[${requestId}] Error creating checklist:`, error);
          return res.status(500).json({
            error: 'Failed to initialize onboarding checklist',
            message: error.message,
            requestId
          });
        }

        return res.status(201).json({
          checklist: newChecklist,
          message: 'Onboarding checklist initialized successfully',
          requestId
        });
      }

      if (action === 'complete_item') {
        if (!checklist_item_id) {
          return res.status(400).json({
            error: 'Checklist item ID required',
            message: 'Checklist item ID is required for completion',
            requestId
          });
        }

        const { data: updatedItem, error } = await supabase
          .from('staff_onboarding_checklist')
          .update({
            is_completed: true,
            completed_at: new Date().toISOString(),
            completed_by: user.id,
            notes: notes || null
          })
          .eq('id', checklist_item_id)
          .eq('staff_id', staff_id)
          .select()
          .single();

        if (error) {
          console.error(`[${requestId}] Error completing item:`, error);
          return res.status(500).json({
            error: 'Failed to complete checklist item',
            message: error.message,
            requestId
          });
        }

        return res.status(200).json({
          item: updatedItem,
          message: 'Checklist item completed successfully',
          requestId
        });
      }

      if (action === 'uncomplete_item') {
        if (!checklist_item_id) {
          return res.status(400).json({
            error: 'Checklist item ID required',
            message: 'Checklist item ID is required for uncompletion',
            requestId
          });
        }

        const { data: updatedItem, error } = await supabase
          .from('staff_onboarding_checklist')
          .update({
            is_completed: false,
            completed_at: null,
            completed_by: null,
            notes: notes || null
          })
          .eq('id', checklist_item_id)
          .eq('staff_id', staff_id)
          .select()
          .single();

        if (error) {
          console.error(`[${requestId}] Error uncompleting item:`, error);
          return res.status(500).json({
            error: 'Failed to uncomplete checklist item',
            message: error.message,
            requestId
          });
        }

        return res.status(200).json({
          item: updatedItem,
          message: 'Checklist item uncompleted successfully',
          requestId
        });
      }

      return res.status(400).json({
        error: 'Invalid action',
        message: 'Action must be initialize, complete_item, or uncomplete_item',
        requestId
      });
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
}
