"use strict";(()=>{var e={};e.id=7412,e.ids=[7412],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},3539:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>l,routeModule:()=>_});var s={};r.r(s),r.d(s,{default:()=>d});var a=r(1802),o=r(7153),n=r(8781),i=r(7474),c=r(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,m=(0,c.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function d(e,t){let r=Math.random().toString(36).substring(2,8);console.log(`[${r}] Customer communications API called - ${e.method}`);try{let s=await (0,i.SA)(e);if(!s.success)return t.status(401).json({error:"Authentication required",message:s.message,requestId:r});let{user:a}=s;if(!a)return t.status(401).json({error:"User not found",requestId:r});if("GET"===e.method){let{customer_id:s,booking_id:a,type:o,status:n,limit:i="50",offset:c="0"}=e.query,u=m.from("customer_communications").select(`
          id,
          customer_id,
          booking_id,
          template_id,
          communication_type,
          recipient,
          subject,
          status,
          sent_at,
          delivered_at,
          opened_at,
          error_message,
          created_at,
          customers!inner(
            id,
            first_name,
            last_name,
            email
          ),
          bookings(
            id,
            start_time,
            services(name)
          ),
          email_templates(
            id,
            name,
            type
          )
        `).order("created_at",{ascending:!1}).range(parseInt(c),parseInt(c)+parseInt(i)-1);s&&(u=u.eq("customer_id",s)),a&&(u=u.eq("booking_id",a)),o&&"all"!==o&&(u=u.eq("communication_type",o)),n&&"all"!==n&&(u=u.eq("status",n));let{data:d,error:l}=await u;if(l)return console.error(`[${r}] Database error:`,l),t.status(500).json({error:"Failed to fetch communications",message:l.message,requestId:r});let p=m.from("customer_communications").select("*",{count:"exact",head:!0});s&&(p=p.eq("customer_id",s)),a&&(p=p.eq("booking_id",a)),o&&"all"!==o&&(p=p.eq("communication_type",o)),n&&"all"!==n&&(p=p.eq("status",n));let{count:_}=await p;return t.status(200).json({communications:d||[],total:_||0,limit:parseInt(i),offset:parseInt(c),requestId:r})}if("POST"===e.method){let{customer_id:s,booking_id:o,template_id:n,communication_type:i,recipient:c,subject:u,content:d,status:l="pending"}=e.body;if(!s||!i||!c||!d)return t.status(400).json({error:"Missing required fields",message:"Customer ID, communication type, recipient, and content are required",requestId:r});let{data:p,error:_}=await m.from("customer_communications").insert([{customer_id:s,booking_id:o,template_id:n,communication_type:i,recipient:c,subject:u,content:d,status:l,created_by:a.id}]).select(`
          id,
          customer_id,
          booking_id,
          template_id,
          communication_type,
          recipient,
          subject,
          status,
          sent_at,
          created_at
        `).single();if(_)return console.error(`[${r}] Error creating communication:`,_),t.status(500).json({error:"Failed to create communication record",message:_.message,requestId:r});return t.status(201).json({communication:p,message:"Communication record created successfully",requestId:r})}return t.status(405).json({error:"Method not allowed",requestId:r})}catch(e){return console.error(`[${r}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:e instanceof Error?e.message:"Unknown error",requestId:r})}}let l=(0,n.l)(s,"default"),p=(0,n.l)(s,"config"),_=new a.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/communications",pathname:"/api/admin/communications",bundlePath:"",filename:""},userland:s})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2805],()=>r(3539));module.exports=s})();