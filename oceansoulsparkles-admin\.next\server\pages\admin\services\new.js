(()=>{var e={};e.id=1456,e.ids=[1456,660],e.modules={8154:e=>{e.exports={serviceFormContainer:"ServiceForm_serviceFormContainer__x_rm_",header:"ServiceForm_header__MJoC5",breadcrumb:"ServiceForm_breadcrumb__Sujex",backButton:"ServiceForm_backButton__qvGeh",formContent:"ServiceForm_formContent__7hqCc",form:"ServiceForm_form__y0s41",errorAlert:"ServiceForm_errorAlert__zu0M_",formGrid:"ServiceForm_formGrid__z4a_v",formGroup:"ServiceForm_formGroup__bBtrC",inputError:"ServiceForm_inputError__4HD91",errorText:"ServiceForm_errorText__3nMcX",visibilitySection:"ServiceForm_visibilitySection__L70gF",checkboxGroup:"ServiceForm_checkboxGroup__HHCB0",checkboxLabel:"ServiceForm_checkboxLabel__dHq0_",formActions:"ServiceForm_formActions__RKZe1",submitButton:"ServiceForm_submitButton__kH7U0",cancelButton:"ServiceForm_cancelButton__eloNY",loadingContainer:"ServiceForm_loadingContainer__OdLd7",errorContainer:"ServiceForm_errorContainer__Z68Z1",notFoundContainer:"ServiceForm_notFoundContainer__bRMUf",loadingSpinner:"ServiceForm_loadingSpinner__wb_pk",spin:"ServiceForm_spin__VG_ya"}},2867:(e,r,i)=>{"use strict";i.a(e,async(e,t)=>{try{i.r(r),i.d(r,{config:()=>_,default:()=>u,getServerSideProps:()=>v,getStaticPaths:()=>h,getStaticProps:()=>p,reportWebVitals:()=>x,routeModule:()=>N,unstable_getServerProps:()=>g,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>b});var s=i(7093),a=i(5244),n=i(1323),c=i(2899),o=i.n(c),l=i(6814),d=i(8237),m=e([l,d]);[l,d]=m.then?(await m)():m;let u=(0,n.l)(d,"default"),p=(0,n.l)(d,"getStaticProps"),h=(0,n.l)(d,"getStaticPaths"),v=(0,n.l)(d,"getServerSideProps"),_=(0,n.l)(d,"config"),x=(0,n.l)(d,"reportWebVitals"),b=(0,n.l)(d,"unstable_getStaticProps"),S=(0,n.l)(d,"unstable_getStaticPaths"),j=(0,n.l)(d,"unstable_getStaticParams"),g=(0,n.l)(d,"unstable_getServerProps"),f=(0,n.l)(d,"unstable_getServerSideProps"),N=new s.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/admin/services/new",pathname:"/admin/services/new",bundlePath:"",filename:""},components:{App:l.default,Document:o()},userland:d});t()}catch(e){t(e)}})},8237:(e,r,i)=>{"use strict";i.a(e,async(e,t)=>{try{i.r(r),i.d(r,{default:()=>_});var s=i(997),a=i(6689),n=i(1163),c=i(968),o=i.n(c),l=i(1664),d=i.n(l),m=i(8568),u=i(4845),p=i(8154),h=i.n(p),v=e([u]);function _(){let e=(0,n.useRouter)(),{user:r,loading:i}=(0,m.a)(),[t,c]=(0,a.useState)(!1),[l,p]=(0,a.useState)({name:"",description:"",duration:"",price:"",category:"Hair Braiding",status:"active",visible_on_public:!0,visible_on_pos:!0,visible_on_events:!0}),[v,_]=(0,a.useState)({}),x=e=>{let{name:r,value:i,type:t,checked:s}=e.target;p(e=>({...e,[r]:"checkbox"===t?s:i})),v[r]&&_(e=>({...e,[r]:null}))},b=()=>{let e={};return l.name.trim()||(e.name="Service name is required"),l.category||(e.category="Category is required"),l.duration&&(isNaN(l.duration)||0>=parseInt(l.duration))&&(e.duration="Duration must be a positive number"),l.price&&(isNaN(l.price)||0>parseFloat(l.price))&&(e.price="Price must be a valid number"),_(e),0===Object.keys(e).length},S=async r=>{if(r.preventDefault(),b())try{c(!0);let r=localStorage.getItem("admin-token"),i=await fetch("/api/admin/services",{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify(l)});if(!i.ok){let e=await i.json();throw Error(e.error||"Failed to create service")}let t=await i.json();e.push(`/admin/services/${t.service.id}`)}catch(e){console.error("Error creating service:",e),_({submit:e.message})}finally{c(!1)}};return i?s.jsx(u.Z,{children:(0,s.jsxs)("div",{className:h().loadingContainer,children:[s.jsx("div",{className:h().loadingSpinner}),s.jsx("p",{children:"Loading..."})]})}):r?(0,s.jsxs)(u.Z,{children:[(0,s.jsxs)(o(),{children:[s.jsx("title",{children:"Add New Service | Ocean Soul Sparkles Admin"}),s.jsx("meta",{name:"description",content:"Create a new service offering"})]}),(0,s.jsxs)("div",{className:h().serviceFormContainer,children:[(0,s.jsxs)("header",{className:h().header,children:[(0,s.jsxs)("div",{className:h().breadcrumb,children:[s.jsx(d(),{href:"/admin/services",children:"Services"}),s.jsx("span",{children:"/"}),s.jsx("span",{children:"New Service"})]}),s.jsx(d(),{href:"/admin/services",className:h().backButton,children:"← Back to Services"})]}),(0,s.jsxs)("div",{className:h().formContent,children:[s.jsx("h1",{children:"Add New Service"}),(0,s.jsxs)("form",{onSubmit:S,className:h().form,children:[v.submit&&s.jsx("div",{className:h().errorAlert,children:v.submit}),(0,s.jsxs)("div",{className:h().formGrid,children:[(0,s.jsxs)("div",{className:h().formGroup,children:[s.jsx("label",{htmlFor:"name",children:"Service Name *"}),s.jsx("input",{type:"text",id:"name",name:"name",value:l.name,onChange:x,className:v.name?h().inputError:"",placeholder:"Enter service name",required:!0}),v.name&&s.jsx("span",{className:h().errorText,children:v.name})]}),(0,s.jsxs)("div",{className:h().formGroup,children:[s.jsx("label",{htmlFor:"category",children:"Category *"}),s.jsx("select",{id:"category",name:"category",value:l.category,onChange:x,className:v.category?h().inputError:"",required:!0,children:["Hair Braiding","Protective Styles","Hair Care","Styling","Consultation","Special Events","Maintenance"].map(e=>s.jsx("option",{value:e,children:e},e))}),v.category&&s.jsx("span",{className:h().errorText,children:v.category})]}),(0,s.jsxs)("div",{className:h().formGroup,children:[s.jsx("label",{htmlFor:"duration",children:"Duration (minutes)"}),s.jsx("input",{type:"number",id:"duration",name:"duration",value:l.duration,onChange:x,className:v.duration?h().inputError:"",placeholder:"e.g., 120",min:"1"}),v.duration&&s.jsx("span",{className:h().errorText,children:v.duration})]}),(0,s.jsxs)("div",{className:h().formGroup,children:[s.jsx("label",{htmlFor:"price",children:"Price (AUD)"}),s.jsx("input",{type:"number",id:"price",name:"price",value:l.price,onChange:x,className:v.price?h().inputError:"",placeholder:"e.g., 150.00",min:"0",step:"0.01"}),v.price&&s.jsx("span",{className:h().errorText,children:v.price})]}),(0,s.jsxs)("div",{className:h().formGroup,children:[s.jsx("label",{htmlFor:"status",children:"Status"}),(0,s.jsxs)("select",{id:"status",name:"status",value:l.status,onChange:x,children:[s.jsx("option",{value:"active",children:"Active"}),s.jsx("option",{value:"inactive",children:"Inactive"}),s.jsx("option",{value:"draft",children:"Draft"})]})]})]}),(0,s.jsxs)("div",{className:h().formGroup,children:[s.jsx("label",{htmlFor:"description",children:"Description"}),s.jsx("textarea",{id:"description",name:"description",value:l.description,onChange:x,rows:"4",placeholder:"Describe the service, what's included, any special requirements..."})]}),(0,s.jsxs)("div",{className:h().visibilitySection,children:[s.jsx("h3",{children:"Visibility Options"}),(0,s.jsxs)("div",{className:h().checkboxGroup,children:[(0,s.jsxs)("label",{className:h().checkboxLabel,children:[s.jsx("input",{type:"checkbox",name:"visible_on_public",checked:l.visible_on_public,onChange:x}),s.jsx("span",{children:"Show on public website"})]}),(0,s.jsxs)("label",{className:h().checkboxLabel,children:[s.jsx("input",{type:"checkbox",name:"visible_on_pos",checked:l.visible_on_pos,onChange:x}),s.jsx("span",{children:"Available in POS system"})]}),(0,s.jsxs)("label",{className:h().checkboxLabel,children:[s.jsx("input",{type:"checkbox",name:"visible_on_events",checked:l.visible_on_events,onChange:x}),s.jsx("span",{children:"Available for events"})]})]})]}),(0,s.jsxs)("div",{className:h().formActions,children:[s.jsx("button",{type:"submit",className:h().submitButton,disabled:t,children:t?"Creating...":"Create Service"}),s.jsx(d(),{href:"/admin/services",className:h().cancelButton,children:"Cancel"})]})]})]})]})]}):null}u=(v.then?(await v)():v)[0],t()}catch(e){t(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var r=require("../../../webpack-runtime.js");r.C(e);var i=e=>r(r.s=e),t=r.X(0,[2899,6212,1664,7441],()=>i(2867));module.exports=t})();