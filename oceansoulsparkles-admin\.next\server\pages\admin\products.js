(()=>{var e={};e.id=8209,e.ids=[8209,660],e.modules={6806:e=>{e.exports={exportButton:"ExportButton_exportButton__utvly",exportBtn:"ExportButton_exportBtn__tIopM",exporting:"ExportButton_exporting__sXhBs",spinner:"ExportButton_spinner__9VMfk",spin:"ExportButton_spin__rvyvr",dropdownArrow:"ExportButton_dropdownArrow__ukQOV",exportDropdown:"ExportButton_exportDropdown__Rv4p1",slideDown:"ExportButton_slideDown__Cf7OP",dropdownHeader:"ExportButton_dropdownHeader__ZrlI_",dropdownItem:"ExportButton_dropdownItem__fkbTm",disabled:"ExportButton_disabled__D8pyR",formatIcon:"ExportButton_formatIcon__jrB0p",formatInfo:"ExportButton_formatInfo__CpXUd",formatName:"ExportButton_formatName__yKvvD",formatDesc:"ExportButton_formatDesc__vDwii",success:"ExportButton_success__suWHu",successPulse:"ExportButton_successPulse__zvjZh"}},7139:e=>{e.exports={productsContainer:"Products_productsContainer__A_eJy",header:"Products_header__MesnD",title:"Products_title__JVkKQ",headerActions:"Products_headerActions__GgUlt",newProductBtn:"Products_newProductBtn__B6HYU",filtersSection:"Products_filtersSection__PVoFG",searchBar:"Products_searchBar__TQ_b8",searchInput:"Products_searchInput__IpuX4",filters:"Products_filters__4UXYp",filterSelect:"Products_filterSelect__Yj55b",sortSelect:"Products_sortSelect__bw2db",statsBar:"Products_statsBar__WO_hI",stat:"Products_stat__Ervdg",statNumber:"Products_statNumber__YW9mB",statLabel:"Products_statLabel__1hE37",productsGrid:"Products_productsGrid__MUvv7",productCard:"Products_productCard__NnRkS",productImage:"Products_productImage__gvfA_",placeholderImage:"Products_placeholderImage__Oqni0",productInfo:"Products_productInfo__8ul2C",productHeader:"Products_productHeader__PdZEO",productName:"Products_productName__PiTku",statusBadge:"Products_statusBadge__rF99R",active:"Products_active__Ksddh",inactive:"Products_inactive__P5xy_",productDetails:"Products_productDetails__IsBKI",productMeta:"Products_productMeta__WJ2VG",sku:"Products_sku__UiXDl",category:"Products_category__Z3_8B",priceStock:"Products_priceStock__cb6a5",pricing:"Products_pricing__il8UX",price:"Products_price__Vkpvv",salePrice:"Products_salePrice__F6lrv",stock:"Products_stock__n5hZ0",stockStatus:"Products_stockStatus__Xv9LX",inStock:"Products_inStock__S_LsJ",lowStock:"Products_lowStock__TNx9r",outOfStock:"Products_outOfStock__sdTDi",stockCount:"Products_stockCount__mIggZ",description:"Products_description__s2ui2",productActions:"Products_productActions__mAlgI",viewBtn:"Products_viewBtn__hU_0X",editBtn:"Products_editBtn__nrfcL",emptyState:"Products_emptyState__VF4ic",addFirstBtn:"Products_addFirstBtn__vzLL1",loadingContainer:"Products_loadingContainer__ExoGg",errorContainer:"Products_errorContainer__9B_vB",loadingSpinner:"Products_loadingSpinner__M9t_l",spin:"Products_spin__plmlc",retryButton:"Products_retryButton___SoyA"}},9206:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{config:()=>x,default:()=>p,getServerSideProps:()=>h,getStaticPaths:()=>_,getStaticProps:()=>m,reportWebVitals:()=>g,routeModule:()=>j,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>k});var a=s(7093),o=s(5244),c=s(1323),n=s(2899),l=s.n(n),i=s(6814),d=s(5924),u=e([i,d]);[i,d]=u.then?(await u)():u;let p=(0,c.l)(d,"default"),m=(0,c.l)(d,"getStaticProps"),_=(0,c.l)(d,"getStaticPaths"),h=(0,c.l)(d,"getServerSideProps"),x=(0,c.l)(d,"config"),g=(0,c.l)(d,"reportWebVitals"),k=(0,c.l)(d,"unstable_getStaticProps"),v=(0,c.l)(d,"unstable_getStaticPaths"),b=(0,c.l)(d,"unstable_getStaticParams"),f=(0,c.l)(d,"unstable_getServerProps"),N=(0,c.l)(d,"unstable_getServerSideProps"),j=new a.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/admin/products",pathname:"/admin/products",bundlePath:"",filename:""},components:{App:i.default,Document:l()},userland:d});r()}catch(e){r(e)}})},5179:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var r=s(997),a=s(6689),o=s(8370),c=s(6806),n=s.n(c);function l({data:e,type:t,className:c="",disabled:l=!1,options:i={},onExportStart:d,onExportComplete:u,onExportError:p}){let[m,_]=(0,a.useState)(!1),[h,x]=(0,a.useState)(!1),g=(0,a.useRef)(null),k=async(r="csv")=>{if(!e||0===e.length){p?.(Error("No data to export"));return}_(!0),x(!1),d?.();try{if("csv"===r)switch(t){case"bookings":(0,o.FC)(e,i);break;case"services":(0,o.r8)(e,i);break;case"customers":(0,o.Aq)(e,i);break;case"products":(0,o.yA)(e,i);break;case"inventory":(0,o.sg)(e,i);break;case"custom":let{exportToCSV:a}=await Promise.resolve().then(s.bind(s,8370));a(e,i);break;default:throw Error(`Unsupported export type: ${t}`)}else throw Error(`Export format ${r} not yet supported`);u?.()}catch(e){console.error("Export error:",e),p?.(e instanceof Error?e:Error("Export failed"))}finally{_(!1)}};return(0,r.jsxs)("div",{className:`${n().exportButton} ${c}`,ref:g,children:[(0,r.jsxs)("button",{onClick:()=>x(!h),disabled:l||m||0===e.length,className:`${n().exportBtn} ${m?n().exporting:""}`,title:l||0===e.length?"No data available to export":`Export ${e.length} ${t} records`,children:[m&&r.jsx("div",{className:n().spinner}),m?"Exporting...":0===e.length?"No Data":`📥 Export (${e.length})`,!m&&e.length>0&&r.jsx("span",{className:n().dropdownArrow,children:"▼"})]}),h&&e.length>0&&(0,r.jsxs)("div",{className:n().exportDropdown,children:[r.jsx("div",{className:n().dropdownHeader,children:r.jsx("span",{children:"Export Format"})}),(0,r.jsxs)("button",{onClick:()=>k("csv"),className:n().dropdownItem,disabled:m,children:[r.jsx("span",{className:n().formatIcon,children:"\uD83D\uDCC4"}),(0,r.jsxs)("div",{className:n().formatInfo,children:[r.jsx("div",{className:n().formatName,children:"CSV File"}),r.jsx("div",{className:n().formatDesc,children:"Comma-separated values"})]})]}),(0,r.jsxs)("button",{onClick:()=>k("excel"),className:`${n().dropdownItem} ${n().disabled}`,disabled:!0,title:"Excel export coming soon",children:[r.jsx("span",{className:n().formatIcon,children:"\uD83D\uDCCA"}),(0,r.jsxs)("div",{className:n().formatInfo,children:[r.jsx("div",{className:n().formatName,children:"Excel File"}),r.jsx("div",{className:n().formatDesc,children:"Coming soon"})]})]}),(0,r.jsxs)("button",{onClick:()=>k("pdf"),className:`${n().dropdownItem} ${n().disabled}`,disabled:!0,title:"PDF export coming soon",children:[r.jsx("span",{className:n().formatIcon,children:"\uD83D\uDCCB"}),(0,r.jsxs)("div",{className:n().formatInfo,children:[r.jsx("div",{className:n().formatName,children:"PDF Report"}),r.jsx("div",{className:n().formatDesc,children:"Coming soon"})]})]})]})]})}let i=({data:e,type:t,className:s="",...a})=>r.jsx(l,{data:e,type:t,className:s,onExportError:e=>{console.error("Export error:",e),alert(`Export failed: ${e.message}`)},...a})},8370:(e,t,s)=>{"use strict";function r(e,t={}){let{filename:s="export",columns:r,includeTimestamp:a=!0,dateFormat:o="short"}=t;if(!e||0===e.length)throw Error("No data to export");let c=r||Object.keys(e[0]).map(e=>({key:e,label:e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())})),n=[c.map(e=>e.label),...e.map(e=>c.map(t=>{let s=t.key.split(".").reduce((e,t)=>e?.[t],e);return t.formatter?t.formatter(s,e):null==s?"":"boolean"==typeof s?s?"Yes":"No":s instanceof Date||"string"==typeof s&&/^\d{4}-\d{2}-\d{2}/.test(s)&&!isNaN(Date.parse(s))?d(s):String(s)}))].map(e=>e.map(e=>`"${String(e||"").replace(/"/g,'""')}"`).join(",")).join("\n"),l=a?`-${new Date().toISOString().split("T")[0]}`:"";(function(e,t,s){let r=new Blob([e],{type:s}),a=window.URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download=t,document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(a),document.body.removeChild(o)})(n,`${s}${l}.csv`,"text/csv")}function a(e,t={}){r(e,{filename:"bookings",columns:[{key:"customer_name",label:"Customer Name"},{key:"customer_email",label:"Customer Email"},{key:"customer_phone",label:"Customer Phone"},{key:"service_name",label:"Service"},{key:"artist_name",label:"Artist"},{key:"booking_date",label:"Date",formatter:e=>i(e)},{key:"booking_time",label:"Time"},{key:"status",label:"Status",formatter:e=>e?.toUpperCase()||"UNKNOWN"},{key:"total_amount",label:"Amount",formatter:e=>u(e)},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function o(e,t={}){r(e,{filename:"services",columns:[{key:"name",label:"Service Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"base_price",label:"Base Price",formatter:e=>u(e)},{key:"duration",label:"Duration (min)"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"total_bookings",label:"Total Bookings"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function c(e,t={}){r(e,{filename:"customers",columns:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"email",label:"Email"},{key:"phone",label:"Phone"},{key:"total_bookings",label:"Total Bookings"},{key:"notes",label:"Notes"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function n(e,t={}){r(e,{filename:"products",columns:[{key:"name",label:"Product Name"},{key:"category",label:"Category"},{key:"description",label:"Description"},{key:"price",label:"Price",formatter:e=>u(e)},{key:"stock_quantity",label:"Stock"},{key:"is_active",label:"Status",formatter:e=>e?"ACTIVE":"INACTIVE"},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function l(e,t={}){r(e,{filename:"inventory",columns:[{key:"name",label:"Item Name"},{key:"category",label:"Category"},{key:"current_stock",label:"Current Stock"},{key:"minimum_stock",label:"Minimum Stock"},{key:"unit_cost",label:"Unit Cost",formatter:e=>u(e)},{key:"supplier_name",label:"Supplier"},{key:"last_restocked",label:"Last Restocked",formatter:e=>i(e)},{key:"created_at",label:"Created",formatter:e=>d(e)}],...t})}function i(e){return e?new Date(e).toLocaleDateString("en-AU"):""}function d(e){if(!e)return"";let t=new Date(e);return t.toLocaleDateString("en-AU")+" "+t.toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit"})}function u(e){return!e||isNaN(e)?"$0.00":new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(Number(e))}s.d(t,{Aq:()=>c,FC:()=>a,exportToCSV:()=>r,r8:()=>o,sg:()=>l,yA:()=>n})},5924:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{default:()=>x});var a=s(997),o=s(6689),c=s(968),n=s.n(c),l=s(1664),i=s.n(l),d=s(8568),u=s(4845),p=s(5179),m=s(7139),_=s.n(m),h=e([u]);function x(){let{user:e,loading:t}=(0,d.a)(),[s,r]=(0,o.useState)(!0),[c,l]=(0,o.useState)([]),[m,h]=(0,o.useState)([]),[x,g]=(0,o.useState)(""),[k,v]=(0,o.useState)(""),[b,f]=(0,o.useState)(""),[N,j]=(0,o.useState)("name"),[y,S]=(0,o.useState)(null),P=async()=>{try{r(!0);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/products",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to load products");let s=await t.json();l(s.products||[])}catch(e){console.error("Error loading products:",e),S(e.message)}finally{r(!1)}},C=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e||0),w=e=>e.stock<=0?{status:"out_of_stock",label:"Out of Stock",class:"outOfStock"}:e.stock<=(e.low_stock_threshold||5)?{status:"low_stock",label:"Low Stock",class:"lowStock"}:{status:"in_stock",label:"In Stock",class:"inStock"},B=[...new Set(c.map(e=>e.category_name).filter(Boolean))];return t||s?a.jsx(u.Z,{children:(0,a.jsxs)("div",{className:_().loadingContainer,children:[a.jsx("div",{className:_().loadingSpinner}),a.jsx("p",{children:"Loading products..."})]})}):y?a.jsx(u.Z,{children:(0,a.jsxs)("div",{className:_().errorContainer,children:[a.jsx("h2",{children:"Error Loading Products"}),a.jsx("p",{children:y}),a.jsx("button",{onClick:P,className:_().retryButton,children:"Try Again"})]})}):(0,a.jsxs)(u.Z,{children:[(0,a.jsxs)(n(),{children:[a.jsx("title",{children:"Products Management | Ocean Soul Sparkles Admin"}),a.jsx("meta",{name:"description",content:"Manage product catalog and inventory"})]}),(0,a.jsxs)("div",{className:_().productsContainer,children:[(0,a.jsxs)("header",{className:_().header,children:[a.jsx("h1",{className:_().title,children:"Products Management"}),(0,a.jsxs)("div",{className:_().headerActions,children:[a.jsx(p.F,{data:m,type:"products",className:_().exportBtn}),a.jsx(i(),{href:"/admin/products/new",className:_().newProductBtn,children:"+ Add Product"})]})]}),(0,a.jsxs)("div",{className:_().filtersSection,children:[a.jsx("div",{className:_().searchBar,children:a.jsx("input",{type:"text",placeholder:"Search products by name, SKU, or description...",value:x,onChange:e=>g(e.target.value),className:_().searchInput})}),(0,a.jsxs)("div",{className:_().filters,children:[(0,a.jsxs)("select",{value:k,onChange:e=>v(e.target.value),className:_().filterSelect,children:[a.jsx("option",{value:"",children:"All Categories"}),B.map(e=>a.jsx("option",{value:e,children:e},e))]}),(0,a.jsxs)("select",{value:b,onChange:e=>f(e.target.value),className:_().filterSelect,children:[a.jsx("option",{value:"",children:"All Status"}),a.jsx("option",{value:"active",children:"Active"}),a.jsx("option",{value:"inactive",children:"Inactive"}),a.jsx("option",{value:"low_stock",children:"Low Stock"})]}),(0,a.jsxs)("select",{value:N,onChange:e=>j(e.target.value),className:_().sortSelect,children:[a.jsx("option",{value:"name",children:"Sort by Name"}),a.jsx("option",{value:"price",children:"Sort by Price"}),a.jsx("option",{value:"stock",children:"Sort by Stock"}),a.jsx("option",{value:"category",children:"Sort by Category"})]})]})]}),(0,a.jsxs)("div",{className:_().statsBar,children:[(0,a.jsxs)("div",{className:_().stat,children:[a.jsx("span",{className:_().statNumber,children:c.length}),a.jsx("span",{className:_().statLabel,children:"Total Products"})]}),(0,a.jsxs)("div",{className:_().stat,children:[a.jsx("span",{className:_().statNumber,children:c.filter(e=>e.is_active).length}),a.jsx("span",{className:_().statLabel,children:"Active"})]}),(0,a.jsxs)("div",{className:_().stat,children:[a.jsx("span",{className:_().statNumber,children:c.filter(e=>e.stock<=(e.low_stock_threshold||5)).length}),a.jsx("span",{className:_().statLabel,children:"Low Stock"})]}),(0,a.jsxs)("div",{className:_().stat,children:[a.jsx("span",{className:_().statNumber,children:C(c.reduce((e,t)=>e+(t.price||0)*(t.stock||0),0))}),a.jsx("span",{className:_().statLabel,children:"Total Value"})]})]}),a.jsx("div",{className:_().productsGrid,children:0===m.length?(0,a.jsxs)("div",{className:_().emptyState,children:[a.jsx("h3",{children:"No products found"}),a.jsx("p",{children:x||k||b?"Try adjusting your filters or search terms.":"Start by adding your first product."}),!x&&!k&&!b&&a.jsx(i(),{href:"/admin/products/new",className:_().addFirstBtn,children:"Add Your First Product"})]}):m.map(e=>{let t=w(e);return(0,a.jsxs)("div",{className:_().productCard,children:[a.jsx("div",{className:_().productImage,children:e.image_url?a.jsx("img",{src:e.image_url,alt:e.name}):a.jsx("div",{className:_().placeholderImage,children:a.jsx("span",{children:"\uD83D\uDCE6"})})}),(0,a.jsxs)("div",{className:_().productInfo,children:[(0,a.jsxs)("div",{className:_().productHeader,children:[a.jsx("h3",{className:_().productName,children:e.name}),a.jsx("span",{className:`${_().statusBadge} ${_()[e.is_active?"active":"inactive"]}`,children:e.is_active?"Active":"Inactive"})]}),(0,a.jsxs)("div",{className:_().productDetails,children:[(0,a.jsxs)("div",{className:_().productMeta,children:[(0,a.jsxs)("span",{className:_().sku,children:["SKU: ",e.sku||"N/A"]}),a.jsx("span",{className:_().category,children:e.category_name||"Uncategorized"})]}),(0,a.jsxs)("div",{className:_().priceStock,children:[(0,a.jsxs)("div",{className:_().pricing,children:[a.jsx("span",{className:_().price,children:C(e.price)}),e.sale_price&&e.sale_price<e.price&&a.jsx("span",{className:_().salePrice,children:C(e.sale_price)})]}),(0,a.jsxs)("div",{className:_().stock,children:[a.jsx("span",{className:`${_().stockStatus} ${_()[t.class]}`,children:t.label}),(0,a.jsxs)("span",{className:_().stockCount,children:[e.stock||0," units"]})]})]}),e.description&&a.jsx("p",{className:_().description,children:e.description.length>100?`${e.description.substring(0,100)}...`:e.description})]}),(0,a.jsxs)("div",{className:_().productActions,children:[a.jsx(i(),{href:`/admin/products/${e.id}`,className:_().viewBtn,children:"View Details"}),a.jsx(i(),{href:`/admin/products/${e.id}/edit`,className:_().editBtn,children:"Edit"})]})]})]},e.id)})})]})]})}u=(h.then?(await h)():h)[0],r()}catch(e){r(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[2899,6212,1664,7441],()=>s(9206));module.exports=r})();