/* Artists Management Styles */

.artistsContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.newArtistBtn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.newArtistBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.backButton {
  background: linear-gradient(135deg, #64748b, #475569);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(100, 116, 139, 0.4);
}

.controlsPanel {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  flex-wrap: wrap;
  gap: 1rem;
}

.searchSection {
  flex: 1;
  min-width: 300px;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.specializationFilter, .statusFilter, .sortSelect {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

.artistsContent {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.statsCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.statCard h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statCard .statValue {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.emptyState {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 3rem;
  border-radius: 12px;
  text-align: center;
  color: #64748b;
  font-size: 1.1rem;
}

.artistsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.artistCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.artistCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.artistHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.artistInfo h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.artistInfo p {
  color: #64748b;
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
}

.badges {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
}

.statusBadge, .availabilityBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusActive {
  background: #d1fae5;
  color: #065f46;
}

.statusInactive {
  background: #fee2e2;
  color: #991b1b;
}

.statusDefault {
  background: #f1f5f9;
  color: #475569;
}

.availabilityAvailable {
  background: #d1fae5;
  color: #065f46;
}

.availabilityBusy {
  background: #fef3c7;
  color: #92400e;
}

.availabilityUnavailable {
  background: #fee2e2;
  color: #991b1b;
}

.availabilityDefault {
  background: #f1f5f9;
  color: #475569;
}

.specializations {
  margin-bottom: 1rem;
}

.specializations h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #475569;
  margin: 0 0 0.5rem 0;
}

.specializationTags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.specializationTag {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.artistStats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.statItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statLabel {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.artistStats .statValue {
  color: #1e293b;
  font-weight: 600;
  font-size: 0.875rem;
}

.artistActions {
  display: flex;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.viewBtn, .toggleBtn {
  flex: 1;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.viewBtn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  text-decoration: none;
}

.toggleBtn.activate {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.toggleBtn.deactivate {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.viewBtn:hover, .toggleBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .artistsGrid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .controlsPanel {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
  }

  .searchSection {
    min-width: auto;
  }

  .filters {
    justify-content: center;
    flex-wrap: wrap;
  }

  .statsCards {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  }

  .artistsGrid {
    grid-template-columns: 1fr;
  }

  .artistHeader {
    flex-direction: column;
    gap: 1rem;
  }

  .badges {
    flex-direction: row;
    align-items: flex-start;
  }

  .artistStats {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .artistActions {
    flex-direction: column;
  }
}
