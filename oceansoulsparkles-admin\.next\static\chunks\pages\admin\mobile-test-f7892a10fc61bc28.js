(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[302],{6890:function(r,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/mobile-test",function(){return n(3112)}])},3112:function(r,e,n){"use strict";n.r(e),n.d(e,{default:function(){return c}}),function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}(),function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}();var o=n(9008),t=n.n(o),i=n(99),d=n(6026);function c(){let{user:r}=(0,d.a)(),[e,n]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(!1),[o,c]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())({width:0,height:0});return Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(()=>{let r=()=>{n(window.innerWidth<=768),c({width:window.innerWidth,height:window.innerHeight})};return r(),window.addEventListener("resize",r),()=>window.removeEventListener("resize",r)},[]),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(i.Z,{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(t(),{children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("title",{children:"Mobile Test - Ocean Soul Sparkles Admin"})}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{padding:"20px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h1",{children:"Mobile Responsiveness Test"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{background:"#f8f9fa",padding:"20px",borderRadius:"8px",marginBottom:"20px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{children:"Screen Information"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{children:"Screen Size:"})," ",o.width," x ",o.height]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{children:"Is Mobile:"})," ",e?"Yes":"No"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("strong",{children:"User Agent:"})," ",window.navigator.userAgent]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{background:"#e3f2fd",padding:"20px",borderRadius:"8px",marginBottom:"20px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{children:"Mobile Components Status"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{display:"grid",gap:"10px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"✅ PWA Manager: Integrated"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:["✅ Mobile Bottom Navigation: ",e?"Visible":"Hidden (Desktop)"]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"✅ Mobile Hamburger Menu: Available"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"✅ Responsive Layout: Active"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"✅ Touch Gestures: Enabled"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"✅ Service Worker: Registered"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{background:"#f3e5f5",padding:"20px",borderRadius:"8px",marginBottom:"20px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{children:"Test Elements"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{marginBottom:"15px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:"Touch-Friendly Buttons"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{display:"flex",gap:"10px",flexWrap:"wrap"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",minWidth:"44px",background:"#16213e",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},children:"Primary"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",minWidth:"44px",background:"#4CAF50",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},children:"Success"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{style:{padding:"12px 24px",fontSize:"16px",minHeight:"44px",minWidth:"44px",background:"#f44336",color:"white",border:"none",borderRadius:"8px",cursor:"pointer"},children:"Danger"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{marginBottom:"15px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:"Responsive Grid"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{display:"grid",gridTemplateColumns:e?"1fr":"repeat(auto-fit, minmax(200px, 1fr))",gap:"15px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{background:"#fff",padding:"15px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h4",{children:"Card 1"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"This card adapts to mobile screens"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{background:"#fff",padding:"15px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h4",{children:"Card 2"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Responsive design in action"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{background:"#fff",padding:"15px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h4",{children:"Card 3"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Mobile-first approach"})]})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{marginBottom:"15px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:"Form Elements"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{display:"flex",flexDirection:"column",gap:"10px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("input",{type:"text",placeholder:"Touch-friendly input",style:{padding:"12px",fontSize:"16px",border:"2px solid #e0e0e0",borderRadius:"8px",minHeight:"44px"}}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("select",{style:{padding:"12px",fontSize:"16px",border:"2px solid #e0e0e0",borderRadius:"8px",minHeight:"44px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{children:"Select an option"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{children:"Option 1"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("option",{children:"Option 2"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("textarea",{placeholder:"Touch-friendly textarea",style:{padding:"12px",fontSize:"16px",border:"2px solid #e0e0e0",borderRadius:"8px",minHeight:"100px",resize:"vertical"}})]})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{background:"#fff3e0",padding:"20px",borderRadius:"8px",marginBottom:"20px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{children:"Mobile Navigation Test"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:e?"✅ Mobile bottom navigation should be visible at the bottom of the screen":"⚠️ Switch to mobile view (width ≤ 768px) to see mobile navigation"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"The hamburger menu in the header should open the mobile menu when tapped."})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{background:"#e8f5e8",padding:"20px",borderRadius:"8px",marginBottom:"80px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{children:"PWA Features Test"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{style:{display:"grid",gap:"10px"},children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"\uD83D\uDCF1 Install prompt should appear after 5 seconds"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"\uD83D\uDD04 Service worker caching active"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"\uD83D\uDCF6 Offline mode detection"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"\uD83D\uDD14 Push notifications available"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{children:"\uD83D\uDCBE Background sync enabled"})]})]})]})]})}}},function(r){r.O(0,[736,592,888,179],function(){return r(r.s=6890)}),_N_E=r.O()}]);