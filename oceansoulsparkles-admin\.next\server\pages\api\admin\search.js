"use strict";(()=>{var e={};e.id=9307,e.ids=[9307],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},1059:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>m,routeModule:()=>_});var i={};r.r(i),r.d(i,{default:()=>c});var a=r(1802),s=r(7153),o=r(8781),n=r(7474),l=r(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,d=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function c(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!r)return t.status(401).json({error:"No authentication token"});let i=await (0,n.Wg)(r);if(!i.valid||!i.user)return t.status(401).json({error:"Invalid authentication"});let a=i.user,{q:s,type:o,limit:l=10}=e.query;if(!s||"string"!=typeof s)return t.status(400).json({error:"Search query is required"});let u=s.trim().toLowerCase(),c=Math.min(parseInt(l)||10,50),m=[];if((!o||"customer"===o)&&("Admin"===a.role||"DEV"===a.role)){let{data:e}=await d.from("customers").select("id, first_name, last_name, email, phone, created_at").or(`first_name.ilike.%${u}%,last_name.ilike.%${u}%,email.ilike.%${u}%,phone.ilike.%${u}%`).limit(c);e&&e.forEach(e=>{m.push({id:e.id,type:"customer",title:`${e.first_name} ${e.last_name}`,subtitle:e.email,description:e.phone,url:`/admin/customers/${e.id}`,metadata:{created_at:e.created_at,phone:e.phone}})})}if(!o||"booking"===o){let e=d.from("bookings").select(`
          id,
          booking_date,
          start_time,
          status,
          total_amount,
          customers (first_name, last_name, email),
          services (name),
          artist_profiles!assigned_artist_id (artist_name, display_name)
        `).limit(c);("Artist"===a.role||"Braider"===a.role)&&(e=e.eq("assigned_artist_id",a.id));let{data:t}=await e;t&&t.forEach(e=>{let t=Array.isArray(e.customers)?e.customers[0]:e.customers,r=Array.isArray(e.services)?e.services[0]:e.services,i=Array.isArray(e.artist_profiles)?e.artist_profiles[0]:e.artist_profiles,a=t?`${t.first_name} ${t.last_name}`:"Unknown Customer",s=r?.name||"Unknown Service",o=i?.artist_name||i?.display_name||"Unassigned";`${a} ${s} ${o} ${e.status}`.toLowerCase().includes(u)&&m.push({id:e.id,type:"booking",title:`${a} - ${s}`,subtitle:`${e.booking_date} • ${o}`,description:`Status: ${e.status} • $${e.total_amount||0}`,url:`/admin/bookings/${e.id}`,metadata:{booking_date:e.booking_date,status:e.status,total_amount:e.total_amount,artist_name:o}})})}if((!o||"service"===o)&&("Admin"===a.role||"DEV"===a.role)){let{data:e}=await d.from("services").select("id, name, description, category, base_price, duration, is_active").or(`name.ilike.%${u}%,description.ilike.%${u}%,category.ilike.%${u}%`).limit(c);e&&e.forEach(e=>{m.push({id:e.id,type:"service",title:e.name,subtitle:e.category,description:`$${e.base_price||0} • ${e.duration||0} min • ${e.is_active?"Active":"Inactive"}`,url:`/admin/services/${e.id}`,metadata:{category:e.category,base_price:e.base_price,duration:e.duration,is_active:e.is_active}})})}return m.sort((e,t)=>{let r=e.title.toLowerCase().includes(u)?1:0;return(t.title.toLowerCase().includes(u)?1:0)-r}),t.status(200).json({query:u,results:m.slice(0,c),total:m.length,types:{customer:m.filter(e=>"customer"===e.type).length,booking:m.filter(e=>"booking"===e.type).length,service:m.filter(e=>"service"===e.type).length}})}catch(e){return console.error("Search API error:",e),t.status(500).json({error:"Internal server error"})}}let m=(0,o.l)(i,"default"),p=(0,o.l)(i,"config"),_=new a.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/admin/search",pathname:"/api/admin/search",bundlePath:"",filename:""},userland:i})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[2805],()=>r(1059));module.exports=i})();