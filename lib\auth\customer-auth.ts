/**
 * Ocean Soul Sparkles - Customer Authentication Library
 * Handles customer portal authentication, sessions, and security
 */

import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export interface CustomerUser {
  id: string;
  customerId: string;
  email: string;
  firstName: string;
  lastName: string;
  emailVerified: boolean;
  isActive: boolean;
  lastActivity: number;
}

export interface CustomerAuthResult {
  valid: boolean;
  user?: CustomerUser;
  error?: string;
}

export interface CustomerLoginResult {
  success: boolean;
  token?: string;
  user?: CustomerUser;
  error?: string;
  requiresEmailVerification?: boolean;
}

/**
 * Verify customer authentication token
 */
export async function verifyCustomerToken(token: string): Promise<CustomerAuthResult> {
  try {
    const jwtSecret = process.env.JWT_SECRET || 'placeholder-secret';
    const decoded = jwt.verify(token, jwtSecret) as any;
    
    // Get customer from database with latest info
    const { data: customerAuth, error } = await supabase
      .from('customer_auth')
      .select(`
        id,
        customer_id,
        email,
        email_verified,
        is_active,
        customers (
          id,
          first_name,
          last_name
        )
      `)
      .eq('id', decoded.customerAuthId)
      .eq('is_active', true)
      .single();

    if (error || !customerAuth) {
      return { valid: false, error: 'Customer not found or inactive' };
    }

    const customer = customerAuth.customers as any;
    if (!customer) {
      return { valid: false, error: 'Customer profile not found' };
    }

    return {
      valid: true,
      user: {
        id: customerAuth.id,
        customerId: customerAuth.customer_id,
        email: customerAuth.email,
        firstName: customer.first_name,
        lastName: customer.last_name,
        emailVerified: customerAuth.email_verified,
        isActive: customerAuth.is_active,
        lastActivity: Date.now()
      }
    };
  } catch (error) {
    console.error('Customer token verification error:', error);
    return { valid: false, error: 'Invalid token' };
  }
}

/**
 * Customer login function
 */
export async function customerLogin(email: string, password: string, ip: string): Promise<CustomerLoginResult> {
  try {
    // Get customer auth record
    const { data: customerAuth, error } = await supabase
      .from('customer_auth')
      .select(`
        id,
        customer_id,
        email,
        password_hash,
        email_verified,
        is_active,
        login_attempts,
        locked_until,
        customers (
          id,
          first_name,
          last_name
        )
      `)
      .eq('email', email.toLowerCase())
      .single();

    if (error || !customerAuth) {
      await recordCustomerFailedAttempt(email, ip, 'Customer not found');
      return { success: false, error: 'Invalid credentials' };
    }

    // Check if account is active
    if (!customerAuth.is_active) {
      return { success: false, error: 'Account is deactivated' };
    }

    // Check if account is locked
    if (customerAuth.locked_until && new Date(customerAuth.locked_until) > new Date()) {
      return { success: false, error: 'Account is temporarily locked. Please try again later.' };
    }

    // Check login attempts
    if (customerAuth.login_attempts >= 5) {
      // Lock account for 30 minutes
      await supabase
        .from('customer_auth')
        .update({
          locked_until: new Date(Date.now() + 30 * 60 * 1000).toISOString()
        })
        .eq('id', customerAuth.id);
      
      return { success: false, error: 'Too many failed attempts. Account locked for 30 minutes.' };
    }

    // Verify password
    const passwordValid = await bcrypt.compare(password, customerAuth.password_hash);
    if (!passwordValid) {
      await recordCustomerFailedAttempt(email, ip, 'Invalid password');
      return { success: false, error: 'Invalid credentials' };
    }

    // Clear failed attempts on successful password verification
    await supabase
      .from('customer_auth')
      .update({
        login_attempts: 0,
        locked_until: null,
        last_login_at: new Date().toISOString()
      })
      .eq('id', customerAuth.id);

    const customer = customerAuth.customers as any;

    // Check if email verification is required
    if (!customerAuth.email_verified) {
      return {
        success: false,
        requiresEmailVerification: true,
        error: 'Please verify your email address before logging in'
      };
    }

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'placeholder-secret';
    const token = jwt.sign(
      {
        customerAuthId: customerAuth.id,
        customerId: customerAuth.customer_id,
        email: customerAuth.email
      },
      jwtSecret,
      { expiresIn: '24h' }
    );

    // Create session record
    const sessionToken = crypto.randomBytes(32).toString('hex');
    await supabase
      .from('customer_sessions')
      .insert({
        customer_id: customerAuth.customer_id,
        session_token: sessionToken,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        ip_address: ip
      });

    // Log activity
    await logCustomerActivity(customerAuth.customer_id, 'login', 'Customer logged in', ip);

    return {
      success: true,
      token,
      user: {
        id: customerAuth.id,
        customerId: customerAuth.customer_id,
        email: customerAuth.email,
        firstName: customer.first_name,
        lastName: customer.last_name,
        emailVerified: customerAuth.email_verified,
        isActive: customerAuth.is_active,
        lastActivity: Date.now()
      }
    };
  } catch (error) {
    console.error('Customer login error:', error);
    return { success: false, error: 'Login failed' };
  }
}

/**
 * Customer registration function
 */
export async function customerRegister(
  firstName: string,
  lastName: string,
  email: string,
  password: string,
  phone?: string,
  ip?: string
): Promise<CustomerLoginResult> {
  try {
    // Check if customer already exists
    const { data: existingAuth } = await supabase
      .from('customer_auth')
      .select('id')
      .eq('email', email.toLowerCase())
      .single();

    if (existingAuth) {
      return { success: false, error: 'An account with this email already exists' };
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create customer record first
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .insert({
        first_name: firstName,
        last_name: lastName,
        email: email.toLowerCase(),
        phone: phone || null,
        created_via: 'customer_portal'
      })
      .select()
      .single();

    if (customerError || !customer) {
      console.error('Customer creation error:', customerError);
      return { success: false, error: 'Failed to create customer account' };
    }

    // Generate email verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');
    const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Create customer auth record
    const { data: customerAuth, error: authError } = await supabase
      .from('customer_auth')
      .insert({
        customer_id: customer.id,
        email: email.toLowerCase(),
        password_hash: passwordHash,
        email_verification_token: emailVerificationToken,
        email_verification_expires: emailVerificationExpires.toISOString()
      })
      .select()
      .single();

    if (authError || !customerAuth) {
      console.error('Customer auth creation error:', authError);
      // Clean up customer record
      await supabase.from('customers').delete().eq('id', customer.id);
      return { success: false, error: 'Failed to create authentication record' };
    }

    // Create customer preferences with defaults
    await supabase
      .from('customer_preferences')
      .insert({
        customer_id: customer.id
      });

    // Create loyalty program record
    const referralCode = generateReferralCode(firstName, lastName);
    await supabase
      .from('customer_loyalty')
      .insert({
        customer_id: customer.id,
        referral_code: referralCode
      });

    // Log activity
    await logCustomerActivity(customer.id, 'registration', 'Customer registered', ip);

    // TODO: Send email verification email
    // await sendEmailVerification(email, emailVerificationToken);

    return {
      success: true,
      requiresEmailVerification: true,
      user: {
        id: customerAuth.id,
        customerId: customer.id,
        email: customerAuth.email,
        firstName: customer.first_name,
        lastName: customer.last_name,
        emailVerified: false,
        isActive: true,
        lastActivity: Date.now()
      }
    };
  } catch (error) {
    console.error('Customer registration error:', error);
    return { success: false, error: 'Registration failed' };
  }
}

/**
 * Record failed login attempt
 */
async function recordCustomerFailedAttempt(email: string, ip: string, reason: string) {
  try {
    // Increment login attempts
    await supabase
      .from('customer_auth')
      .update({
        login_attempts: supabase.raw('login_attempts + 1')
      })
      .eq('email', email.toLowerCase());

    // Log activity
    await supabase
      .from('customer_portal_activity')
      .insert({
        customer_id: null,
        activity_type: 'failed_login',
        description: `Failed login attempt: ${reason}`,
        ip_address: ip,
        metadata: { email, reason }
      });
  } catch (error) {
    console.error('Failed to record customer failed attempt:', error);
  }
}

/**
 * Log customer activity
 */
export async function logCustomerActivity(
  customerId: string,
  activityType: string,
  description: string,
  ip?: string,
  metadata?: any
) {
  try {
    await supabase
      .from('customer_portal_activity')
      .insert({
        customer_id: customerId,
        activity_type: activityType,
        description,
        ip_address: ip,
        metadata
      });
  } catch (error) {
    console.error('Failed to log customer activity:', error);
  }
}

/**
 * Generate unique referral code
 */
function generateReferralCode(firstName: string, lastName: string): string {
  const prefix = (firstName.substring(0, 2) + lastName.substring(0, 2)).toUpperCase();
  const suffix = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${prefix}${suffix}`;
}

/**
 * Customer logout function
 */
export async function customerLogout(sessionToken: string, customerId: string) {
  try {
    // Remove session
    await supabase
      .from('customer_sessions')
      .delete()
      .eq('session_token', sessionToken);

    // Log activity
    await logCustomerActivity(customerId, 'logout', 'Customer logged out');
  } catch (error) {
    console.error('Customer logout error:', error);
  }
}
