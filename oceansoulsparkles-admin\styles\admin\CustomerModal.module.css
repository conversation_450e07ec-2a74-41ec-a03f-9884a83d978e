/* Customer Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background: white;
  border-radius: 16px;
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.modalHeader h2 {
  margin: 0;
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 700;
}

.modalActions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.editButton, .deleteButton, .saveButton, .cancelButton {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.editButton {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.deleteButton {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.saveButton {
  background: linear-gradient(135deg, #10b981, #059669);
}

.cancelButton {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.closeButton {
  background: #f1f5f9;
  color: #64748b;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.25rem;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: #e2e8f0;
}

.modalContent {
  padding: 2rem;
}

.customerInfo {
  margin-bottom: 2rem;
}

.customerInfo h3 {
  margin: 0 0 1.5rem 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #667eea;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.infoGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.infoGroup label {
  color: #374151;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.infoGroup span {
  color: #64748b;
  font-size: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.infoGroup input, .infoGroup textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.infoGroup input:focus, .infoGroup textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.infoGroup textarea {
  resize: vertical;
  min-height: 80px;
}

.customerStats {
  margin-bottom: 2rem;
}

.customerStats h3 {
  margin: 0 0 1.5rem 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #667eea;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.statItem {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e2e8f0;
}

.statValue {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.statLabel {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.bookingHistory {
  margin-bottom: 2rem;
}

.bookingHistory h3 {
  margin: 0 0 1.5rem 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #667eea;
}

.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.bookingItem {
  display: grid;
  grid-template-columns: 120px 1fr auto;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  align-items: center;
}

.bookingDate {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.bookingService {
  color: #1e293b;
  font-weight: 600;
}

.bookingStatus {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal {
    width: 95%;
    margin: 1rem;
  }

  .modalHeader {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .modalActions {
    justify-content: center;
  }

  .modalContent {
    padding: 1.5rem;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .bookingItem {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: center;
  }
}
