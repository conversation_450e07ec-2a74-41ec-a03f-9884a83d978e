-- ============================================================================
-- OCEAN SOUL SPARKLES - CUSTOMER PORTAL INTEGRATION DATABASE SCHEMA
-- ============================================================================
-- This file contains database schema extensions for the Customer Portal Integration
-- Implementation Date: 2025-06-15
-- Feature: Customer Self-Service Portal & Loyalty Program
-- ============================================================================

-- 1. C<PERSON><PERSON>MER AUTHENTICATION TABLE
CREATE TABLE IF NOT EXISTS customer_auth (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  email_verified BOOLEAN DEFAULT false,
  email_verification_token VARCHAR(255),
  email_verification_expires TIMESTAMP WITH TIME ZONE,
  password_reset_token VARCHAR(255),
  password_reset_expires TIMESTAMP WITH TIME ZONE,
  last_login_at TIMESTAMP WITH TIME ZONE,
  login_attempts INTEGER DEFAULT 0,
  locked_until TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. CUSTOMER SESSIONS TABLE
CREATE TABLE IF NOT EXISTS customer_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. CUSTOMER PREFERENCES TABLE
CREATE TABLE IF NOT EXISTS customer_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  notification_email BOOLEAN DEFAULT true,
  notification_sms BOOLEAN DEFAULT true,
  notification_booking_reminders BOOLEAN DEFAULT true,
  notification_promotions BOOLEAN DEFAULT false,
  preferred_artist_id UUID REFERENCES artist_profiles(id),
  preferred_time_slots TEXT[], -- Array of preferred time slots like ['morning', 'afternoon']
  preferred_services TEXT[], -- Array of preferred service categories
  booking_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(customer_id)
);

-- 4. CUSTOMER LOYALTY PROGRAM TABLE
CREATE TABLE IF NOT EXISTS customer_loyalty (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  points_balance INTEGER DEFAULT 0,
  total_points_earned INTEGER DEFAULT 0,
  total_points_redeemed INTEGER DEFAULT 0,
  tier_level VARCHAR(20) DEFAULT 'Bronze' CHECK (tier_level IN ('Bronze', 'Silver', 'Gold', 'Platinum')),
  tier_start_date DATE DEFAULT CURRENT_DATE,
  next_tier_points INTEGER DEFAULT 100,
  lifetime_spend DECIMAL(10,2) DEFAULT 0.00,
  referral_code VARCHAR(20) UNIQUE,
  referred_by_customer_id UUID REFERENCES customers(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(customer_id)
);

-- 5. LOYALTY TRANSACTIONS TABLE
CREATE TABLE IF NOT EXISTS loyalty_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
  transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('earned', 'redeemed', 'expired', 'bonus', 'referral')),
  points_amount INTEGER NOT NULL,
  description TEXT,
  reference_id UUID, -- Can reference various entities
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. CUSTOMER BOOKING REQUESTS TABLE (For self-service booking)
CREATE TABLE IF NOT EXISTS customer_booking_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id),
  tier_id UUID REFERENCES service_pricing_tiers(id),
  preferred_artist_id UUID REFERENCES artist_profiles(id),
  requested_date DATE NOT NULL,
  requested_time_start TIME NOT NULL,
  requested_time_end TIME NOT NULL,
  alternative_dates DATE[], -- Array of alternative dates
  alternative_times TIME[], -- Array of alternative time slots
  special_requests TEXT,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
  admin_notes TEXT,
  approved_by UUID REFERENCES admin_users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. CUSTOMER NOTIFICATIONS TABLE
CREATE TABLE IF NOT EXISTS customer_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL, -- 'booking_confirmation', 'booking_reminder', 'loyalty_update', etc.
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  action_url VARCHAR(500),
  action_text VARCHAR(100),
  priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. LOYALTY PROGRAM RULES TABLE
CREATE TABLE IF NOT EXISTS loyalty_program_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  rule_name VARCHAR(100) NOT NULL,
  rule_type VARCHAR(50) NOT NULL, -- 'points_per_dollar', 'tier_threshold', 'bonus_multiplier'
  rule_value DECIMAL(10,2) NOT NULL,
  tier_level VARCHAR(20), -- NULL for global rules
  is_active BOOLEAN DEFAULT true,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. CUSTOMER PORTAL ACTIVITY LOG
CREATE TABLE IF NOT EXISTS customer_portal_activity (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  activity_type VARCHAR(50) NOT NULL, -- 'login', 'booking_request', 'profile_update', etc.
  description TEXT,
  ip_address INET,
  user_agent TEXT,
  metadata JSONB, -- Additional activity data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_customer_auth_email ON customer_auth(email);
CREATE INDEX IF NOT EXISTS idx_customer_auth_customer_id ON customer_auth(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_sessions_token ON customer_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_customer_sessions_customer_id ON customer_sessions(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_sessions_expires ON customer_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_customer_preferences_customer_id ON customer_preferences(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_loyalty_customer_id ON customer_loyalty(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_loyalty_referral_code ON customer_loyalty(referral_code);
CREATE INDEX IF NOT EXISTS idx_loyalty_transactions_customer_id ON loyalty_transactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_loyalty_transactions_booking_id ON loyalty_transactions(booking_id);
CREATE INDEX IF NOT EXISTS idx_customer_booking_requests_customer_id ON customer_booking_requests(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_booking_requests_status ON customer_booking_requests(status);
CREATE INDEX IF NOT EXISTS idx_customer_booking_requests_date ON customer_booking_requests(requested_date);
CREATE INDEX IF NOT EXISTS idx_customer_notifications_customer_id ON customer_notifications(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_notifications_read ON customer_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_customer_notifications_created ON customer_notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_loyalty_program_rules_active ON loyalty_program_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_customer_portal_activity_customer_id ON customer_portal_activity(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_portal_activity_type ON customer_portal_activity(activity_type);
CREATE INDEX IF NOT EXISTS idx_customer_portal_activity_created ON customer_portal_activity(created_at);

-- Insert default loyalty program rules
INSERT INTO loyalty_program_rules (rule_name, rule_type, rule_value, description) VALUES
('Points per Dollar', 'points_per_dollar', 1.00, 'Earn 1 point for every dollar spent'),
('Bronze Tier Threshold', 'tier_threshold', 0.00, 'Bronze tier starting point'),
('Silver Tier Threshold', 'tier_threshold', 100.00, 'Silver tier requires 100 points'),
('Gold Tier Threshold', 'tier_threshold', 500.00, 'Gold tier requires 500 points'),
('Platinum Tier Threshold', 'tier_threshold', 1000.00, 'Platinum tier requires 1000 points'),
('Referral Bonus', 'referral_bonus', 50.00, 'Bonus points for successful referrals'),
('Birthday Bonus', 'birthday_bonus', 25.00, 'Birthday bonus points'),
('Review Bonus', 'review_bonus', 10.00, 'Points for leaving reviews')
ON CONFLICT DO NOTHING;

-- Add customer portal settings to system_settings
INSERT INTO system_settings (category, key, value, description) VALUES
('customer_portal', 'enabled', 'true', 'Enable customer portal functionality'),
('customer_portal', 'registration_enabled', 'true', 'Allow new customer registration'),
('customer_portal', 'booking_requests_enabled', 'true', 'Allow customers to request bookings'),
('customer_portal', 'loyalty_program_enabled', 'true', 'Enable loyalty program features'),
('customer_portal', 'email_verification_required', 'true', 'Require email verification for new accounts'),
('customer_portal', 'booking_request_approval_required', 'true', 'Require admin approval for booking requests'),
('customer_portal', 'max_booking_requests_per_day', '3', 'Maximum booking requests per customer per day'),
('customer_portal', 'booking_request_advance_days', '2', 'Minimum days in advance for booking requests'),
('customer_portal', 'session_timeout_hours', '24', 'Customer session timeout in hours')
ON CONFLICT (category, key) DO NOTHING;

-- ============================================================================
-- END OF CUSTOMER PORTAL SCHEMA
-- ============================================================================
