exports.id=3194,exports.ids=[3194],exports.modules={1382:e=>{e.exports={portfolioManager:"Portfolio_portfolioManager__tKA9p",header:"Portfolio_header__22y_0",addButton:"Portfolio_addButton__9O8RI",loading:"Portfolio_loading__67gkW",spinner:"Portfolio_spinner__tEWyh",spin:"Portfolio_spin__6XFRQ",error:"Portfolio_error__Z8mwc",filters:"Portfolio_filters__TWqZS",filterGroup:"Portfolio_filterGroup__QARVH",modal:"Portfolio_modal__WaXc4",modalContent:"Portfolio_modalContent__3Aeg7",modalHeader:"Portfolio_modalHeader__25LHq",closeButton:"Portfolio_closeButton__9j2fq",form:"Portfolio_form__0sacd",formGroup:"Portfolio_formGroup__IDrc_",formRow:"Portfolio_formRow__flUx9",checkboxGroup:"Portfolio_checkboxGroup__hP2Kz",formActions:"Portfolio_formActions__E1NbZ",cancelButton:"Portfolio_cancelButton__MLCU9",submitButton:"Portfolio_submitButton__U8V4E",portfolioGrid:"Portfolio_portfolioGrid__NDva5",portfolioCard:"Portfolio_portfolioCard__IDcID",imageContainer:"Portfolio_imageContainer__6ED4v",portfolioImage:"Portfolio_portfolioImage__e9EB1",featuredBadge:"Portfolio_featuredBadge__n_wmK",privateBadge:"Portfolio_privateBadge__0UjxZ",cardContent:"Portfolio_cardContent__t3nl6",category:"Portfolio_category__hRzSs",description:"Portfolio_description__b5Ex_",tags:"Portfolio_tags__FqD3L",tag:"Portfolio_tag__NbHG0",cardActions:"Portfolio_cardActions__Xp4j7",editButton:"Portfolio_editButton__0OAYF",deleteButton:"Portfolio_deleteButton__uCFCn",emptyState:"Portfolio_emptyState__dtRES",portfolioPage:"Portfolio_portfolioPage___zQQJ",pageHeader:"Portfolio_pageHeader__uAgm3",headerContent:"Portfolio_headerContent__70X88",accessDenied:"Portfolio_accessDenied__XmZqh",statsGrid:"Portfolio_statsGrid__Fuk9Z",statCard:"Portfolio_statCard__q53G0",statIcon:"Portfolio_statIcon__IgjWN",statContent:"Portfolio_statContent__WXBxy",categoriesOverview:"Portfolio_categoriesOverview__Z8gbr",categoryTags:"Portfolio_categoryTags__Xt4Ys",categoryTag:"Portfolio_categoryTag__xCyC6",breadcrumb:"Portfolio_breadcrumb__lW_HI",artistHeader:"Portfolio_artistHeader__KfMO8",artistInfo:"Portfolio_artistInfo__tIz0n",artistDetails:"Portfolio_artistDetails__LqtLb",email:"Portfolio_email__Ue4Be",specializations:"Portfolio_specializations__Uvl_x",specializationTags:"Portfolio_specializationTags__VGsyr",specializationTag:"Portfolio_specializationTag__yFOTM",bio:"Portfolio_bio__u4Z0u",artistStats:"Portfolio_artistStats__gOpRj",statItem:"Portfolio_statItem___qMx2",statValue:"Portfolio_statValue__pbewa",statLabel:"Portfolio_statLabel__W70NS",backButton:"Portfolio_backButton__sCJPK"}},3194:(e,t,o)=>{"use strict";o.d(t,{Z:()=>n});var i=o(997),a=o(6689),r=o(1382),l=o.n(r);let s=[{value:"face_painting",label:"Face Painting"},{value:"hair_braiding",label:"Hair Braiding"},{value:"glitter_art",label:"Glitter Art"},{value:"body_art",label:"Body Art"},{value:"special_effects",label:"Special Effects"}];function n({artistId:e,onItemAdded:t,onItemUpdated:o,onItemDeleted:r}){let[n,d]=(0,a.useState)([]),[c,_]=(0,a.useState)([]),[u,m]=(0,a.useState)(!0),[f,p]=(0,a.useState)(null),[h,g]=(0,a.useState)(!1),[x,j]=(0,a.useState)(null),[v,b]=(0,a.useState)("all"),[P,C]=(0,a.useState)(e||"all"),[y,N]=(0,a.useState)({artist_id:e||"",title:"",description:"",category:"face_painting",image_url:"",thumbnail_url:"",tags:"",is_featured:!1,is_public:!0,work_date:"",customer_consent:!1}),k=async e=>{e.preventDefault();try{let e=localStorage.getItem("adminToken"),i=y.tags?y.tags.split(",").map(e=>e.trim()):[],a={...y,tags:i,work_date:y.work_date||null},r="/api/admin/artists/portfolio",l="POST";x&&(r=`/api/admin/artists/${x.artist_id}/portfolio?item_id=${x.id}`,l="PUT");let s=await fetch(r,{method:l,headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to save portfolio item");let n=await s.json();x?(d(e=>e.map(e=>e.id===x.id?n.portfolioItem:e)),o?.(n.portfolioItem),j(null)):(d(e=>[n.portfolioItem,...e]),t?.(n.portfolioItem)),A(),g(!1)}catch(e){console.error("Error saving portfolio item:",e),p("Failed to save portfolio item")}},B=e=>{j(e),N({artist_id:e.artist_id,title:e.title,description:e.description||"",category:e.category,image_url:e.image_url,thumbnail_url:e.thumbnail_url||"",tags:e.tags?.join(", ")||"",is_featured:e.is_featured,is_public:e.is_public,work_date:e.work_date||"",customer_consent:e.customer_consent}),g(!0)},I=async e=>{if(confirm(`Are you sure you want to delete "${e.title}"?`))try{let t=localStorage.getItem("adminToken");if(!(await fetch(`/api/admin/artists/${e.artist_id}/portfolio?item_id=${e.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).ok)throw Error("Failed to delete portfolio item");d(t=>t.filter(t=>t.id!==e.id)),r?.(e.id)}catch(e){console.error("Error deleting portfolio item:",e),p("Failed to delete portfolio item")}},A=()=>{N({artist_id:e||"",title:"",description:"",category:"face_painting",image_url:"",thumbnail_url:"",tags:"",is_featured:!1,is_public:!0,work_date:"",customer_consent:!1}),j(null)},G=()=>{A(),g(!1)};return u?(0,i.jsxs)("div",{className:l().loading,children:[i.jsx("div",{className:l().spinner}),i.jsx("p",{children:"Loading portfolio..."})]}):(0,i.jsxs)("div",{className:l().portfolioManager,children:[(0,i.jsxs)("div",{className:l().header,children:[i.jsx("h2",{children:"Portfolio Management"}),i.jsx("button",{className:l().addButton,onClick:()=>g(!0),children:"+ Add Portfolio Item"})]}),f&&(0,i.jsxs)("div",{className:l().error,children:[i.jsx("p",{children:f}),i.jsx("button",{onClick:()=>p(null),children:"\xd7"})]}),(0,i.jsxs)("div",{className:l().filters,children:[!e&&(0,i.jsxs)("div",{className:l().filterGroup,children:[i.jsx("label",{children:"Artist:"}),(0,i.jsxs)("select",{value:P,onChange:e=>C(e.target.value),children:[i.jsx("option",{value:"all",children:"All Artists"}),c.map(e=>i.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,i.jsxs)("div",{className:l().filterGroup,children:[i.jsx("label",{children:"Category:"}),(0,i.jsxs)("select",{value:v,onChange:e=>b(e.target.value),children:[i.jsx("option",{value:"all",children:"All Categories"}),s.map(e=>i.jsx("option",{value:e.value,children:e.label},e.value))]})]})]}),h&&i.jsx("div",{className:l().modal,children:(0,i.jsxs)("div",{className:l().modalContent,children:[(0,i.jsxs)("div",{className:l().modalHeader,children:[i.jsx("h3",{children:x?"Edit Portfolio Item":"Add Portfolio Item"}),i.jsx("button",{className:l().closeButton,onClick:G,children:"\xd7"})]}),(0,i.jsxs)("form",{onSubmit:k,className:l().form,children:[!e&&(0,i.jsxs)("div",{className:l().formGroup,children:[i.jsx("label",{children:"Artist *"}),(0,i.jsxs)("select",{value:y.artist_id,onChange:e=>N(t=>({...t,artist_id:e.target.value})),required:!0,children:[i.jsx("option",{value:"",children:"Select Artist"}),c.map(e=>i.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,i.jsxs)("div",{className:l().formGroup,children:[i.jsx("label",{children:"Title *"}),i.jsx("input",{type:"text",value:y.title,onChange:e=>N(t=>({...t,title:e.target.value})),required:!0})]}),(0,i.jsxs)("div",{className:l().formGroup,children:[i.jsx("label",{children:"Description"}),i.jsx("textarea",{value:y.description,onChange:e=>N(t=>({...t,description:e.target.value})),rows:3})]}),(0,i.jsxs)("div",{className:l().formRow,children:[(0,i.jsxs)("div",{className:l().formGroup,children:[i.jsx("label",{children:"Category *"}),i.jsx("select",{value:y.category,onChange:e=>N(t=>({...t,category:e.target.value})),required:!0,children:s.map(e=>i.jsx("option",{value:e.value,children:e.label},e.value))})]}),(0,i.jsxs)("div",{className:l().formGroup,children:[i.jsx("label",{children:"Work Date"}),i.jsx("input",{type:"date",value:y.work_date,onChange:e=>N(t=>({...t,work_date:e.target.value}))})]})]}),(0,i.jsxs)("div",{className:l().formGroup,children:[i.jsx("label",{children:"Image URL *"}),i.jsx("input",{type:"url",value:y.image_url,onChange:e=>N(t=>({...t,image_url:e.target.value})),required:!0})]}),(0,i.jsxs)("div",{className:l().formGroup,children:[i.jsx("label",{children:"Thumbnail URL"}),i.jsx("input",{type:"url",value:y.thumbnail_url,onChange:e=>N(t=>({...t,thumbnail_url:e.target.value}))})]}),(0,i.jsxs)("div",{className:l().formGroup,children:[i.jsx("label",{children:"Tags (comma-separated)"}),i.jsx("input",{type:"text",value:y.tags,onChange:e=>N(t=>({...t,tags:e.target.value})),placeholder:"e.g. butterfly, colorful, glitter"})]}),(0,i.jsxs)("div",{className:l().checkboxGroup,children:[(0,i.jsxs)("label",{children:[i.jsx("input",{type:"checkbox",checked:y.is_featured,onChange:e=>N(t=>({...t,is_featured:e.target.checked}))}),"Featured Item"]}),(0,i.jsxs)("label",{children:[i.jsx("input",{type:"checkbox",checked:y.is_public,onChange:e=>N(t=>({...t,is_public:e.target.checked}))}),"Public (visible to customers)"]}),(0,i.jsxs)("label",{children:[i.jsx("input",{type:"checkbox",checked:y.customer_consent,onChange:e=>N(t=>({...t,customer_consent:e.target.checked}))}),"Customer Consent Obtained"]})]}),(0,i.jsxs)("div",{className:l().formActions,children:[i.jsx("button",{type:"button",onClick:G,className:l().cancelButton,children:"Cancel"}),(0,i.jsxs)("button",{type:"submit",className:l().submitButton,children:[x?"Update":"Add"," Portfolio Item"]})]})]})]})}),i.jsx("div",{className:l().portfolioGrid,children:0===n.length?(0,i.jsxs)("div",{className:l().emptyState,children:[i.jsx("p",{children:"No portfolio items found."}),i.jsx("button",{onClick:()=>g(!0),children:"Add your first portfolio item"})]}):n.map(e=>(0,i.jsxs)("div",{className:l().portfolioCard,children:[(0,i.jsxs)("div",{className:l().imageContainer,children:[i.jsx("img",{src:e.thumbnail_url||e.image_url,alt:e.title,className:l().portfolioImage}),e.is_featured&&i.jsx("div",{className:l().featuredBadge,children:"⭐ Featured"}),!e.is_public&&i.jsx("div",{className:l().privateBadge,children:"\uD83D\uDD12 Private"})]}),(0,i.jsxs)("div",{className:l().cardContent,children:[i.jsx("h3",{children:e.title}),i.jsx("p",{className:l().category,children:s.find(t=>t.value===e.category)?.label}),e.description&&i.jsx("p",{className:l().description,children:e.description}),e.tags&&e.tags.length>0&&i.jsx("div",{className:l().tags,children:e.tags.map(e=>i.jsx("span",{className:l().tag,children:e},e))}),(0,i.jsxs)("div",{className:l().cardActions,children:[i.jsx("button",{onClick:()=>B(e),className:l().editButton,children:"Edit"}),i.jsx("button",{onClick:()=>I(e),className:l().deleteButton,children:"Delete"})]})]})]},e.id))})]})}}};