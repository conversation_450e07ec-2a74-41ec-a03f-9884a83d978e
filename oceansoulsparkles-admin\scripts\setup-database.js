#!/usr/bin/env node

/**
 * Database Setup Script for Development
 * Creates required tables for admin subdomain
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: require('path').join(__dirname, '..', '.env.local') });

async function setupDatabase() {
  console.log('🗄️  Setting up database tables...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase configuration');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  const tables = [
    {
      name: 'admin_users',
      sql: `
        CREATE TABLE IF NOT EXISTS admin_users (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash TEXT NOT NULL,
          first_name VARCHAR(100) NOT NULL,
          last_name VARCHAR(100) NOT NULL,
          role VARCHAR(20) NOT NULL CHECK (role IN ('DEV', 'Admin', 'Artist', 'Braider')),
          is_active BOOLEAN DEFAULT true,
          mfa_enabled BOOLEAN DEFAULT false,
          mfa_secret TEXT,
          last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'audit_logs',
      sql: `
        CREATE TABLE IF NOT EXISTS audit_logs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          action VARCHAR(100) NOT NULL,
          user_id UUID REFERENCES admin_users(id),
          user_role VARCHAR(20),
          email VARCHAR(255),
          ip_address INET,
          path TEXT,
          resource VARCHAR(100),
          resource_id TEXT,
          old_values JSONB,
          new_values JSONB,
          reason TEXT,
          error TEXT,
          metadata JSONB,
          severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'user_roles',
      sql: `
        CREATE TABLE IF NOT EXISTS user_roles (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID NOT NULL,
          role VARCHAR(20) NOT NULL CHECK (role IN ('DEV', 'Admin', 'Artist', 'Braider', 'User')),
          created_by UUID REFERENCES admin_users(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, role)
        );
      `
    },
    {
      name: 'system_settings',
      sql: `
        CREATE TABLE IF NOT EXISTS system_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          category VARCHAR(50) NOT NULL,
          key VARCHAR(100) NOT NULL,
          value TEXT NOT NULL,
          description TEXT,
          created_by UUID REFERENCES admin_users(id),
          updated_by UUID REFERENCES admin_users(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(category, key)
        );
      `
    }
  ];

  try {
    for (const table of tables) {
      console.log(`📋 Creating table: ${table.name}...`);
      
      const { error } = await supabase.rpc('exec_sql', { 
        sql: table.sql 
      });
      
      if (error) {
        // Try direct SQL execution if RPC doesn't work
        const { error: directError } = await supabase
          .from('_sql')
          .select('*')
          .limit(0);
          
        if (directError) {
          console.log(`⚠️  Could not create ${table.name} via Supabase client`);
          console.log(`📝 Please run this SQL manually in Supabase SQL Editor:`);
          console.log(`\n${table.sql}\n`);
        }
      } else {
        console.log(`✅ Table ${table.name} created successfully`);
      }
    }

    // Create RLS policies
    console.log('\n🔒 Setting up Row Level Security...');
    
    const rlsPolicies = [
      `ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;`,
      `ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;`,
      `ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;`,
      `ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;`,

      // Admin users can manage everything
      `CREATE POLICY IF NOT EXISTS "Admin users can manage all users" ON admin_users
       FOR ALL USING (true);`,

      // Audit logs are read-only for admins
      `CREATE POLICY IF NOT EXISTS "Admin users can read audit logs" ON audit_logs
       FOR SELECT USING (true);`,

      // User roles management
      `CREATE POLICY IF NOT EXISTS "Admin users can manage roles" ON user_roles
       FOR ALL USING (true);`,

      // System settings management
      `CREATE POLICY IF NOT EXISTS "Admin users can manage system settings" ON system_settings
       FOR ALL USING (true);`
    ];

    for (const policy of rlsPolicies) {
      const { error } = await supabase.rpc('exec_sql', { sql: policy });
      if (error) {
        console.log(`📝 Please run this RLS policy manually:`);
        console.log(`${policy}\n`);
      }
    }

    console.log('\n✅ Database setup completed!');
    console.log('\n📋 Next Steps:');
    console.log('1. Run: npm run create-dev-user');
    console.log('2. Run: npm run dev');
    console.log('3. Visit: http://localhost:3002/admin/login');

  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    console.log('\n📝 Manual Setup Required:');
    console.log('Please run the following SQL in your Supabase SQL Editor:');
    
    tables.forEach(table => {
      console.log(`\n-- ${table.name} table`);
      console.log(table.sql);
    });
    
    process.exit(1);
  }
}

if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
