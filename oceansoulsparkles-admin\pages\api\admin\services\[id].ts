import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Service ID is required' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;

    // Only admin and dev can manage services
    if (user.role !== 'Admin' && user.role !== 'DEV') {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    if (req.method === 'GET') {
      // Get service details
      const { data: service, error } = await supabase
        .from('services')
        .select(`
          id,
          name,
          description,
          duration,
          price,
          category,
          status,
          visible_on_public,
          visible_on_pos,
          visible_on_events,
          created_at,
          updated_at
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Service query error:', error);
        return res.status(500).json({ error: 'Failed to fetch service' });
      }

      if (!service) {
        return res.status(404).json({ error: 'Service not found' });
      }

      // Get booking count for this service
      const { count: bookingCount } = await supabase
        .from('bookings')
        .select('*', { count: 'exact', head: true })
        .eq('service_id', id);

      // Transform data
      const transformedService = {
        ...service,
        is_active: service.status === 'active',
        total_bookings: bookingCount || 0
      };

      return res.status(200).json({
        service: transformedService
      });

    } else if (req.method === 'PUT') {
      // Update service
      const {
        name,
        description,
        duration,
        price,
        category,
        status,
        visible_on_public,
        visible_on_pos,
        visible_on_events
      } = req.body;

      // Validate required fields
      if (!name || !category) {
        return res.status(400).json({ error: 'Name and category are required' });
      }

      const { data: service, error } = await supabase
        .from('services')
        .update({
          name,
          description,
          duration: duration ? parseInt(duration) : null,
          price: price ? parseFloat(price) : null,
          category,
          status: status || 'active',
          visible_on_public: visible_on_public !== false,
          visible_on_pos: visible_on_pos !== false,
          visible_on_events: visible_on_events !== false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Service update error:', error);
        return res.status(500).json({ error: 'Failed to update service' });
      }

      return res.status(200).json({
        service: {
          ...service,
          is_active: service.status === 'active'
        }
      });

    } else if (req.method === 'DELETE') {
      // Delete service
      const { error } = await supabase
        .from('services')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Service delete error:', error);
        return res.status(500).json({ error: 'Failed to delete service' });
      }

      return res.status(200).json({ message: 'Service deleted successfully' });

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Services API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
