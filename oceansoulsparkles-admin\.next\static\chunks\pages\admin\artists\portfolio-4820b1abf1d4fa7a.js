(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[69],{146:function(r,t,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/artists/portfolio",function(){return e(9857)}])},9857:function(r,t,e){"use strict";e.r(t),e.d(t,{default:function(){return l}}),function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}(),function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}();var n=e(9008),o=e.n(n),a=e(1163),c=e(99),i=e(4632),d=e(6026),O=e(2906),u=e.n(O);function l(){let{user:r,loading:t}=(0,d.a)(),e=(0,a.useRouter)(),[n,O]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())({totalItems:0,featuredItems:0,publicItems:0,categories:[],totalArtists:0}),[l,s]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(!0),[m,f]=Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(null);Object(function(){var r=Error("Cannot find module 'react'");throw r.code="MODULE_NOT_FOUND",r}())(()=>{r&&N()},[r]);let N=async()=>{try{s(!0);let t=localStorage.getItem("adminToken"),e=await fetch("/api/admin/artists/portfolio",{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to load portfolio statistics");let n=(await e.json()).portfolioItems||[],o=await fetch("/api/admin/artists",{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}}),a=0;if(o.ok){var r;let t=await o.json();a=(null===(r=t.artists)||void 0===r?void 0:r.length)||0}let c=new Set(n.map(r=>r.category)),i=Array.from(c),d=n.filter(r=>r.is_featured).length,u=n.filter(r=>r.is_public).length;O({totalItems:n.length,featuredItems:d,publicItems:u,categories:i,totalArtists:a})}catch(r){console.error("Error loading portfolio stats:",r),f("Failed to load portfolio statistics")}finally{s(!1)}};return t||l?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(c.Z,{children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().loading,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().spinner}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Loading portfolio management..."})]})}):r?["DEV","Admin"].includes(r.role)?Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(c.Z,{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(o(),{children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("title",{children:"Artist Portfolio Management | Ocean Soul Sparkles Admin"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("meta",{name:"description",content:"Manage artist portfolios and work samples"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().portfolioPage,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().pageHeader,children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().headerContent,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h1",{children:"Artist Portfolio Management"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Manage artist portfolios, work samples, and showcase galleries"})]})}),m&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().error,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:m}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("button",{onClick:()=>f(null),children:"\xd7"})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statsGrid,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statIcon,children:"\uD83C\uDFA8"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statContent,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:n.totalItems}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Total Portfolio Items"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statIcon,children:"⭐"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statContent,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:n.featuredItems}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Featured Items"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statIcon,children:"\uD83D\uDC41️"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statContent,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:n.publicItems}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Public Items"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statIcon,children:"\uD83D\uDC68‍\uD83C\uDFA8"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statContent,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:n.totalArtists}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Active Artists"})]})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statCard,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statIcon,children:"\uD83D\uDCC2"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().statContent,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:n.categories.length}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"Categories"})]})]})]}),n.categories.length>0&&Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().categoriesOverview,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h3",{children:"Portfolio Categories"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().categoryTags,children:n.categories.map(r=>Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("span",{className:u().categoryTag,children:r.replace("_"," ").replace(/\b\w/g,r=>r.toUpperCase())},r))})]}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(i.Z,{onItemAdded:()=>{N()},onItemUpdated:()=>{N()},onItemDeleted:()=>{N()}})]})]}):Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())(c.Z,{children:Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("div",{className:u().accessDenied,children:[Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("h2",{children:"Access Denied"}),Object(function(){var r=Error("Cannot find module 'react/jsx-runtime'");throw r.code="MODULE_NOT_FOUND",r}())("p",{children:"You don't have permission to access portfolio management."})]})}):(e.push("/admin/login"),null)}}},function(r){r.O(0,[736,592,888,179],function(){return r(r.s=146)}),_N_E=r.O()}]);