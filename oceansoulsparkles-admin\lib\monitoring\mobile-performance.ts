/**
 * Ocean Soul Sparkles Admin - Mobile Performance Monitoring
 * Monitors mobile app performance and user experience metrics
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface UserInteractionMetric {
  type: 'tap' | 'swipe' | 'scroll' | 'pinch';
  element: string;
  duration: number;
  timestamp: number;
  success: boolean;
}

interface NetworkMetric {
  url: string;
  method: string;
  status: number;
  duration: number;
  size: number;
  timestamp: number;
}

interface DeviceInfo {
  userAgent: string;
  screenWidth: number;
  screenHeight: number;
  devicePixelRatio: number;
  connectionType: string;
  memoryInfo?: any;
  isOnline: boolean;
  isMobile: boolean;
  isTouch: boolean;
}

class MobilePerformanceMonitor {
  private static instance: MobilePerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private interactions: UserInteractionMetric[] = [];
  private networkMetrics: NetworkMetric[] = [];
  private deviceInfo: DeviceInfo;
  private observers: Map<string, PerformanceObserver> = new Map();
  private isMonitoring: boolean = false;

  constructor() {
    this.deviceInfo = this.collectDeviceInfo();
    this.setupPerformanceObservers();
    this.setupNetworkMonitoring();
    this.setupUserInteractionMonitoring();
  }

  static getInstance(): MobilePerformanceMonitor {
    if (!MobilePerformanceMonitor.instance) {
      MobilePerformanceMonitor.instance = new MobilePerformanceMonitor();
    }
    return MobilePerformanceMonitor.instance;
  }

  /**
   * Start monitoring performance
   */
  startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('Mobile performance monitoring started');
    
    // Monitor core web vitals
    this.monitorCoreWebVitals();
    
    // Monitor memory usage
    this.monitorMemoryUsage();
    
    // Monitor frame rate
    this.monitorFrameRate();
    
    // Monitor battery status (if available)
    this.monitorBatteryStatus();
  }

  /**
   * Stop monitoring performance
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    
    // Disconnect all observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    
    console.log('Mobile performance monitoring stopped');
  }

  /**
   * Collect device information
   */
  private collectDeviceInfo(): DeviceInfo {
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    return {
      userAgent: navigator.userAgent,
      screenWidth: screen.width,
      screenHeight: screen.height,
      devicePixelRatio: window.devicePixelRatio || 1,
      connectionType: (navigator as any).connection?.effectiveType || 'unknown',
      memoryInfo: (performance as any).memory,
      isOnline: navigator.onLine,
      isMobile,
      isTouch
    };
  }

  /**
   * Setup performance observers
   */
  private setupPerformanceObservers(): void {
    if (!('PerformanceObserver' in window)) return;

    // Observe navigation timing
    try {
      const navObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          if (entry.entryType === 'navigation') {
            this.recordMetric('page-load-time', entry.duration);
            this.recordMetric('dom-content-loaded', (entry as any).domContentLoadedEventEnd - entry.startTime);
            this.recordMetric('first-paint', (entry as any).loadEventEnd - entry.startTime);
          }
        });
      });
      
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.set('navigation', navObserver);
    } catch (error) {
      console.warn('Navigation timing observer not supported');
    }

    // Observe resource timing
    try {
      const resourceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          if (entry.entryType === 'resource') {
            this.recordNetworkMetric({
              url: entry.name,
              method: 'GET',
              status: 200,
              duration: entry.duration,
              size: (entry as any).transferSize || 0,
              timestamp: Date.now()
            });
          }
        });
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.set('resource', resourceObserver);
    } catch (error) {
      console.warn('Resource timing observer not supported');
    }

    // Observe largest contentful paint
    try {
      const lcpObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          this.recordMetric('largest-contentful-paint', entry.startTime);
        });
      });
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.set('lcp', lcpObserver);
    } catch (error) {
      console.warn('LCP observer not supported');
    }

    // Observe first input delay
    try {
      const fidObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          this.recordMetric('first-input-delay', (entry as any).processingStart - entry.startTime);
        });
      });
      
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.set('fid', fidObserver);
    } catch (error) {
      console.warn('FID observer not supported');
    }
  }

  /**
   * Monitor core web vitals
   */
  private monitorCoreWebVitals(): void {
    // Cumulative Layout Shift
    if ('PerformanceObserver' in window) {
      try {
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          list.getEntries().forEach(entry => {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          });
          this.recordMetric('cumulative-layout-shift', clsValue);
        });
        
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.set('cls', clsObserver);
      } catch (error) {
        console.warn('CLS observer not supported');
      }
    }
  }

  /**
   * Monitor memory usage
   */
  private monitorMemoryUsage(): void {
    const checkMemory = () => {
      if ((performance as any).memory) {
        const memory = (performance as any).memory;
        this.recordMetric('memory-used', memory.usedJSHeapSize);
        this.recordMetric('memory-total', memory.totalJSHeapSize);
        this.recordMetric('memory-limit', memory.jsHeapSizeLimit);
      }
    };

    // Check memory every 30 seconds
    setInterval(checkMemory, 30000);
    checkMemory(); // Initial check
  }

  /**
   * Monitor frame rate
   */
  private monitorFrameRate(): void {
    let lastTime = performance.now();
    let frameCount = 0;
    
    const measureFPS = () => {
      const currentTime = performance.now();
      frameCount++;
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        this.recordMetric('frame-rate', fps);
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      if (this.isMonitoring) {
        requestAnimationFrame(measureFPS);
      }
    };
    
    requestAnimationFrame(measureFPS);
  }

  /**
   * Monitor battery status
   */
  private async monitorBatteryStatus(): Promise<void> {
    if ('getBattery' in navigator) {
      try {
        const battery = await (navigator as any).getBattery();
        
        const recordBatteryMetrics = () => {
          this.recordMetric('battery-level', battery.level * 100);
          this.recordMetric('battery-charging', battery.charging ? 1 : 0);
        };
        
        recordBatteryMetrics();
        
        battery.addEventListener('levelchange', recordBatteryMetrics);
        battery.addEventListener('chargingchange', recordBatteryMetrics);
      } catch (error) {
        console.warn('Battery API not supported');
      }
    }
  }

  /**
   * Setup network monitoring
   */
  private setupNetworkMonitoring(): void {
    // Monitor connection changes
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      
      const recordConnectionMetrics = () => {
        this.recordMetric('connection-downlink', connection.downlink || 0);
        this.recordMetric('connection-rtt', connection.rtt || 0);
        this.recordMetric('connection-effective-type', this.getConnectionTypeValue(connection.effectiveType));
      };
      
      recordConnectionMetrics();
      connection.addEventListener('change', recordConnectionMetrics);
    }

    // Monitor online/offline status
    const recordOnlineStatus = () => {
      this.recordMetric('online-status', navigator.onLine ? 1 : 0);
    };
    
    window.addEventListener('online', recordOnlineStatus);
    window.addEventListener('offline', recordOnlineStatus);
  }

  /**
   * Setup user interaction monitoring
   */
  private setupUserInteractionMonitoring(): void {
    let touchStartTime = 0;
    let touchStartElement = '';

    // Touch events
    document.addEventListener('touchstart', (e) => {
      touchStartTime = performance.now();
      touchStartElement = this.getElementSelector(e.target as Element);
    }, { passive: true });

    document.addEventListener('touchend', (e) => {
      const duration = performance.now() - touchStartTime;
      this.recordInteraction({
        type: 'tap',
        element: touchStartElement,
        duration,
        timestamp: Date.now(),
        success: true
      });
    }, { passive: true });

    // Scroll events
    let scrollStartTime = 0;
    document.addEventListener('scroll', () => {
      if (scrollStartTime === 0) {
        scrollStartTime = performance.now();
      }
    }, { passive: true });

    document.addEventListener('scrollend', () => {
      if (scrollStartTime > 0) {
        const duration = performance.now() - scrollStartTime;
        this.recordInteraction({
          type: 'scroll',
          element: 'document',
          duration,
          timestamp: Date.now(),
          success: true
        });
        scrollStartTime = 0;
      }
    }, { passive: true });
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, metadata?: Record<string, any>): void {
    this.metrics.push({
      name,
      value,
      timestamp: Date.now(),
      metadata
    });

    // Keep only last 1000 metrics to prevent memory issues
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  /**
   * Record a user interaction
   */
  recordInteraction(interaction: UserInteractionMetric): void {
    this.interactions.push(interaction);

    // Keep only last 500 interactions
    if (this.interactions.length > 500) {
      this.interactions = this.interactions.slice(-500);
    }
  }

  /**
   * Record a network metric
   */
  recordNetworkMetric(metric: NetworkMetric): void {
    this.networkMetrics.push(metric);

    // Keep only last 500 network metrics
    if (this.networkMetrics.length > 500) {
      this.networkMetrics = this.networkMetrics.slice(-500);
    }
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    device: DeviceInfo;
    metrics: Record<string, { avg: number; min: number; max: number; count: number }>;
    interactions: Record<string, { avg: number; count: number; successRate: number }>;
    network: { avgDuration: number; avgSize: number; requestCount: number };
  } {
    // Aggregate metrics
    const metricSummary: Record<string, { avg: number; min: number; max: number; count: number }> = {};
    
    this.metrics.forEach(metric => {
      if (!metricSummary[metric.name]) {
        metricSummary[metric.name] = { avg: 0, min: Infinity, max: -Infinity, count: 0 };
      }
      
      const summary = metricSummary[metric.name];
      summary.count++;
      summary.min = Math.min(summary.min, metric.value);
      summary.max = Math.max(summary.max, metric.value);
    });

    // Calculate averages
    Object.keys(metricSummary).forEach(key => {
      const values = this.metrics.filter(m => m.name === key).map(m => m.value);
      metricSummary[key].avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    });

    // Aggregate interactions
    const interactionSummary: Record<string, { avg: number; count: number; successRate: number }> = {};
    
    this.interactions.forEach(interaction => {
      if (!interactionSummary[interaction.type]) {
        interactionSummary[interaction.type] = { avg: 0, count: 0, successRate: 0 };
      }
      
      const summary = interactionSummary[interaction.type];
      summary.count++;
    });

    Object.keys(interactionSummary).forEach(type => {
      const interactions = this.interactions.filter(i => i.type === type);
      const durations = interactions.map(i => i.duration);
      const successCount = interactions.filter(i => i.success).length;
      
      interactionSummary[type].avg = durations.reduce((sum, val) => sum + val, 0) / durations.length;
      interactionSummary[type].successRate = successCount / interactions.length;
    });

    // Network summary
    const networkSummary = {
      avgDuration: this.networkMetrics.reduce((sum, m) => sum + m.duration, 0) / this.networkMetrics.length || 0,
      avgSize: this.networkMetrics.reduce((sum, m) => sum + m.size, 0) / this.networkMetrics.length || 0,
      requestCount: this.networkMetrics.length
    };

    return {
      device: this.deviceInfo,
      metrics: metricSummary,
      interactions: interactionSummary,
      network: networkSummary
    };
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): string {
    return JSON.stringify({
      timestamp: Date.now(),
      device: this.deviceInfo,
      metrics: this.metrics,
      interactions: this.interactions,
      network: this.networkMetrics,
      summary: this.getPerformanceSummary()
    }, null, 2);
  }

  /**
   * Helper methods
   */
  private getElementSelector(element: Element): string {
    if (!element) return 'unknown';
    
    let selector = element.tagName.toLowerCase();
    
    if (element.id) {
      selector += `#${element.id}`;
    } else if (element.className) {
      selector += `.${element.className.split(' ')[0]}`;
    }
    
    return selector;
  }

  private getConnectionTypeValue(type: string): number {
    const typeMap: Record<string, number> = {
      'slow-2g': 1,
      '2g': 2,
      '3g': 3,
      '4g': 4,
      '5g': 5
    };
    
    return typeMap[type] || 0;
  }
}

export const mobilePerformanceMonitor = MobilePerformanceMonitor.getInstance();
