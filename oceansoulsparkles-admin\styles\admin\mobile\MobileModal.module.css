/**
 * Ocean Soul Sparkles Admin - Mobile Modal Styles
 * Full-screen mobile modals with proper animations and gestures
 */

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 10000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.modalOverlay.open {
  opacity: 1;
}

.modal {
  background: var(--admin-card-background, #ffffff);
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.modalOverlay.open .modal {
  transform: translateY(0);
}

/* Modal sizes */
.modal.small {
  max-height: 40vh;
}

.modal.medium {
  max-height: 60vh;
}

.modal.large {
  max-height: 80vh;
}

.modal.fullscreen {
  height: 100vh;
  max-height: 100vh;
  border-radius: 0;
}

/* Swipe indicator */
.swipeIndicator {
  display: flex;
  justify-content: center;
  padding: 8px 0 4px 0;
  background: var(--admin-card-background, #ffffff);
  border-radius: 16px 16px 0 0;
}

.swipeHandle {
  width: 36px;
  height: 4px;
  background: var(--admin-border, #e0e0e0);
  border-radius: 2px;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--admin-border, #e0e0e0);
  background: var(--admin-card-background, #ffffff);
  flex-shrink: 0;
}

.modal.fullscreen .header {
  padding-top: max(16px, env(safe-area-inset-top, 16px));
}

.headerLeft,
.headerRight {
  flex: 1;
  display: flex;
  align-items: center;
}

.headerLeft {
  justify-content: flex-start;
}

.headerRight {
  justify-content: flex-end;
}

.headerCenter {
  flex: 2;
  display: flex;
  justify-content: center;
}

.title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  margin: 0;
  text-align: center;
  line-height: 1.4;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--admin-text-secondary, #666666);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.closeButton:hover {
  background: var(--admin-hover-background, #f5f5f5);
}

.closeButton:active {
  background: var(--admin-active-background, #e0e0e0);
  transform: scale(0.95);
}

/* Content */
.content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 20px;
  background: var(--admin-card-background, #ffffff);
}

.modal.fullscreen .content {
  padding-bottom: max(20px, env(safe-area-inset-bottom, 20px));
}

/* Confirmation modal styles */
.confirmationContent {
  text-align: center;
  padding: 20px 0;
}

.confirmationMessage {
  font-size: 1rem;
  color: var(--admin-text-primary, #1a1a1a);
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.confirmationActions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  border: none;
}

.buttonPrimary {
  background: var(--admin-primary, #16213e);
  color: white;
}

.buttonPrimary:hover {
  background: var(--admin-primary-dark, #0f1419);
}

.buttonPrimary:active {
  transform: scale(0.98);
}

.buttonSecondary {
  background: var(--admin-background, #f8f9fa);
  color: var(--admin-text-primary, #1a1a1a);
  border: 1px solid var(--admin-border, #e0e0e0);
}

.buttonSecondary:hover {
  background: var(--admin-hover-background, #f5f5f5);
}

.buttonSecondary:active {
  transform: scale(0.98);
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 0;
  }

  .modal {
    border-radius: 16px 16px 0 0;
  }

  .modal.fullscreen {
    border-radius: 0;
  }

  .header {
    padding: 12px 16px;
  }

  .title {
    font-size: 1rem;
  }

  .content {
    padding: 16px;
  }

  .closeButton {
    width: 36px;
    height: 36px;
    font-size: 1.125rem;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .header {
    padding: 10px 12px;
  }

  .title {
    font-size: 0.9rem;
  }

  .content {
    padding: 12px;
  }

  .confirmationActions {
    flex-direction: column;
    gap: 8px;
  }

  .button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .modal {
    background: var(--admin-card-background-dark, #2a2a2a);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
  }

  .swipeIndicator {
    background: var(--admin-card-background-dark, #2a2a2a);
  }

  .swipeHandle {
    background: var(--admin-border-dark, #404040);
  }

  .header {
    background: var(--admin-card-background-dark, #2a2a2a);
    border-bottom-color: var(--admin-border-dark, #404040);
  }

  .title {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .closeButton {
    color: var(--admin-text-secondary-dark, #cccccc);
  }

  .closeButton:hover {
    background: var(--admin-hover-background-dark, #3a3a3a);
  }

  .closeButton:active {
    background: var(--admin-active-background-dark, #4a4a4a);
  }

  .content {
    background: var(--admin-card-background-dark, #2a2a2a);
  }

  .confirmationMessage {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .buttonSecondary {
    background: var(--admin-background-dark, #1a1a1a);
    color: var(--admin-text-primary-dark, #ffffff);
    border-color: var(--admin-border-dark, #404040);
  }

  .buttonSecondary:hover {
    background: var(--admin-hover-background-dark, #3a3a3a);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modal {
    border: 2px solid var(--admin-border, #000000);
  }

  .header {
    border-bottom-width: 2px;
  }

  .closeButton {
    border: 1px solid var(--admin-border, #000000);
  }

  .button {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modalOverlay {
    transition: opacity 0.1s ease;
  }

  .modal {
    transition: transform 0.1s ease;
  }

  .closeButton:active,
  .button:active {
    transform: none;
  }
}

/* Focus styles for accessibility */
.closeButton:focus,
.button:focus {
  outline: 2px solid var(--admin-primary, #16213e);
  outline-offset: 2px;
}

.closeButton:focus:not(:focus-visible),
.button:focus:not(:focus-visible) {
  outline: none;
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .modal.small {
    max-height: 80vh;
  }

  .modal.medium {
    max-height: 90vh;
  }

  .modal.large {
    max-height: 95vh;
  }

  .header {
    padding: 8px 16px;
  }

  .content {
    padding: 12px 16px;
  }
}
