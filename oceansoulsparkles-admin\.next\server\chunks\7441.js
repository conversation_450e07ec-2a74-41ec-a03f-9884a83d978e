exports.id=7441,exports.ids=[7441],exports.modules={4830:e=>{e.exports={adminHeader:"AdminHeader_adminHeader__tAy8N",headerLeft:"AdminHeader_headerLeft__FXjXr",sidebarToggle:"AdminHeader_sidebarToggle__Vlukg",hamburger:"AdminHeader_hamburger__3oPy_",breadcrumb:"AdminHeader_breadcrumb__z_2w7",headerCenter:"AdminHeader_headerCenter__RwMCL",headerRight:"AdminHeader_headerRight__jgrCt",quickActions:"AdminHeader_quickActions___NuOX",quickAction:"AdminHeader_quickAction__XqmCI",notifications:"AdminHeader_notifications__DWNcH",notificationButton:"AdminHeader_notificationButton__hubpu",notificationBadge:"AdminHeader_notificationBadge__spKqR",notificationDropdown:"AdminHeader_notificationDropdown__mA8dq",notificationHeader:"AdminHeader_notificationHeader__Ue15C",markAllRead:"AdminHeader_markAllRead__UP_0Q",notificationList:"AdminHeader_notificationList__JuL31",notificationItem:"AdminHeader_notificationItem__ABEAH",notificationIcon:"AdminHeader_notificationIcon__BSCLh",notificationContent:"AdminHeader_notificationContent__tFkeh",notificationTitle:"AdminHeader_notificationTitle__C5Il3",notificationTime:"AdminHeader_notificationTime__DWutx",notificationFooter:"AdminHeader_notificationFooter__T4khp",userMenu:"AdminHeader_userMenu__YbO0w",userButton:"AdminHeader_userButton__uP4qu",userAvatar:"AdminHeader_userAvatar__QJdnj",userInfo:"AdminHeader_userInfo__t2PHi",userName:"AdminHeader_userName__4_RNy",userRole:"AdminHeader_userRole__fQkGv",dropdownArrow:"AdminHeader_dropdownArrow___vHwu",userDropdown:"AdminHeader_userDropdown__NFy7A",userDropdownHeader:"AdminHeader_userDropdownHeader__CYxvo",userEmail:"AdminHeader_userEmail__nZCju",userRoleBadge:"AdminHeader_userRoleBadge__W3Lbx",userDropdownMenu:"AdminHeader_userDropdownMenu__7PJEX",dropdownItem:"AdminHeader_dropdownItem__7zn2N",dropdownIcon:"AdminHeader_dropdownIcon__ZZ3_U",dropdownDivider:"AdminHeader_dropdownDivider__6AaxM",logoutItem:"AdminHeader_logoutItem__R0CHw"}},8172:e=>{e.exports={adminLayout:"AdminLayout_adminLayout__5Oi4c",mainContent:"AdminLayout_mainContent__INtLu",sidebarCollapsed:"AdminLayout_sidebarCollapsed__oAEhD",pageContent:"AdminLayout_pageContent__aWMEk",adminFooter:"AdminLayout_adminFooter__mTvA1",footerContent:"AdminLayout_footerContent__z6du0",footerLeft:"AdminLayout_footerLeft__gGY8P",version:"AdminLayout_version__vpU9q",footerRight:"AdminLayout_footerRight__kyodA",footerLink:"AdminLayout_footerLink__jvWuv",securityBanner:"AdminLayout_securityBanner__KTGT5",securityIcon:"AdminLayout_securityIcon__eZwIM",loadingContainer:"AdminLayout_loadingContainer__Wbedv",loadingSpinner:"AdminLayout_loadingSpinner__C8mvO",spin:"AdminLayout_spin__DZv4U",sidebar:"AdminLayout_sidebar__1zyFe",mobileBottomNav:"AdminLayout_mobileBottomNav__2kopO",mobileOverlay:"AdminLayout_mobileOverlay__BNO2v",debugMobile:"AdminLayout_debugMobile__hcFrB"}},5523:e=>{e.exports={sidebar:"AdminSidebar_sidebar__qOEP2",collapsed:"AdminSidebar_collapsed__mPopM",mobile:"AdminSidebar_mobile__sXELg",sidebarHeader:"AdminSidebar_sidebarHeader__h8NsD",logo:"AdminSidebar_logo__MfKT2",logoIcon:"AdminSidebar_logoIcon__ObH7O",logoIconOnly:"AdminSidebar_logoIconOnly__AoqbB",logoText:"AdminSidebar_logoText__6AwFU",logoTitle:"AdminSidebar_logoTitle__rj3SO",logoSubtitle:"AdminSidebar_logoSubtitle__ZlArc",toggleButton:"AdminSidebar_toggleButton__93srV",userInfo:"AdminSidebar_userInfo__0v9i_",userAvatar:"AdminSidebar_userAvatar__Rg3G_",userDetails:"AdminSidebar_userDetails__kA16n",userName:"AdminSidebar_userName__2reke",userRole:"AdminSidebar_userRole__Bo1eM",navigation:"AdminSidebar_navigation__LpNEH",menuList:"AdminSidebar_menuList__krOTx",menuItem:"AdminSidebar_menuItem__A5Arm",menuLink:"AdminSidebar_menuLink__ZSnZI",active:"AdminSidebar_active__4G9nw",menuIcon:"AdminSidebar_menuIcon__yJF_1",menuLabel:"AdminSidebar_menuLabel__WEpLi",expandButton:"AdminSidebar_expandButton__qS2q4",submenu:"AdminSidebar_submenu__4dAAZ",submenuItem:"AdminSidebar_submenuItem__WiecI",submenuLink:"AdminSidebar_submenuLink__ZYwCJ",submenuIcon:"AdminSidebar_submenuIcon__ThbKs",submenuLabel:"AdminSidebar_submenuLabel__ocpuH",sidebarFooter:"AdminSidebar_sidebarFooter__NML_U",footerContent:"AdminSidebar_footerContent__qOiZI",versionInfo:"AdminSidebar_versionInfo__bpisr",version:"AdminSidebar_version__EyLxD",environment:"AdminSidebar_environment__teF9S",securityIndicator:"AdminSidebar_securityIndicator__S_6EA",securityIcon:"AdminSidebar_securityIcon__GdGG2",securityText:"AdminSidebar_securityText___evKe"}},3920:e=>{e.exports={breadcrumbNavigation:"BreadcrumbNavigation_breadcrumbNavigation__qrpk6",breadcrumbList:"BreadcrumbNavigation_breadcrumbList__J3l8C",breadcrumbItem:"BreadcrumbNavigation_breadcrumbItem__ntSbT",breadcrumbLink:"BreadcrumbNavigation_breadcrumbLink__zUz4h",breadcrumbCurrent:"BreadcrumbNavigation_breadcrumbCurrent__piKeq",active:"BreadcrumbNavigation_active__nMBn7",breadcrumbIcon:"BreadcrumbNavigation_breadcrumbIcon__NwAjD",breadcrumbLabel:"BreadcrumbNavigation_breadcrumbLabel__RfAkY",breadcrumbSeparator:"BreadcrumbNavigation_breadcrumbSeparator__s8MDJ",breadcrumbFadeIn:"BreadcrumbNavigation_breadcrumbFadeIn__8jkp9",truncated:"BreadcrumbNavigation_truncated__ZieS_",srOnly:"BreadcrumbNavigation_srOnly__BJXug",loading:"BreadcrumbNavigation_loading__POMFv",error:"BreadcrumbNavigation_error__3_Q_W"}},1077:e=>{e.exports={globalSearch:"GlobalSearch_globalSearch__x64r6",searchInput:"GlobalSearch_searchInput__NDaxq",searchIcon:"GlobalSearch_searchIcon__COB30",input:"GlobalSearch_input__Bshcp",loadingSpinner:"GlobalSearch_loadingSpinner__Pu_Vy",spinner:"GlobalSearch_spinner__Xfout",spin:"GlobalSearch_spin__dL1SW",clearButton:"GlobalSearch_clearButton__FHcBJ",searchResults:"GlobalSearch_searchResults__pRA9E",slideDown:"GlobalSearch_slideDown__pmrfB",resultsHeader:"GlobalSearch_resultsHeader__vSZYk",resultsCount:"GlobalSearch_resultsCount__JZUY_",resultsList:"GlobalSearch_resultsList__regZQ",resultItem:"GlobalSearch_resultItem__zKY59",selected:"GlobalSearch_selected__9T29Y",resultIcon:"GlobalSearch_resultIcon__rrjs_",resultContent:"GlobalSearch_resultContent__ayKhj",resultTitle:"GlobalSearch_resultTitle__5oylH",resultSubtitle:"GlobalSearch_resultSubtitle___rge4",resultDescription:"GlobalSearch_resultDescription__mUJl9",resultType:"GlobalSearch_resultType__va8Tu",resultsFooter:"GlobalSearch_resultsFooter__l4OQy",moreResults:"GlobalSearch_moreResults__pHkvq",noResults:"GlobalSearch_noResults__3CVFL",noResultsIcon:"GlobalSearch_noResultsIcon__sRUUy",noResultsText:"GlobalSearch_noResultsText__kE3v7",noResultsHint:"GlobalSearch_noResultsHint__U_W50",errorMessage:"GlobalSearch_errorMessage__zGgnP",errorIcon:"GlobalSearch_errorIcon___QqjF"}},875:e=>{e.exports={overlay:"KeyboardShortcuts_overlay__50H6k",fadeIn:"KeyboardShortcuts_fadeIn__3V9UQ",modal:"KeyboardShortcuts_modal___mIHb",slideUp:"KeyboardShortcuts_slideUp__v_6v_",header:"KeyboardShortcuts_header__qglVm",title:"KeyboardShortcuts_title__I96H_",closeBtn:"KeyboardShortcuts_closeBtn__uBydE",content:"KeyboardShortcuts_content__JMnWB",intro:"KeyboardShortcuts_intro__4pzvg",shortcutsGrid:"KeyboardShortcuts_shortcutsGrid__lq1C_",category:"KeyboardShortcuts_category__atJiS",categoryTitle:"KeyboardShortcuts_categoryTitle__DcncJ",shortcutsList:"KeyboardShortcuts_shortcutsList__9cq54",shortcutItem:"KeyboardShortcuts_shortcutItem__vBzTj",keys:"KeyboardShortcuts_keys__5JoYl",key:"KeyboardShortcuts_key__oyNAe",keySeparator:"KeyboardShortcuts_keySeparator__2xiWn",description:"KeyboardShortcuts_description__e_Npl",footer:"KeyboardShortcuts_footer__hvpay",tip:"KeyboardShortcuts_tip__PMQsZ",helpButton:"KeyboardShortcuts_helpButton__2NzAk"}},7044:e=>{e.exports={mobileBottomNav:"MobileBottomNav_mobileBottomNav__AlYiY",navContainer:"MobileBottomNav_navContainer__0GjFb",navItem:"MobileBottomNav_navItem__y5tGx",active:"MobileBottomNav_active__gBrP7",navIcon:"MobileBottomNav_navIcon__a9GVa",navLabel:"MobileBottomNav_navLabel__okVa_",activeIndicator:"MobileBottomNav_activeIndicator__QbcyY",pulse:"MobileBottomNav_pulse__jftGw",safeAreaPadding:"MobileBottomNav_safeAreaPadding__GKpOi"}},1867:(e,i,r)=>{"use strict";r.d(i,{Z:()=>S});var s=r(997),t=r(6689),n=r.n(t),a=r(1664),o=r.n(a),c=r(1163),l=r(1077),d=r.n(l);function u({placeholder:e="Search customers, bookings, services...",className:i=""}){let[r,n]=(0,t.useState)(""),[a,o]=(0,t.useState)([]),[l,u]=(0,t.useState)(!1),[m,h]=(0,t.useState)(!1),[b,p]=(0,t.useState)(null),[_,g]=(0,t.useState)(-1),f=(0,t.useRef)(null),v=(0,t.useRef)(null),D=(0,c.useRouter)(),y=e=>{h(!1),n(""),g(-1),D.push(e.url)},x=e=>{switch(e){case"customer":return"\uD83D\uDC64";case"booking":return"\uD83D\uDCC5";case"service":return"✨";default:return"\uD83D\uDCC4"}},N=e=>{switch(e){case"customer":return"Customer";case"booking":return"Booking";case"service":return"Service";default:return"Result"}};return(0,s.jsxs)("div",{className:`${d().globalSearch} ${i}`,ref:f,children:[(0,s.jsxs)("div",{className:d().searchInput,children:[s.jsx("div",{className:d().searchIcon,children:"\uD83D\uDD0D"}),s.jsx("input",{ref:v,type:"text",value:r,onChange:e=>n(e.target.value),onFocus:()=>{a.length>0&&h(!0)},placeholder:e,className:d().input,autoComplete:"off","data-global-search":"true"}),l&&s.jsx("div",{className:d().loadingSpinner,children:s.jsx("div",{className:d().spinner})}),r&&s.jsx("button",{className:d().clearButton,onClick:()=>{n(""),o([]),h(!1),v.current?.focus()},title:"Clear search",children:"✕"})]}),m&&s.jsx("div",{className:d().searchResults,children:b?(0,s.jsxs)("div",{className:d().errorMessage,children:[s.jsx("span",{className:d().errorIcon,children:"⚠️"}),b]}):a.length>0?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:d().resultsHeader,children:(0,s.jsxs)("span",{className:d().resultsCount,children:[a.length," result",1!==a.length?"s":"",' for "',r,'"']})}),s.jsx("div",{className:d().resultsList,children:a.map((e,i)=>(0,s.jsxs)("div",{className:`${d().resultItem} ${i===_?d().selected:""}`,onClick:()=>y(e),onMouseEnter:()=>g(i),children:[s.jsx("div",{className:d().resultIcon,children:x(e.type)}),(0,s.jsxs)("div",{className:d().resultContent,children:[s.jsx("div",{className:d().resultTitle,children:e.title}),s.jsx("div",{className:d().resultSubtitle,children:e.subtitle}),e.description&&s.jsx("div",{className:d().resultDescription,children:e.description})]}),s.jsx("div",{className:d().resultType,children:N(e.type)})]},`${e.type}-${e.id}`))}),a.length>=10&&s.jsx("div",{className:d().resultsFooter,children:s.jsx("span",{className:d().moreResults,children:"Showing first 10 results. Refine your search for more specific results."})})]}):r.trim()&&!l?(0,s.jsxs)("div",{className:d().noResults,children:[s.jsx("span",{className:d().noResultsIcon,children:"\uD83D\uDD0D"}),(0,s.jsxs)("div",{className:d().noResultsText,children:['No results found for "',r,'"']}),s.jsx("div",{className:d().noResultsHint,children:"Try searching for customer names, booking dates, or service names"})]}):null})]})}var m=r(3920),h=r.n(m);let b={"/admin":{label:"Admin",icon:"\uD83C\uDFE0"},"/admin/dashboard":{label:"Dashboard",icon:"\uD83D\uDCCA",parent:"/admin"},"/admin/services":{label:"Services",icon:"✨",parent:"/admin/dashboard"},"/admin/services/new":{label:"New Service",icon:"➕",parent:"/admin/services"},"/admin/services/[id]":{label:"Service Details",icon:"\uD83D\uDCDD",parent:"/admin/services"},"/admin/products":{label:"Products",icon:"\uD83D\uDECD️",parent:"/admin/dashboard"},"/admin/products/new":{label:"New Product",icon:"➕",parent:"/admin/products"},"/admin/products/[id]":{label:"Product Details",icon:"\uD83D\uDCDD",parent:"/admin/products"},"/admin/inventory":{label:"Inventory",icon:"\uD83D\uDCE6",parent:"/admin/dashboard"},"/admin/inventory/new":{label:"New Item",icon:"➕",parent:"/admin/inventory"},"/admin/pos":{label:"POS Terminal",icon:"\uD83D\uDCB3",parent:"/admin/dashboard"},"/admin/customers":{label:"Customers",icon:"\uD83D\uDC65",parent:"/admin/dashboard"},"/admin/customers/new":{label:"New Customer",icon:"➕",parent:"/admin/customers"},"/admin/customers/[id]":{label:"Customer Details",icon:"\uD83D\uDC64",parent:"/admin/customers"},"/admin/bookings":{label:"Bookings",icon:"\uD83D\uDCC5",parent:"/admin/dashboard"},"/admin/bookings/new":{label:"New Booking",icon:"➕",parent:"/admin/bookings"},"/admin/bookings/[id]":{label:"Booking Details",icon:"\uD83D\uDCDD",parent:"/admin/bookings"},"/admin/staff":{label:"Staff Management",icon:"\uD83D\uDC68‍\uD83D\uDCBC",parent:"/admin/dashboard"},"/admin/staff/onboarding":{label:"Staff Onboarding",icon:"\uD83C\uDFAF",parent:"/admin/staff"},"/admin/staff/training":{label:"Training",icon:"\uD83D\uDCDA",parent:"/admin/staff"},"/admin/staff/performance":{label:"Performance",icon:"\uD83D\uDCC8",parent:"/admin/staff"},"/admin/artists":{label:"Artists",icon:"\uD83C\uDFA8",parent:"/admin/dashboard"},"/admin/artists/portfolio":{label:"Portfolio",icon:"\uD83D\uDDBC️",parent:"/admin/artists"},"/admin/artists/[id]":{label:"Artist Details",icon:"\uD83D\uDC68‍\uD83C\uDFA8",parent:"/admin/artists"},"/admin/communications":{label:"Communications",icon:"\uD83D\uDCE7",parent:"/admin/dashboard"},"/admin/email-templates":{label:"Email Templates",icon:"\uD83D\uDCE7",parent:"/admin/communications"},"/admin/sms-templates":{label:"SMS Templates",icon:"\uD83D\uDCF1",parent:"/admin/communications"},"/admin/notifications":{label:"Notifications",icon:"\uD83D\uDD14",parent:"/admin/communications"},"/admin/reports":{label:"Reports & Analytics",icon:"\uD83D\uDCCA",parent:"/admin/dashboard"},"/admin/receipts":{label:"Receipt Templates",icon:"\uD83E\uDDFE",parent:"/admin/reports"},"/admin/feedback":{label:"Customer Feedback",icon:"\uD83D\uDCAC",parent:"/admin/reports"},"/admin/suppliers":{label:"Suppliers",icon:"\uD83C\uDFE2",parent:"/admin/inventory"},"/admin/suppliers/new":{label:"New Supplier",icon:"➕",parent:"/admin/suppliers"},"/admin/purchase-orders":{label:"Purchase Orders",icon:"\uD83D\uDCCB",parent:"/admin/inventory"},"/admin/purchase-orders/new":{label:"New Order",icon:"➕",parent:"/admin/purchase-orders"},"/admin/settings":{label:"Settings",icon:"⚙️",parent:"/admin/dashboard"},"/admin/tips":{label:"Tips Management",icon:"\uD83D\uDCB0",parent:"/admin/settings"},"/admin/mobile-debug":{label:"Mobile Debug",icon:"\uD83D\uDD27",parent:"/admin/settings"},"/admin/mobile-test":{label:"Mobile Test",icon:"\uD83D\uDCF1",parent:"/admin/settings"}};function p({className:e="",showIcons:i=!0,maxItems:r=5}){let n=(0,c.useRouter)(),a=function(){(0,c.useRouter)();let[e,i]=(0,t.useState)({loading:!1});return e}(),l=(0,t.useMemo)(()=>{let e=n.asPath.split("?")[0];e.split("/").filter(Boolean);let i=[];e.startsWith("/admin")&&"/admin/dashboard"!==e&&i.push({label:"Dashboard",href:"/admin/dashboard",icon:"\uD83D\uDCCA"});let s=e=>{let i=b[e];if(!i){let i=b[e.replace(/\/[^/]+$/,"/[id]")];if(i){let r=[];i.parent&&r.push(...s(i.parent));let t=e.split("/"),n=t[t.length-1],o=i.label,c=a.loading?"Loading...":e.includes("/customers/")&&a.customerName?a.customerName:e.includes("/bookings/")&&a.bookingId?a.customerName&&a.serviceName?`${a.customerName} - ${a.serviceName}`:a.customerName?`Booking for ${a.customerName}`:`Booking #${a.bookingId.slice(0,8)}`:e.includes("/services/")&&a.serviceName?a.serviceName:e.includes("/products/")&&a.productName?a.productName:e.includes("/artists/")&&a.artistName?a.artistName:null;return c?o=c:"new"!==n&&(e.includes("/customers/")?o=`Customer #${n.slice(0,8)}`:e.includes("/bookings/")?o=`Booking #${n.slice(0,8)}`:e.includes("/services/")?o=`Service #${n.slice(0,8)}`:e.includes("/products/")?o=`Product #${n.slice(0,8)}`:e.includes("/artists/")&&(o=`Artist #${n.slice(0,8)}`)),r.push({label:o,href:e,icon:i.icon}),r}let r=e.split("/").filter(Boolean);return[{label:r[r.length-1].charAt(0).toUpperCase()+r[r.length-1].slice(1),href:e,icon:"\uD83D\uDCC4"}]}let r=[];return i.parent&&r.push(...s(i.parent)),r.push({label:i.label,href:e,icon:i.icon}),r},t=s(e),o=[...i];return(t.forEach(e=>{o.some(i=>i.href===e.href)||o.push(e)}),o.length>0&&(o[o.length-1].isActive=!0),r&&o.length>r)?[o[0],{label:"...",href:"",icon:"⋯"},...o.slice(-2)]:o},[n.asPath,r]);return l.length<=1?null:s.jsx("nav",{className:`${h().breadcrumbNavigation} ${e}`,"aria-label":"Breadcrumb",children:s.jsx("ol",{className:h().breadcrumbList,children:l.map((e,r)=>(0,s.jsxs)("li",{className:h().breadcrumbItem,children:[e.href&&!e.isActive?(0,s.jsxs)(o(),{href:e.href,className:h().breadcrumbLink,children:[i&&e.icon&&s.jsx("span",{className:h().breadcrumbIcon,children:e.icon}),s.jsx("span",{className:h().breadcrumbLabel,children:e.label})]}):(0,s.jsxs)("span",{className:`${h().breadcrumbCurrent} ${e.isActive?h().active:""}`,children:[i&&e.icon&&s.jsx("span",{className:h().breadcrumbIcon,children:e.icon}),s.jsx("span",{className:h().breadcrumbLabel,children:e.label})]}),r<l.length-1&&s.jsx("span",{className:h().breadcrumbSeparator,"aria-hidden":"true",children:"/"})]},`${e.href}-${r}`))})})}var _=r(6405),g=r(875),f=r.n(g);let v=[{keys:["Ctrl","K"],description:"Open global search",category:"Navigation"},{keys:["Ctrl","H"],description:"Go to dashboard",category:"Navigation"},{keys:["Ctrl","U"],description:"Go to customers",category:"Navigation"},{keys:["Ctrl","B"],description:"Go to bookings",category:"Navigation"},{keys:["Ctrl","S"],description:"Go to services",category:"Navigation"},{keys:["Ctrl","P"],description:"Go to products",category:"Navigation"},{keys:["Ctrl","I"],description:"Go to inventory",category:"Navigation"},{keys:["Ctrl","T"],description:"Go to staff management",category:"Navigation"},{keys:["Ctrl","N"],description:"Create new item",category:"Actions"},{keys:["Ctrl","E"],description:"Export current data",category:"Actions"},{keys:["Ctrl","A"],description:"Select all items",category:"Actions"},{keys:["Delete"],description:"Delete selected items",category:"Actions"},{keys:["Ctrl","Z"],description:"Undo last action",category:"Actions"},{keys:["Ctrl","Y"],description:"Redo last action",category:"Actions"},{keys:["Escape"],description:"Close modal/dropdown",category:"Interface"},{keys:["Tab"],description:"Navigate between elements",category:"Interface"},{keys:["Shift","Tab"],description:"Navigate backwards",category:"Interface"},{keys:["Enter"],description:"Confirm action",category:"Interface"},{keys:["Space"],description:"Toggle selection",category:"Interface"},{keys:["Ctrl","F"],description:"Focus search field",category:"Search & Filters"},{keys:["Ctrl","L"],description:"Clear all filters",category:"Search & Filters"},{keys:["↑","↓"],description:"Navigate search results",category:"Search & Filters"},{keys:["Ctrl","?"],description:"Show keyboard shortcuts",category:"Help"},{keys:["F1"],description:"Open help documentation",category:"Help"}];function D({isOpen:e,onClose:i}){let[r,a]=(0,t.useState)(!1);if(!r||!e)return null;let o=Array.from(new Set(v.map(e=>e.category))),c=e=>({Ctrl:"⌘",Alt:"⌥",Shift:"⇧",Enter:"↵",Space:"␣",Tab:"⇥",Escape:"⎋",Delete:"⌫","↑":"↑","↓":"↓","←":"←","→":"→"})[e]||e,l=s.jsx("div",{className:f().overlay,onClick:i,children:(0,s.jsxs)("div",{className:f().modal,onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("header",{className:f().header,children:[s.jsx("h2",{className:f().title,children:"Keyboard Shortcuts"}),s.jsx("button",{onClick:i,className:f().closeBtn,"aria-label":"Close shortcuts help",children:"✕"})]}),(0,s.jsxs)("div",{className:f().content,children:[s.jsx("div",{className:f().intro,children:s.jsx("p",{children:"Use these keyboard shortcuts to navigate and interact with the admin dashboard more efficiently."})}),s.jsx("div",{className:f().shortcutsGrid,children:o.map(e=>(0,s.jsxs)("div",{className:f().category,children:[s.jsx("h3",{className:f().categoryTitle,children:e}),s.jsx("div",{className:f().shortcutsList,children:v.filter(i=>i.category===e).map((e,i)=>(0,s.jsxs)("div",{className:f().shortcutItem,children:[s.jsx("div",{className:f().keys,children:e.keys.map((i,r)=>(0,s.jsxs)(n().Fragment,{children:[s.jsx("kbd",{className:f().key,children:c(i)}),r<e.keys.length-1&&s.jsx("span",{className:f().keySeparator,children:"+"})]},r))}),s.jsx("div",{className:f().description,children:e.description})]},i))})]},e))}),s.jsx("div",{className:f().footer,children:(0,s.jsxs)("div",{className:f().tip,children:[s.jsx("strong",{children:"\uD83D\uDCA1 Tip:"})," Most shortcuts work globally throughout the admin dashboard. Press ",s.jsx("kbd",{className:f().key,children:"Ctrl"})," + ",s.jsx("kbd",{className:f().key,children:"?"})," anytime to open this help."]})})]})]})});return(0,_.createPortal)(l,document.body)}function y({className:e=""}){let{showShortcuts:i,openShortcuts:r,closeShortcuts:n}=function(){let[e,i]=(0,t.useState)(!1);return{showShortcuts:e,setShowShortcuts:i,openShortcuts:()=>i(!0),closeShortcuts:()=>i(!1)}}();return(0,s.jsxs)(s.Fragment,{children:[s.jsx("button",{onClick:r,className:`${f().helpButton} ${e}`,title:"Keyboard shortcuts (Ctrl + ?)","aria-label":"Show keyboard shortcuts help",children:"⌨️"}),s.jsx(D,{isOpen:i,onClose:n})]})}var x=r(4830),N=r.n(x);function S({user:e,onLogout:i,onToggleSidebar:r,sidebarCollapsed:n}){let[a,c]=(0,t.useState)(!1),[l,d]=(0,t.useState)(!1),m=(0,t.useRef)(null),h=(0,t.useRef)(null),b=e=>{switch(e){case"DEV":return"#dc3545";case"Admin":return"#3788d8";case"Artist":return"#28a745";case"Braider":return"#fd7e14";default:return"#6c757d"}};return(0,s.jsxs)("header",{className:N().adminHeader,children:[(0,s.jsxs)("div",{className:N().headerLeft,children:[s.jsx("button",{className:N().sidebarToggle,onClick:()=>{console.log("\uD83C\uDF54 Hamburger menu clicked!",{sidebarCollapsed:n}),r()},title:n?"Expand sidebar":"Collapse sidebar",children:(0,s.jsxs)("span",{className:N().hamburger,children:[s.jsx("span",{}),s.jsx("span",{}),s.jsx("span",{})]})}),s.jsx(p,{className:N().breadcrumb,showIcons:!0,maxItems:4})]}),s.jsx("div",{className:N().headerCenter,children:s.jsx(u,{})}),(0,s.jsxs)("div",{className:N().headerRight,children:[(0,s.jsxs)("div",{className:N().quickActions,children:[s.jsx(o(),{href:"/admin/bookings/new",className:N().quickAction,title:"New Booking",children:"\uD83D\uDCC5"}),s.jsx(o(),{href:"/admin/customers/new",className:N().quickAction,title:"New Customer",children:"\uD83D\uDC64"}),s.jsx("button",{className:N().quickAction,title:"Refresh",children:"\uD83D\uDD04"}),s.jsx(y,{className:N().quickAction})]}),(0,s.jsxs)("div",{className:N().notifications,ref:h,children:[(0,s.jsxs)("button",{className:N().notificationButton,onClick:()=>d(!l),title:"Notifications",children:["\uD83D\uDD14",s.jsx("span",{className:N().notificationBadge,children:"3"})]}),l&&(0,s.jsxs)("div",{className:N().notificationDropdown,children:[(0,s.jsxs)("div",{className:N().notificationHeader,children:[s.jsx("h3",{children:"Notifications"}),s.jsx("button",{className:N().markAllRead,children:"Mark all read"})]}),(0,s.jsxs)("div",{className:N().notificationList,children:[(0,s.jsxs)("div",{className:N().notificationItem,children:[s.jsx("div",{className:N().notificationIcon,children:"\uD83D\uDCC5"}),(0,s.jsxs)("div",{className:N().notificationContent,children:[s.jsx("div",{className:N().notificationTitle,children:"New booking request"}),s.jsx("div",{className:N().notificationTime,children:"5 minutes ago"})]})]}),(0,s.jsxs)("div",{className:N().notificationItem,children:[s.jsx("div",{className:N().notificationIcon,children:"\uD83D\uDCB0"}),(0,s.jsxs)("div",{className:N().notificationContent,children:[s.jsx("div",{className:N().notificationTitle,children:"Payment received"}),s.jsx("div",{className:N().notificationTime,children:"1 hour ago"})]})]}),(0,s.jsxs)("div",{className:N().notificationItem,children:[s.jsx("div",{className:N().notificationIcon,children:"⚠️"}),(0,s.jsxs)("div",{className:N().notificationContent,children:[s.jsx("div",{className:N().notificationTitle,children:"Low inventory alert"}),s.jsx("div",{className:N().notificationTime,children:"2 hours ago"})]})]})]}),s.jsx("div",{className:N().notificationFooter,children:s.jsx(o(),{href:"/admin/notifications",children:"View all notifications"})})]})]}),(0,s.jsxs)("div",{className:N().userMenu,ref:m,children:[(0,s.jsxs)("button",{className:N().userButton,onClick:()=>{try{console.log("\uD83D\uDC64 User menu clicked!",{current:a,willBe:!a}),c(!a)}catch(e){console.error("❌ Error toggling user menu:",e)}},children:[(0,s.jsxs)("div",{className:N().userAvatar,children:[e.firstName.charAt(0),e.lastName.charAt(0)]}),(0,s.jsxs)("div",{className:N().userInfo,children:[(0,s.jsxs)("div",{className:N().userName,children:[e.firstName," ",e.lastName]}),s.jsx("div",{className:N().userRole,style:{color:b(e.role)},children:e.role})]}),s.jsx("div",{className:N().dropdownArrow,children:a?"▲":"▼"})]}),a&&(0,s.jsxs)("div",{className:N().userDropdown,children:[(0,s.jsxs)("div",{className:N().userDropdownHeader,children:[s.jsx("div",{className:N().userEmail,children:e.email}),s.jsx("div",{className:N().userRoleBadge,style:{backgroundColor:b(e.role)},children:e.role})]}),(0,s.jsxs)("div",{className:N().userDropdownMenu,children:[(0,s.jsxs)(o(),{href:"/admin/profile",className:N().dropdownItem,children:[s.jsx("span",{className:N().dropdownIcon,children:"\uD83D\uDC64"}),"Profile Settings"]}),(0,s.jsxs)(o(),{href:"/admin/security",className:N().dropdownItem,children:[s.jsx("span",{className:N().dropdownIcon,children:"\uD83D\uDD12"}),"Security & MFA"]}),(0,s.jsxs)(o(),{href:"/admin/preferences",className:N().dropdownItem,children:[s.jsx("span",{className:N().dropdownIcon,children:"⚙️"}),"Preferences"]}),s.jsx("div",{className:N().dropdownDivider}),(0,s.jsxs)(o(),{href:"/admin/help",className:N().dropdownItem,children:[s.jsx("span",{className:N().dropdownIcon,children:"❓"}),"Help & Support"]}),s.jsx("div",{className:N().dropdownDivider}),(0,s.jsxs)("button",{className:`${N().dropdownItem} ${N().logoutItem}`,onClick:i,children:[s.jsx("span",{className:N().dropdownIcon,children:"\uD83D\uDEAA"}),"Sign Out"]})]})]})]})]})]})}},4845:(e,i,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(i,{Z:()=>f});var t=r(997),n=r(6689),a=r(1163),o=r(1664),c=r.n(o),l=r(3590),d=r(4528),u=r(1867),m=r(9489),h=r(5462),b=r(8568),p=r(8172),_=r.n(p),g=e([l]);function f({children:e}){let i=(0,a.useRouter)(),{user:r,loading:s}=(0,b.a)(),[o,p]=(0,n.useState)(!1),[g,f]=(0,n.useState)(!1),[v,D]=(0,n.useState)(!1),y=async()=>{try{if((await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}})).ok)localStorage.removeItem("admin-token"),l.toast.success("Logged out successfully"),i.push("/admin/login");else throw Error("Logout failed")}catch(e){console.error("Logout error:",e),localStorage.removeItem("admin-token"),i.push("/admin/login")}},x=()=>{p(!o)};return s?(0,t.jsxs)("div",{className:_().loadingContainer,children:[t.jsx("div",{className:_().loadingSpinner}),t.jsx("p",{children:"Loading admin portal..."})]}):r?t.jsx(h.Z,{children:(0,t.jsxs)("div",{className:_().adminLayout,children:[t.jsx(d.Z,{user:r,collapsed:o,onToggle:x,isMobile:g}),(0,t.jsxs)("div",{className:`${_().mainContent} ${o?_().sidebarCollapsed:""}`,children:[t.jsx(u.Z,{user:r,onLogout:y,onToggleSidebar:g?()=>{console.log("\uD83D\uDCF1 Mobile menu toggle:",{current:v,willBe:!v,isMobile:g}),D(!v)}:x,sidebarCollapsed:o}),t.jsx("main",{className:_().pageContent,children:e}),t.jsx("footer",{className:_().adminFooter,children:(0,t.jsxs)("div",{className:_().footerContent,children:[(0,t.jsxs)("div",{className:_().footerLeft,children:[t.jsx("span",{children:"\xa9 2024 Ocean Soul Sparkles Admin Portal"}),t.jsx("span",{className:_().version,children:"v1.0.0"})]}),(0,t.jsxs)("div",{className:_().footerRight,children:[t.jsx(c(),{href:"/admin/help",className:_().footerLink,children:"Help"}),t.jsx(c(),{href:"/admin/privacy",className:_().footerLink,children:"Privacy"}),t.jsx(c(),{href:"/admin/terms",className:_().footerLink,children:"Terms"})]})]})})]}),g&&v&&t.jsx("div",{className:_().mobileOverlay,onClick:()=>D(!1)}),t.jsx("div",{className:_().mobileBottomNav,children:t.jsx(m.Z,{userRole:r?.role||"Admin"})}),(0,t.jsxs)("div",{className:_().debugMobile,children:["Mobile: ",g?"YES":"NO"," | Width: ","N/A"]}),(0,t.jsxs)("div",{className:_().securityBanner,children:[t.jsx("div",{className:_().securityIcon,children:"\uD83D\uDD12"}),t.jsx("span",{children:"Secure Admin Portal - All actions are logged and monitored"})]})]})}):null}l=(g.then?(await g)():g)[0],s()}catch(e){s(e)}})},4528:(e,i,r)=>{"use strict";r.d(i,{Z:()=>u});var s=r(997),t=r(6689),n=r(1664),a=r.n(n),o=r(1163),c=r(5523),l=r.n(c);let d=[{id:"dashboard",label:"Dashboard",icon:"\uD83D\uDCCA",href:"/admin/dashboard",roles:["DEV","Admin","Artist","Braider"]},{id:"bookings",label:"Bookings",icon:"\uD83D\uDCC5",href:"/admin/bookings",roles:["DEV","Admin","Artist","Braider"]},{id:"customers",label:"Customers",icon:"\uD83D\uDC65",href:"/admin/customers",roles:["DEV","Admin"]},{id:"services",label:"Services",icon:"✨",href:"/admin/services",roles:["DEV","Admin"]},{id:"products",label:"Products",icon:"\uD83D\uDECD️",href:"/admin/products",roles:["DEV","Admin"]},{id:"suppliers",label:"Suppliers",icon:"\uD83D\uDCE6",href:"/admin/suppliers",roles:["DEV","Admin"]},{id:"purchase-orders",label:"Purchase Orders",icon:"\uD83D\uDCCB",href:"/admin/purchase-orders",roles:["DEV","Admin"]},{id:"staff",label:"Staff Management",icon:"\uD83D\uDC68‍\uD83D\uDCBC",href:"/admin/staff",roles:["DEV","Admin"],children:[{id:"staff-overview",label:"Staff Overview",icon:"\uD83D\uDC65",href:"/admin/staff",roles:["DEV","Admin"]},{id:"staff-onboarding",label:"Onboarding",icon:"\uD83D\uDCCB",href:"/admin/staff/onboarding",roles:["DEV","Admin"]},{id:"staff-training",label:"Training",icon:"\uD83C\uDF93",href:"/admin/staff/training",roles:["DEV","Admin"]},{id:"staff-performance",label:"Performance",icon:"\uD83D\uDCCA",href:"/admin/staff/performance",roles:["DEV","Admin"]},{id:"staff-schedule",label:"Schedule Management",icon:"\uD83D\uDDD3️",href:"/admin/staff/schedule",roles:["DEV","Admin"]}]},{id:"artists",label:"Artists",icon:"\uD83C\uDFA8",href:"/admin/artists",roles:["DEV","Admin"],children:[{id:"artists-overview",label:"Artists Overview",icon:"\uD83D\uDC68‍\uD83C\uDFA8",href:"/admin/artists",roles:["DEV","Admin"]},{id:"artists-portfolio",label:"Portfolio Management",icon:"\uD83D\uDDBC️",href:"/admin/artists/portfolio",roles:["DEV","Admin"]},{id:"artists-schedule",label:"Artist Scheduling",icon:"\uD83D\uDCC5",href:"/admin/artists/schedule",roles:["DEV","Admin"]},{id:"artists-commissions",label:"Commission Tracking",icon:"\uD83D\uDCB0",href:"/admin/artists/commissions",roles:["DEV","Admin"]}]},{id:"tips",label:"Tip Management",icon:"\uD83D\uDCB0",href:"/admin/tips",roles:["DEV","Admin"]},{id:"receipts",label:"Receipts",icon:"\uD83E\uDDFE",href:"/admin/receipts",roles:["DEV","Admin"]},{id:"reports",label:"Reports",icon:"\uD83D\uDCC8",href:"/admin/reports",roles:["DEV","Admin"]},{id:"communications",label:"Communications",icon:"\uD83D\uDCE7",href:"/admin/communications",roles:["DEV","Admin"],children:[{id:"email-templates",label:"Email Templates",icon:"\uD83D\uDCDD",href:"/admin/email-templates",roles:["DEV","Admin"]},{id:"sms-templates",label:"SMS Templates",icon:"\uD83D\uDCF1",href:"/admin/sms-templates",roles:["DEV","Admin"]},{id:"communications-log",label:"Communications Log",icon:"\uD83D\uDCCB",href:"/admin/communications",roles:["DEV","Admin"]},{id:"feedback",label:"Customer Feedback",icon:"⭐",href:"/admin/feedback",roles:["DEV","Admin"]}]},{id:"notifications",label:"Notifications",icon:"\uD83D\uDD14",href:"/admin/notifications",roles:["DEV","Admin"]},{id:"settings",label:"Settings",icon:"⚙️",href:"/admin/settings",roles:["DEV","Admin"]}];function u({user:e,collapsed:i,onToggle:r,isMobile:n}){let c=(0,o.useRouter)(),[u,m]=(0,t.useState)([]),h=e=>{m(i=>i.includes(e)?i.filter(i=>i!==e):[...i,e])},b=i=>i.includes(e.role),p=e=>c.pathname===e||c.pathname.startsWith(e+"/"),_=d.filter(e=>b(e.roles));return(0,s.jsxs)("aside",{className:`${l().sidebar} ${i?l().collapsed:""} ${n?l().mobile:""}`,children:[(0,s.jsxs)("div",{className:l().sidebarHeader,children:[(0,s.jsxs)("div",{className:l().logo,children:[!i&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:l().logoIcon,children:"\uD83C\uDF0A"}),(0,s.jsxs)("div",{className:l().logoText,children:[s.jsx("div",{className:l().logoTitle,children:"Ocean Soul"}),s.jsx("div",{className:l().logoSubtitle,children:"Admin"})]})]}),i&&s.jsx("div",{className:l().logoIconOnly,children:"\uD83C\uDF0A"})]}),!n&&s.jsx("button",{className:l().toggleButton,onClick:r,title:i?"Expand sidebar":"Collapse sidebar",children:i?"→":"←"})]}),(0,s.jsxs)("div",{className:l().userInfo,children:[(0,s.jsxs)("div",{className:l().userAvatar,children:[e.firstName.charAt(0),e.lastName.charAt(0)]}),!i&&(0,s.jsxs)("div",{className:l().userDetails,children:[(0,s.jsxs)("div",{className:l().userName,children:[e.firstName," ",e.lastName]}),s.jsx("div",{className:l().userRole,children:e.role})]})]}),s.jsx("nav",{className:l().navigation,children:s.jsx("ul",{className:l().menuList,children:_.map(e=>(0,s.jsxs)("li",{className:l().menuItem,children:[(0,s.jsxs)(a(),{href:e.href,className:`${l().menuLink} ${p(e.href)?l().active:""}`,title:i?e.label:void 0,children:[s.jsx("span",{className:l().menuIcon,children:e.icon}),!i&&s.jsx("span",{className:l().menuLabel,children:e.label}),!i&&e.children&&s.jsx("button",{className:l().expandButton,onClick:i=>{i.preventDefault(),h(e.id)},children:u.includes(e.id)?"▼":"▶"})]}),!i&&e.children&&u.includes(e.id)&&s.jsx("ul",{className:l().submenu,children:e.children.filter(e=>b(e.roles)).map(e=>s.jsx("li",{className:l().submenuItem,children:(0,s.jsxs)(a(),{href:e.href,className:`${l().submenuLink} ${p(e.href)?l().active:""}`,children:[s.jsx("span",{className:l().submenuIcon,children:e.icon}),s.jsx("span",{className:l().submenuLabel,children:e.label})]})},e.id))})]},e.id))})}),(0,s.jsxs)("div",{className:l().sidebarFooter,children:[!i&&s.jsx("div",{className:l().footerContent,children:(0,s.jsxs)("div",{className:l().versionInfo,children:[s.jsx("div",{className:l().version,children:"v1.0.0"}),s.jsx("div",{className:l().environment,children:"PROD"})]})}),(0,s.jsxs)("div",{className:l().securityIndicator,children:[s.jsx("div",{className:l().securityIcon,children:"\uD83D\uDD12"}),!i&&s.jsx("div",{className:l().securityText,children:"Secure Portal"})]})]})]})}},5462:(e,i,r)=>{"use strict";r.d(i,{Z:()=>o});var s=r(997),t=r(6689);class n{async initialize(){return new Promise((e,i)=>{let r=indexedDB.open(this.config.dbName,this.config.version);r.onerror=()=>{console.error("Failed to open IndexedDB:",r.error),i(r.error)},r.onsuccess=()=>{this.db=r.result,console.log("PWA Cache Manager initialized"),e()},r.onupgradeneeded=e=>{let i=e.target.result;if(!i.objectStoreNames.contains(this.config.stores.transactions)){let e=i.createObjectStore(this.config.stores.transactions,{keyPath:"id"});e.createIndex("timestamp","timestamp",{unique:!1}),e.createIndex("type","type",{unique:!1}),e.createIndex("synced","synced",{unique:!1})}if(!i.objectStoreNames.contains(this.config.stores.bookings)){let e=i.createObjectStore(this.config.stores.bookings,{keyPath:"id"});e.createIndex("timestamp","timestamp",{unique:!1}),e.createIndex("synced","synced",{unique:!1})}i.objectStoreNames.contains(this.config.stores.customers)||i.createObjectStore(this.config.stores.customers,{keyPath:"id"}).createIndex("lastUpdated","lastUpdated",{unique:!1}),i.objectStoreNames.contains(this.config.stores.settings)||i.createObjectStore(this.config.stores.settings,{keyPath:"key"})}})}async storeOfflineTransaction(e,i){if(!this.db)throw Error("Cache manager not initialized");let r={id:`${e}_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,timestamp:Date.now(),type:e,data:i,synced:!1};return new Promise((e,i)=>{let s=this.db.transaction([this.config.stores.transactions],"readwrite").objectStore(this.config.stores.transactions).add(r);s.onsuccess=()=>{console.log("Offline transaction stored:",r.id),e(r.id)},s.onerror=()=>{console.error("Failed to store offline transaction:",s.error),i(s.error)}})}async getUnsyncedTransactions(){if(!this.db)throw Error("Cache manager not initialized");return new Promise((e,i)=>{let r=this.db.transaction([this.config.stores.transactions],"readonly").objectStore(this.config.stores.transactions).index("synced").getAll(IDBKeyRange.only(!1));r.onsuccess=()=>{e(r.result)},r.onerror=()=>{console.error("Failed to get unsynced transactions:",r.error),i(r.error)}})}async markTransactionSynced(e){if(!this.db)throw Error("Cache manager not initialized");return new Promise((i,r)=>{let s=this.db.transaction([this.config.stores.transactions],"readwrite").objectStore(this.config.stores.transactions),t=s.get(e);t.onsuccess=()=>{let n=t.result;if(n){n.synced=!0;let t=s.put(n);t.onsuccess=()=>{console.log("Transaction marked as synced:",e),i()},t.onerror=()=>{r(t.error)}}else r(Error("Transaction not found"))},t.onerror=()=>{r(t.error)}})}async cacheCustomerData(e){if(!this.db)throw Error("Cache manager not initialized");return new Promise((i,r)=>{let s=this.db.transaction([this.config.stores.customers],"readwrite").objectStore(this.config.stores.customers),t=0,n=e.length;if(0===n){i();return}e.forEach(e=>{let a={...e,lastUpdated:Date.now()},o=s.put(a);o.onsuccess=()=>{++t===n&&(console.log(`Cached ${n} customers for offline access`),i())},o.onerror=()=>{console.error("Failed to cache customer:",e.id,o.error),r(o.error)}})})}async getCachedCustomers(){if(!this.db)throw Error("Cache manager not initialized");return new Promise((e,i)=>{let r=this.db.transaction([this.config.stores.customers],"readonly").objectStore(this.config.stores.customers).getAll();r.onsuccess=()=>{e(r.result)},r.onerror=()=>{console.error("Failed to get cached customers:",r.error),i(r.error)}})}isOnline(){return navigator.onLine}async registerBackgroundSync(e){if("serviceWorker"in navigator&&"sync"in window.ServiceWorkerRegistration.prototype)try{let i=await navigator.serviceWorker.ready;await i.sync.register(e),console.log("Background sync registered:",e)}catch(e){console.error("Failed to register background sync:",e)}}async clearOldCache(e=6048e5){if(!this.db)throw Error("Cache manager not initialized");let i=Date.now()-e;return new Promise((e,r)=>{let s=this.db.transaction([this.config.stores.transactions],"readwrite").objectStore(this.config.stores.transactions).index("timestamp"),t=IDBKeyRange.upperBound(i),n=s.openCursor(t),a=0;n.onsuccess=i=>{let r=i.target.result;r?(r.value.synced&&(r.delete(),a++),r.continue()):(console.log(`Cleared ${a} old cached transactions`),e())},n.onerror=()=>{console.error("Failed to clear old cache:",n.error),r(n.error)}})}async getCacheStats(){if(!this.db)throw Error("Cache manager not initialized");let[e,i,r]=await Promise.all([this.getTransactionCount(),this.getUnsyncedTransactionCount(),this.getCachedCustomerCount()]),s=this.formatBytes(1024*e+512*r);return{totalTransactions:e,unsyncedTransactions:i,cachedCustomers:r,cacheSize:s}}async getTransactionCount(){return new Promise((e,i)=>{let r=this.db.transaction([this.config.stores.transactions],"readonly").objectStore(this.config.stores.transactions).count();r.onsuccess=()=>e(r.result),r.onerror=()=>i(r.error)})}async getUnsyncedTransactionCount(){return new Promise((e,i)=>{let r=this.db.transaction([this.config.stores.transactions],"readonly").objectStore(this.config.stores.transactions).index("synced").count(IDBKeyRange.only(!1));r.onsuccess=()=>e(r.result),r.onerror=()=>i(r.error)})}async getCachedCustomerCount(){return new Promise((e,i)=>{let r=this.db.transaction([this.config.stores.customers],"readonly").objectStore(this.config.stores.customers).count();r.onsuccess=()=>e(r.result),r.onerror=()=>i(r.error)})}formatBytes(e){if(0===e)return"0 Bytes";let i=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,i)).toFixed(2))+" "+["Bytes","KB","MB","GB"][i]}constructor(){this.db=null,this.config={dbName:"OSSAdminCache",version:1,stores:{transactions:"offline_transactions",bookings:"offline_bookings",customers:"offline_customers",settings:"cache_settings"}}}}new n;class a{async initialize(){if(!("serviceWorker"in navigator)||!("PushManager"in window))return console.warn("Push notifications not supported"),!1;try{let e=await this.requestPermission();if("granted"!==e)return console.warn("Notification permission not granted"),!1;let i=await navigator.serviceWorker.ready;return await this.subscribeToPush(i),console.log("Push notifications initialized successfully"),!0}catch(e){return console.error("Failed to initialize push notifications:",e),!1}}async requestPermission(){if(!("Notification"in window))throw Error("Notifications not supported");let e=Notification.permission;return"default"===e&&(e=await Notification.requestPermission()),e}async subscribeToPush(e){try{let i=await e.pushManager.getSubscription();return i||(i=await e.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:this.urlBase64ToUint8Array(this.vapidPublicKey)})),this.subscription=i,await this.sendSubscriptionToServer(i),i}catch(e){return console.error("Failed to subscribe to push notifications:",e),null}}async sendSubscriptionToServer(e){try{let i={endpoint:e.endpoint,keys:{p256dh:this.arrayBufferToBase64(e.getKey("p256dh")),auth:this.arrayBufferToBase64(e.getKey("auth"))}};if(!(await fetch("/api/admin/notifications/push/subscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})).ok)throw Error("Failed to send subscription to server");console.log("Push subscription sent to server")}catch(e){console.error("Failed to send subscription to server:",e)}}async showNotification(e){if(!("serviceWorker"in navigator))throw Error("Service worker not supported");try{let i=await navigator.serviceWorker.ready,r={body:e.body,icon:e.icon||"/icons/icon-192x192.png",badge:e.badge||"/icons/badge-72x72.png",data:e.data,tag:e.tag,requireInteraction:e.requireInteraction||!1,silent:e.silent||!1};e.image&&(r.image=e.image),e.vibrate?r.vibrate=e.vibrate:r.vibrate=[100,50,100],e.actions&&(r.actions=e.actions),await i.showNotification(e.title,r)}catch(e){console.error("Failed to show notification:",e)}}async sendPushNotification(e,i){try{return(await fetch("/api/admin/notifications/push/send",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e,payload:i})})).ok}catch(e){return console.error("Failed to send push notification:",e),!1}}async unsubscribe(){if(!this.subscription)return!0;try{let e=await this.subscription.unsubscribe();return e&&(await fetch("/api/admin/notifications/push/unsubscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({endpoint:this.subscription.endpoint})}),this.subscription=null,console.log("Unsubscribed from push notifications")),e}catch(e){return console.error("Failed to unsubscribe from push notifications:",e),!1}}async getSubscriptionStatus(){let e;let i="serviceWorker"in navigator&&"PushManager"in window,r=i?Notification.permission:"denied",s=!1;if(i&&"granted"===r)try{let i=await navigator.serviceWorker.ready;s=!!(e=await i.pushManager.getSubscription()||void 0)}catch(e){console.error("Failed to get subscription status:",e)}return{supported:i,permission:r,subscribed:s,subscription:e}}getNotificationTemplates(){return{newBooking:(e,i,r)=>({title:"New Booking Received",body:`${e} booked ${i} for ${r}`,icon:"/icons/booking-notification.png",tag:"new-booking",data:{type:"booking",action:"view"},actions:[{action:"view",title:"View Booking",icon:"/icons/view.png"},{action:"dismiss",title:"Dismiss",icon:"/icons/dismiss.png"}],vibrate:[100,50,100,50,100]}),lowInventory:(e,i)=>({title:"Low Inventory Alert",body:`${e} is running low (${i} remaining)`,icon:"/icons/inventory-alert.png",tag:"low-inventory",requireInteraction:!0,data:{type:"inventory",product:e},actions:[{action:"reorder",title:"Reorder",icon:"/icons/reorder.png"},{action:"dismiss",title:"Dismiss",icon:"/icons/dismiss.png"}],vibrate:[200,100,200]}),staffSchedule:e=>({title:"Schedule Update",body:e,icon:"/icons/schedule-notification.png",tag:"staff-schedule",data:{type:"schedule"},actions:[{action:"view",title:"View Schedule",icon:"/icons/calendar.png"},{action:"dismiss",title:"Dismiss",icon:"/icons/dismiss.png"}]}),paymentReceived:(e,i)=>({title:"Payment Received",body:`$${e} payment received from ${i}`,icon:"/icons/payment-notification.png",tag:"payment-received",data:{type:"payment",amount:e,customer:i},actions:[{action:"view",title:"View Transaction",icon:"/icons/receipt.png"},{action:"dismiss",title:"Dismiss",icon:"/icons/dismiss.png"}],vibrate:[100,50,100]})}}urlBase64ToUint8Array(e){let i="=".repeat((4-e.length%4)%4),r=(e+i).replace(/-/g,"+").replace(/_/g,"/"),s=window.atob(r),t=new Uint8Array(s.length);for(let e=0;e<s.length;++e)t[e]=s.charCodeAt(e);return t}arrayBufferToBase64(e){let i=new Uint8Array(e),r="";for(let e=0;e<i.byteLength;e++)r+=String.fromCharCode(i[e]);return window.btoa(r)}constructor(){this.vapidPublicKey=process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY||"",this.subscription=null}}function o({children:e}){let[i,r]=(0,t.useState)({serviceWorkerRegistered:!1,cacheInitialized:!1,pushNotificationsEnabled:!1,isOnline:navigator.onLine,installPromptAvailable:!1}),[n,a]=(0,t.useState)(null),[o,c]=(0,t.useState)(!1),l=async()=>{if(n){n.prompt();let{outcome:e}=await n.userChoice;"accepted"===e?console.log("User accepted the install prompt"):console.log("User dismissed the install prompt"),a(null),c(!1)}};return(0,s.jsxs)(s.Fragment,{children:[e,o&&(0,s.jsxs)("div",{style:{position:"fixed",bottom:"20px",left:"20px",right:"20px",background:"#16213e",color:"white",padding:"16px",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.3)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,s.jsxs)("div",{children:[s.jsx("strong",{children:"Install Ocean Soul Sparkles Admin"}),s.jsx("p",{style:{margin:"4px 0 0 0",fontSize:"14px",opacity:.9},children:"Add to home screen for quick access"})]}),(0,s.jsxs)("div",{style:{display:"flex",gap:"8px"},children:[s.jsx("button",{onClick:l,style:{background:"#4CAF50",color:"white",border:"none",padding:"8px 16px",borderRadius:"4px",cursor:"pointer"},children:"Install"}),s.jsx("button",{onClick:()=>c(!1),style:{background:"transparent",color:"white",border:"1px solid rgba(255,255,255,0.3)",padding:"8px 16px",borderRadius:"4px",cursor:"pointer"},children:"Later"})]})]}),!i.isOnline&&s.jsx("div",{style:{position:"fixed",top:"0",left:"0",right:"0",background:"#f44336",color:"white",padding:"8px",textAlign:"center",fontSize:"14px",zIndex:10001},children:"\uD83D\uDCF1 Offline Mode - Some features may be limited"})]})}new a},9489:(e,i,r)=>{"use strict";r.d(i,{Z:()=>d});var s=r(997);r(6689);var t=r(1664),n=r.n(t),a=r(1163),o=r(1989),c=r(7044),l=r.n(c);function d({userRole:e}){let i=(0,a.useRouter)(),r=e=>{if(o.s.light(),i.pathname===e.href)return},t=e=>"/admin/dashboard"===e?"/admin/dashboard"===i.pathname||"/admin"===i.pathname:i.pathname.startsWith(e),c=[{id:"dashboard",label:"Dashboard",icon:"\uD83D\uDCCA",href:"/admin/dashboard",roles:["Admin","Manager","Staff"]},{id:"pos",label:"POS",icon:"\uD83D\uDCB3",href:"/admin/pos",roles:["Admin","Manager","Staff"]},{id:"bookings",label:"Bookings",icon:"\uD83D\uDCC5",href:"/admin/bookings",roles:["Admin","Manager","Staff"]},{id:"customers",label:"Customers",icon:"\uD83D\uDC65",href:"/admin/customers",roles:["Admin","Manager","Staff"]},{id:"more",label:"More",icon:"⋯",href:"/admin/menu",roles:["Admin","Manager","Staff"]}].filter(i=>i.roles.includes(e));return(0,s.jsxs)("nav",{className:l().mobileBottomNav,children:[s.jsx("div",{className:l().navContainer,children:c.map(e=>(0,s.jsxs)(n(),{href:e.href,className:`${l().navItem} ${t(e.href)?l().active:""}`,onClick:()=>r(e),children:[s.jsx("div",{className:l().navIcon,children:e.icon}),s.jsx("span",{className:l().navLabel,children:e.label}),t(e.href)&&s.jsx("div",{className:l().activeIndicator})]},e.id))}),s.jsx("div",{className:l().safeAreaPadding})]})}},8568:(e,i,r)=>{"use strict";r.d(i,{a:()=>n});var s=r(6689),t=r(1163);function n(){let e=(0,t.useRouter)(),[i,r]=(0,s.useState)({user:null,loading:!0,error:null}),n=async()=>{try{let e=localStorage.getItem("admin-token");if(!e){r({user:null,loading:!1,error:null});return}let i=await fetch("/api/auth/verify",{headers:{Authorization:`Bearer ${e}`}});if(!i.ok){localStorage.removeItem("admin-token"),r({user:null,loading:!1,error:"Session expired"});return}let s=await i.json();r({user:s.user,loading:!1,error:null})}catch(e){console.error("Auth check error:",e),localStorage.removeItem("admin-token"),r({user:null,loading:!1,error:"Authentication failed"})}},a=async(e,i)=>{try{r(e=>({...e,loading:!0,error:null}));let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:i})}),t=await s.json();if(!s.ok)throw Error(t.error||"Login failed");if(t.requiresMFA)return{requiresMFA:!0,user:t.user};return localStorage.setItem("admin-token",t.token),r({user:t.user,loading:!1,error:null}),{success:!0,user:t.user}}catch(i){let e=i instanceof Error?i.message:"Login failed";throw r(i=>({...i,loading:!1,error:e})),i}},o=async(e,i)=>{try{r(e=>({...e,loading:!0,error:null}));let s=await fetch("/api/auth/mfa-verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e,mfaCode:i})}),t=await s.json();if(!s.ok)throw Error(t.error||"MFA verification failed");return localStorage.setItem("admin-token",t.token),r({user:t.user,loading:!1,error:null}),{success:!0,user:t.user}}catch(i){let e=i instanceof Error?i.message:"MFA verification failed";throw r(i=>({...i,loading:!1,error:e})),i}},c=async()=>{try{let e=localStorage.getItem("admin-token");e&&await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${e}`}})}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("admin-token"),r({user:null,loading:!1,error:null}),e.push("/admin/login")}},l=e=>!!i.user&&(Array.isArray(e)?e:[e]).includes(i.user.role);return{user:i.user,loading:i.loading,error:i.error,login:a,verifyMFA:o,logout:c,updateUser:e=>{r(i=>({...i,user:i.user?{...i.user,...e}:null}))},hasPermission:e=>!!i.user&&("DEV"===i.user.role||i.user.permissions.includes(e)),hasRole:l,isAdmin:()=>l(["DEV","Admin"]),isStaff:()=>l(["DEV","Admin","Artist","Braider"]),checkAuth:n}}},1989:(e,i,r)=>{"use strict";r.d(i,{s:()=>s}),r(6689);class s{static vibrate(e=100){"vibrate"in navigator&&navigator.vibrate(e)}static light(){this.vibrate(50)}static medium(){this.vibrate(100)}static heavy(){this.vibrate([100,50,100])}static error(){this.vibrate([100,50,100,50,100])}static success(){this.vibrate([50,25,50])}}},6814:(e,i,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(i),r.d(i,{default:()=>l});var t=r(997),n=r(968),a=r.n(n);r(6689);var o=r(3590);r(8819),r(6764);var c=e([o]);function l({Component:e,pageProps:i}){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(a(),{children:[t.jsx("meta",{charSet:"utf-8"}),t.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),t.jsx("meta",{httpEquiv:"X-Content-Type-Options",content:"nosniff"}),t.jsx("meta",{httpEquiv:"X-XSS-Protection",content:"1; mode=block"}),t.jsx("meta",{name:"referrer",content:"strict-origin-when-cross-origin"}),t.jsx("meta",{name:"robots",content:"noindex, nofollow, noarchive, nosnippet"}),t.jsx("meta",{name:"googlebot",content:"noindex, nofollow"}),t.jsx("meta",{name:"description",content:"Ocean Soul Sparkles Admin Portal - Secure staff access only"}),t.jsx("link",{rel:"icon",href:"/admin/favicon.ico"}),t.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/admin/apple-touch-icon.png"}),t.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/admin/favicon-32x32.png"}),t.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/admin/favicon-16x16.png"}),t.jsx("meta",{name:"theme-color",content:"#3788d8"}),t.jsx("meta",{name:"msapplication-TileColor",content:"#3788d8"}),t.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),t.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),t.jsx("link",{rel:"preconnect",href:"https://ndlgbcsbidyhxbpqzgqp.supabase.co"}),t.jsx("link",{rel:"dns-prefetch",href:"https://js.squareup.com"}),t.jsx("link",{rel:"dns-prefetch",href:"https://api.onesignal.com"}),t.jsx("title",{children:"Ocean Soul Sparkles Admin Portal"})]}),t.jsx(e,{...i}),t.jsx(o.ToastContainer,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light",toastStyle:{fontFamily:"inherit",fontSize:"14px"}}),t.jsx("div",{style:{position:"fixed",bottom:"10px",right:"10px",background:"rgba(0, 0, 0, 0.1)",color:"rgba(0, 0, 0, 0.3)",padding:"4px 8px",borderRadius:"4px",fontSize:"10px",fontWeight:"bold",pointerEvents:"none",zIndex:9999,userSelect:"none"},children:"ADMIN PORTAL"}),!1]})}o=(c.then?(await c)():c)[0],s()}catch(e){s(e)}})},6764:()=>{}};