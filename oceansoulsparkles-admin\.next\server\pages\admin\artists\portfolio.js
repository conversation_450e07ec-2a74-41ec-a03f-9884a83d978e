"use strict";(()=>{var e={};e.id=5069,e.ids=[5069,660],e.modules={9311:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>j,default:()=>p,getServerSideProps:()=>x,getStaticPaths:()=>u,getStaticProps:()=>m,reportWebVitals:()=>g,routeModule:()=>C,unstable_getServerProps:()=>N,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>v});var r=s(7093),i=s(5244),l=s(1323),n=s(2899),o=s.n(n),c=s(6814),d=s(5982),h=e([c,d]);[c,d]=h.then?(await h)():h;let p=(0,l.l)(d,"default"),m=(0,l.l)(d,"getStaticProps"),u=(0,l.l)(d,"getStaticPaths"),x=(0,l.l)(d,"getServerSideProps"),j=(0,l.l)(d,"config"),g=(0,l.l)(d,"reportWebVitals"),v=(0,l.l)(d,"unstable_getStaticProps"),f=(0,l.l)(d,"unstable_getStaticPaths"),S=(0,l.l)(d,"unstable_getStaticParams"),N=(0,l.l)(d,"unstable_getServerProps"),P=(0,l.l)(d,"unstable_getServerSideProps"),C=new r.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/admin/artists/portfolio",pathname:"/admin/artists/portfolio",bundlePath:"",filename:""},components:{App:c.default,Document:o()},userland:d});a()}catch(e){a(e)}})},5982:(e,t,s)=>{s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>x});var r=s(997),i=s(6689),l=s(968),n=s.n(l),o=s(1163),c=s(4845),d=s(3194),h=s(8568),p=s(1382),m=s.n(p),u=e([c]);function x(){let{user:e,loading:t}=(0,h.a)(),s=(0,o.useRouter)(),[a,l]=(0,i.useState)({totalItems:0,featuredItems:0,publicItems:0,categories:[],totalArtists:0}),[p,u]=(0,i.useState)(!0),[x,j]=(0,i.useState)(null),g=async()=>{try{u(!0);let e=localStorage.getItem("adminToken"),t=await fetch("/api/admin/artists/portfolio",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to load portfolio statistics");let s=(await t.json()).portfolioItems||[],a=await fetch("/api/admin/artists",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}}),r=0;if(a.ok){let e=await a.json();r=e.artists?.length||0}let i=new Set(s.map(e=>e.category)),n=Array.from(i),o=s.filter(e=>e.is_featured).length,c=s.filter(e=>e.is_public).length;l({totalItems:s.length,featuredItems:o,publicItems:c,categories:n,totalArtists:r})}catch(e){console.error("Error loading portfolio stats:",e),j("Failed to load portfolio statistics")}finally{u(!1)}};return t||p?r.jsx(c.Z,{children:(0,r.jsxs)("div",{className:m().loading,children:[r.jsx("div",{className:m().spinner}),r.jsx("p",{children:"Loading portfolio management..."})]})}):e?["DEV","Admin"].includes(e.role)?(0,r.jsxs)(c.Z,{children:[(0,r.jsxs)(n(),{children:[r.jsx("title",{children:"Artist Portfolio Management | Ocean Soul Sparkles Admin"}),r.jsx("meta",{name:"description",content:"Manage artist portfolios and work samples"})]}),(0,r.jsxs)("div",{className:m().portfolioPage,children:[r.jsx("div",{className:m().pageHeader,children:(0,r.jsxs)("div",{className:m().headerContent,children:[r.jsx("h1",{children:"Artist Portfolio Management"}),r.jsx("p",{children:"Manage artist portfolios, work samples, and showcase galleries"})]})}),x&&(0,r.jsxs)("div",{className:m().error,children:[r.jsx("p",{children:x}),r.jsx("button",{onClick:()=>j(null),children:"\xd7"})]}),(0,r.jsxs)("div",{className:m().statsGrid,children:[(0,r.jsxs)("div",{className:m().statCard,children:[r.jsx("div",{className:m().statIcon,children:"\uD83C\uDFA8"}),(0,r.jsxs)("div",{className:m().statContent,children:[r.jsx("h3",{children:a.totalItems}),r.jsx("p",{children:"Total Portfolio Items"})]})]}),(0,r.jsxs)("div",{className:m().statCard,children:[r.jsx("div",{className:m().statIcon,children:"⭐"}),(0,r.jsxs)("div",{className:m().statContent,children:[r.jsx("h3",{children:a.featuredItems}),r.jsx("p",{children:"Featured Items"})]})]}),(0,r.jsxs)("div",{className:m().statCard,children:[r.jsx("div",{className:m().statIcon,children:"\uD83D\uDC41️"}),(0,r.jsxs)("div",{className:m().statContent,children:[r.jsx("h3",{children:a.publicItems}),r.jsx("p",{children:"Public Items"})]})]}),(0,r.jsxs)("div",{className:m().statCard,children:[r.jsx("div",{className:m().statIcon,children:"\uD83D\uDC68‍\uD83C\uDFA8"}),(0,r.jsxs)("div",{className:m().statContent,children:[r.jsx("h3",{children:a.totalArtists}),r.jsx("p",{children:"Active Artists"})]})]}),(0,r.jsxs)("div",{className:m().statCard,children:[r.jsx("div",{className:m().statIcon,children:"\uD83D\uDCC2"}),(0,r.jsxs)("div",{className:m().statContent,children:[r.jsx("h3",{children:a.categories.length}),r.jsx("p",{children:"Categories"})]})]})]}),a.categories.length>0&&(0,r.jsxs)("div",{className:m().categoriesOverview,children:[r.jsx("h3",{children:"Portfolio Categories"}),r.jsx("div",{className:m().categoryTags,children:a.categories.map(e=>r.jsx("span",{className:m().categoryTag,children:e.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase())},e))})]}),r.jsx(d.Z,{onItemAdded:()=>{g()},onItemUpdated:()=>{g()},onItemDeleted:()=>{g()}})]})]}):r.jsx(c.Z,{children:(0,r.jsxs)("div",{className:m().accessDenied,children:[r.jsx("h2",{children:"Access Denied"}),r.jsx("p",{children:"You don't have permission to access portfolio management."})]})}):(s.push("/admin/login"),null)}c=(u.then?(await u)():u)[0],a()}catch(e){a(e)}})},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},2048:e=>{e.exports=require("fs")},5315:e=>{e.exports=require("path")},6162:e=>{e.exports=require("stream")},1568:e=>{e.exports=require("zlib")},3590:e=>{e.exports=import("react-toastify")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2899,6212,1664,7441,3194],()=>s(9311));module.exports=a})();