(()=>{var e={};e.id=1333,e.ids=[1333,660],e.modules={723:e=>{e.exports={feedbackContainer:"Feedback_feedbackContainer__5bDxl",header:"Feedback_header__daF0e",headerLeft:"Feedback_headerLeft__MbO1O",title:"Feedback_title__exrDT",subtitle:"Feedback_subtitle__pG0mE",errorMessage:"Feedback_errorMessage__3mf7J",closeError:"Feedback_closeError__eQhr1",averagesSection:"Feedback_averagesSection__OANL3",averagesTitle:"Feedback_averagesTitle__2hb7p",averagesGrid:"Feedback_averagesGrid__a7AJ6",averageCard:"Feedback_averageCard__DIQJG",averageValue:"Feedback_averageValue__3Jk_s",averageStars:"Feedback_averageStars__r7Mye",averageLabel:"Feedback_averageLabel__SMguk",starFilled:"Feedback_starFilled__3WZ2I",starEmpty:"Feedback_starEmpty__Bh9UA",filters:"Feedback_filters__r8NtG",filterGroup:"Feedback_filterGroup__O4tFQ",filterSelect:"Feedback_filterSelect__VGyiu",checkboxLabel:"Feedback_checkboxLabel__IvJnA",feedbackList:"Feedback_feedbackList__lE42t",feedbackCard:"Feedback_feedbackCard__e1xsU",cardHeader:"Feedback_cardHeader__rYRy8",customerInfo:"Feedback_customerInfo__I_F8y",customerName:"Feedback_customerName__l33Lk",bookingInfo:"Feedback_bookingInfo__Qep8h",artistInfo:"Feedback_artistInfo__fllOo",cardMeta:"Feedback_cardMeta__VRUhw",overallRating:"Feedback_overallRating__P_OfS",ratingValue:"Feedback_ratingValue__qpvL2",ratingStars:"Feedback_ratingStars__5BPvW",feedbackDate:"Feedback_feedbackDate__dNSAi",publicBadge:"Feedback_publicBadge__tW5Ln",detailedRatings:"Feedback_detailedRatings__ZT1HX",ratingDetail:"Feedback_ratingDetail__5LrKg",ratingNumber:"Feedback_ratingNumber__tNfUO",feedbackText:"Feedback_feedbackText__oCK1x",suggestions:"Feedback_suggestions__fZ3LS",recommendation:"Feedback_recommendation__dm2MJ",recommendYes:"Feedback_recommendYes__Dx87X",recommendNo:"Feedback_recommendNo__3CKyo",adminResponse:"Feedback_adminResponse__IP7Tm",responseDate:"Feedback_responseDate__2guyb",emptyState:"Feedback_emptyState__FM28K",pagination:"Feedback_pagination__qHx5x",paginationBtn:"Feedback_paginationBtn__EvNz7",paginationInfo:"Feedback_paginationInfo___GmJj",loadingContainer:"Feedback_loadingContainer__s_TVl",loadingSpinner:"Feedback_loadingSpinner__RAKVz",spin:"Feedback_spin__YrNIV"}},7822:(e,a,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(a),s.d(a,{config:()=>u,default:()=>m,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>g,reportWebVitals:()=>v,routeModule:()=>f,unstable_getServerProps:()=>k,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>p,unstable_getStaticProps:()=>b});var i=s(7093),t=s(5244),n=s(1323),l=s(2899),c=s.n(l),d=s(6814),o=s(4326),_=e([d,o]);[d,o]=_.then?(await _)():_;let m=(0,n.l)(o,"default"),g=(0,n.l)(o,"getStaticProps"),h=(0,n.l)(o,"getStaticPaths"),x=(0,n.l)(o,"getServerSideProps"),u=(0,n.l)(o,"config"),v=(0,n.l)(o,"reportWebVitals"),b=(0,n.l)(o,"unstable_getStaticProps"),p=(0,n.l)(o,"unstable_getStaticPaths"),j=(0,n.l)(o,"unstable_getStaticParams"),k=(0,n.l)(o,"unstable_getServerProps"),N=(0,n.l)(o,"unstable_getServerSideProps"),f=new i.PagesRouteModule({definition:{kind:t.x.PAGES,page:"/admin/feedback",pathname:"/admin/feedback",bundlePath:"",filename:""},components:{App:d.default,Document:c()},userland:o});r()}catch(e){r(e)}})},4326:(e,a,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(a),s.d(a,{default:()=>g});var i=s(997),t=s(6689),n=s(968),l=s.n(n),c=s(8568),d=s(4845),o=s(723),_=s.n(o),m=e([d]);function g(){let{user:e}=(0,c.a)(),[a,s]=(0,t.useState)([]),[r,n]=(0,t.useState)({overall:0,service:0,cleanliness:0,timeliness:0,experience:0}),[o,m]=(0,t.useState)(!0),[g,h]=(0,t.useState)(null),[x,u]=(0,t.useState)("all"),[v,b]=(0,t.useState)(!1),[p,j]=(0,t.useState)(1),[k,N]=(0,t.useState)(0),[f]=(0,t.useState)(20),F=e=>new Date(e).toLocaleString("en-AU",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),S=e=>Array.from({length:5},(a,s)=>i.jsx("span",{className:s<e?_().starFilled:_().starEmpty,children:"★"},s)),y=e=>e>=4.5?"#10b981":e>=4?"#84cc16":e>=3?"#f59e0b":e>=2?"#f97316":"#ef4444",C=Math.ceil(k/f);return o&&0===a.length?i.jsx(d.Z,{children:(0,i.jsxs)("div",{className:_().loadingContainer,children:[i.jsx("div",{className:_().loadingSpinner}),i.jsx("p",{children:"Loading feedback..."})]})}):(0,i.jsxs)(d.Z,{children:[(0,i.jsxs)(l(),{children:[i.jsx("title",{children:"Customer Feedback | Ocean Soul Sparkles Admin"}),i.jsx("meta",{name:"description",content:"View and manage customer feedback and reviews"})]}),(0,i.jsxs)("div",{className:_().feedbackContainer,children:[i.jsx("header",{className:_().header,children:(0,i.jsxs)("div",{className:_().headerLeft,children:[i.jsx("h1",{className:_().title,children:"Customer Feedback"}),i.jsx("p",{className:_().subtitle,children:"View and manage customer reviews and feedback"})]})}),g&&(0,i.jsxs)("div",{className:_().errorMessage,children:[g,i.jsx("button",{onClick:()=>h(null),className:_().closeError,children:"\xd7"})]}),(0,i.jsxs)("div",{className:_().averagesSection,children:[i.jsx("h2",{className:_().averagesTitle,children:"Average Ratings"}),(0,i.jsxs)("div",{className:_().averagesGrid,children:[(0,i.jsxs)("div",{className:_().averageCard,children:[i.jsx("div",{className:_().averageValue,style:{color:y(r.overall)},children:r.overall.toFixed(1)}),i.jsx("div",{className:_().averageStars,children:S(Math.round(r.overall))}),i.jsx("div",{className:_().averageLabel,children:"Overall Rating"})]}),(0,i.jsxs)("div",{className:_().averageCard,children:[i.jsx("div",{className:_().averageValue,style:{color:y(r.service)},children:r.service.toFixed(1)}),i.jsx("div",{className:_().averageStars,children:S(Math.round(r.service))}),i.jsx("div",{className:_().averageLabel,children:"Service Quality"})]}),(0,i.jsxs)("div",{className:_().averageCard,children:[i.jsx("div",{className:_().averageValue,style:{color:y(r.cleanliness)},children:r.cleanliness.toFixed(1)}),i.jsx("div",{className:_().averageStars,children:S(Math.round(r.cleanliness))}),i.jsx("div",{className:_().averageLabel,children:"Cleanliness"})]}),(0,i.jsxs)("div",{className:_().averageCard,children:[i.jsx("div",{className:_().averageValue,style:{color:y(r.timeliness)},children:r.timeliness.toFixed(1)}),i.jsx("div",{className:_().averageStars,children:S(Math.round(r.timeliness))}),i.jsx("div",{className:_().averageLabel,children:"Timeliness"})]}),(0,i.jsxs)("div",{className:_().averageCard,children:[i.jsx("div",{className:_().averageValue,style:{color:y(r.experience)},children:r.experience.toFixed(1)}),i.jsx("div",{className:_().averageStars,children:S(Math.round(r.experience))}),i.jsx("div",{className:_().averageLabel,children:"Overall Experience"})]})]})]}),(0,i.jsxs)("div",{className:_().filters,children:[(0,i.jsxs)("div",{className:_().filterGroup,children:[i.jsx("label",{htmlFor:"ratingFilter",children:"Filter by Rating:"}),(0,i.jsxs)("select",{id:"ratingFilter",value:x,onChange:e=>{u(e.target.value),j(1)},className:_().filterSelect,children:[i.jsx("option",{value:"all",children:"All Ratings"}),i.jsx("option",{value:"5",children:"5 Stars"}),i.jsx("option",{value:"4",children:"4 Stars"}),i.jsx("option",{value:"3",children:"3 Stars"}),i.jsx("option",{value:"2",children:"2 Stars"}),i.jsx("option",{value:"1",children:"1 Star"})]})]}),i.jsx("div",{className:_().filterGroup,children:(0,i.jsxs)("label",{className:_().checkboxLabel,children:[i.jsx("input",{type:"checkbox",checked:v,onChange:e=>{b(e.target.checked),j(1)}}),"Show public reviews only"]})})]}),i.jsx("div",{className:_().feedbackList,children:0===a.length?(0,i.jsxs)("div",{className:_().emptyState,children:[i.jsx("h3",{children:"No feedback found"}),i.jsx("p",{children:"No customer feedback matches your current filters."})]}):a.map(e=>(0,i.jsxs)("div",{className:_().feedbackCard,children:[(0,i.jsxs)("div",{className:_().cardHeader,children:[(0,i.jsxs)("div",{className:_().customerInfo,children:[(0,i.jsxs)("h3",{className:_().customerName,children:[e.customers.first_name," ",e.customers.last_name]}),e.bookings&&(0,i.jsxs)("p",{className:_().bookingInfo,children:[e.bookings.services.name," - ",F(e.bookings.start_time)]}),e.artist_profiles&&(0,i.jsxs)("p",{className:_().artistInfo,children:["Artist: ",e.artist_profiles.name||e.artist_profiles.artist_name]})]}),(0,i.jsxs)("div",{className:_().cardMeta,children:[(0,i.jsxs)("div",{className:_().overallRating,children:[i.jsx("span",{className:_().ratingValue,style:{color:y(e.rating)},children:e.rating}),i.jsx("div",{className:_().ratingStars,children:S(e.rating)})]}),i.jsx("div",{className:_().feedbackDate,children:F(e.created_at)}),e.is_public&&i.jsx("span",{className:_().publicBadge,children:"Public"})]})]}),(e.service_rating||e.cleanliness_rating||e.timeliness_rating||e.overall_experience_rating)&&(0,i.jsxs)("div",{className:_().detailedRatings,children:[e.service_rating&&(0,i.jsxs)("div",{className:_().ratingDetail,children:[i.jsx("span",{children:"Service: "}),S(e.service_rating),(0,i.jsxs)("span",{className:_().ratingNumber,children:["(",e.service_rating,")"]})]}),e.cleanliness_rating&&(0,i.jsxs)("div",{className:_().ratingDetail,children:[i.jsx("span",{children:"Cleanliness: "}),S(e.cleanliness_rating),(0,i.jsxs)("span",{className:_().ratingNumber,children:["(",e.cleanliness_rating,")"]})]}),e.timeliness_rating&&(0,i.jsxs)("div",{className:_().ratingDetail,children:[i.jsx("span",{children:"Timeliness: "}),S(e.timeliness_rating),(0,i.jsxs)("span",{className:_().ratingNumber,children:["(",e.timeliness_rating,")"]})]}),e.overall_experience_rating&&(0,i.jsxs)("div",{className:_().ratingDetail,children:[i.jsx("span",{children:"Experience: "}),S(e.overall_experience_rating),(0,i.jsxs)("span",{className:_().ratingNumber,children:["(",e.overall_experience_rating,")"]})]})]}),e.feedback_text&&(0,i.jsxs)("div",{className:_().feedbackText,children:[i.jsx("h4",{children:"Feedback:"}),i.jsx("p",{children:e.feedback_text})]}),e.improvement_suggestions&&(0,i.jsxs)("div",{className:_().suggestions,children:[i.jsx("h4",{children:"Suggestions for Improvement:"}),i.jsx("p",{children:e.improvement_suggestions})]}),null!==e.would_recommend&&(0,i.jsxs)("div",{className:_().recommendation,children:[i.jsx("strong",{children:"Would recommend: "}),i.jsx("span",{className:e.would_recommend?_().recommendYes:_().recommendNo,children:e.would_recommend?"Yes":"No"})]}),e.response_text&&(0,i.jsxs)("div",{className:_().adminResponse,children:[i.jsx("h4",{children:"Admin Response:"}),i.jsx("p",{children:e.response_text}),(0,i.jsxs)("div",{className:_().responseDate,children:["Responded on ",F(e.responded_at)]})]})]},e.id))}),C>1&&(0,i.jsxs)("div",{className:_().pagination,children:[i.jsx("button",{onClick:()=>j(e=>Math.max(1,e-1)),disabled:1===p,className:_().paginationBtn,children:"Previous"}),(0,i.jsxs)("span",{className:_().paginationInfo,children:["Page ",p," of ",C," (",k," total)"]}),i.jsx("button",{onClick:()=>j(e=>Math.min(C,e+1)),disabled:p===C,className:_().paginationBtn,children:"Next"})]})]})]})}d=(m.then?(await m)():m)[0],r()}catch(e){r(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var a=require("../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),r=a.X(0,[2899,6212,1664,7441],()=>s(7822));module.exports=r})();