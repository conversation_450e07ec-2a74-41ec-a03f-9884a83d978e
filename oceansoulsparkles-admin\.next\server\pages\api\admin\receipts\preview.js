"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/receipts/preview";
exports.ids = ["pages/api/admin/receipts/preview"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "speakeasy":
/*!****************************!*\
  !*** external "speakeasy" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("speakeasy");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freceipts%2Fpreview&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creceipts%5Cpreview.js&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freceipts%2Fpreview&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creceipts%5Cpreview.js&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_receipts_preview_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\receipts\\preview.js */ \"(api)/./pages/api/admin/receipts/preview.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_receipts_preview_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_receipts_preview_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/receipts/preview\",\n        pathname: \"/api/admin/receipts/preview\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_admin_receipts_preview_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freceipts%2Fpreview&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creceipts%5Cpreview.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/auth/admin-auth.ts":
/*!********************************!*\
  !*** ./lib/auth/admin-auth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminLogin: () => (/* binding */ adminLogin),\n/* harmony export */   adminLogout: () => (/* binding */ adminLogout),\n/* harmony export */   authenticateAdminRequest: () => (/* binding */ authenticateAdminRequest),\n/* harmony export */   enableMFA: () => (/* binding */ enableMFA),\n/* harmony export */   generateMFASecret: () => (/* binding */ generateMFASecret),\n/* harmony export */   verifyAdminAuth: () => (/* binding */ verifyAdminAuth),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyMFAAndLogin: () => (/* binding */ verifyMFAAndLogin)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _security_audit_logging__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../security/audit-logging */ \"(api)/./lib/security/audit-logging.ts\");\n\n\n\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Verify admin authentication token\r\n */ async function verifyAdminToken(token) {\n    try {\n        // Handle missing JWT secret gracefully\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, jwtSecret);\n        // Get user from database with latest info\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        last_activity,\r\n        permissions\r\n      `).eq(\"id\", decoded.userId).eq(\"is_active\", true).single();\n        if (error || !user) {\n            return {\n                valid: false,\n                error: \"User not found or inactive\"\n            };\n        }\n        // Check if user is still active\n        if (!user.is_active) {\n            return {\n                valid: false,\n                error: \"User account is deactivated\"\n            };\n        }\n        // Update last activity\n        await supabase.from(\"admin_users\").update({\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        return {\n            valid: true,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            error: \"Invalid token\"\n        };\n    }\n}\n/**\r\n * Admin login with email and password\r\n */ async function adminLogin(email, password, ip) {\n    try {\n        // Check for rate limiting\n        const { data: attempts } = await supabase.from(\"login_attempts\").select(\"*\").eq(\"email\", email).gte(\"created_at\", new Date(Date.now() - 15 * 60 * 1000).toISOString()).order(\"created_at\", {\n            ascending: false\n        });\n        if (attempts && attempts.length >= 5) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_BLOCKED\",\n                email,\n                ip,\n                reason: \"Too many failed attempts\"\n            });\n            return {\n                success: false,\n                error: \"Account temporarily locked due to too many failed attempts\"\n            };\n        }\n        // Get user from database\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        password_hash,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"email\", email.toLowerCase()).single();\n        if (error || !user) {\n            await recordFailedAttempt(email, ip, error ? `Database error: ${error.message}` : \"User not found\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Check if user is active\n        if (!user.is_active) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_DENIED\",\n                userId: user.id,\n                email,\n                ip,\n                reason: \"Account deactivated\"\n            });\n            return {\n                success: false,\n                error: \"Account is deactivated\"\n            };\n        }\n        // Verify password\n        const passwordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n        if (!passwordValid) {\n            await recordFailedAttempt(email, ip, \"Invalid password\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Clear failed attempts on successful password verification\n        await supabase.from(\"login_attempts\").delete().eq(\"email\", email);\n        // Check if MFA is required\n        if (user.mfa_enabled && user.mfa_secret) {\n            // Return success but indicate MFA is required\n            return {\n                success: true,\n                requiresMFA: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    firstName: user.first_name,\n                    lastName: user.last_name,\n                    isActive: user.is_active,\n                    mfaEnabled: user.mfa_enabled,\n                    lastActivity: Date.now(),\n                    permissions: user.permissions || []\n                }\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"Admin login error:\", error);\n        return {\n            success: false,\n            error: \"Login failed\"\n        };\n    }\n}\n/**\r\n * Verify MFA token and complete login\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function verifyMFAAndLogin(userId, mfaToken, ip) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"id\", userId).single();\n        if (error || !user || !user.mfa_secret) {\n            return {\n                success: false,\n                error: \"Invalid MFA setup\"\n            };\n        }\n        // Verify MFA token\n        const verified = speakeasy.totp.verify({\n            secret: user.mfa_secret,\n            encoding: \"base32\",\n            token: mfaToken,\n            window: 2\n        });\n        if (!verified) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"MFA_FAILED\",\n                userId: user.id,\n                email: user.email,\n                ip,\n                reason: \"Invalid MFA token\"\n            });\n            return {\n                success: false,\n                error: \"Invalid MFA token\"\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email: user.email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"MFA verification error:\", error);\n        return {\n            success: false,\n            error: \"MFA verification failed\"\n        };\n    }\n}\n/**\r\n * Generate MFA secret for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function generateMFASecret(userId) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user } = await supabase.from(\"admin_users\").select(\"email, first_name, last_name\").eq(\"id\", userId).single();\n        if (!user) return null;\n        const secret = speakeasy.generateSecret({\n            name: `${user.first_name} ${user.last_name}`,\n            issuer: \"Ocean Soul Sparkles Admin\",\n            length: 32\n        });\n        return {\n            secret: secret.base32,\n            qrCode: secret.otpauth_url\n        };\n    } catch (error) {\n        console.error(\"MFA secret generation error:\", error);\n        return null;\n    }\n}\n/**\r\n * Enable MFA for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function enableMFA(userId, secret, token) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        // Verify the token first\n        const verified = speakeasy.totp.verify({\n            secret,\n            encoding: \"base32\",\n            token,\n            window: 2\n        });\n        if (!verified) return false;\n        // Save MFA secret to database\n        const { error } = await supabase.from(\"admin_users\").update({\n            mfa_secret: secret,\n            mfa_enabled: true\n        }).eq(\"id\", userId);\n        if (error) return false;\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_ENABLED\",\n            userId,\n            reason: \"User enabled MFA\"\n        });\n        return true;\n    } catch (error) {\n        console.error(\"MFA enable error:\", error);\n        return false;\n    }\n}\n/**\r\n * Record failed login attempt\r\n */ async function recordFailedAttempt(email, ip, reason) {\n    await supabase.from(\"login_attempts\").insert({\n        email,\n        ip_address: ip,\n        success: false,\n        reason,\n        created_at: new Date().toISOString()\n    });\n    await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n        action: \"LOGIN_FAILED\",\n        email,\n        ip,\n        reason\n    });\n}\n/**\r\n * Admin logout\r\n */ async function adminLogout(userId, ip) {\n    try {\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGOUT\",\n            userId,\n            ip\n        });\n    } catch (error) {\n        console.error(\"Logout audit error:\", error);\n    }\n}\n/**\r\n * Authenticate admin request (alias for verifyAdminToken for backward compatibility)\r\n */ async function authenticateAdminRequest(token) {\n    return verifyAdminToken(token);\n}\n/**\r\n * Verify admin authentication from NextApiRequest\r\n */ async function verifyAdminAuth(req) {\n    try {\n        // Extract token from headers or cookies\n        const token = req.headers.authorization?.replace(\"Bearer \", \"\") || req.cookies[\"admin-token\"];\n        if (!token) {\n            return {\n                success: false,\n                message: \"No authentication token\"\n            };\n        }\n        const authResult = await verifyAdminToken(token);\n        if (!authResult.valid || !authResult.user) {\n            return {\n                success: false,\n                message: authResult.error || \"Invalid authentication\"\n            };\n        }\n        return {\n            success: true,\n            user: authResult.user\n        };\n    } catch (error) {\n        return {\n            success: false,\n            message: \"Authentication failed\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/auth/admin-auth.ts\n");

/***/ }),

/***/ "(api)/./lib/receipt-generator.js":
/*!**********************************!*\
  !*** ./lib/receipt-generator.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generatePOSReceipt: () => (/* binding */ generatePOSReceipt),\n/* harmony export */   generateReceipt: () => (/* binding */ generateReceipt)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(api)/./lib/supabase.js\");\n/**\n * Receipt Generator for Ocean Soul Sparkles\n * Generates customized receipts based on templates and booking data\n */ \n/**\n * Generate receipt HTML based on template and booking data\n */ async function generateReceipt(bookingData, templateId = null) {\n    try {\n        // Get receipt template\n        const template = await getReceiptTemplate(templateId);\n        if (!template) {\n            console.log(\"No template found, using default template\");\n            const defaultTemplate = getDefaultTemplate();\n            const receiptHtml = buildReceiptHtml(defaultTemplate, bookingData);\n            return {\n                success: true,\n                html: receiptHtml,\n                template: defaultTemplate\n            };\n        }\n        // Generate receipt HTML\n        const receiptHtml = buildReceiptHtml(template, bookingData);\n        return {\n            success: true,\n            html: receiptHtml,\n            template: template\n        };\n    } catch (error) {\n        console.error(\"Error generating receipt:\", error);\n        // Fallback to default template\n        try {\n            console.log(\"Falling back to default template due to error\");\n            const defaultTemplate = getDefaultTemplate();\n            const receiptHtml = buildReceiptHtml(defaultTemplate, bookingData);\n            return {\n                success: true,\n                html: receiptHtml,\n                template: defaultTemplate\n            };\n        } catch (fallbackError) {\n            console.error(\"Fallback template generation failed:\", fallbackError);\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    }\n}\n/**\n * Get receipt template by ID or default template\n */ async function getReceiptTemplate(templateId = null) {\n    try {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"receipt_templates\").select(\"*\").eq(\"is_active\", true);\n        if (templateId) {\n            query = query.eq(\"id\", templateId);\n        } else {\n            query = query.eq(\"is_default\", true);\n        }\n        const { data: templates, error } = await query.limit(1);\n        if (error) {\n            // If table doesn't exist, return default template\n            if (error.code === \"42P01\") {\n                console.log(\"Receipt templates table not found, using default template\");\n                return getDefaultTemplate();\n            }\n            throw new Error(`Database error: ${error.message}`);\n        }\n        if (!templates || templates.length === 0) {\n            // Fallback to any active template\n            const { data: fallbackTemplates } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"receipt_templates\").select(\"*\").eq(\"is_active\", true).limit(1);\n            return fallbackTemplates?.[0] || getDefaultTemplate();\n        }\n        return templates[0];\n    } catch (error) {\n        console.error(\"Error fetching receipt template:\", error);\n        return getDefaultTemplate();\n    }\n}\n/**\n * Get default template when database is not available\n */ function getDefaultTemplate() {\n    return {\n        id: \"default-standard\",\n        name: \"Standard Receipt\",\n        description: \"Default receipt template\",\n        template_type: \"standard\",\n        is_default: true,\n        is_active: true,\n        business_name: \"Ocean Soul Sparkles\",\n        business_address: \"Australia\",\n        business_phone: \"+61 XXX XXX XXX\",\n        business_email: \"<EMAIL>\",\n        business_website: \"oceansoulsparkles.com.au\",\n        business_abn: \"\",\n        show_logo: true,\n        logo_position: \"center\",\n        header_color: \"#667eea\",\n        text_color: \"#333333\",\n        font_family: \"Arial\",\n        font_size: 12,\n        show_customer_details: true,\n        show_service_details: true,\n        show_artist_details: true,\n        show_payment_details: true,\n        show_booking_notes: false,\n        show_terms_conditions: true,\n        footer_message: \"Thank you for choosing Ocean Soul Sparkles!\",\n        show_social_media: false,\n        social_media_links: null,\n        custom_fields: []\n    };\n}\n/**\n * Build receipt HTML from template and booking data\n */ function buildReceiptHtml(template, booking) {\n    const { business_name, business_address, business_phone, business_email, business_website, business_abn, show_logo, logo_position, header_color, text_color, font_family, font_size, show_customer_details, show_service_details, show_artist_details, show_payment_details, show_booking_notes, show_terms_conditions, footer_message, show_social_media, social_media_links, template_type } = template;\n    // Generate receipt number if not provided\n    const receiptNumber = booking.receipt_number || `OSS-${Date.now()}`;\n    const receiptDate = new Date().toLocaleDateString(\"en-AU\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    });\n    // Build CSS styles\n    const styles = `\n    <style>\n      body { \n        font-family: ${font_family}, sans-serif; \n        font-size: ${font_size}px; \n        color: ${text_color}; \n        margin: 0; \n        padding: 20px; \n        line-height: 1.4;\n        max-width: 400px;\n        margin: 0 auto;\n      }\n      .receipt-header { \n        text-align: ${logo_position}; \n        margin-bottom: 20px; \n        padding-bottom: 15px;\n        border-bottom: 2px solid ${header_color};\n      }\n      .business-name { \n        font-size: ${Math.round(font_size * 1.5)}px; \n        font-weight: bold; \n        color: ${header_color}; \n        margin: 0 0 5px 0;\n      }\n      .business-info { \n        font-size: ${Math.round(font_size * 0.9)}px; \n        color: #666; \n        margin: 2px 0;\n      }\n      .receipt-title { \n        font-size: ${Math.round(font_size * 1.2)}px; \n        font-weight: bold; \n        text-align: center; \n        margin: 20px 0 15px 0;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n      }\n      .receipt-info { \n        margin-bottom: 20px; \n        padding: 10px 0;\n        border-bottom: 1px solid #eee;\n      }\n      .section { \n        margin-bottom: 15px; \n      }\n      .section-title { \n        font-weight: bold; \n        margin-bottom: 8px; \n        color: ${header_color};\n        font-size: ${Math.round(font_size * 1.1)}px;\n      }\n      .detail-row { \n        display: flex; \n        justify-content: space-between; \n        margin-bottom: 3px;\n        align-items: flex-start;\n      }\n      .detail-label { \n        font-weight: 500; \n        flex: 1;\n      }\n      .detail-value { \n        text-align: right; \n        flex: 1;\n        word-break: break-word;\n      }\n      .total-section { \n        border-top: 2px solid ${header_color}; \n        padding-top: 10px; \n        margin-top: 15px;\n      }\n      .total-row { \n        display: flex; \n        justify-content: space-between; \n        font-weight: bold; \n        font-size: ${Math.round(font_size * 1.1)}px;\n        margin-bottom: 5px;\n      }\n      .footer { \n        text-align: center; \n        margin-top: 20px; \n        padding-top: 15px; \n        border-top: 1px solid #eee;\n        font-size: ${Math.round(font_size * 0.9)}px;\n      }\n      .footer-message { \n        font-style: italic; \n        color: #666; \n        margin-bottom: 10px;\n      }\n      .compact { font-size: ${Math.round(font_size * 0.9)}px; }\n      .detailed { font-size: ${font_size}px; }\n      @media print {\n        body { margin: 0; padding: 10px; }\n        .receipt-header { page-break-inside: avoid; }\n      }\n    </style>\n  `;\n    // Build header section\n    let headerHtml = `\n    <div class=\"receipt-header\">\n      ${show_logo ? `<div class=\"business-name\">${business_name}</div>` : \"\"}\n      ${business_address ? `<div class=\"business-info\">${business_address}</div>` : \"\"}\n      ${business_phone ? `<div class=\"business-info\">${business_phone}</div>` : \"\"}\n      ${business_email ? `<div class=\"business-info\">${business_email}</div>` : \"\"}\n      ${business_website ? `<div class=\"business-info\">${business_website}</div>` : \"\"}\n      ${business_abn ? `<div class=\"business-info\">ABN: ${business_abn}</div>` : \"\"}\n    </div>\n  `;\n    // Build receipt info section\n    let receiptInfoHtml = `\n    <div class=\"receipt-title\">Receipt</div>\n    <div class=\"receipt-info\">\n      <div class=\"detail-row\">\n        <span class=\"detail-label\">Receipt #:</span>\n        <span class=\"detail-value\">${receiptNumber}</span>\n      </div>\n      <div class=\"detail-row\">\n        <span class=\"detail-label\">Date:</span>\n        <span class=\"detail-value\">${receiptDate}</span>\n      </div>\n    </div>\n  `;\n    // Build customer details section\n    let customerHtml = \"\";\n    if (show_customer_details && booking.customer_name) {\n        customerHtml = `\n      <div class=\"section\">\n        <div class=\"section-title\">Customer Details</div>\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Name:</span>\n          <span class=\"detail-value\">${booking.customer_name}</span>\n        </div>\n        ${booking.customer_email ? `\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Email:</span>\n          <span class=\"detail-value\">${booking.customer_email}</span>\n        </div>` : \"\"}\n        ${booking.customer_phone ? `\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Phone:</span>\n          <span class=\"detail-value\">${booking.customer_phone}</span>\n        </div>` : \"\"}\n      </div>\n    `;\n    }\n    // Build service details section\n    let serviceHtml = \"\";\n    if (show_service_details) {\n        const startTime = booking.start_time ? new Date(booking.start_time).toLocaleString(\"en-AU\") : \"N/A\";\n        const duration = booking.duration ? `${booking.duration} minutes` : \"N/A\";\n        serviceHtml = `\n      <div class=\"section\">\n        <div class=\"section-title\">Service Details</div>\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Service:</span>\n          <span class=\"detail-value\">${booking.service_name || \"N/A\"}</span>\n        </div>\n        ${booking.tier_name ? `\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Tier:</span>\n          <span class=\"detail-value\">${booking.tier_name}</span>\n        </div>` : \"\"}\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Date & Time:</span>\n          <span class=\"detail-value\">${startTime}</span>\n        </div>\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Duration:</span>\n          <span class=\"detail-value\">${duration}</span>\n        </div>\n      </div>\n    `;\n    }\n    // Build artist details section\n    let artistHtml = \"\";\n    if (show_artist_details && booking.artist_name) {\n        artistHtml = `\n      <div class=\"section\">\n        <div class=\"section-title\">Artist Details</div>\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Artist:</span>\n          <span class=\"detail-value\">${booking.artist_name}</span>\n        </div>\n      </div>\n    `;\n    }\n    // Build payment details section\n    let paymentHtml = \"\";\n    if (show_payment_details) {\n        const totalAmount = booking.total_amount || 0;\n        const tipAmount = booking.tip_amount || 0;\n        const subtotal = totalAmount - tipAmount;\n        paymentHtml = `\n      <div class=\"section\">\n        <div class=\"section-title\">Payment Details</div>\n        ${tipAmount > 0 ? `\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Subtotal:</span>\n          <span class=\"detail-value\">$${subtotal.toFixed(2)}</span>\n        </div>\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">Tip:</span>\n          <span class=\"detail-value\">$${tipAmount.toFixed(2)}</span>\n        </div>` : \"\"}\n        <div class=\"total-section\">\n          <div class=\"total-row\">\n            <span>Total:</span>\n            <span>$${totalAmount.toFixed(2)}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">Payment Method:</span>\n            <span class=\"detail-value\">${booking.payment_method || \"N/A\"}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">Status:</span>\n            <span class=\"detail-value\">Paid</span>\n          </div>\n        </div>\n      </div>\n    `;\n    }\n    // Build notes section\n    let notesHtml = \"\";\n    if (show_booking_notes && booking.notes) {\n        notesHtml = `\n      <div class=\"section\">\n        <div class=\"section-title\">Notes</div>\n        <div>${booking.notes}</div>\n      </div>\n    `;\n    }\n    // Build footer section\n    let footerHtml = \"\";\n    if (footer_message || show_terms_conditions) {\n        footerHtml = `\n      <div class=\"footer\">\n        ${footer_message ? `<div class=\"footer-message\">${footer_message}</div>` : \"\"}\n        ${show_terms_conditions ? `\n        <div style=\"font-size: ${Math.round(font_size * 0.8)}px; color: #888;\">\n          Terms & Conditions apply. Visit our website for details.\n        </div>` : \"\"}\n        ${show_social_media && social_media_links ? `\n        <div style=\"margin-top: 10px;\">\n          Follow us on social media for updates and inspiration!\n        </div>` : \"\"}\n      </div>\n    `;\n    }\n    // Combine all sections\n    const receiptHtml = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>Receipt - ${receiptNumber}</title>\n      ${styles}\n    </head>\n    <body class=\"${template_type}\">\n      ${headerHtml}\n      ${receiptInfoHtml}\n      ${customerHtml}\n      ${serviceHtml}\n      ${artistHtml}\n      ${paymentHtml}\n      ${notesHtml}\n      ${footerHtml}\n    </body>\n    </html>\n  `;\n    return receiptHtml;\n}\n/**\n * Generate receipt for POS transaction\n */ async function generatePOSReceipt(transactionData, templateId = null) {\n    const bookingData = {\n        receipt_number: transactionData.receiptNumber || `POS-${Date.now()}`,\n        customer_name: transactionData.customerName || \"Walk-in Customer\",\n        customer_email: transactionData.customerEmail,\n        customer_phone: transactionData.customerPhone,\n        service_name: transactionData.serviceName,\n        tier_name: transactionData.tierName,\n        artist_name: transactionData.artistName,\n        start_time: transactionData.startTime || new Date().toISOString(),\n        duration: transactionData.duration,\n        total_amount: transactionData.totalAmount,\n        tip_amount: transactionData.tipAmount || 0,\n        payment_method: transactionData.paymentMethod,\n        notes: transactionData.notes\n    };\n    return await generateReceipt(bookingData, templateId);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/receipt-generator.js\n");

/***/ }),

/***/ "(api)/./lib/security/audit-logging.ts":
/*!***************************************!*\
  !*** ./lib/security/audit-logging.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditActions: () => (/* binding */ AuditActions),\n/* harmony export */   auditLog: () => (/* binding */ auditLog),\n/* harmony export */   exportAuditLogs: () => (/* binding */ exportAuditLogs),\n/* harmony export */   getAuditLogs: () => (/* binding */ getAuditLogs),\n/* harmony export */   logCriticalEvent: () => (/* binding */ logCriticalEvent),\n/* harmony export */   logDataChange: () => (/* binding */ logDataChange),\n/* harmony export */   logSecurityEvent: () => (/* binding */ logSecurityEvent),\n/* harmony export */   logUserAction: () => (/* binding */ logUserAction)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Log audit event to database and console\r\n */ async function auditLog(entry) {\n    try {\n        const logEntry = {\n            action: entry.action,\n            user_id: entry.userId,\n            user_role: entry.userRole,\n            email: entry.email,\n            ip_address: entry.ip,\n            path: entry.path,\n            resource: entry.resource,\n            resource_id: entry.resourceId,\n            old_values: entry.oldValues,\n            new_values: entry.newValues,\n            reason: entry.reason,\n            error: entry.error,\n            metadata: entry.metadata,\n            severity: entry.severity || \"medium\",\n            created_at: new Date().toISOString()\n        };\n        // Log to database\n        const { error } = await supabase.from(\"audit_logs\").insert(logEntry);\n        if (error) {\n            console.error(\"Failed to write audit log to database:\", error);\n        }\n        // Log to console for immediate visibility\n        const logLevel = getLogLevel(entry.severity || \"medium\");\n        const logMessage = formatLogMessage(entry);\n        console[logLevel](logMessage);\n        // For critical events, also send alerts\n        if (entry.severity === \"critical\") {\n            await sendCriticalAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Audit logging failed:\", error);\n        // Fallback to console logging\n        console.error(\"AUDIT_LOG_FAILURE:\", JSON.stringify(entry, null, 2));\n    }\n}\n/**\r\n * Get appropriate console log level based on severity\r\n */ function getLogLevel(severity) {\n    switch(severity){\n        case \"low\":\n            return \"log\";\n        case \"medium\":\n            return \"log\";\n        case \"high\":\n            return \"warn\";\n        case \"critical\":\n            return \"error\";\n        default:\n            return \"log\";\n    }\n}\n/**\r\n * Format audit log message for console output\r\n */ function formatLogMessage(entry) {\n    const timestamp = new Date().toISOString();\n    const user = entry.userId ? `[User: ${entry.userId}]` : \"\";\n    const ip = entry.ip ? `[IP: ${entry.ip}]` : \"\";\n    const path = entry.path ? `[Path: ${entry.path}]` : \"\";\n    return `[AUDIT] ${timestamp} ${entry.action} ${user} ${ip} ${path} ${entry.reason || \"\"}`.trim();\n}\n/**\r\n * Send critical alert for high-severity events\r\n */ async function sendCriticalAlert(entry) {\n    try {\n        // In production, this would send alerts via:\n        // - Email to admin team\n        // - Slack/Discord webhook\n        // - SMS for critical security events\n        // - Push notifications\n        console.error(\"\\uD83D\\uDEA8 CRITICAL SECURITY EVENT:\", {\n            action: entry.action,\n            userId: entry.userId,\n            ip: entry.ip,\n            reason: entry.reason,\n            timestamp: new Date().toISOString()\n        });\n        // Example: Send email alert (implement based on your email service)\n        if (process.env.ENABLE_CRITICAL_ALERTS === \"true\") {\n            await sendEmailAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Failed to send critical alert:\", error);\n    }\n}\n/**\r\n * Send email alert for critical events\r\n */ async function sendEmailAlert(entry) {\n    // Implementation would depend on your email service\n    // This is a placeholder for the actual implementation\n    console.log(\"Email alert would be sent for:\", entry.action);\n}\n/**\r\n * Audit log helper functions for common actions\r\n */ const AuditActions = {\n    // Authentication events\n    LOGIN_SUCCESS: \"LOGIN_SUCCESS\",\n    LOGIN_FAILED: \"LOGIN_FAILED\",\n    LOGIN_BLOCKED: \"LOGIN_BLOCKED\",\n    LOGOUT: \"LOGOUT\",\n    MFA_ENABLED: \"MFA_ENABLED\",\n    MFA_DISABLED: \"MFA_DISABLED\",\n    MFA_FAILED: \"MFA_FAILED\",\n    PASSWORD_CHANGED: \"PASSWORD_CHANGED\",\n    PASSWORD_RESET: \"PASSWORD_RESET\",\n    // Access control events\n    ACCESS_GRANTED: \"ACCESS_GRANTED\",\n    ACCESS_DENIED: \"ACCESS_DENIED\",\n    UNAUTHORIZED_ACCESS: \"UNAUTHORIZED_ACCESS\",\n    INSUFFICIENT_PERMISSIONS: \"INSUFFICIENT_PERMISSIONS\",\n    SESSION_TIMEOUT: \"SESSION_TIMEOUT\",\n    // Data modification events\n    RECORD_CREATED: \"RECORD_CREATED\",\n    RECORD_UPDATED: \"RECORD_UPDATED\",\n    RECORD_DELETED: \"RECORD_DELETED\",\n    BULK_UPDATE: \"BULK_UPDATE\",\n    BULK_DELETE: \"BULK_DELETE\",\n    // Admin actions\n    USER_CREATED: \"USER_CREATED\",\n    USER_UPDATED: \"USER_UPDATED\",\n    USER_DEACTIVATED: \"USER_DEACTIVATED\",\n    ROLE_CHANGED: \"ROLE_CHANGED\",\n    PERMISSIONS_CHANGED: \"PERMISSIONS_CHANGED\",\n    // System events\n    SYSTEM_ERROR: \"SYSTEM_ERROR\",\n    CONFIGURATION_CHANGED: \"CONFIGURATION_CHANGED\",\n    BACKUP_CREATED: \"BACKUP_CREATED\",\n    BACKUP_RESTORED: \"BACKUP_RESTORED\",\n    // Security events\n    IP_BLOCKED: \"IP_BLOCKED\",\n    RATE_LIMITED: \"RATE_LIMITED\",\n    SUSPICIOUS_ACTIVITY: \"SUSPICIOUS_ACTIVITY\",\n    SECURITY_SCAN: \"SECURITY_SCAN\",\n    VULNERABILITY_DETECTED: \"VULNERABILITY_DETECTED\",\n    // Business events\n    BOOKING_CREATED: \"BOOKING_CREATED\",\n    BOOKING_MODIFIED: \"BOOKING_MODIFIED\",\n    BOOKING_CANCELLED: \"BOOKING_CANCELLED\",\n    PAYMENT_PROCESSED: \"PAYMENT_PROCESSED\",\n    REFUND_ISSUED: \"REFUND_ISSUED\"\n};\n/**\r\n * Helper function to log user actions\r\n */ async function logUserAction(action, userId, userRole, details = {}) {\n    await auditLog({\n        action,\n        userId,\n        userRole,\n        severity: \"medium\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log security events\r\n */ async function logSecurityEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"high\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log critical security events\r\n */ async function logCriticalEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"critical\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log data changes\r\n */ async function logDataChange(action, userId, resource, resourceId, oldValues, newValues) {\n    await auditLog({\n        action,\n        userId,\n        resource,\n        resourceId,\n        oldValues,\n        newValues,\n        severity: \"medium\"\n    });\n}\n/**\r\n * Get audit logs with filtering and pagination\r\n */ async function getAuditLogs(filters) {\n    try {\n        let query = supabase.from(\"audit_logs\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        if (filters.userId) {\n            query = query.eq(\"user_id\", filters.userId);\n        }\n        if (filters.action) {\n            query = query.eq(\"action\", filters.action);\n        }\n        if (filters.severity) {\n            query = query.eq(\"severity\", filters.severity);\n        }\n        if (filters.startDate) {\n            query = query.gte(\"created_at\", filters.startDate);\n        }\n        if (filters.endDate) {\n            query = query.lte(\"created_at\", filters.endDate);\n        }\n        if (filters.limit) {\n            query = query.limit(filters.limit);\n        }\n        if (filters.offset) {\n            query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw error;\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error fetching audit logs:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n/**\r\n * Export audit logs for compliance\r\n */ async function exportAuditLogs(startDate, endDate, format = \"json\") {\n    try {\n        const { data, error } = await supabase.from(\"audit_logs\").select(\"*\").gte(\"created_at\", startDate).lte(\"created_at\", endDate).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) {\n            throw error;\n        }\n        if (format === \"csv\") {\n            return convertToCSV(data);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Error exporting audit logs:\", error);\n        throw error;\n    }\n}\n/**\r\n * Convert audit logs to CSV format\r\n */ function convertToCSV(data) {\n    if (!data || data.length === 0) {\n        return \"\";\n    }\n    const headers = Object.keys(data[0]);\n    const csvContent = [\n        headers.join(\",\"),\n        ...data.map((row)=>headers.map((header)=>{\n                const value = row[header];\n                if (typeof value === \"object\" && value !== null) {\n                    return `\"${JSON.stringify(value).replace(/\"/g, '\"\"')}\"`;\n                }\n                return `\"${String(value || \"\").replace(/\"/g, '\"\"')}\"`;\n            }).join(\",\"))\n    ].join(\"\\n\");\n    return csvContent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/security/audit-logging.ts\n");

/***/ }),

/***/ "(api)/./lib/supabase.js":
/*!*************************!*\
  !*** ./lib/supabase.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\n// Client for browser-side operations (with RLS)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    },\n    realtime: {\n        params: {\n            eventsPerSecond: 10\n        }\n    }\n});\n// Admin client for server-side operations (bypasses RLS)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceRoleKey || supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Test connection function\nasync function testConnection() {\n    try {\n        const { data, error } = await supabaseAdmin.from(\"admin_users\").select(\"count(*)\").limit(1);\n        if (error) {\n            console.error(\"Supabase connection test failed:\", error);\n            return false;\n        }\n        console.log(\"✅ Supabase connection successful\");\n        return true;\n    } catch (err) {\n        console.error(\"❌ Supabase connection error:\", err);\n        return false;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.js\n");

/***/ }),

/***/ "(api)/./pages/api/admin/receipts/preview.js":
/*!*********************************************!*\
  !*** ./pages/api/admin/receipts/preview.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth/admin-auth */ \"(api)/./lib/auth/admin-auth.ts\");\n/* harmony import */ var _lib_receipt_generator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/receipt-generator */ \"(api)/./lib/receipt-generator.js\");\n\n\n/**\n * API endpoint for generating receipt previews\n * Used by the receipt customizer to show live previews\n */ async function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        // Authenticate admin user\n        const token = req.headers.authorization?.replace(\"Bearer \", \"\") || req.cookies[\"admin-token\"];\n        if (!token) {\n            return res.status(401).json({\n                error: \"No authentication token\"\n            });\n        }\n        const authResult = await (0,_lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__.authenticateAdminRequest)(token);\n        if (!authResult.valid || !authResult.user) {\n            return res.status(401).json({\n                error: \"Invalid authentication\"\n            });\n        }\n        const { templateId, bookingData } = req.body;\n        const requestId = Math.random().toString(36).substring(2, 8);\n        console.log(`[${requestId}] Receipt preview requested for template: ${templateId}`);\n        if (!bookingData) {\n            return res.status(400).json({\n                error: \"Booking data is required\"\n            });\n        }\n        // Generate receipt HTML\n        const result = await (0,_lib_receipt_generator__WEBPACK_IMPORTED_MODULE_1__.generateReceipt)(bookingData, templateId);\n        if (!result.success) {\n            console.error(`[${requestId}] Error generating receipt preview:`, result.error);\n            return res.status(500).json({\n                error: result.error\n            });\n        }\n        console.log(`[${requestId}] Receipt preview generated successfully`);\n        return res.status(200).json({\n            html: result.html,\n            template: result.template\n        });\n    } catch (error) {\n        console.error(\"Receipt preview API error:\", error);\n        return res.status(500).json({\n            error: \"Internal server error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/admin/receipts/preview.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freceipts%2Fpreview&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creceipts%5Cpreview.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();