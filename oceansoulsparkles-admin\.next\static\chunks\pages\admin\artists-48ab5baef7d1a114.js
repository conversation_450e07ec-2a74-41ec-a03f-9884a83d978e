(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[291],{489:function(t,r,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/artists",function(){return e(1183)}])},1183:function(t,r,e){"use strict";e.r(r),e.d(r,{default:function(){return u}}),function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();var a=e(9008),n=e.n(a);e(1163);var i=e(1664),o=e.n(i),c=e(6026),s=e(99),d=e(8988),l=e.n(d);function u(){let{user:t,loading:r}=(0,c.a)(),[e,a]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(!0),[i,d]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())([]),[u,O]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())([]),[_,m]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(""),[N,f]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())("all"),[v,h]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())("all"),[D,E]=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())("name"),U=async()=>{try{a(!0);let t=localStorage.getItem("admin-token"),r=await fetch("/api/admin/artists",{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}});if(!r.ok)throw Error("Failed to fetch artists");let e=await r.json();d(e.artists||[]),O(e.artists||[])}catch(t){console.error("Error loading artists:",t),d([]),O([])}finally{a(!1)}};Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{!r&&t&&U()},[r,t]),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{let t=i;_&&(t=t.filter(t=>t.name.toLowerCase().includes(_.toLowerCase())||t.email.toLowerCase().includes(_.toLowerCase())||t.specializations&&t.specializations.some&&t.specializations.some(t=>t.toLowerCase().includes(_.toLowerCase())))),"all"!==N&&(t=t.filter(t=>t.specializations.includes(N))),"all"!==v&&(t=t.filter(t=>t.status===v)),t.sort((t,r)=>{switch(D){case"name":default:return t.name.localeCompare(r.name);case"rating":return r.rating-t.rating;case"bookings":return r.total_bookings-t.total_bookings;case"revenue":return r.total_revenue-t.total_revenue}}),O(t)},[i,_,N,v,D]);let j=[...new Set(i.flatMap(t=>t.specializations))],b=t=>{switch(t){case"active":return l().statusActive;case"inactive":return l().statusInactive;default:return l().statusDefault}},C=t=>{switch(t){case"available":return l().availabilityAvailable;case"busy":return l().availabilityBusy;case"unavailable":return l().availabilityUnavailable;default:return l().availabilityDefault}},w=async t=>{try{d(r=>r.map(r=>r.id===t?{...r,status:"active"===r.status?"inactive":"active"}:r))}catch(t){console.error("Error updating artist status:",t)}},L={totalArtists:i.length,activeArtists:i.filter(t=>"active"===t.status).length,availableArtists:i.filter(t=>"available"===t.availability).length,totalRevenue:i.reduce((t,r)=>t+r.total_revenue,0),avgRating:i.reduce((t,r)=>t+r.rating,0)/i.length};return r||e?Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(s.Z,{children:Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().loadingContainer,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().loadingSpinner}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("p",{children:"Loading artists..."})]})}):t?Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(s.Z,{children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(n(),{children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("title",{children:"Artists Management | Ocean Soul Sparkles Admin"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("meta",{name:"description",content:"Manage artist profiles and assignments"})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().artistsContainer,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("header",{className:l().header,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h1",{className:l().title,children:"Artists Management"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().headerActions,children:Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(o(),{href:"/admin/artists/new",className:l().newArtistBtn,children:"+ Add Artist"})})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().controlsPanel,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().searchSection,children:Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("input",{type:"text",placeholder:"Search artists by name, email, or specialization...",value:_,onChange:t=>m(t.target.value),className:l().searchInput})}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().filters,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("select",{value:N,onChange:t=>f(t.target.value),className:l().specializationFilter,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"all",children:"All Specializations"}),j.map(t=>Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:t,children:t},t))]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("select",{value:v,onChange:t=>h(t.target.value),className:l().statusFilter,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"all",children:"All Status"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"active",children:"Active"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"inactive",children:"Inactive"})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("select",{value:D,onChange:t=>E(t.target.value),className:l().sortSelect,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"name",children:"Sort by Name"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"rating",children:"Sort by Rating"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"bookings",children:"Sort by Bookings"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("option",{value:"revenue",children:"Sort by Revenue"})]})]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().artistsContent,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statsCards,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statCard,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h3",{children:"Total Artists"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statValue,children:L.totalArtists})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statCard,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h3",{children:"Active Artists"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statValue,children:L.activeArtists})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statCard,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h3",{children:"Available Now"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statValue,children:L.availableArtists})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statCard,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h3",{children:"Total Revenue"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statValue,children:["$",L.totalRevenue.toLocaleString()]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statCard,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h3",{children:"Avg Rating"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statValue,children:[L.avgRating.toFixed(1),"★"]})]})]}),0===u.length?Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().emptyState,children:Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("p",{children:"No artists found matching your criteria."})}):Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().artistsGrid,children:u.map(t=>Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().artistCard,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().artistHeader,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().artistInfo,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h3",{children:t.name}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("p",{children:t.email}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("p",{children:t.phone})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().badges,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:"".concat(l().statusBadge," ").concat(b(t.status)),children:t.status}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:"".concat(l().availabilityBadge," ").concat(C(t.availability)),children:t.availability})]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().specializations,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("h4",{children:"Specializations:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().specializationTags,children:t.specializations.map(t=>Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:l().specializationTag,children:t},t))})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().artistStats,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statItem,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:l().statLabel,children:"Rating:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:l().statValue,children:[t.rating,"★"]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statItem,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:l().statLabel,children:"Bookings:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:l().statValue,children:t.total_bookings})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statItem,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:l().statLabel,children:"Revenue:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:l().statValue,children:["$",t.total_revenue.toLocaleString()]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().statItem,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:l().statLabel,children:"Joined:"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("span",{className:l().statValue,children:new Date(t.joined_date).toLocaleDateString()})]})]}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("div",{className:l().artistActions,children:[Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(o(),{href:"/admin/artists/".concat(t.id),className:l().viewBtn,children:"View Profile"}),Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())("button",{onClick:()=>w(t.id),className:"".concat(l().toggleBtn," ").concat("active"===t.status?l().deactivate:l().activate),children:"active"===t.status?"Deactivate":"Activate"})]})]},t.id))})]})]})]}):null}},8988:function(t){t.exports={artistsContainer:"Artists_artistsContainer__0rRfo",header:"Artists_header__l056z",title:"Artists_title__vjxcu",headerActions:"Artists_headerActions__90PqJ",newArtistBtn:"Artists_newArtistBtn__bzTM4",backButton:"Artists_backButton__4_qDa",controlsPanel:"Artists_controlsPanel__hiH1g",searchSection:"Artists_searchSection__YNGbF",searchInput:"Artists_searchInput__L5n_I",filters:"Artists_filters__brZMt",specializationFilter:"Artists_specializationFilter__pdB2f",statusFilter:"Artists_statusFilter__NXaRr",sortSelect:"Artists_sortSelect__Mo0wU",artistsContent:"Artists_artistsContent__oRUhp",statsCards:"Artists_statsCards__szEZ5",statCard:"Artists_statCard__fzXfl",statValue:"Artists_statValue__yQNVC",emptyState:"Artists_emptyState__quJ1M",artistsGrid:"Artists_artistsGrid__gpdVz",artistCard:"Artists_artistCard__BAcxg",artistHeader:"Artists_artistHeader__NZbK_",artistInfo:"Artists_artistInfo__GEQEA",badges:"Artists_badges__eZJwh",statusBadge:"Artists_statusBadge__o_ZJI",availabilityBadge:"Artists_availabilityBadge__WHNQb",statusActive:"Artists_statusActive__Xr6co",statusInactive:"Artists_statusInactive__HVOhk",statusDefault:"Artists_statusDefault__BVw_v",availabilityAvailable:"Artists_availabilityAvailable__XSsWU",availabilityBusy:"Artists_availabilityBusy__fXExt",availabilityUnavailable:"Artists_availabilityUnavailable__LIv9A",availabilityDefault:"Artists_availabilityDefault__9jx5n",specializations:"Artists_specializations__bGPbn",specializationTags:"Artists_specializationTags__2x8sm",specializationTag:"Artists_specializationTag__nPNET",artistStats:"Artists_artistStats__VqwDN",statItem:"Artists_statItem__PrhZt",statLabel:"Artists_statLabel__UcjBT",artistActions:"Artists_artistActions__dYGhw",viewBtn:"Artists_viewBtn__LP0kP",toggleBtn:"Artists_toggleBtn__HJwor",activate:"Artists_activate__tWij9",deactivate:"Artists_deactivate__OU5kr",loadingContainer:"Artists_loadingContainer__Jck0K",loadingSpinner:"Artists_loadingSpinner__DTj7G",spin:"Artists_spin__RDcmV"}}},function(t){t.O(0,[736,592,888,179],function(){return t(t.s=489)}),_N_E=t.O()}]);