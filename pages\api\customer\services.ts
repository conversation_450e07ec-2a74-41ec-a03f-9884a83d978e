import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { category } = req.query;

    // Build query for public services
    let query = supabase
      .from('services')
      .select(`
        id,
        name,
        description,
        category,
        base_price,
        duration,
        service_pricing_tiers (
          id,
          name,
          description,
          price,
          duration,
          is_default,
          sort_order
        )
      `)
      .eq('is_active', true)
      .eq('visible_on_public', true);

    // Filter by category if provided
    if (category && typeof category === 'string') {
      query = query.eq('category', category);
    }

    const { data: services, error } = await query
      .order('category', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('Services query error:', error);
      return res.status(500).json({ error: 'Failed to fetch services' });
    }

    // Transform services data for customer portal
    const transformedServices = services?.map(service => {
      // Sort pricing tiers
      const sortedTiers = service.service_pricing_tiers?.sort((a, b) => a.sort_order - b.sort_order) || [];
      
      // Find default tier or use first tier
      const defaultTier = sortedTiers.find(tier => tier.is_default) || sortedTiers[0];
      
      return {
        id: service.id,
        name: service.name,
        description: service.description,
        category: service.category,
        base_price: service.base_price,
        duration: service.duration,
        default_price: defaultTier?.price || service.base_price,
        default_duration: defaultTier?.duration || service.duration,
        pricing_tiers: sortedTiers.map(tier => ({
          id: tier.id,
          name: tier.name,
          description: tier.description,
          price: tier.price,
          duration: tier.duration,
          is_default: tier.is_default
        }))
      };
    }) || [];

    // Group services by category
    const servicesByCategory = transformedServices.reduce((acc, service) => {
      const category = service.category || 'Other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(service);
      return acc;
    }, {} as Record<string, any[]>);

    return res.status(200).json({
      services: transformedServices,
      servicesByCategory,
      total: transformedServices.length,
      categories: Object.keys(servicesByCategory)
    });

  } catch (error) {
    console.error('Customer services API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
