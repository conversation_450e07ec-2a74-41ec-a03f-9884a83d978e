/**
 * Ocean Soul Sparkles Admin Dashboard - Main Type Definitions
 * Comprehensive TypeScript types for production-ready admin system
 */

// Re-export all domain-specific types
export * from './customer';
export * from './booking';
export * from './service';
export * from './product';
export * from './inventory';
export * from './staff';
export * from './api';
export * from './common';

// Global utility types
export type ID = string;
export type Timestamp = string; // ISO 8601 format
export type Currency = number; // Always in cents/smallest unit
export type Percentage = number; // 0-100
export type Email = string;
export type PhoneNumber = string;
export type URL = string;

// Common status types
export type Status = 'active' | 'inactive' | 'pending' | 'suspended';
export type BookingStatus = 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded' | 'partially_refunded';
export type UserRole = 'Admin' | 'Manager' | 'Artist' | 'Braider' | 'Staff' | 'DEV';

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter and sort types
export interface FilterParams {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  [key: string]: any;
}

export interface SortParams {
  field: string;
  direction: 'asc' | 'desc';
}

// CRUD operation types
export interface CreateOperation<T> {
  data: Omit<T, 'id' | 'created_at' | 'updated_at'>;
}

export interface UpdateOperation<T> {
  id: ID;
  data: Partial<Omit<T, 'id' | 'created_at'>>;
}

export interface DeleteOperation {
  id: ID;
  soft?: boolean; // Soft delete vs hard delete
}

// Database relationship types
export interface DatabaseRelation<T> {
  id: ID;
  data?: T;
  loaded?: boolean;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface FormState<T> {
  data: T;
  errors: ValidationError[];
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  id?: string;
  'data-testid'?: string;
}

export interface LoadingProps {
  loading?: boolean;
  loadingText?: string;
}

export interface ErrorProps {
  error?: string | Error | null;
  onRetry?: () => void;
}

// Mobile-specific types
export interface MobileProps {
  isMobile?: boolean;
  touchOptimized?: boolean;
}

export interface ResponsiveProps extends MobileProps {
  breakpoint?: 'mobile' | 'tablet' | 'desktop';
}

// Chart and analytics types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: Record<string, any>;
}

export interface TimeSeriesData {
  timestamp: Timestamp;
  value: number;
  label?: string;
}

// Notification types
export interface NotificationData {
  id: ID;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Timestamp;
  read: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

// Search types
export interface SearchResult {
  id: ID;
  type: 'customer' | 'booking' | 'service' | 'product' | 'staff';
  title: string;
  subtitle: string;
  description?: string;
  url: string;
  metadata?: Record<string, any>;
  relevanceScore?: number;
}

export interface SearchParams {
  query: string;
  types?: SearchResult['type'][];
  limit?: number;
  includeInactive?: boolean;
}

// Audit and logging types
export interface AuditLogEntry {
  id: ID;
  action: string;
  userId?: ID;
  userRole?: UserRole;
  email?: Email;
  ipAddress?: string;
  path?: string;
  resource?: string;
  resourceId?: ID;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  reason?: string;
  error?: string;
  metadata?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Timestamp;
}

// System settings types
export interface SystemSetting {
  id: ID;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'json';
  category: string;
  description?: string;
  isPublic: boolean;
  updatedBy: ID;
  updatedAt: Timestamp;
}

// Feature toggle types
export interface FeatureToggle {
  key: string;
  enabled: boolean;
  description?: string;
  rolloutPercentage?: number;
  conditions?: Record<string, any>;
}

// Performance monitoring types
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Timestamp;
  tags?: Record<string, string>;
}

// Generic utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Type guards
export const isValidEmail = (email: string): email is Email => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};

export const isValidPhoneNumber = (phone: string): phone is PhoneNumber => {
  return /^\+?[\d\s\-\(\)]{10,}$/.test(phone);
};

export const isValidURL = (url: string): url is URL => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};
