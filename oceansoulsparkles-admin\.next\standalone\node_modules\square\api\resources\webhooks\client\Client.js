"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Webhooks = void 0;
const Client_1 = require("../resources/eventTypes/client/Client");
const Client_2 = require("../resources/subscriptions/client/Client");
class Webhooks {
    constructor(_options = {}) {
        this._options = _options;
    }
    get eventTypes() {
        var _a;
        return ((_a = this._eventTypes) !== null && _a !== void 0 ? _a : (this._eventTypes = new Client_1.EventTypes(this._options)));
    }
    get subscriptions() {
        var _a;
        return ((_a = this._subscriptions) !== null && _a !== void 0 ? _a : (this._subscriptions = new Client_2.Subscriptions(this._options)));
    }
}
exports.Webhooks = Webhooks;
