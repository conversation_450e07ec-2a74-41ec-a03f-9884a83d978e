/**
 * POSSquarePayment - Square Web Payments SDK integration for card payments
 * Handles secure card entry, payment processing, and error handling
 */

import { useState, useEffect, useRef, useCallback } from 'react'
import styles from '@/styles/admin/POS.module.css'

export default function POSSquarePayment({
  amount,
  currency = 'AUD',
  onSuccess,
  onError,
  orderDetails = {}
}) {
  const [paymentForm, setPaymentForm] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const initializationAttemptedRef = useRef(false)
  const [billingAddress, setBillingAddress] = useState({
    addressLine1: '1455 Market St',
    addressLine2: 'Suite 600',
    locality: 'San Francisco',
    administrativeDistrictLevel1: 'CA',
    postalCode: '94103',
    country: 'US'
  })
  const [showBillingAddress, setShowBillingAddress] = useState(false)
  const containerRef = useRef(null)
  const mountedRef = useRef(false)
  const paymentFormRef = useRef(null)
  const isAttachingRef = useRef(false)
  const sdkLoadControllerRef = useRef(null)
  const selfContainerIdRef = useRef(null)

  // Initialize Square payment form
  const initializeSquareForm = useCallback(async () => {
    const initStartTime = performance.now()

    try {
      isAttachingRef.current = true

      if (!mountedRef.current) {
        console.warn('InitializeSquareForm: Component unmounted before starting.')
        isAttachingRef.current = false
        return
      }

      if (initializationAttemptedRef.current) {
        console.log('InitializeSquareForm: Already attempted in this lifecycle.')
        isAttachingRef.current = false
        return
      }

      if (!containerRef.current) {
        console.error('InitializeSquareForm: Container ref not available')
        isAttachingRef.current = false
        return
      }

      setErrorMessage('')

      // Check if Square SDK is loaded
      if (typeof window === 'undefined' || !window.Square) {
        throw new Error('Square SDK not loaded')
      }

      const appId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
      const locationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID

      if (!appId || !locationId) {
        throw new Error('Square configuration missing')
      }

      if (!containerRef.current || !mountedRef.current) {
        console.warn('Container reference lost during initialization')
        return
      }

      const reactContainer = containerRef.current

      // Create a completely isolated container for Square SDK with stable ID
      const squareContainer = document.createElement('div')
      const containerId = `square-card-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
      squareContainer.id = containerId
      squareContainer.style.cssText = `
        width: 100%;
        min-height: 60px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        background: white;
        position: relative;
      `

      reactContainer.appendChild(squareContainer)
      selfContainerIdRef.current = containerId

      console.log(`Creating Square payments instance for container: ${containerId}`)
      const payments = window.Square.payments(appId, locationId)

      // Enhanced card options with better error handling
      const isSandbox = appId.startsWith('sandbox-')
      const cardOptions = {
        style: {
          '.input-container': {
            borderColor: '#e0e0e0',
            borderRadius: '8px'
          },
          '.input-container.is-focus': {
            borderColor: '#4ECDC4'
          },
          '.input-container.is-error': {
            borderColor: '#dc3545'
          },
          '.message-text': {
            color: '#dc3545'
          }
        }
      }

      // For sandbox environment, configure to include billing address
      if (isSandbox) {
        setShowBillingAddress(true)
        console.log('Sandbox mode: Enabling billing address collection for AVS compatibility')
      }

      console.log(`Creating Square card object for container: ${containerId}`)
      const card = await payments.card(cardOptions)

      // Enhanced pre-attachment validation
      if (!squareContainer.parentNode || !mountedRef.current) {
        console.warn(`Square container ${containerId} was removed during card creation`)
        return
      }

      // Verify container integrity
      const verifyContainer = document.getElementById(containerId)
      if (!verifyContainer) {
        console.error(`Container ${containerId} no longer exists in DOM`)
        isAttachingRef.current = false
        return
      }

      console.log(`Attaching card to container: ${containerId}`)
      await card.attach(`#${containerId}`)
      paymentFormRef.current = card
      initializationAttemptedRef.current = true

      // Only set state if component is still mounted and container is stable
      if (mountedRef.current) {
        setPaymentForm(card)
        setIsLoading(false)

        // Performance logging
        const initEndTime = performance.now()
        const totalInitTime = initEndTime - initStartTime

        console.log(`Square form initialization completed successfully: ${containerId}`, {
          totalTime: `${Math.round(totalInitTime)}ms`,
          performanceGrade: totalInitTime < 1000 ? 'Good' : totalInitTime < 2000 ? 'Fair' : 'Slow'
        })

        if (totalInitTime > 2000) {
          console.warn(`Square form initialization took ${Math.round(totalInitTime)}ms - consider optimizing`)
        }
      } else {
        console.warn(`Component unmounted after card attachment: ${containerId}`)
        if (card && typeof card.destroy === 'function') {
          try {
            await card.destroy()
          } catch (destroyError) {
            console.warn('Error destroying card after unmount:', destroyError)
          }
        }
        return
      }

    } catch (error) {
      console.error('Square form initialization error:', error)
      setErrorMessage(error.message || 'Failed to initialize payment form')
      setIsLoading(false)
    } finally {
      isAttachingRef.current = false
    }
  }, [])

  // Setup and cleanup
  useEffect(() => {
    mountedRef.current = true
    
    const timer = setTimeout(() => {
      if (mountedRef.current && !initializationAttemptedRef.current) {
        initializeSquareForm()
      }
    }, 100)

    return () => {
      mountedRef.current = false
      clearTimeout(timer)
      
      // Cleanup Square form
      if (paymentFormRef.current) {
        try {
          if (typeof paymentFormRef.current.destroy === 'function') {
            paymentFormRef.current.destroy()
          }
        } catch (error) {
          console.warn('Error destroying payment form:', error)
        }
        paymentFormRef.current = null
      }

      // Clean up DOM container
      const containerId = selfContainerIdRef.current
      if (containerId) {
        const container = document.getElementById(containerId)
        if (container && container.parentNode) {
          container.parentNode.removeChild(container)
        }
      }
    }
  }, [initializeSquareForm])

  const retryInitialization = () => {
    initializationAttemptedRef.current = false
    setErrorMessage('')
    setIsLoading(true)
    initializeSquareForm()
  }

  // Handle payment submission
  const handlePayment = useCallback(async () => {
    if (!paymentForm || isProcessing) return

    setIsProcessing(true)
    setErrorMessage('')

    try {
      // Start POS payment protection
      try {
        const { startPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
        startPOSPaymentOperation()
      } catch (protectionError) {
        console.warn('POS payment protection not available:', protectionError)
      }

      console.log('🔄 Tokenizing card...')

      const tokenizeOptions = showBillingAddress ? {
        billingContact: {
          addressLines: [billingAddress.addressLine1, billingAddress.addressLine2].filter(Boolean),
          city: billingAddress.locality,
          countryCode: billingAddress.country,
          postalCode: billingAddress.postalCode,
          state: billingAddress.administrativeDistrictLevel1
        }
      } : {}

      const tokenResult = await paymentForm.tokenize(tokenizeOptions)

      if (tokenResult.status === 'OK') {
        console.log('✅ Card tokenized successfully')
        const paymentResult = await processPayment(tokenResult.token)
        
        onSuccess(paymentResult)
      } else {
        console.error('❌ Tokenization failed:', tokenResult.errors)
        const errorMsg = tokenResult.errors?.[0]?.message || 'Card tokenization failed'
        setErrorMessage(errorMsg)
        onError(new Error(errorMsg))
      }
    } catch (error) {
      console.error('Payment processing error:', error)
      setErrorMessage(error.message || 'Payment failed. Please try again.')
      onError(error)
    } finally {
      setIsProcessing(false)

      try {
        const { endPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
        endPOSPaymentOperation()
      } catch (protectionError) {
        console.warn('Error ending POS payment protection:', protectionError)
      }
    }
  }, [isProcessing, showBillingAddress, billingAddress, amount, currency, onSuccess, onError])

  // Process payment using Square API
  const processPayment = async (token) => {
    console.log('🔄 Processing payment with token...')

    try {
      const response = await fetch('/api/admin/pos/process-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceId: token,
          amount: Math.round(parseFloat(amount) * 100), // Convert to cents
          currency: currency,
          orderDetails: orderDetails,
          idempotencyKey: `pos_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `Payment failed: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ Payment processed successfully:', result)
      return result
    } catch (error) {
      console.error('Payment API error:', error)
      throw error
    }
  }

  // Debug render state
  console.log('🔍 POSSquarePayment render state:', {
    showBillingAddress,
    isLoading,
    paymentForm: !!paymentFormRef.current,
    squareSDKLoaded: typeof window !== 'undefined' && !!window.Square,
    initializationAttempted: initializationAttemptedRef.current
  })

  return (
    <div className={styles.squarePaymentContainer}>
      <div className={styles.paymentFormHeader}>
        <h4>Card Payment</h4>
        <div className={styles.paymentAmount}>
          Amount: <span>${parseFloat(amount || 0).toFixed(2)} {currency}</span>
        </div>
      </div>

      {errorMessage && (
        <div className={styles.paymentError}>
          <span className={styles.errorIcon}>⚠️</span>
          <div className={styles.errorContent}>
            <div className={styles.errorText}>{errorMessage}</div>
            <button
              onClick={retryInitialization}
              className={styles.retryButton}
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className={styles.cardFormContainer}>
        <div
          ref={containerRef}
          className={styles.cardForm}
          style={{
            minHeight: '60px',
            border: '1px solid #e0e0e0',
            borderRadius: '8px',
            padding: '16px',
            background: 'white'
          }}
        >
          {isLoading && (
            <div className={styles.cardFormPlaceholder}>
              <div className={styles.loadingSpinner}></div>
              <p>Initializing secure payment form...</p>
            </div>
          )}
        </div>

        {showBillingAddress && (
          <div className={styles.billingAddressSection}>
            <h5>Billing Address</h5>
            <div className={styles.addressGrid}>
              <div className={styles.addressField}>
                <label>Address Line 1</label>
                <input
                  type="text"
                  value={billingAddress.addressLine1}
                  onChange={(e) => setBillingAddress(prev => ({...prev, addressLine1: e.target.value}))}
                  placeholder="1455 Market St"
                />
              </div>
              <div className={styles.addressField}>
                <label>Address Line 2</label>
                <input
                  type="text"
                  value={billingAddress.addressLine2}
                  onChange={(e) => setBillingAddress(prev => ({...prev, addressLine2: e.target.value}))}
                  placeholder="Suite 600"
                />
              </div>
              <div className={styles.addressField}>
                <label>City</label>
                <input
                  type="text"
                  value={billingAddress.locality}
                  onChange={(e) => setBillingAddress(prev => ({...prev, locality: e.target.value}))}
                  placeholder="San Francisco"
                />
              </div>
              <div className={styles.addressField}>
                <label>State</label>
                <input
                  type="text"
                  value={billingAddress.administrativeDistrictLevel1}
                  onChange={(e) => setBillingAddress(prev => ({...prev, administrativeDistrictLevel1: e.target.value}))}
                  placeholder="CA"
                />
              </div>
              <div className={styles.addressField}>
                <label>ZIP Code</label>
                <input
                  type="text"
                  value={billingAddress.postalCode}
                  onChange={(e) => setBillingAddress(prev => ({...prev, postalCode: e.target.value}))}
                  placeholder="94103"
                />
              </div>
              <div className={styles.addressField}>
                <label>Country</label>
                <select
                  value={billingAddress.country}
                  onChange={(e) => setBillingAddress(prev => ({...prev, country: e.target.value}))}
                >
                  <option value="US">United States</option>
                  <option value="AU">Australia</option>
                  <option value="CA">Canada</option>
                  <option value="GB">United Kingdom</option>
                </select>
              </div>
            </div>
            <div className={styles.addressNote}>
              <small>💡 Billing address is required for card verification in sandbox mode</small>
            </div>
          </div>
        )}
      </div>

      <div className={styles.paymentActions}>
        <button
          onClick={handlePayment}
          disabled={!paymentForm || isProcessing || isLoading}
          className={styles.payButton}
        >
          {isProcessing ? (
            <>
              <div className={styles.buttonSpinner}></div>
              Processing...
            </>
          ) : (
            `Pay ${currency} $${parseFloat(amount || 0).toFixed(2)}`
          )}
        </button>

        {!paymentForm && !isLoading && !errorMessage && (
          <div className={styles.formNotReady}>
            <span className={styles.errorIcon}>⚠️</span>
            Card form not initialized. Please wait for the form to load.
          </div>
        )}

        {errorMessage && (
          <div className={styles.errorContainer}>
            <p className={styles.errorMessage}>{errorMessage}</p>
            <button
              onClick={retryInitialization}
              className={styles.retryButton}
            >
              Retry
            </button>
          </div>
        )}
      </div>

      <div className={styles.paymentSecurity}>
        <div className={styles.securityBadges}>
          <span className={styles.securityBadge}>🔒 SSL Encrypted</span>
          <span className={styles.securityBadge}>✅ PCI Compliant</span>
          <span className={styles.securityBadge}>🛡️ Square Secure</span>
        </div>
        <p className={styles.securityText}>
          Your payment information is processed securely by Square and never stored on our servers.
        </p>
      </div>
    </div>
  )
}
