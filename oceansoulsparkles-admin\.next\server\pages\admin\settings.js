(()=>{var e={};e.id=577,e.ids=[577,660],e.modules={4237:e=>{e.exports={settingsContainer:"Settings_settingsContainer__E9ikM",header:"Settings_header__iprwI",title:"Settings_title__UPQFQ",headerActions:"Settings_headerActions__RuDIB",resetBtn:"Settings_resetBtn__Ibi0t",saveBtn:"Settings_saveBtn__8_bRU",settingsContent:"Settings_settingsContent__nNeed",tabNavigation:"Settings_tabNavigation__IKynR",tabButton:"Settings_tabButton__jz8Cs",active:"Settings_active__0_8Bo",tabContent:"Settings_tabContent__cAICo",settingsSection:"Settings_settingsSection__GC4In",settingsGrid:"Settings_settingsGrid__wLtMu",settingItem:"Settings_settingItem__KxWbr",comingSoon:"Settings_comingSoon__Szu72",loadingContainer:"Settings_loadingContainer__uPTPv",loadingSpinner:"Settings_loadingSpinner__6_Tk_",spin:"Settings_spin__3eCCm",accessDenied:"Settings_accessDenied___aepr"}},6569:(e,i,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(i),t.d(i,{config:()=>f,default:()=>g,getServerSideProps:()=>x,getStaticPaths:()=>u,getStaticProps:()=>h,reportWebVitals:()=>j,routeModule:()=>S,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>k,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>p});var n=t(7093),a=t(5244),o=t(1323),c=t(2899),l=t.n(c),r=t(6814),d=t(86),m=e([r,d]);[r,d]=m.then?(await m)():m;let g=(0,o.l)(d,"default"),h=(0,o.l)(d,"getStaticProps"),u=(0,o.l)(d,"getStaticPaths"),x=(0,o.l)(d,"getServerSideProps"),f=(0,o.l)(d,"config"),j=(0,o.l)(d,"reportWebVitals"),p=(0,o.l)(d,"unstable_getStaticProps"),b=(0,o.l)(d,"unstable_getStaticPaths"),k=(0,o.l)(d,"unstable_getStaticParams"),v=(0,o.l)(d,"unstable_getServerProps"),N=(0,o.l)(d,"unstable_getServerSideProps"),S=new n.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/admin/settings",pathname:"/admin/settings",bundlePath:"",filename:""},components:{App:r.default,Document:l()},userland:d});s()}catch(e){s(e)}})},86:(e,i,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(i),t.d(i,{default:()=>h});var n=t(997),a=t(6689),o=t(968),c=t.n(o),l=t(8568),r=t(4845),d=t(4237),m=t.n(d),g=e([r]);function h(){let{user:e,loading:i}=(0,l.a)(),[t,s]=(0,a.useState)(!0),[o,d]=(0,a.useState)("general"),[g,h]=(0,a.useState)({general:{businessName:"Ocean Soul Sparkles",businessEmail:"<EMAIL>",businessPhone:"+61 XXX XXX XXX",businessAddress:"Australia",timezone:"Australia/Sydney",currency:"AUD"},booking:{defaultBookingDuration:60,advanceBookingDays:30,cancellationHours:24,autoConfirmBookings:!0,requireDeposit:!1,depositPercentage:20},payment:{squareEnabled:!0,squareEnvironment:"sandbox",cashEnabled:!0,cardEnabled:!0,allowPartialPayments:!0,autoProcessRefunds:!1},notifications:{emailNotifications:!0,smsNotifications:!1,pushNotifications:!1,emailBookingConfirmation:!0,emailBookingReminder:!0,emailBookingCancellation:!0,emailPaymentReceipt:!0,emailStaffNotification:!0,emailLowInventoryAlert:!0,emailPromotional:!0,smsBookingConfirmation:!1,smsBookingReminder:!1,smsBookingCancellation:!1,smsPaymentReceipt:!1,smsStaffNotification:!1,smsPromotional:!1,pushBookingConfirmation:!1,pushBookingReminder:!1,pushBookingCancellation:!1,pushStaffNotification:!1,bookingReminders:!0,reminderHours:24,adminNotifications:!0,customerNotifications:!0,emailFallbackWhenSMSFails:!0,smsFallbackWhenEmailFails:!1},security:{sessionTimeout:1800,adminSessionTimeout:1800,maxLoginAttempts:5,lockoutDuration:900,requireMFA:!1,ipRestrictions:!1}}),[u,x]=(0,a.useState)(!1),[f,j]=(0,a.useState)(!1),p=async()=>{try{s(!0);let e=await fetch("/api/admin/settings",{headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}});if(e.ok){let i=await e.json();h(i.settings||g)}else console.log("Using default settings - API not available")}catch(e){console.error("Error loading settings:",e)}finally{s(!1)}},b=(e,i,t)=>{h(s=>({...s,[e]:{...s[e],[i]:t}})),x(!0)},k=async()=>{try{j(!0),(await fetch("/api/admin/settings",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin-token")}`},body:JSON.stringify({settings:g})})).ok?(x(!1),alert("Settings saved successfully!")):alert("Failed to save settings. Please try again.")}catch(e){console.error("Error saving settings:",e),alert("Error saving settings. Please try again.")}finally{j(!1)}};return i||t?n.jsx(r.Z,{children:(0,n.jsxs)("div",{className:m().loadingContainer,children:[n.jsx("div",{className:m().loadingSpinner}),n.jsx("p",{children:"Loading settings..."})]})}):e?["DEV","Admin"].includes(e.role)?(0,n.jsxs)(r.Z,{children:[(0,n.jsxs)(c(),{children:[n.jsx("title",{children:"Settings | Ocean Soul Sparkles Admin"}),n.jsx("meta",{name:"description",content:"Manage system settings and configuration"})]}),(0,n.jsxs)("div",{className:m().settingsContainer,children:[(0,n.jsxs)("header",{className:m().header,children:[n.jsx("h1",{className:m().title,children:"Settings"}),n.jsx("div",{className:m().headerActions,children:u&&(0,n.jsxs)(n.Fragment,{children:[n.jsx("button",{onClick:()=>{confirm("Are you sure you want to reset all settings to defaults?")&&(p(),x(!1))},className:m().resetBtn,disabled:f,children:"Reset"}),n.jsx("button",{onClick:k,className:m().saveBtn,disabled:f,children:f?"Saving...":"Save Changes"})]})})]}),(0,n.jsxs)("div",{className:m().settingsContent,children:[(0,n.jsxs)("nav",{className:m().tabNavigation,children:[n.jsx("button",{className:`${m().tabButton} ${"general"===o?m().active:""}`,onClick:()=>d("general"),children:"General"}),n.jsx("button",{className:`${m().tabButton} ${"booking"===o?m().active:""}`,onClick:()=>d("booking"),children:"Booking"}),n.jsx("button",{className:`${m().tabButton} ${"payment"===o?m().active:""}`,onClick:()=>d("payment"),children:"Payment"}),n.jsx("button",{className:`${m().tabButton} ${"notifications"===o?m().active:""}`,onClick:()=>d("notifications"),children:"Notifications"}),n.jsx("button",{className:`${m().tabButton} ${"security"===o?m().active:""}`,onClick:()=>d("security"),children:"Security"})]}),(0,n.jsxs)("div",{className:m().tabContent,children:["general"===o&&(0,n.jsxs)("div",{className:m().settingsSection,children:[n.jsx("h2",{children:"General Settings"}),(0,n.jsxs)("div",{className:m().settingsGrid,children:[(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Business Name"}),n.jsx("input",{type:"text",value:g.general.businessName,onChange:e=>b("general","businessName",e.target.value)})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Business Email"}),n.jsx("input",{type:"email",value:g.general.businessEmail,onChange:e=>b("general","businessEmail",e.target.value)})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Business Phone"}),n.jsx("input",{type:"tel",value:g.general.businessPhone,onChange:e=>b("general","businessPhone",e.target.value)})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Business Address"}),n.jsx("textarea",{value:g.general.businessAddress,onChange:e=>b("general","businessAddress",e.target.value),rows:3})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Timezone"}),(0,n.jsxs)("select",{value:g.general.timezone,onChange:e=>b("general","timezone",e.target.value),children:[n.jsx("option",{value:"Australia/Sydney",children:"Australia/Sydney"}),n.jsx("option",{value:"Australia/Melbourne",children:"Australia/Melbourne"}),n.jsx("option",{value:"Australia/Brisbane",children:"Australia/Brisbane"}),n.jsx("option",{value:"Australia/Perth",children:"Australia/Perth"})]})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Currency"}),(0,n.jsxs)("select",{value:g.general.currency,onChange:e=>b("general","currency",e.target.value),children:[n.jsx("option",{value:"AUD",children:"AUD - Australian Dollar"}),n.jsx("option",{value:"USD",children:"USD - US Dollar"}),n.jsx("option",{value:"EUR",children:"EUR - Euro"})]})]})]})]}),"booking"===o&&(0,n.jsxs)("div",{className:m().settingsSection,children:[n.jsx("h2",{children:"Booking Settings"}),(0,n.jsxs)("div",{className:m().settingsGrid,children:[(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Default Booking Duration (minutes)"}),n.jsx("input",{type:"number",value:g.booking.defaultBookingDuration,onChange:e=>b("booking","defaultBookingDuration",parseInt(e.target.value)),min:"15",max:"480"})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Advance Booking Days"}),n.jsx("input",{type:"number",value:g.booking.advanceBookingDays,onChange:e=>b("booking","advanceBookingDays",parseInt(e.target.value)),min:"1",max:"365"})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Cancellation Notice (hours)"}),n.jsx("input",{type:"number",value:g.booking.cancellationHours,onChange:e=>b("booking","cancellationHours",parseInt(e.target.value)),min:"1",max:"168"})]}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.booking.autoConfirmBookings,onChange:e=>b("booking","autoConfirmBookings",e.target.checked)}),"Auto-confirm bookings"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.booking.requireDeposit,onChange:e=>b("booking","requireDeposit",e.target.checked)}),"Require deposit"]})}),g.booking.requireDeposit&&(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Deposit Percentage"}),n.jsx("input",{type:"number",value:g.booking.depositPercentage,onChange:e=>b("booking","depositPercentage",parseInt(e.target.value)),min:"5",max:"100"})]})]})]}),"payment"===o&&(0,n.jsxs)("div",{className:m().settingsSection,children:[n.jsx("h2",{children:"Payment Settings"}),n.jsx("p",{className:m().comingSoon,children:"Payment settings configuration coming soon..."})]}),"notifications"===o&&(0,n.jsxs)("div",{className:m().settingsSection,children:[n.jsx("h2",{children:"Notification Settings"}),(0,n.jsxs)("div",{className:m().settingsGroup,children:[n.jsx("h3",{children:"Communication Channels"}),(0,n.jsxs)("div",{className:m().settingsGrid,children:[(0,n.jsxs)("div",{className:m().settingItem,children:[(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.emailNotifications,onChange:e=>b("notifications","emailNotifications",e.target.checked)}),"Enable Email Notifications"]}),n.jsx("small",{children:"Master toggle for all email communications"})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.smsNotifications,onChange:e=>b("notifications","smsNotifications",e.target.checked)}),"Enable SMS Notifications"]}),n.jsx("small",{children:"Master toggle for all SMS communications"})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.pushNotifications,onChange:e=>b("notifications","pushNotifications",e.target.checked)}),"Enable Push Notifications"]}),n.jsx("small",{children:"Master toggle for push notifications (future feature)"})]})]})]}),(0,n.jsxs)("div",{className:m().settingsGroup,children:[n.jsx("h3",{children:"Email Notifications"}),(0,n.jsxs)("div",{className:m().settingsGrid,children:[n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.emailBookingConfirmation,onChange:e=>b("notifications","emailBookingConfirmation",e.target.checked),disabled:!g.notifications.emailNotifications}),"Booking Confirmations"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.emailBookingReminder,onChange:e=>b("notifications","emailBookingReminder",e.target.checked),disabled:!g.notifications.emailNotifications}),"Booking Reminders"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.emailBookingCancellation,onChange:e=>b("notifications","emailBookingCancellation",e.target.checked),disabled:!g.notifications.emailNotifications}),"Booking Cancellations"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.emailPaymentReceipt,onChange:e=>b("notifications","emailPaymentReceipt",e.target.checked),disabled:!g.notifications.emailNotifications}),"Payment Receipts"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.emailStaffNotification,onChange:e=>b("notifications","emailStaffNotification",e.target.checked),disabled:!g.notifications.emailNotifications}),"Staff Notifications"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.emailPromotional,onChange:e=>b("notifications","emailPromotional",e.target.checked),disabled:!g.notifications.emailNotifications}),"Promotional Emails"]})})]})]}),(0,n.jsxs)("div",{className:m().settingsGroup,children:[n.jsx("h3",{children:"SMS Notifications"}),(0,n.jsxs)("div",{className:m().settingsGrid,children:[n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.smsBookingConfirmation,onChange:e=>b("notifications","smsBookingConfirmation",e.target.checked),disabled:!g.notifications.smsNotifications}),"Booking Confirmations"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.smsBookingReminder,onChange:e=>b("notifications","smsBookingReminder",e.target.checked),disabled:!g.notifications.smsNotifications}),"Booking Reminders"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.smsBookingCancellation,onChange:e=>b("notifications","smsBookingCancellation",e.target.checked),disabled:!g.notifications.smsNotifications}),"Booking Cancellations"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.smsPaymentReceipt,onChange:e=>b("notifications","smsPaymentReceipt",e.target.checked),disabled:!g.notifications.smsNotifications}),"Payment Receipts"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.smsStaffNotification,onChange:e=>b("notifications","smsStaffNotification",e.target.checked),disabled:!g.notifications.smsNotifications}),"Staff Notifications"]})}),n.jsx("div",{className:m().settingItem,children:(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.smsPromotional,onChange:e=>b("notifications","smsPromotional",e.target.checked),disabled:!g.notifications.smsNotifications}),"Promotional SMS"]})})]})]}),(0,n.jsxs)("div",{className:m().settingsGroup,children:[n.jsx("h3",{children:"General Settings"}),(0,n.jsxs)("div",{className:m().settingsGrid,children:[(0,n.jsxs)("div",{className:m().settingItem,children:[n.jsx("label",{children:"Reminder Hours Before Appointment"}),n.jsx("input",{type:"number",value:g.notifications.reminderHours,onChange:e=>b("notifications","reminderHours",parseInt(e.target.value)),min:"1",max:"168"}),n.jsx("small",{children:"How many hours before appointment to send reminders"})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.adminNotifications,onChange:e=>b("notifications","adminNotifications",e.target.checked)}),"Admin Notifications"]}),n.jsx("small",{children:"Receive notifications for admin events"})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.customerNotifications,onChange:e=>b("notifications","customerNotifications",e.target.checked)}),"Customer Notifications"]}),n.jsx("small",{children:"Send notifications to customers"})]})]})]}),(0,n.jsxs)("div",{className:m().settingsGroup,children:[n.jsx("h3",{children:"Fallback Behavior"}),(0,n.jsxs)("div",{className:m().settingsGrid,children:[(0,n.jsxs)("div",{className:m().settingItem,children:[(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.emailFallbackWhenSMSFails,onChange:e=>b("notifications","emailFallbackWhenSMSFails",e.target.checked)}),"Send Email When SMS Fails"]}),n.jsx("small",{children:"Automatically send email if SMS delivery fails"})]}),(0,n.jsxs)("div",{className:m().settingItem,children:[(0,n.jsxs)("label",{children:[n.jsx("input",{type:"checkbox",checked:g.notifications.smsFallbackWhenEmailFails,onChange:e=>b("notifications","smsFallbackWhenEmailFails",e.target.checked)}),"Send SMS When Email Fails"]}),n.jsx("small",{children:"Automatically send SMS if email delivery fails"})]})]})]})]}),"security"===o&&(0,n.jsxs)("div",{className:m().settingsSection,children:[n.jsx("h2",{children:"Security Settings"}),n.jsx("p",{className:m().comingSoon,children:"Security settings configuration coming soon..."})]})]})]})]})]}):n.jsx(r.Z,{children:(0,n.jsxs)("div",{className:m().accessDenied,children:[n.jsx("h2",{children:"Access Denied"}),n.jsx("p",{children:"You don't have permission to access system settings."})]})}):null}r=(g.then?(await g)():g)[0],s()}catch(e){s(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var i=require("../../webpack-runtime.js");i.C(e);var t=e=>i(i.s=e),s=i.X(0,[2899,6212,1664,7441],()=>t(6569));module.exports=s})();