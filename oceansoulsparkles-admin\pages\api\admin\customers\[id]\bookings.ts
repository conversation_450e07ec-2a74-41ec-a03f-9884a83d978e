import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Customer ID is required' });
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    // Get customer bookings with service and artist details
    const { data: bookings, error } = await supabase
      .from('bookings')
      .select(`
        id,
        booking_date,
        start_time,
        end_time,
        status,
        total_amount,
        notes,
        created_at,
        services (
          id,
          name,
          duration,
          price
        ),
        artist_profiles!assigned_artist_id (
          id,
          artist_name,
          display_name
        )
      `)
      .eq('customer_id', id)
      .order('booking_date', { ascending: false })
      .order('start_time', { ascending: false });

    if (error) {
      console.error('Customer bookings query error:', error);
      return res.status(500).json({ error: 'Failed to fetch customer bookings' });
    }

    // Transform data to match expected format
    const transformedBookings = (bookings || []).map(booking => {
      const service = Array.isArray(booking.services) ? booking.services[0] : booking.services;
      const artist = Array.isArray(booking.artist_profiles) ? booking.artist_profiles[0] : booking.artist_profiles;
      
      // Extract time from start_time timestamp
      const startTime = new Date(booking.start_time);
      const bookingTime = startTime.toTimeString().slice(0, 5); // HH:MM format
      
      return {
        id: booking.id,
        service_name: service?.name || 'Unknown Service',
        service_duration: service?.duration || 0,
        service_price: service?.price || 0,
        artist_name: artist?.artist_name || artist?.display_name || 'Unassigned',
        booking_date: booking.booking_date,
        booking_time: bookingTime,
        start_time: booking.start_time,
        end_time: booking.end_time,
        status: booking.status,
        total_amount: booking.total_amount,
        notes: booking.notes,
        created_at: booking.created_at
      };
    });

    return res.status(200).json({
      bookings: transformedBookings,
      total: transformedBookings.length
    });

  } catch (error) {
    console.error('Customer bookings API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
