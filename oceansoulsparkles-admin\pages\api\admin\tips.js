import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth/admin-auth'

/**
 * API endpoint for tip management
 * Handles tip distribution, reporting, and management
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Tips API called - ${req.method}`)

  try {
    // Authenticate admin request
    const { user, error: authError } = await authenticateAdminRequest(req)
    if (authError || !user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: authError?.message || 'Authentication failed',
        requestId
      })
    }

    if (req.method === 'GET') {
      const { 
        status = 'all', 
        artist_id, 
        date_from, 
        date_to, 
        limit = 50, 
        offset = 0 
      } = req.query

      try {
        let query = supabaseAdmin
          .from('tips')
          .select(`
            *,
            artist_profiles!tips_artist_id_fkey (
              id,
              name,
              email
            ),
            bookings!tips_booking_id_fkey (
              id,
              service_name,
              start_time
            ),
            payments!tips_payment_id_fkey (
              id,
              method,
              payment_time
            )
          `)
          .order('created_at', { ascending: false })

        // Apply filters
        if (status !== 'all') {
          query = query.eq('distribution_status', status)
        }

        if (artist_id) {
          query = query.eq('artist_id', artist_id)
        }

        if (date_from) {
          query = query.gte('created_at', date_from)
        }

        if (date_to) {
          query = query.lte('created_at', date_to)
        }

        // Apply pagination
        query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)

        const { data: tips, error: tipsError } = await query

        if (tipsError) {
          throw new Error(`Failed to fetch tips: ${tipsError.message}`)
        }

        // Get summary statistics
        const { data: summaryData, error: summaryError } = await supabaseAdmin
          .from('tips')
          .select('distribution_status, amount.sum(), artist_id')
          .eq('distribution_status', status !== 'all' ? status : undefined)

        const summary = {
          total_tips: tips.length,
          total_amount: tips.reduce((sum, tip) => sum + parseFloat(tip.amount), 0),
          pending_amount: tips.filter(t => t.distribution_status === 'pending')
            .reduce((sum, tip) => sum + parseFloat(tip.amount), 0),
          distributed_amount: tips.filter(t => t.distribution_status === 'distributed')
            .reduce((sum, tip) => sum + parseFloat(tip.amount), 0)
        }

        return res.status(200).json({
          tips,
          summary,
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset),
            total: tips.length
          },
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error fetching tips:`, error)
        return res.status(500).json({
          error: 'Failed to fetch tips',
          message: error.message,
          requestId
        })
      }
    }

    if (req.method === 'PATCH') {
      const { tip_id, action, distribution_method, notes } = req.body

      if (!tip_id || !action) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'tip_id and action are required',
          requestId
        })
      }

      try {
        let updateData = {
          updated_at: new Date().toISOString()
        }

        if (action === 'distribute') {
          updateData.distribution_status = 'distributed'
          updateData.distribution_date = new Date().toISOString()
          updateData.distribution_method = distribution_method || 'manual'
          if (notes) updateData.notes = notes
        } else if (action === 'hold') {
          updateData.distribution_status = 'held'
          if (notes) updateData.notes = notes
        } else if (action === 'release') {
          updateData.distribution_status = 'pending'
          updateData.distribution_date = null
          updateData.distribution_method = null
        }

        const { data: updatedTip, error: updateError } = await supabaseAdmin
          .from('tips')
          .update(updateData)
          .eq('id', tip_id)
          .select(`
            *,
            artist_profiles!tips_artist_id_fkey (
              id,
              name,
              email
            ),
            bookings!tips_booking_id_fkey (
              id,
              service_name,
              start_time
            )
          `)
          .single()

        if (updateError) {
          throw new Error(`Failed to update tip: ${updateError.message}`)
        }

        console.log(`[${requestId}] Tip ${action}d successfully:`, updatedTip.id)

        return res.status(200).json({
          success: true,
          tip: updatedTip,
          message: `Tip ${action}d successfully`,
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error updating tip:`, error)
        return res.status(500).json({
          error: 'Failed to update tip',
          message: error.message,
          requestId
        })
      }
    }

    if (req.method === 'POST') {
      const { action, tip_ids, distribution_method, notes } = req.body

      if (!action || !tip_ids || !Array.isArray(tip_ids)) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'action and tip_ids array are required',
          requestId
        })
      }

      try {
        let updateData = {
          updated_at: new Date().toISOString()
        }

        if (action === 'bulk_distribute') {
          updateData.distribution_status = 'distributed'
          updateData.distribution_date = new Date().toISOString()
          updateData.distribution_method = distribution_method || 'bulk'
          if (notes) updateData.notes = notes
        } else if (action === 'bulk_hold') {
          updateData.distribution_status = 'held'
          if (notes) updateData.notes = notes
        }

        const { data: updatedTips, error: updateError } = await supabaseAdmin
          .from('tips')
          .update(updateData)
          .in('id', tip_ids)
          .select()

        if (updateError) {
          throw new Error(`Failed to bulk update tips: ${updateError.message}`)
        }

        console.log(`[${requestId}] Bulk ${action} completed for ${updatedTips.length} tips`)

        return res.status(200).json({
          success: true,
          updated_count: updatedTips.length,
          tips: updatedTips,
          message: `${updatedTips.length} tips ${action}d successfully`,
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error bulk updating tips:`, error)
        return res.status(500).json({
          error: 'Failed to bulk update tips',
          message: error.message,
          requestId
        })
      }
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    })

  } catch (error) {
    console.error(`[${requestId}] Tips API error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      requestId
    })
  }
}
