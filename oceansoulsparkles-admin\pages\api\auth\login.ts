import type { NextApiRequest, NextApiResponse } from 'next';
import { adminLogin } from '../../../lib/auth/admin-auth';
import { rateLimitCheck } from '../../../lib/security/rate-limiting';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Rate limiting check
    const rateLimitResult = await rateLimitCheck(req as any);
    if (!rateLimitResult.allowed) {
      return res.status(429).json({ 
        error: 'Too many login attempts. Please try again later.',
        resetTime: rateLimitResult.resetTime
      });
    }

    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    // Get client IP for audit logging
    const clientIP = req.headers['x-forwarded-for'] as string || 
                    req.headers['x-real-ip'] as string || 
                    req.connection.remoteAddress || 
                    'unknown';

    // Attempt login
    const loginResult = await adminLogin(email, password, clientIP);

    if (!loginResult.success) {
      return res.status(401).json({ error: loginResult.error });
    }

    // Set secure cookie
    if (loginResult.token) {
      res.setHeader('Set-Cookie', [
        `admin-token=${loginResult.token}; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=28800` // 8 hours
      ]);
    }

    // Return response based on whether MFA is required
    if (loginResult.requiresMFA) {
      return res.status(200).json({
        success: true,
        requiresMFA: true,
        user: loginResult.user
      });
    }

    return res.status(200).json({
      success: true,
      token: loginResult.token,
      user: loginResult.user
    });

  } catch (error) {
    console.error('Login API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
