/**
 * SMS Templates API Endpoint
 * 
 * Manages SMS templates with CRUD operations and template variables.
 */

import { createClient } from '@supabase/supabase-js'
import { verifyAdminAuth } from '../../../lib/auth/admin-auth'
import { getAllTemplates } from '../../../lib/sms/sms-templates'

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export default async function handler(req, res) {
  const requestId = `sms-templates-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: authResult.message,
        requestId
      })
    }

    const { user } = authResult

    if (req.method === 'GET') {
      try {
        // Get SMS templates from database
        const { data: dbTemplates, error } = await supabaseAdmin
          .from('sms_templates')
          .select('*')
          .order('category', { ascending: true })
          .order('name', { ascending: true })

        if (error && error.code !== 'PGRST116') { // PGRST116 = table doesn't exist
          console.error(`[${requestId}] Database error:`, error)
          return res.status(500).json({
            error: 'Failed to fetch SMS templates',
            message: error.message,
            requestId
          })
        }

        // Get default templates
        const defaultTemplates = getAllTemplates()
        
        // If no database templates or table doesn't exist, return defaults
        if (!dbTemplates || dbTemplates.length === 0) {
          const templatesArray = Object.keys(defaultTemplates).map(key => ({
            id: key,
            type: key,
            name: defaultTemplates[key].name,
            description: defaultTemplates[key].description,
            template: defaultTemplates[key].template,
            variables: defaultTemplates[key].variables,
            category: defaultTemplates[key].category,
            is_active: true,
            is_default: true
          }))

          return res.status(200).json({
            templates: templatesArray,
            source: 'default',
            message: 'Using default SMS templates',
            requestId
          })
        }

        // Merge database templates with defaults
        const mergedTemplates = dbTemplates.map(template => ({
          ...template,
          variables: typeof template.variables === 'string' 
            ? JSON.parse(template.variables) 
            : template.variables
        }))

        return res.status(200).json({
          templates: mergedTemplates,
          source: 'database',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error fetching SMS templates:`, error)
        return res.status(500).json({
          error: 'Failed to fetch SMS templates',
          message: error.message,
          requestId
        })
      }
    }

    if (req.method === 'POST') {
      const { name, description, template, variables, category, type, is_active = true } = req.body

      if (!name || !template || !type) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Name, template, and type are required',
          requestId
        })
      }

      try {
        const { data: newTemplate, error } = await supabaseAdmin
          .from('sms_templates')
          .insert([
            {
              type,
              name,
              description: description || null,
              template,
              variables: JSON.stringify(variables || []),
              category: category || 'custom',
              is_active,
              created_by: user.id,
              updated_by: user.id,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ])
          .select()
          .single()

        if (error) {
          console.error(`[${requestId}] Error creating SMS template:`, error)
          return res.status(500).json({
            error: 'Failed to create SMS template',
            message: error.message,
            requestId
          })
        }

        return res.status(201).json({
          template: {
            ...newTemplate,
            variables: JSON.parse(newTemplate.variables)
          },
          message: 'SMS template created successfully',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error creating SMS template:`, error)
        return res.status(500).json({
          error: 'Failed to create SMS template',
          message: error.message,
          requestId
        })
      }
    }

    if (req.method === 'PUT') {
      const { id, name, description, template, variables, category, is_active } = req.body

      if (!id) {
        return res.status(400).json({
          error: 'Template ID is required',
          requestId
        })
      }

      try {
        const updateData = {
          updated_by: user.id,
          updated_at: new Date().toISOString()
        }

        if (name !== undefined) updateData.name = name
        if (description !== undefined) updateData.description = description
        if (template !== undefined) updateData.template = template
        if (variables !== undefined) updateData.variables = JSON.stringify(variables)
        if (category !== undefined) updateData.category = category
        if (is_active !== undefined) updateData.is_active = is_active

        const { data: updatedTemplate, error } = await supabaseAdmin
          .from('sms_templates')
          .update(updateData)
          .eq('id', id)
          .select()
          .single()

        if (error) {
          console.error(`[${requestId}] Error updating SMS template:`, error)
          return res.status(500).json({
            error: 'Failed to update SMS template',
            message: error.message,
            requestId
          })
        }

        return res.status(200).json({
          template: {
            ...updatedTemplate,
            variables: JSON.parse(updatedTemplate.variables)
          },
          message: 'SMS template updated successfully',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error updating SMS template:`, error)
        return res.status(500).json({
          error: 'Failed to update SMS template',
          message: error.message,
          requestId
        })
      }
    }

    if (req.method === 'DELETE') {
      const { id } = req.query

      if (!id) {
        return res.status(400).json({
          error: 'Template ID is required',
          requestId
        })
      }

      try {
        const { error } = await supabaseAdmin
          .from('sms_templates')
          .delete()
          .eq('id', id)

        if (error) {
          console.error(`[${requestId}] Error deleting SMS template:`, error)
          return res.status(500).json({
            error: 'Failed to delete SMS template',
            message: error.message,
            requestId
          })
        }

        return res.status(200).json({
          message: 'SMS template deleted successfully',
          requestId
        })

      } catch (error) {
        console.error(`[${requestId}] Error deleting SMS template:`, error)
        return res.status(500).json({
          error: 'Failed to delete SMS template',
          message: error.message,
          requestId
        })
      }
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    })

  } catch (error) {
    console.error(`[${requestId}] SMS templates API error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      requestId
    })
  }
}
