/**
 * SMS Notifications API Endpoint
 * 
 * Handles SMS sending with feature flag checking and communication logging.
 */

import { createClient } from '@supabase/supabase-js'
import { verifyAdminAuth } from '../../../../lib/auth/admin-auth'
import smsService from '../../../../lib/sms/sms-service'

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export default async function handler(req, res) {
  const requestId = `sms-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: authResult.message,
        requestId
      })
    }

    const { user } = authResult

    if (req.method === 'POST') {
      const { type, data } = req.body

      if (!type || !data) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Type and data are required',
          requestId
        })
      }

      console.log(`[${requestId}] Processing SMS request:`, { type, recipient: data.to || data.customerPhone || data.staffPhone })

      let result
      
      switch (type) {
        case 'booking_confirmation':
          result = await smsService.sendBookingConfirmation(data)
          break
          
        case 'booking_reminder':
          result = await smsService.sendBookingReminder(data)
          break
          
        case 'booking_cancellation':
          result = await smsService.sendBookingCancellation(data)
          break
          
        case 'staff_notification':
          result = await smsService.sendStaffNotification(data)
          break
          
        case 'promotional':
          result = await smsService.sendPromotionalSMS(data.customer, data.message)
          break
          
        case 'test_sms':
          if (!data.to || !data.message) {
            return res.status(400).json({
              error: 'Recipient phone and message are required for test SMS',
              requestId
            })
          }
          result = await smsService.sendSMS({
            to: data.to,
            message: data.message,
            type: 'test'
          })
          break
          
        case 'bulk_sms':
          if (!data.recipients || !data.message) {
            return res.status(400).json({
              error: 'Recipients and message are required for bulk SMS',
              requestId
            })
          }
          result = await smsService.sendBulkSMS(data.recipients, data.message, data.type || 'bulk')
          break
          
        default:
          return res.status(400).json({
            error: `Unknown SMS type: ${type}`,
            requestId
          })
      }

      // Log communication if SMS was sent successfully
      if (result.success && !result.skipped) {
        try {
          await logCommunication({
            type: 'sms',
            recipient: data.to || data.customerPhone || data.staffPhone,
            messageId: result.messageId,
            status: 'sent',
            content: data.message || `${type} SMS`,
            customerId: data.customerId,
            bookingId: data.bookingId || data.id,
            staffId: data.staffId,
            sentBy: user.id
          })
        } catch (logError) {
          console.error(`[${requestId}] Failed to log communication:`, logError)
          // Don't fail the request if logging fails
        }
      }

      if (result.success) {
        console.log(`[${requestId}] SMS sent successfully:`, result.messageId)
        return res.status(200).json({
          success: true,
          messageId: result.messageId,
          message: result.skipped ? `SMS skipped: ${result.reason}` : 'SMS sent successfully',
          skipped: result.skipped || false,
          fallback: result.fallback || false,
          requestId
        })
      } else {
        console.error(`[${requestId}] SMS sending failed:`, result.error)
        return res.status(500).json({
          error: 'Failed to send SMS',
          message: result.error,
          skipped: result.skipped || false,
          reason: result.reason,
          requestId
        })
      }
    }

    if (req.method === 'GET') {
      // Get SMS service status and settings
      const status = smsService.getStatus()
      const verification = await smsService.verifyConfiguration()
      
      // Get current SMS settings
      const { data: settingsData } = await supabaseAdmin
        .from('system_settings')
        .select('*')
        .eq('category', 'notifications')
      
      const smsSettings = {}
      settingsData?.forEach(setting => {
        if (setting.key.toLowerCase().includes('sms')) {
          try {
            smsSettings[setting.key] = JSON.parse(setting.value)
          } catch (e) {
            smsSettings[setting.key] = setting.value
          }
        }
      })
      
      return res.status(200).json({
        status,
        verification,
        settings: smsSettings,
        requestId
      })
    }

    return res.status(405).json({
      error: 'Method not allowed',
      requestId
    })

  } catch (error) {
    console.error(`[${requestId}] SMS API error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      requestId
    })
  }
}

/**
 * Log SMS communication to database
 */
async function logCommunication({
  type,
  recipient,
  messageId,
  status,
  content,
  customerId,
  bookingId,
  staffId,
  sentBy
}) {
  try {
    const { error } = await supabaseAdmin
      .from('customer_communications')
      .insert([
        {
          customer_id: customerId || null,
          booking_id: bookingId || null,
          communication_type: type,
          recipient: recipient,
          subject: null, // SMS doesn't have subjects
          content: content,
          status: status,
          sent_at: new Date().toISOString(),
          external_id: messageId,
          metadata: {
            staff_id: staffId,
            sent_by: sentBy,
            provider: 'twilio'
          }
        }
      ])

    if (error) {
      console.error('Error logging SMS communication:', error)
    }
  } catch (error) {
    console.error('Error logging SMS communication:', error)
  }
}
