(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[92],{8940:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/bookings",function(){return n(9435)}])},9435:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return v}});var o=n(9008),r=n.n(o),a=n(1163),i=n(1664),c=n.n(i),d=n(99),s=n(8052),l=n.n(s);function u(e){let{bookings:t,onBookingClick:n,onDateClick:o}=e,[r,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(new Date),[i,c]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[d,s]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({});Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{u(),O()},[r,t]);let u=()=>{let e=r.getFullYear(),t=r.getMonth(),n=new Date(e,t,1),o=new Date(e,t+1,0),a=n.getDay(),i=[];for(let n=a-1;n>=0;n--){let o=new Date(e,t,-n);i.push(o)}for(let n=1;n<=o.getDate();n++){let o=new Date(e,t,n);i.push(o)}let d=42-i.length;for(let n=1;n<=d;n++){let o=new Date(e,t+1,n);i.push(o)}c(i)},O=()=>{let e={};t.forEach(t=>{let n=new Date(t.start_time).toISOString().split("T")[0];e[n]||(e[n]=[]),e[n].push(t)}),s(e)},_=e=>{let t=new Date(r);"prev"===e?t.setMonth(t.getMonth()-1):t.setMonth(t.getMonth()+1),a(t)},m=e=>{let t=new Date;return e.toDateString()===t.toDateString()},N=e=>e.getMonth()===r.getMonth(),h=e=>new Date(e).toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit",hour12:!1}),f=e=>{switch(null==e?void 0:e.toLowerCase()){case"confirmed":return"#28a745";case"pending":return"#ffc107";case"cancelled":return"#dc3545";case"completed":return"#17a2b8";default:return"#6c757d"}};return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().calendarContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().calendarHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().monthNavigation,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>_("prev"),className:l().navButton,children:"←"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{className:l().monthTitle,children:[["January","February","March","April","May","June","July","August","September","October","November","December"][r.getMonth()]," ",r.getFullYear()]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>_("next"),className:l().navButton,children:"→"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>{a(new Date)},className:l().todayButton,children:"Today"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().calendar,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().dayHeaders,children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().dayHeader,children:e},e))}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().calendarGrid,children:i.map((e,t)=>{let r=d[e.toISOString().split("T")[0]]||[];return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"".concat(l().calendarDay," ").concat(m(e)?l().today:""," ").concat(N(e)?"":l().otherMonth),onClick:()=>null==o?void 0:o(e),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().dayNumber,children:e.getDate()}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().bookingsContainer,children:[r.slice(0,3).map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().bookingItem,style:{borderLeftColor:f(e.status)},onClick:t=>{t.stopPropagation(),null==n||n(e)},title:"".concat(e.customer_name," - ").concat(e.service_name," at ").concat(h(e.start_time)),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().bookingTime,children:h(e.start_time)}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().bookingCustomer,children:e.customer_name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().bookingService,children:e.service_name})]},e.id)),r.length>3&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:l().moreBookings,children:["+",r.length-3," more"]})]})]},t)})})]})]})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var O=n(9734),_=n.n(O);function m(e){let{bookings:t}=e,[n,o]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[r,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("30d");Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{i()},[t,r]);let i=()=>{if(!t||0===t.length){o(null);return}let e=new Date,n=new Date;switch(r){case"7d":n.setDate(e.getDate()-7);break;case"30d":n.setDate(e.getDate()-30);break;case"90d":n.setDate(e.getDate()-90);break;case"1y":n.setFullYear(e.getFullYear()-1)}let a=t.filter(e=>new Date(e.start_time||e.created_at)>=n),i=a.length,c=a.reduce((e,t)=>e+(t.total_amount||0),0),d=new Date;d.setDate(1);let s=t.filter(e=>new Date(e.start_time||e.created_at)>=d),l=s.length,u=s.reduce((e,t)=>e+(t.total_amount||0),0),O={};a.forEach(e=>{let t=e.status||"unknown";O[t]=(O[t]||0)+1});let _={};a.forEach(e=>{let t=e.service_name||"Unknown Service";_[t]||(_[t]={count:0,revenue:0}),_[t].count++,_[t].revenue+=e.total_amount||0});let m=Object.entries(_).map(e=>{let[t,n]=e;return{name:t,...n}}).sort((e,t)=>t.revenue-e.revenue).slice(0,5),N={};a.forEach(e=>{let t=e.artist_name||"Unassigned";N[t]||(N[t]={count:0,revenue:0}),N[t].count++,N[t].revenue+=e.total_amount||0});let h=Object.entries(N).map(e=>{let[t,n]=e;return{name:t,...n}}).sort((e,t)=>t.revenue-e.revenue).slice(0,5),f=[];for(let e=5;e>=0;e--){let n=new Date;n.setMonth(n.getMonth()-e);let o=new Date(n.getFullYear(),n.getMonth(),1),r=new Date(n.getFullYear(),n.getMonth()+1,0),a=t.filter(e=>{let t=new Date(e.start_time||e.created_at);return t>=o&&t<=r});f.push({month:n.toLocaleDateString("en-AU",{month:"short",year:"numeric"}),bookings:a.length,revenue:a.reduce((e,t)=>e+(t.total_amount||0),0)})}o({totalBookings:i,totalRevenue:c,averageBookingValue:i>0?c/i:0,bookingsThisMonth:l,revenueThisMonth:u,statusBreakdown:O,topServices:m,topArtists:h,monthlyTrend:f})},c=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),d=e=>{switch(null==e?void 0:e.toLowerCase()){case"confirmed":return"#28a745";case"pending":return"#ffc107";case"cancelled":return"#dc3545";case"completed":return"#17a2b8";default:return"#6c757d"}};return n?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().analyticsContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().analyticsHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Booking Analytics"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().timeRangeSelector,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:"7d"===r?_().active:"",onClick:()=>a("7d"),children:"7 Days"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:"30d"===r?_().active:"",onClick:()=>a("30d"),children:"30 Days"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:"90d"===r?_().active:"",onClick:()=>a("90d"),children:"90 Days"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:"1y"===r?_().active:"",onClick:()=>a("1y"),children:"1 Year"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricsGrid,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricValue,children:n.totalBookings}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricLabel,children:"Total Bookings"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricValue,children:c(n.totalRevenue)}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricLabel,children:"Total Revenue"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricValue,children:c(n.averageBookingValue)}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricLabel,children:"Avg Booking Value"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricValue,children:n.bookingsThisMonth}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().metricLabel,children:"This Month"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().chartsGrid,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().chartCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{children:"Booking Status"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().statusChart,children:Object.entries(n.statusBreakdown).map(e=>{let[t,n]=e;return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().statusItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().statusIndicator,style:{backgroundColor:d(t)}}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:_().statusLabel,children:t}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:_().statusCount,children:n})]},t)})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().chartCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{children:"Top Services"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topList,children:n.topServices.map((e,t)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topRank,children:["#",t+1]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topName,children:e.name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topStats,children:[e.count," bookings • ",c(e.revenue)]})]})]},e.name))})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().chartCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{children:"Top Artists"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topList,children:n.topArtists.map((e,t)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topRank,children:["#",t+1]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topName,children:e.name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().topStats,children:[e.count," bookings • ",c(e.revenue)]})]})]},e.name))})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().chartCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{children:"Monthly Trend"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().trendChart,children:n.monthlyTrend.map((e,t)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().trendItem,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().trendMonth,children:e.month}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().trendBookings,children:[e.bookings," bookings"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().trendRevenue,children:c(e.revenue)})]},t))})]})]})]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().analyticsContainer,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:_().emptyState,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"No booking data available for analysis"})})})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var N=n(8935),h=n(6026),f=n(2920),D=n(8352),E=n.n(D);function v(){let e=(0,a.useRouter)(),{user:t,loading:n}=(0,h.a)(),[o,i]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[s,l]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[O,_]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[D,v]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(new Date().toISOString().split("T")[0]),[U,j]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("all"),[g,C]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[b,w]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("list"),k=async()=>{try{_(!0);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/bookings",{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to load bookings");let n=await t.json();i(n.bookings||[]),l(n.bookings||[])}catch(e){console.error("Error loading bookings:",e),i([]),l([])}finally{_(!1)}};Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{!n&&t&&k()},[t,n]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=o;g&&(e=e.filter(e=>(e.customer_name||"").toLowerCase().includes(g.toLowerCase())||(e.service_name||"").toLowerCase().includes(g.toLowerCase())||(e.customer_email||"").toLowerCase().includes(g.toLowerCase()))),"all"!==U&&(e=e.filter(e=>e.status===U)),"list"===b&&D&&(e=e.filter(e=>new Date(e.start_time||e.booking_date).toISOString().split("T")[0]===D)),l(e)},[o,g,U,D,b]);let L=async(e,t)=>{try{let n=localStorage.getItem("admin-token");if(!(await fetch("/api/admin/bookings/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(n)},body:JSON.stringify({status:t})})).ok)throw Error("Failed to update booking status");i(n=>n.map(n=>n.id===e?{...n,status:t}:n))}catch(e){console.error("Error updating booking status:",e)}},T=e=>{switch(e){case"confirmed":return E().statusConfirmed;case"pending":return E().statusPending;case"completed":return E().statusCompleted;case"cancelled":return E().statusCancelled;default:return E().statusDefault}},M=e=>{let t=new Date(e);return{date:t.toLocaleDateString(),time:t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}};return n||O?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(d.Z,{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().loadingContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().loadingSpinner}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Loading bookings..."})]})}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(d.Z,{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(r(),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("title",{children:"Bookings Management | Ocean Soul Sparkles Admin"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("meta",{name:"description",content:"Manage customer bookings and appointments"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().bookingsContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("header",{className:E().header,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:E().title,children:"Bookings Management"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().headerActions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(N.F,{data:s,type:"bookings",className:E().exportBtn}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/bookings/new",className:E().newBookingBtn,children:"+ New Booking"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:E().backButton,onClick:()=>e.push("/"),children:"← Back to Dashboard"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(m,{bookings:o}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().controlsPanel,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().viewToggle,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:"".concat(E().viewBtn," ").concat("list"===b?E().active:""),onClick:()=>w("list"),children:"List View"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:"".concat(E().viewBtn," ").concat("calendar"===b?E().active:""),onClick:()=>w("calendar"),children:"Calendar View"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().filters,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",placeholder:"Search bookings...",value:g,onChange:e=>C(e.target.value),className:E().searchInput}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:U,onChange:e=>j(e.target.value),className:E().statusFilter,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"all",children:"All Status"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"pending",children:"Pending"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"confirmed",children:"Confirmed"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"completed",children:"Completed"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"cancelled",children:"Cancelled"})]}),"list"===b&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"date",value:D,onChange:e=>v(e.target.value),className:E().dateFilter})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().bookingsContent,children:["list"===b?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().bookingsList,children:0===s.length?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().emptyState,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"No bookings found for the selected criteria."})}):s.map(e=>{let{date:t,time:n}=M(e.start_time),o=M(e.end_time).time;return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().bookingCard,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().bookingHeader,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().customerInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:e.customer_name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:e.customer_email})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"".concat(E().statusBadge," ").concat(T(e.status)),children:e.status})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().bookingDetails,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().serviceInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:e.service_name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:["Artist: ",e.artist]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:["Price: $",e.price]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().timeInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:t})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:[n," - ",o]})]})]}),e.notes&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().bookingNotes,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Notes:"})," ",e.notes]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().bookingActions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/bookings/".concat(e.id),className:E().viewBtn,children:"View Details"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{value:e.status,onChange:t=>L(e.id,t.target.value),className:E().statusSelect,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"pending",children:"Pending"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"confirmed",children:"Confirmed"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"completed",children:"Completed"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"cancelled",children:"Cancelled"})]})]})]},e.id)})}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:E().calendarView,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(u,{bookings:o,onBookingClick:t=>{e.push("/admin/bookings/".concat(t.id))},onDateClick:e=>{v(e.toISOString().split("T")[0]),w("list"),f.Am.info("Switched to list view for ".concat(e.toLocaleDateString()))}})}),"        "]})]})]})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}()},9734:function(e){e.exports={analyticsContainer:"BookingAnalytics_analyticsContainer__9mwvM",analyticsHeader:"BookingAnalytics_analyticsHeader__xLMhj",timeRangeSelector:"BookingAnalytics_timeRangeSelector__PDmBu",active:"BookingAnalytics_active__TxOIw",metricsGrid:"BookingAnalytics_metricsGrid__RhMfg",metricCard:"BookingAnalytics_metricCard__Wulh9",metricValue:"BookingAnalytics_metricValue__y1B6e",metricLabel:"BookingAnalytics_metricLabel__8Zt27",chartsGrid:"BookingAnalytics_chartsGrid__j_qu3",chartCard:"BookingAnalytics_chartCard__MnasY",statusChart:"BookingAnalytics_statusChart__nf1N9",statusItem:"BookingAnalytics_statusItem__7JNm3",statusIndicator:"BookingAnalytics_statusIndicator__CYo8r",statusLabel:"BookingAnalytics_statusLabel__Hd5ye",statusCount:"BookingAnalytics_statusCount__uFsxj",topList:"BookingAnalytics_topList__AZWQO",topItem:"BookingAnalytics_topItem__zZAmz",topRank:"BookingAnalytics_topRank__P6LSm",topInfo:"BookingAnalytics_topInfo__cTaK_",topName:"BookingAnalytics_topName__Rp2A8",topStats:"BookingAnalytics_topStats__0sP2c",trendChart:"BookingAnalytics_trendChart__Y5IeY",trendItem:"BookingAnalytics_trendItem__2BYPl",trendMonth:"BookingAnalytics_trendMonth__yqawq",trendBookings:"BookingAnalytics_trendBookings__jtFHq",trendRevenue:"BookingAnalytics_trendRevenue__Haw3F",emptyState:"BookingAnalytics_emptyState__cVXz4"}},8052:function(e){e.exports={calendarContainer:"BookingCalendar_calendarContainer__gwvLi",calendarHeader:"BookingCalendar_calendarHeader__6KhH7",monthNavigation:"BookingCalendar_monthNavigation___DEjo",navButton:"BookingCalendar_navButton__g2PdZ",monthTitle:"BookingCalendar_monthTitle__DiK7E",todayButton:"BookingCalendar_todayButton__Lht_7",calendar:"BookingCalendar_calendar__9Rf9A",dayHeaders:"BookingCalendar_dayHeaders__gEDwd",dayHeader:"BookingCalendar_dayHeader__zSjjo",calendarGrid:"BookingCalendar_calendarGrid__umy5o",calendarDay:"BookingCalendar_calendarDay__fFkuf",today:"BookingCalendar_today__ItoLp",otherMonth:"BookingCalendar_otherMonth__qwT2U",dayNumber:"BookingCalendar_dayNumber__bJmAK",bookingsContainer:"BookingCalendar_bookingsContainer__c_ltx",bookingItem:"BookingCalendar_bookingItem__Ex83v",bookingTime:"BookingCalendar_bookingTime__XlFnR",bookingCustomer:"BookingCalendar_bookingCustomer__Gbocf",bookingService:"BookingCalendar_bookingService__by0T_",moreBookings:"BookingCalendar_moreBookings__RymFm"}},8352:function(e){e.exports={bookingsContainer:"Bookings_bookingsContainer__LxQns",header:"Bookings_header__LPpER",title:"Bookings_title__kkjQ_",headerActions:"Bookings_headerActions__oOFo9",newBookingBtn:"Bookings_newBookingBtn__DThjN",backButton:"Bookings_backButton__d7v8l",controlsPanel:"Bookings_controlsPanel__OqLxe",viewToggle:"Bookings_viewToggle__qHn5e",viewBtn:"Bookings_viewBtn__halWp",active:"Bookings_active__EPiR9",filters:"Bookings_filters__efSCE",searchInput:"Bookings_searchInput__WU9rK",statusFilter:"Bookings_statusFilter__cEsIO",dateFilter:"Bookings_dateFilter__91_jc",bookingsContent:"Bookings_bookingsContent__UNoyE",bookingsList:"Bookings_bookingsList__1shaa",emptyState:"Bookings_emptyState__vwGqK",bookingCard:"Bookings_bookingCard__rr_2I",bookingHeader:"Bookings_bookingHeader__kN4o8",customerInfo:"Bookings_customerInfo__sr86g",statusBadge:"Bookings_statusBadge__YyBWn",statusPending:"Bookings_statusPending__KxyNp",statusConfirmed:"Bookings_statusConfirmed__9YBLQ",statusCompleted:"Bookings_statusCompleted___ZbBM",statusCancelled:"Bookings_statusCancelled__L3mcN",statusDefault:"Bookings_statusDefault__P9Wkr",bookingDetails:"Bookings_bookingDetails__uGdps",serviceInfo:"Bookings_serviceInfo__aPhqp",timeInfo:"Bookings_timeInfo__fzguw",bookingNotes:"Bookings_bookingNotes__y0sB6",bookingActions:"Bookings_bookingActions__GQrrp",statusSelect:"Bookings_statusSelect__1MW5p",calendarView:"Bookings_calendarView__mzDJ0",comingSoon:"Bookings_comingSoon__Ssaev",loadingContainer:"Bookings_loadingContainer__aLt2y",loadingSpinner:"Bookings_loadingSpinner__V78_d",spin:"Bookings_spin__RJm_5"}}},function(e){e.O(0,[736,592,888,179],function(){return e(e.s=8940)}),_N_E=e.O()}]);