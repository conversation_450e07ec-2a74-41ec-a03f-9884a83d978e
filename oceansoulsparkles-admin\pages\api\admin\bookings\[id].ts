import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Booking ID is required' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;

    if (req.method === 'GET') {
      // Get booking details with customer, service, and artist information
      const { data: booking, error } = await supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          start_time,
          end_time,
          status,
          total_amount,
          notes,
          created_at,
          customer_id,
          service_id,
          assigned_artist_id,
          customers (
            id,
            first_name,
            last_name,
            email,
            phone
          ),
          services (
            id,
            name,
            duration,
            price
          ),
          artist_profiles!assigned_artist_id (
            id,
            artist_name,
            display_name
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Booking query error:', error);
        return res.status(500).json({ error: 'Failed to fetch booking' });
      }

      if (!booking) {
        return res.status(404).json({ error: 'Booking not found' });
      }

      // If user is an artist, only allow access to their own bookings
      if (user.role === 'Artist' || user.role === 'Braider') {
        if (booking.assigned_artist_id !== user.id) {
          return res.status(403).json({ error: 'Access denied to this booking' });
        }
      }

      // Transform data to match expected format
      const customer = Array.isArray(booking.customers) ? booking.customers[0] : booking.customers;
      const service = Array.isArray(booking.services) ? booking.services[0] : booking.services;
      const artist = Array.isArray(booking.artist_profiles) ? booking.artist_profiles[0] : booking.artist_profiles;
      
      // Extract time from start_time timestamp
      const startTime = new Date(booking.start_time);
      const bookingTime = startTime.toTimeString().slice(0, 5); // HH:MM format
      
      const transformedBooking = {
        id: booking.id,
        customer_name: customer ? 
          `${customer.first_name} ${customer.last_name}` : 'Unknown Customer',
        customer_email: customer?.email || '',
        customer_phone: customer?.phone || '',
        service_name: service?.name || 'Unknown Service',
        service_duration: service?.duration || 0,
        service_price: service?.price || 0,
        artist_name: artist?.artist_name || artist?.display_name || 'Unassigned',
        booking_date: booking.booking_date,
        booking_time: bookingTime,
        start_time: booking.start_time,
        end_time: booking.end_time,
        status: booking.status,
        total_amount: booking.total_amount,
        notes: booking.notes,
        created_at: booking.created_at,
        // Include full objects for frontend use
        customers: customer,
        services: service,
        artist_profiles: artist
      };

      return res.status(200).json({
        booking: transformedBooking
      });

    } else if (req.method === 'PUT') {
      // Update booking
      const {
        booking_date,
        start_time,
        end_time,
        status,
        total_amount,
        notes,
        assigned_artist_id
      } = req.body;

      // If user is an artist, only allow status updates to their own bookings
      if (user.role === 'Artist' || user.role === 'Braider') {
        // First check if this is their booking
        const { data: existingBooking } = await supabase
          .from('bookings')
          .select('assigned_artist_id')
          .eq('id', id)
          .single();

        if (!existingBooking || existingBooking.assigned_artist_id !== user.id) {
          return res.status(403).json({ error: 'Access denied to this booking' });
        }

        // Artists can only update status and notes
        const { data: booking, error } = await supabase
          .from('bookings')
          .update({
            status,
            notes,
            updated_at: new Date().toISOString()
          })
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error('Booking update error:', error);
          return res.status(500).json({ error: 'Failed to update booking' });
        }

        return res.status(200).json({ booking });
      }

      // Admin/DEV can update all fields
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (booking_date) updateData.booking_date = booking_date;
      if (start_time) updateData.start_time = start_time;
      if (end_time) updateData.end_time = end_time;
      if (status) updateData.status = status;
      if (total_amount !== undefined) updateData.total_amount = parseFloat(total_amount);
      if (notes !== undefined) updateData.notes = notes;
      if (assigned_artist_id) updateData.assigned_artist_id = assigned_artist_id;

      const { data: booking, error } = await supabase
        .from('bookings')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Booking update error:', error);
        return res.status(500).json({ error: 'Failed to update booking' });
      }

      return res.status(200).json({ booking });

    } else if (req.method === 'DELETE') {
      // Delete booking (only admin can delete)
      if (user.role !== 'Admin' && user.role !== 'DEV') {
        return res.status(403).json({ error: 'Only admins can delete bookings' });
      }

      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Booking delete error:', error);
        return res.status(500).json({ error: 'Failed to delete booking' });
      }

      return res.status(200).json({ message: 'Booking deleted successfully' });

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Booking API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
