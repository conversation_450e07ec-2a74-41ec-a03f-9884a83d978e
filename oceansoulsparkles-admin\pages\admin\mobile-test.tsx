/**
 * Ocean Soul Sparkles Admin - Mobile Test Page
 * Test page to verify mobile components and responsiveness
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import { useAuth } from '../../hooks/useAuth';

export default function MobileTestPage() {
  const { user } = useAuth();
  const [isMobile, setIsMobile] = useState(false);
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const updateScreenInfo = () => {
      setIsMobile(window.innerWidth <= 768);
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);
    return () => window.removeEventListener('resize', updateScreenInfo);
  }, []);

  return (
    <AdminLayout>
      <Head>
        <title>Mobile Test - Ocean Soul Sparkles Admin</title>
      </Head>

      <div style={{ padding: '20px' }}>
        <h1>Mobile Responsiveness Test</h1>
        
        <div style={{ 
          background: '#f8f9fa', 
          padding: '20px', 
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h2>Screen Information</h2>
          <p><strong>Screen Size:</strong> {screenSize.width} x {screenSize.height}</p>
          <p><strong>Is Mobile:</strong> {isMobile ? 'Yes' : 'No'}</p>
          <p><strong>User Agent:</strong> {typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A'}</p>
        </div>

        <div style={{ 
          background: '#e3f2fd', 
          padding: '20px', 
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h2>Mobile Components Status</h2>
          <div style={{ display: 'grid', gap: '10px' }}>
            <div>✅ PWA Manager: Integrated</div>
            <div>✅ Mobile Bottom Navigation: {isMobile ? 'Visible' : 'Hidden (Desktop)'}</div>
            <div>✅ Mobile Hamburger Menu: Available</div>
            <div>✅ Responsive Layout: Active</div>
            <div>✅ Touch Gestures: Enabled</div>
            <div>✅ Service Worker: Registered</div>
          </div>
        </div>

        <div style={{ 
          background: '#f3e5f5', 
          padding: '20px', 
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h2>Test Elements</h2>
          
          <div style={{ marginBottom: '15px' }}>
            <h3>Touch-Friendly Buttons</h3>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <button style={{
                padding: '12px 24px',
                fontSize: '16px',
                minHeight: '44px',
                minWidth: '44px',
                background: '#16213e',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer'
              }}>
                Primary
              </button>
              <button style={{
                padding: '12px 24px',
                fontSize: '16px',
                minHeight: '44px',
                minWidth: '44px',
                background: '#4CAF50',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer'
              }}>
                Success
              </button>
              <button style={{
                padding: '12px 24px',
                fontSize: '16px',
                minHeight: '44px',
                minWidth: '44px',
                background: '#f44336',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer'
              }}>
                Danger
              </button>
            </div>
          </div>

          <div style={{ marginBottom: '15px' }}>
            <h3>Responsive Grid</h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: isMobile ? '1fr' : 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '15px'
            }}>
              <div style={{
                background: '#fff',
                padding: '15px',
                borderRadius: '8px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                <h4>Card 1</h4>
                <p>This card adapts to mobile screens</p>
              </div>
              <div style={{
                background: '#fff',
                padding: '15px',
                borderRadius: '8px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                <h4>Card 2</h4>
                <p>Responsive design in action</p>
              </div>
              <div style={{
                background: '#fff',
                padding: '15px',
                borderRadius: '8px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                <h4>Card 3</h4>
                <p>Mobile-first approach</p>
              </div>
            </div>
          </div>

          <div style={{ marginBottom: '15px' }}>
            <h3>Form Elements</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
              <input 
                type="text" 
                placeholder="Touch-friendly input"
                style={{
                  padding: '12px',
                  fontSize: '16px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  minHeight: '44px'
                }}
              />
              <select style={{
                padding: '12px',
                fontSize: '16px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                minHeight: '44px'
              }}>
                <option>Select an option</option>
                <option>Option 1</option>
                <option>Option 2</option>
              </select>
              <textarea 
                placeholder="Touch-friendly textarea"
                style={{
                  padding: '12px',
                  fontSize: '16px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  minHeight: '100px',
                  resize: 'vertical'
                }}
              />
            </div>
          </div>
        </div>

        <div style={{ 
          background: '#fff3e0', 
          padding: '20px', 
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h2>Mobile Navigation Test</h2>
          <p>
            {isMobile 
              ? '✅ Mobile bottom navigation should be visible at the bottom of the screen'
              : '⚠️ Switch to mobile view (width ≤ 768px) to see mobile navigation'
            }
          </p>
          <p>
            The hamburger menu in the header should open the mobile menu when tapped.
          </p>
        </div>

        <div style={{ 
          background: '#e8f5e8', 
          padding: '20px', 
          borderRadius: '8px',
          marginBottom: '80px' // Extra margin for mobile bottom nav
        }}>
          <h2>PWA Features Test</h2>
          <div style={{ display: 'grid', gap: '10px' }}>
            <div>📱 Install prompt should appear after 5 seconds</div>
            <div>🔄 Service worker caching active</div>
            <div>📶 Offline mode detection</div>
            <div>🔔 Push notifications available</div>
            <div>💾 Background sync enabled</div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
