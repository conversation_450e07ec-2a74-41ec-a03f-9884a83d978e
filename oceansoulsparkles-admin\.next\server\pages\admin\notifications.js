(()=>{var e={};e.id=744,e.ids=[744,660],e.modules={7658:e=>{e.exports={container:"Notifications_container__f9pcf",header:"Notifications_header__vW6hL",loading:"Notifications_loading__RJEOZ",spinner:"Notifications_spinner__ipCe_",spin:"Notifications_spin__ATuiU",statusCard:"Notifications_statusCard__fHK7w",statusGrid:"Notifications_statusGrid__wwmhE",statusItem:"Notifications_statusItem__rxF8p",statusLabel:"Notifications_statusLabel__yfFBi",statusValue:"Notifications_statusValue__kacAH",success:"Notifications_success__lihLu",warning:"Notifications_warning__3FxBO",error:"Notifications_error__uCBrB",testCard:"Notifications_testCard__RvGEN",testForm:"Notifications_testForm__3fMl2",emailInput:"Notifications_emailInput__2iO8R",testButton:"Notifications_testButton__D_wlH",testResult:"Notifications_testResult__0Y9Wu",templatesCard:"Notifications_templatesCard__7utA_",templatesList:"Notifications_templatesList__raCvr",templateItem:"Notifications_templateItem__u3CKl",helpCard:"Notifications_helpCard___VqBJ",envVars:"Notifications_envVars__z2tVh"}},6159:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{config:()=>x,default:()=>m,getServerSideProps:()=>_,getStaticPaths:()=>p,getStaticProps:()=>h,reportWebVitals:()=>f,routeModule:()=>P,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>g,unstable_getStaticPaths:()=>N,unstable_getStaticProps:()=>j});var i=t(7093),n=t(5244),r=t(1323),l=t(2899),c=t.n(l),o=t(6814),d=t(1243),u=e([o,d]);[o,d]=u.then?(await u)():u;let m=(0,r.l)(d,"default"),h=(0,r.l)(d,"getStaticProps"),p=(0,r.l)(d,"getStaticPaths"),_=(0,r.l)(d,"getServerSideProps"),x=(0,r.l)(d,"config"),f=(0,r.l)(d,"reportWebVitals"),j=(0,r.l)(d,"unstable_getStaticProps"),N=(0,r.l)(d,"unstable_getStaticPaths"),g=(0,r.l)(d,"unstable_getStaticParams"),S=(0,r.l)(d,"unstable_getServerProps"),v=(0,r.l)(d,"unstable_getServerSideProps"),P=new i.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/notifications",pathname:"/admin/notifications",bundlePath:"",filename:""},components:{App:o.default,Document:c()},userland:d});a()}catch(e){a(e)}})},1243:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{default:()=>u});var i=t(997),n=t(6689),r=t(8568),l=t(4845),c=t(7658),o=t.n(c),d=e([l]);function u(){let{user:e,loading:s}=(0,r.a)(),[t,a]=(0,n.useState)(!0),[c,d]=(0,n.useState)(null),[u,m]=(0,n.useState)(""),[h,p]=(0,n.useState)(null),[_,x]=(0,n.useState)(!1),f=async()=>{if(!u){alert("Please enter an email address");return}x(!0),p(null);try{let e=await fetch("/api/admin/notifications/email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"test_email",data:{to:u}})}),s=await e.json();e.ok?p({success:!0,message:"Test email sent successfully!"}):p({success:!1,message:s.message||"Failed to send test email"})}catch(e){p({success:!1,message:"Network error occurred"})}finally{x(!1)}};return s||t?i.jsx(l.Z,{children:(0,i.jsxs)("div",{className:o().loading,children:[i.jsx("div",{className:o().spinner}),i.jsx("p",{children:"Loading notifications management..."})]})}):i.jsx(l.Z,{children:(0,i.jsxs)("div",{className:o().container,children:[(0,i.jsxs)("div",{className:o().header,children:[i.jsx("h1",{children:"\uD83D\uDCE7 Email Notifications"}),i.jsx("p",{children:"Manage and test email notification system"})]}),(0,i.jsxs)("div",{className:o().statusCard,children:[i.jsx("h2",{children:"Email System Status"}),c?(0,i.jsxs)("div",{className:o().statusGrid,children:[(0,i.jsxs)("div",{className:o().statusItem,children:[i.jsx("span",{className:o().statusLabel,children:"Configuration:"}),i.jsx("span",{className:`${o().statusValue} ${c.configured?o().success:o().warning}`,children:c.configured?"✅ Configured":"⚠️ Not Configured"})]}),(0,i.jsxs)("div",{className:o().statusItem,children:[i.jsx("span",{className:o().statusLabel,children:"SMTP Host:"}),i.jsx("span",{className:o().statusValue,children:c.smtpHost})]}),(0,i.jsxs)("div",{className:o().statusItem,children:[i.jsx("span",{className:o().statusLabel,children:"SMTP User:"}),i.jsx("span",{className:o().statusValue,children:c.smtpUser})]}),(0,i.jsxs)("div",{className:o().statusItem,children:[i.jsx("span",{className:o().statusLabel,children:"SMTP Port:"}),i.jsx("span",{className:o().statusValue,children:c.smtpPort})]})]}):i.jsx("p",{children:"Loading status..."})]}),(0,i.jsxs)("div",{className:o().testCard,children:[i.jsx("h2",{children:"Send Test Email"}),i.jsx("p",{children:"Send a test email to verify the email system is working correctly."}),(0,i.jsxs)("div",{className:o().testForm,children:[i.jsx("input",{type:"email",placeholder:"Enter email address",value:u,onChange:e=>m(e.target.value),className:o().emailInput}),i.jsx("button",{onClick:f,disabled:_||!u,className:o().testButton,children:_?"Sending...":"Send Test Email"})]}),h&&i.jsx("div",{className:`${o().testResult} ${h.success?o().success:o().error}`,children:h.message})]}),(0,i.jsxs)("div",{className:o().templatesCard,children:[i.jsx("h2",{children:"Available Email Templates"}),(0,i.jsxs)("div",{className:o().templatesList,children:[(0,i.jsxs)("div",{className:o().templateItem,children:[i.jsx("h3",{children:"\uD83D\uDCC5 Booking Confirmation"}),i.jsx("p",{children:"Sent automatically when a booking is confirmed"})]}),(0,i.jsxs)("div",{className:o().templateItem,children:[i.jsx("h3",{children:"⏰ Booking Reminder"}),i.jsx("p",{children:"Sent 24 hours before appointment"})]}),(0,i.jsxs)("div",{className:o().templateItem,children:[i.jsx("h3",{children:"❌ Booking Cancellation"}),i.jsx("p",{children:"Sent when a booking is cancelled"})]}),(0,i.jsxs)("div",{className:o().templateItem,children:[i.jsx("h3",{children:"\uD83D\uDCB3 Payment Receipt"}),i.jsx("p",{children:"Sent after successful payment"})]}),(0,i.jsxs)("div",{className:o().templateItem,children:[i.jsx("h3",{children:"\uD83D\uDC65 Staff Notifications"}),i.jsx("p",{children:"Internal notifications for staff"})]}),(0,i.jsxs)("div",{className:o().templateItem,children:[i.jsx("h3",{children:"\uD83D\uDCE6 Low Inventory Alerts"}),i.jsx("p",{children:"Alerts when inventory is running low"})]})]})]}),c&&!c.configured&&(0,i.jsxs)("div",{className:o().helpCard,children:[i.jsx("h2",{children:"Email Configuration"}),i.jsx("p",{children:"To enable email notifications, add these environment variables:"}),(0,i.jsxs)("div",{className:o().envVars,children:[i.jsx("code",{children:"SMTP_HOST=smtp.gmail.com"}),i.jsx("code",{children:"SMTP_USER=<EMAIL>"}),i.jsx("code",{children:"SMTP_PASS=your-app-password"}),i.jsx("code",{children:"SMTP_PORT=587"})]}),(0,i.jsxs)("p",{children:[i.jsx("strong",{children:"Note:"})," For Gmail, use an App Password instead of your regular password."]})]})]})})}l=(d.then?(await d)():d)[0],a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[2899,6212,1664,7441],()=>t(6159));module.exports=a})();