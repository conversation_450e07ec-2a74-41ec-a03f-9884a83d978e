/**
 * Ocean Soul Sparkles - Responsive Chart Component
 * Automatically switches between desktop and mobile chart displays
 */

import React, { useState, useEffect } from 'react';
import MobileChart from './mobile/MobileChart';
import { ChartData } from 'chart.js';
import styles from '../../styles/admin/ResponsiveChart.module.css';

interface ResponsiveChartProps {
  type: 'line' | 'bar' | 'doughnut';
  data: ChartData<any>;
  title: string;
  height?: number;
  showLegend?: boolean;
  showTooltips?: boolean;
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  className?: string;
  desktopOptions?: any;
  mobileOptions?: any;
}

export default function ResponsiveChart({
  type,
  data,
  title,
  height = 300,
  showLegend = true,
  showTooltips = true,
  responsive = true,
  maintainAspectRatio = false,
  className = '',
  desktopOptions = {},
  mobileOptions = {}
}: ResponsiveChartProps) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Mobile view - use MobileChart
  if (isMobile) {
    return (
      <MobileChart
        type={type}
        data={data}
        title={title}
        height={height}
        showLegend={showLegend}
        showTooltips={showTooltips}
        responsive={responsive}
        maintainAspectRatio={maintainAspectRatio}
        className={className}
        {...mobileOptions}
      />
    );
  }

  // Desktop view - use standard Chart.js components
  const getDesktopOptions = () => {
    return {
      responsive,
      maintainAspectRatio,
      plugins: {
        title: {
          display: true,
          text: title,
          font: {
            size: 18,
            weight: '600'
          },
          color: '#2d3748',
          padding: {
            top: 15,
            bottom: 25
          }
        },
        legend: {
          display: showLegend,
          position: 'top' as const,
          labels: {
            font: {
              size: 14
            },
            color: '#4a5568',
            padding: 20,
            usePointStyle: true,
            pointStyle: 'circle'
          }
        },
        tooltip: {
          enabled: showTooltips,
          backgroundColor: 'rgba(45, 55, 72, 0.95)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#667eea',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          titleFont: {
            size: 14,
            weight: '600'
          },
          bodyFont: {
            size: 13
          },
          padding: 12,
          caretSize: 6
        }
      },
      scales: type !== 'doughnut' ? {
        x: {
          grid: {
            display: true,
            color: 'rgba(226, 232, 240, 0.5)'
          },
          ticks: {
            font: {
              size: 12
            },
            color: '#718096'
          }
        },
        y: {
          grid: {
            color: 'rgba(226, 232, 240, 0.5)'
          },
          ticks: {
            font: {
              size: 12
            },
            color: '#718096'
          }
        }
      } : undefined,
      elements: {
        point: {
          radius: type === 'line' ? 3 : 0,
          hoverRadius: 5,
          hitRadius: 8
        },
        line: {
          borderWidth: 2,
          tension: 0.1
        },
        bar: {
          borderRadius: 4
        }
      },
      ...desktopOptions
    };
  };

  // Import Chart.js components dynamically for desktop
  const Chart = React.lazy(() => 
    import('react-chartjs-2').then(module => ({
      default: module[type.charAt(0).toUpperCase() + type.slice(1) as keyof typeof module] as any
    }))
  );

  return (
    <div className={`${styles.responsiveChart} ${className}`}>
      <div className={styles.chartContainer}>
        <React.Suspense fallback={
          <div className={styles.chartLoading}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading chart...</p>
          </div>
        }>
          <Chart 
            data={data} 
            options={getDesktopOptions()} 
            height={height}
          />
        </React.Suspense>
      </div>
    </div>
  );
}
