(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[888],{6840:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return t(9645)}])},9645:function(n,e,t){"use strict";t.r(e),t.d(e,{default:function(){return i}}),function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}();var r=t(9008),o=t.n(r);!function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}();var c=t(2920);function i(n){let{Component:e,pageProps:t}=n;return Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(()=>{{let n=n=>(n.preventDefault(),!1),e=n=>{if("F12"===n.key||n.ctrlKey&&n.shiftKey&&("I"===n.key||"J"===n.key)||n.ctrlKey&&"U"===n.key)return n.preventDefault(),!1};return document.addEventListener("contextmenu",n),document.addEventListener("keydown",e),()=>{document.removeEventListener("contextmenu",n),document.removeEventListener("keydown",e)}}},[]),Object(function(){var n=Error("Cannot find module 'react'");throw n.code="MODULE_NOT_FOUND",n}())(()=>{},[]),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}()),{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(o(),{children:[Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{charSet:"utf-8"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{httpEquiv:"X-Content-Type-Options",content:"nosniff"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{httpEquiv:"X-XSS-Protection",content:"1; mode=block"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{name:"referrer",content:"strict-origin-when-cross-origin"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{name:"robots",content:"noindex, nofollow, noarchive, nosnippet"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{name:"googlebot",content:"noindex, nofollow"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{name:"description",content:"Ocean Soul Sparkles Admin Portal - Secure staff access only"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("link",{rel:"icon",href:"/admin/favicon.ico"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/admin/apple-touch-icon.png"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/admin/favicon-32x32.png"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/admin/favicon-16x16.png"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{name:"theme-color",content:"#3788d8"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("meta",{name:"msapplication-TileColor",content:"#3788d8"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("link",{rel:"preconnect",href:"https://ndlgbcsbidyhxbpqzgqp.supabase.co"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("link",{rel:"dns-prefetch",href:"https://js.squareup.com"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("link",{rel:"dns-prefetch",href:"https://api.onesignal.com"}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("title",{children:"Ocean Soul Sparkles Admin Portal"})]}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(e,{...t}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())(c.Ix,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light",toastStyle:{fontFamily:"inherit",fontSize:"14px"}}),Object(function(){var n=Error("Cannot find module 'react/jsx-runtime'");throw n.code="MODULE_NOT_FOUND",n}())("div",{style:{position:"fixed",bottom:"10px",right:"10px",background:"rgba(0, 0, 0, 0.1)",color:"rgba(0, 0, 0, 0.3)",padding:"4px 8px",borderRadius:"4px",fontSize:"10px",fontWeight:"bold",pointerEvents:"none",zIndex:9999,userSelect:"none"},children:"ADMIN PORTAL"}),!1]})}t(7677),t(3434)},3434:function(){}},function(n){var e=function(e){return n(n.s=e)};n.O(0,[736,179],function(){return e(6840),e(3079)}),_N_E=n.O()}]);