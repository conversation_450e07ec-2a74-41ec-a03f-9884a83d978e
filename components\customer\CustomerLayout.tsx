import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '../../styles/customer/CustomerLayout.module.css';

interface CustomerUser {
  id: string;
  customerId: string;
  email: string;
  firstName: string;
  lastName: string;
  emailVerified: boolean;
  isActive: boolean;
}

interface CustomerLayoutProps {
  children: React.ReactNode;
  title?: string;
  user?: CustomerUser;
}

export default function CustomerLayout({ children, title = 'Ocean Soul Sparkles', user }: CustomerLayoutProps) {
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [notifications, setNotifications] = useState<any[]>([]);

  useEffect(() => {
    // Load notifications if user is logged in
    if (user) {
      loadNotifications();
    }
  }, [user]);

  const loadNotifications = async () => {
    try {
      const token = localStorage.getItem('customer-token');
      if (!token) return;

      const response = await fetch('/api/customer/notifications', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setNotifications(data.notifications || []);
      }
    } catch (error) {
      console.error('Failed to load notifications:', error);
    }
  };

  const handleLogout = async () => {
    try {
      const token = localStorage.getItem('customer-token');
      if (token) {
        await fetch('/api/customer/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ sessionToken: token })
        });
      }

      localStorage.removeItem('customer-token');
      document.cookie = 'customer-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      router.push('/customer/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Still redirect even if logout API fails
      localStorage.removeItem('customer-token');
      router.push('/customer/login');
    }
  };

  const unreadNotifications = notifications.filter(n => !n.is_read).length;

  return (
    <div className={styles.layout}>
      <header className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.logo}>
            <Link href="/customer/dashboard">
              <h1>Ocean Soul Sparkles</h1>
            </Link>
          </div>

          {user && (
            <>
              <nav className={`${styles.nav} ${isMobileMenuOpen ? styles.navOpen : ''}`}>
                <Link 
                  href="/customer/dashboard" 
                  className={router.pathname === '/customer/dashboard' ? styles.active : ''}
                >
                  Dashboard
                </Link>
                <Link 
                  href="/customer/services" 
                  className={router.pathname === '/customer/services' ? styles.active : ''}
                >
                  Services
                </Link>
                <Link 
                  href="/customer/artists" 
                  className={router.pathname === '/customer/artists' ? styles.active : ''}
                >
                  Artists
                </Link>
                <Link 
                  href="/customer/bookings" 
                  className={router.pathname === '/customer/bookings' ? styles.active : ''}
                >
                  My Bookings
                </Link>
                <Link 
                  href="/customer/loyalty" 
                  className={router.pathname === '/customer/loyalty' ? styles.active : ''}
                >
                  Loyalty
                </Link>
                <Link 
                  href="/customer/profile" 
                  className={router.pathname === '/customer/profile' ? styles.active : ''}
                >
                  Profile
                </Link>
              </nav>

              <div className={styles.userActions}>
                <div className={styles.notifications}>
                  <Link href="/customer/notifications" className={styles.notificationIcon}>
                    🔔
                    {unreadNotifications > 0 && (
                      <span className={styles.notificationBadge}>{unreadNotifications}</span>
                    )}
                  </Link>
                </div>

                <div className={styles.userMenu}>
                  <span className={styles.userName}>
                    {user.firstName} {user.lastName}
                  </span>
                  <button onClick={handleLogout} className={styles.logoutBtn}>
                    Logout
                  </button>
                </div>
              </div>

              <button 
                className={styles.mobileMenuToggle}
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                ☰
              </button>
            </>
          )}

          {!user && (
            <div className={styles.authActions}>
              <Link href="/customer/login" className={styles.loginBtn}>
                Login
              </Link>
              <Link href="/customer/register" className={styles.registerBtn}>
                Register
              </Link>
            </div>
          )}
        </div>
      </header>

      <main className={styles.main}>
        <div className={styles.container}>
          {title && title !== 'Ocean Soul Sparkles' && (
            <div className={styles.pageHeader}>
              <h1>{title}</h1>
            </div>
          )}
          {children}
        </div>
      </main>

      <footer className={styles.footer}>
        <div className={styles.footerContent}>
          <div className={styles.footerSection}>
            <h3>Ocean Soul Sparkles</h3>
            <p>Professional face painting, hair braiding, and glitter art services.</p>
          </div>
          
          <div className={styles.footerSection}>
            <h4>Quick Links</h4>
            <ul>
              <li><Link href="/customer/services">Our Services</Link></li>
              <li><Link href="/customer/artists">Meet Our Artists</Link></li>
              <li><Link href="/customer/contact">Contact Us</Link></li>
            </ul>
          </div>

          <div className={styles.footerSection}>
            <h4>Contact Info</h4>
            <p>📧 <EMAIL></p>
            <p>📱 +61 ***********</p>
            <p>📍 Brisbane, Australia</p>
          </div>

          <div className={styles.footerSection}>
            <h4>Follow Us</h4>
            <div className={styles.socialLinks}>
              <a href="#" target="_blank" rel="noopener noreferrer">Facebook</a>
              <a href="#" target="_blank" rel="noopener noreferrer">Instagram</a>
              <a href="#" target="_blank" rel="noopener noreferrer">TikTok</a>
            </div>
          </div>
        </div>
        
        <div className={styles.footerBottom}>
          <p>&copy; 2025 Ocean Soul Sparkles. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
}
