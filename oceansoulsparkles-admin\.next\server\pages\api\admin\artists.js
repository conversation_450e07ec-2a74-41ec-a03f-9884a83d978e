"use strict";(()=>{var a={};a.id=9867,a.ids=[9867],a.modules={2885:a=>{a.exports=require("@supabase/supabase-js")},8432:a=>{a.exports=require("bcryptjs")},9344:a=>{a.exports=require("jsonwebtoken")},1287:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:a=>{a.exports=require("speakeasy")},5433:(a,e,t)=>{t.r(e),t.d(e,{config:()=>m,default:()=>c,routeModule:()=>p});var s={};t.r(s),t.d(s,{default:()=>u});var i=t(1802),r=t(7153),n=t(8781),l=t(2885),o=t(7474);let d=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function u(a,e){try{let t=a.headers.authorization?.replace("Bearer ","")||a.cookies["admin-token"];if(!t)return e.status(401).json({error:"No authentication token"});let s=await (0,o.Wg)(t);if(!s.valid||!s.user)return e.status(401).json({error:"Invalid authentication"});if("GET"===a.method){let{data:a,error:t}=await d.from("artist_profiles").select(`
          id,
          artist_name,
          name,
          email,
          phone,
          specializations,
          bio,
          portfolio_url,
          instagram_handle,
          is_active,
          hourly_rate,
          commission_rate,
          availability_schedule,
          created_at,
          updated_at
        `).eq("is_active",!0).order("artist_name",{ascending:!0});if(t&&t.message.includes('relation "artist_profiles" does not exist'))return e.status(200).json({artists:[{id:1,name:"Sarah Johnson",email:"<EMAIL>",phone:"0412 345 678",specializations:["Box Braids","Cornrows","Twists"],status:"active",availability:{monday:{start:"09:00",end:"17:00",available:!0},tuesday:{start:"09:00",end:"17:00",available:!0},wednesday:{start:"09:00",end:"17:00",available:!0},thursday:{start:"09:00",end:"17:00",available:!0},friday:{start:"09:00",end:"17:00",available:!0},saturday:{start:"10:00",end:"16:00",available:!0},sunday:{start:"10:00",end:"16:00",available:!1}},rating:4.8,total_bookings:127,total_revenue:15240,created_at:"2024-01-15T00:00:00Z"},{id:2,name:"Maya Patel",email:"<EMAIL>",phone:"0423 456 789",specializations:["Locs","Faux Locs","Goddess Braids"],status:"active",availability:{monday:{start:"10:00",end:"18:00",available:!0},tuesday:{start:"10:00",end:"18:00",available:!0},wednesday:{start:"10:00",end:"18:00",available:!1},thursday:{start:"10:00",end:"18:00",available:!0},friday:{start:"10:00",end:"18:00",available:!0},saturday:{start:"09:00",end:"17:00",available:!0},sunday:{start:"11:00",end:"15:00",available:!0}},rating:4.9,total_bookings:98,total_revenue:12800,created_at:"2024-02-01T00:00:00Z"},{id:3,name:"Keisha Williams",email:"<EMAIL>",phone:"0434 567 890",specializations:["Senegalese Twists","Marley Twists","Passion Twists"],status:"active",availability:{monday:{start:"08:00",end:"16:00",available:!0},tuesday:{start:"08:00",end:"16:00",available:!0},wednesday:{start:"08:00",end:"16:00",available:!0},thursday:{start:"08:00",end:"16:00",available:!0},friday:{start:"08:00",end:"16:00",available:!0},saturday:{start:"09:00",end:"15:00",available:!0},sunday:{start:"09:00",end:"15:00",available:!1}},rating:4.7,total_bookings:145,total_revenue:18600,created_at:"2024-01-20T00:00:00Z"}],source:"mock"});if(t)throw t;let s=(a||[]).map(a=>({id:a.id,name:a.artist_name||a.name,artist_name:a.artist_name,email:a.email,phone:a.phone,specializations:a.specializations||[],bio:a.bio,portfolio_url:a.portfolio_url,instagram_handle:a.instagram_handle,is_active:a.is_active,hourly_rate:a.hourly_rate,commission_rate:a.commission_rate,availability_schedule:a.availability_schedule,created_at:a.created_at,updated_at:a.updated_at}));return e.status(200).json({artists:s,total:s.length,source:"database"})}if("POST"===a.method){let{name:t,email:s,phone:i,specializations:r,availability:n}=a.body,{data:l,error:o}=await d.from("artist_profiles").insert([{artist_name:t,name:t,email:s,phone:i,specializations:r,availability_schedule:n,is_active:!0,created_at:new Date().toISOString()}]).select();if(o)throw o;return e.status(201).json({artist:l[0]})}return e.status(405).json({error:"Method not allowed"})}catch(a){return console.error("Artists API error:",a),e.status(500).json({error:"Internal server error"})}}let c=(0,n.l)(s,"default"),m=(0,n.l)(s,"config"),p=new i.PagesAPIRouteModule({definition:{kind:r.x.PAGES_API,page:"/api/admin/artists",pathname:"/api/admin/artists",bundlePath:"",filename:""},userland:s})}};var e=require("../../../webpack-api-runtime.js");e.C(a);var t=a=>e(e.s=a),s=e.X(0,[2805],()=>t(5433));module.exports=s})();