import type { NextRequest } from 'next/server';

interface IPCheckResult {
  allowed: boolean;
  ip: string;
  reason?: string;
}

/**
 * Check if IP address is allowed to access admin portal
 */
export async function checkIPRestrictions(request: NextRequest): Promise<IPCheckResult> {
  // Get client IP address
  const ip = getClientIP(request);
  
  // If IP restrictions are disabled, allow all
  if (process.env.ENABLE_IP_RESTRICTIONS !== 'true') {
    return { allowed: true, ip };
  }

  // Check against whitelist
  const allowedRanges = process.env.ALLOWED_IP_RANGES?.split(',') || [];
  const adminWhitelist = process.env.ADMIN_IP_WHITELIST?.split(',') || [];
  
  // Combine all allowed IPs/ranges
  const allAllowed = [...allowedRanges, ...adminWhitelist];
  
  if (allAllowed.length === 0) {
    // No restrictions configured, allow all
    return { allowed: true, ip };
  }

  // Check if IP is in allowed list
  for (const allowed of allAllowed) {
    if (isIPInRange(ip, allowed.trim())) {
      return { allowed: true, ip };
    }
  }

  return { 
    allowed: false, 
    ip, 
    reason: 'IP address not in whitelist' 
  };
}

/**
 * Get client IP address from request
 */
function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return request.ip || 'unknown';
}

/**
 * Check if IP address is within a given range/CIDR
 */
function isIPInRange(ip: string, range: string): boolean {
  // Handle exact IP match
  if (ip === range) {
    return true;
  }
  
  // Handle CIDR notation
  if (range.includes('/')) {
    return isIPInCIDR(ip, range);
  }
  
  // Handle wildcard patterns (basic)
  if (range.includes('*')) {
    const pattern = range.replace(/\*/g, '.*');
    const regex = new RegExp(`^${pattern}$`);
    return regex.test(ip);
  }
  
  return false;
}

/**
 * Check if IP is within CIDR range
 */
function isIPInCIDR(ip: string, cidr: string): boolean {
  try {
    const [network, prefixLength] = cidr.split('/');
    const prefix = parseInt(prefixLength, 10);
    
    if (isIPv4(ip) && isIPv4(network)) {
      return isIPv4InCIDR(ip, network, prefix);
    }
    
    // IPv6 support could be added here if needed
    return false;
  } catch (error) {
    console.error('CIDR check error:', error);
    return false;
  }
}

/**
 * Check if IPv4 address is valid
 */
function isIPv4(ip: string): boolean {
  const parts = ip.split('.');
  if (parts.length !== 4) return false;
  
  return parts.every(part => {
    const num = parseInt(part, 10);
    return num >= 0 && num <= 255 && part === num.toString();
  });
}

/**
 * Check if IPv4 is within CIDR range
 */
function isIPv4InCIDR(ip: string, network: string, prefixLength: number): boolean {
  const ipNum = ipv4ToNumber(ip);
  const networkNum = ipv4ToNumber(network);
  const mask = (0xffffffff << (32 - prefixLength)) >>> 0;
  
  return (ipNum & mask) === (networkNum & mask);
}

/**
 * Convert IPv4 address to number
 */
function ipv4ToNumber(ip: string): number {
  return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0;
}

/**
 * Add IP to whitelist (for dynamic management)
 */
export async function addIPToWhitelist(ip: string, description?: string): Promise<boolean> {
  try {
    // This would typically update a database table
    // For now, we'll just log it
    console.log(`Adding IP ${ip} to whitelist: ${description || 'No description'}`);
    
    // In a real implementation, you might:
    // 1. Update a database table
    // 2. Update environment variables
    // 3. Trigger a configuration reload
    
    return true;
  } catch (error) {
    console.error('Error adding IP to whitelist:', error);
    return false;
  }
}

/**
 * Remove IP from whitelist
 */
export async function removeIPFromWhitelist(ip: string): Promise<boolean> {
  try {
    console.log(`Removing IP ${ip} from whitelist`);
    return true;
  } catch (error) {
    console.error('Error removing IP from whitelist:', error);
    return false;
  }
}

/**
 * Get current IP whitelist
 */
export function getCurrentWhitelist(): string[] {
  const allowedRanges = process.env.ALLOWED_IP_RANGES?.split(',') || [];
  const adminWhitelist = process.env.ADMIN_IP_WHITELIST?.split(',') || [];
  
  return [...allowedRanges, ...adminWhitelist].map(ip => ip.trim()).filter(Boolean);
}

/**
 * Validate IP address format
 */
export function validateIPAddress(ip: string): { valid: boolean; type?: 'ipv4' | 'ipv6' | 'cidr'; error?: string } {
  // Check for CIDR notation
  if (ip.includes('/')) {
    const [address, prefix] = ip.split('/');
    const prefixNum = parseInt(prefix, 10);
    
    if (isIPv4(address) && prefixNum >= 0 && prefixNum <= 32) {
      return { valid: true, type: 'cidr' };
    }
    
    return { valid: false, error: 'Invalid CIDR notation' };
  }
  
  // Check for IPv4
  if (isIPv4(ip)) {
    return { valid: true, type: 'ipv4' };
  }
  
  // Basic IPv6 check (simplified)
  if (ip.includes(':') && ip.length <= 39) {
    return { valid: true, type: 'ipv6' };
  }
  
  return { valid: false, error: 'Invalid IP address format' };
}
