/* Ocean Soul Sparkles - Mobile Date Picker Styles */

.mobileDatePicker {
  position: relative;
  margin-bottom: 1.5rem;
  width: 100%;
}

.inputContainer {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.hiddenInput {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 2;
}

.displayLayer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1rem 0.75rem 1rem;
  min-height: 56px; /* Touch-friendly minimum height */
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.displayValue {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: #2d3748;
}

.displayIcon {
  font-size: 1.2rem;
  color: var(--admin-primary);
  flex-shrink: 0;
}

.displayText {
  color: #2d3748;
  font-weight: 500;
}

.placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.inputArrow {
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.arrow {
  font-size: 0.8rem;
  color: #718096;
  transition: transform 0.3s ease;
}

.displayLayer:active .arrow {
  transform: rotate(180deg);
}

.label {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.1rem;
  font-weight: 500;
  color: #718096;
  pointer-events: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  padding: 0 0.25rem;
  cursor: text;
  z-index: 0;
}

.label.floating {
  top: 0.75rem;
  transform: translateY(0);
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--admin-primary);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
}

.label.required::after {
  content: '*';
  color: #e53e3e;
  margin-left: 0.25rem;
}

.inputBorder {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--admin-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.inputBorder.focused {
  transform: scaleX(1);
}

.inputContainer:focus-within {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(55, 136, 216, 0.1);
}

.quickButtons {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
  flex-wrap: wrap;
}

.quickButton {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  font-weight: 500;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 40px; /* Touch-friendly height */
  display: flex;
  align-items: center;
  justify-content: center;
}

.quickButton:hover {
  background: rgba(55, 136, 216, 0.1);
  border-color: var(--admin-primary);
  color: var(--admin-primary);
  transform: translateY(-1px);
}

.quickButton:active {
  transform: translateY(0);
  background: rgba(55, 136, 216, 0.15);
}

.helperText {
  margin-top: 0.5rem;
  padding: 0 1rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.hintText {
  color: #718096;
  font-weight: 400;
}

.errorText {
  color: #e53e3e;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.errorIcon {
  font-size: 0.9rem;
}

/* Error State */
.mobileDatePicker.error .inputContainer {
  border-color: #e53e3e;
  background: rgba(229, 62, 62, 0.05);
}

.mobileDatePicker.error .inputBorder {
  background: #e53e3e;
}

.mobileDatePicker.error .label.floating {
  color: #e53e3e;
}

.mobileDatePicker.error .displayIcon {
  color: #e53e3e;
}

/* Disabled State */
.mobileDatePicker.disabled .inputContainer {
  background: #f7fafc;
  border-color: #e2e8f0;
  opacity: 0.6;
}

.mobileDatePicker.disabled .displayLayer {
  cursor: not-allowed;
}

.mobileDatePicker.disabled .displayValue {
  color: #a0aec0;
}

.mobileDatePicker.disabled .label {
  color: #a0aec0;
  cursor: not-allowed;
}

.mobileDatePicker.disabled .arrow {
  color: #a0aec0;
}

.mobileDatePicker.disabled .quickButton {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .displayLayer {
    padding: 1.5rem 1rem 1rem 1rem;
    min-height: 60px; /* Larger touch target on mobile */
  }

  .displayValue {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .label {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .label.floating {
    font-size: 0.8rem;
    top: 0.5rem;
  }

  .inputContainer {
    border-radius: 8px;
  }

  .quickButtons {
    gap: 0.75rem;
    margin-top: 1rem;
  }

  .quickButton {
    flex: 1;
    min-width: 0;
    padding: 0.75rem 0.5rem;
    min-height: 44px; /* Larger touch target */
    font-size: 0.8rem;
  }

  .helperText {
    font-size: 0.85rem;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .quickButtons {
    flex-direction: column;
  }

  .quickButton {
    width: 100%;
    justify-content: center;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .inputContainer {
    border-width: 3px;
  }

  .label {
    font-weight: 600;
  }

  .inputBorder {
    height: 3px;
  }

  .quickButton {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .displayLayer,
  .label,
  .inputBorder,
  .inputContainer,
  .arrow,
  .quickButton {
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .inputContainer {
    background: rgba(45, 55, 72, 0.95);
    border-color: #4a5568;
  }

  .displayValue {
    color: #f7fafc;
  }

  .displayText {
    color: #f7fafc;
  }

  .placeholder {
    color: #718096;
  }

  .label {
    color: #a0aec0;
  }

  .label.floating {
    color: #63b3ed;
    background: rgba(45, 55, 72, 0.9);
  }

  .quickButton {
    background: rgba(45, 55, 72, 0.9);
    border-color: #4a5568;
    color: #f7fafc;
  }

  .quickButton:hover {
    background: rgba(99, 179, 237, 0.1);
    border-color: #63b3ed;
    color: #63b3ed;
  }

  .hintText {
    color: #a0aec0;
  }

  .mobileDatePicker.disabled .inputContainer {
    background: #2d3748;
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  .hiddenInput {
    /* Ensure native date picker works on iOS */
    -webkit-appearance: none;
  }

  .displayLayer {
    /* Prevent iOS zoom on input focus */
    -webkit-user-select: none;
    user-select: none;
  }
}

/* Android Chrome specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .hiddenInput::-webkit-calendar-picker-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    color: transparent;
    background: transparent;
  }
}
