/**
 * Ocean Soul Sparkles Admin Dashboard - Structured Logger
 * Production-grade logging infrastructure with configurable levels and JSON output
 */

import { v4 as uuidv4 } from 'uuid';

// Log levels enum
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

// Log level names
export const LogLevelNames = {
  [LogLevel.DEBUG]: 'DEBUG',
  [LogLevel.INFO]: 'INFO',
  [LogLevel.WARN]: 'WARN',
  [LogLevel.ERROR]: 'ERROR',
  [LogLevel.FATAL]: 'FATAL'
};

// Log entry interface
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  levelName: string;
  message: string;
  requestId?: string;
  userId?: string;
  userRole?: string;
  ip?: string;
  path?: string;
  method?: string;
  statusCode?: number;
  duration?: number;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  metadata?: Record<string, any>;
  tags?: string[];
  service: string;
  version: string;
  environment: string;
}

// Logger configuration
export interface LoggerConfig {
  level: LogLevel;
  service: string;
  version: string;
  environment: string;
  enableConsole: boolean;
  enableFile: boolean;
  enableRemote: boolean;
  remoteEndpoint?: string;
  remoteApiKey?: string;
  maxFileSize?: number;
  maxFiles?: number;
}

// Performance metrics interface
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: string;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

// Business event interface
export interface BusinessEvent {
  event: string;
  userId?: string;
  customerId?: string;
  bookingId?: string;
  amount?: number;
  currency?: string;
  metadata?: Record<string, any>;
  timestamp: string;
}

class Logger {
  private config: LoggerConfig;
  private requestId?: string;
  private context: Record<string, any> = {};

  constructor(config?: Partial<LoggerConfig>) {
    this.config = {
      level: LogLevel.INFO,
      service: 'oceansoul-admin',
      version: process.env.API_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      enableConsole: true,
      enableFile: false,
      enableRemote: false,
      ...config
    };

    // Set log level from environment
    const envLevel = process.env.LOG_LEVEL?.toUpperCase();
    if (envLevel && envLevel in LogLevel) {
      this.config.level = LogLevel[envLevel as keyof typeof LogLevel];
    }
  }

  // Set request context
  setRequestId(requestId: string): Logger {
    const newLogger = new Logger(this.config);
    newLogger.requestId = requestId;
    newLogger.context = { ...this.context };
    return newLogger;
  }

  // Set user context
  setUser(userId: string, userRole?: string): Logger {
    const newLogger = new Logger(this.config);
    newLogger.requestId = this.requestId;
    newLogger.context = { 
      ...this.context, 
      userId, 
      userRole 
    };
    return newLogger;
  }

  // Add context data
  withContext(context: Record<string, any>): Logger {
    const newLogger = new Logger(this.config);
    newLogger.requestId = this.requestId;
    newLogger.context = { ...this.context, ...context };
    return newLogger;
  }

  // Core logging method
  private log(level: LogLevel, message: string, metadata?: Record<string, any>, error?: Error): void {
    if (level < this.config.level) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      levelName: LogLevelNames[level],
      message,
      requestId: this.requestId,
      service: this.config.service,
      version: this.config.version,
      environment: this.config.environment,
      ...this.context,
      metadata,
    };

    // Add error details if provided
    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: this.config.environment === 'development' ? error.stack : undefined,
        code: (error as any).code
      };
    }

    // Output to console in development or if enabled
    if (this.config.enableConsole) {
      this.outputToConsole(entry);
    }

    // Output to file if enabled
    if (this.config.enableFile) {
      this.outputToFile(entry);
    }

    // Send to remote logging service if enabled
    if (this.config.enableRemote) {
      this.outputToRemote(entry);
    }
  }

  // Console output with colors
  private outputToConsole(entry: LogEntry): void {
    const colors = {
      [LogLevel.DEBUG]: '\x1b[36m', // Cyan
      [LogLevel.INFO]: '\x1b[32m',  // Green
      [LogLevel.WARN]: '\x1b[33m',  // Yellow
      [LogLevel.ERROR]: '\x1b[31m', // Red
      [LogLevel.FATAL]: '\x1b[35m'  // Magenta
    };

    const reset = '\x1b[0m';
    const color = colors[entry.level] || '';

    if (this.config.environment === 'development') {
      // Pretty format for development
      const prefix = `${color}[${entry.levelName}]${reset}`;
      const timestamp = `\x1b[90m${entry.timestamp}${reset}`;
      const requestId = entry.requestId ? `\x1b[90m[${entry.requestId}]${reset}` : '';
      
      console.log(`${timestamp} ${prefix} ${requestId} ${entry.message}`);
      
      if (entry.metadata && Object.keys(entry.metadata).length > 0) {
        console.log('  Metadata:', entry.metadata);
      }
      
      if (entry.error) {
        console.log('  Error:', entry.error);
      }
    } else {
      // JSON format for production
      console.log(JSON.stringify(entry));
    }
  }

  // File output (placeholder - would implement with fs in real app)
  private outputToFile(entry: LogEntry): void {
    // In a real implementation, you would write to a file
    // This is a placeholder for the file logging functionality
    if (this.config.environment === 'development') {
      console.log('[FILE LOG]', JSON.stringify(entry));
    }
  }

  // Remote logging service output
  private outputToRemote(entry: LogEntry): void {
    // In a real implementation, you would send to a service like DataDog, LogRocket, etc.
    if (this.config.remoteEndpoint) {
      // Async send to remote service
      fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.remoteApiKey}`
        },
        body: JSON.stringify(entry)
      }).catch(error => {
        console.error('Failed to send log to remote service:', error);
      });
    }
  }

  // Public logging methods
  debug(message: string, metadata?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, metadata);
  }

  info(message: string, metadata?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, metadata);
  }

  warn(message: string, metadata?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, metadata);
  }

  error(message: string, error?: Error, metadata?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, metadata, error);
  }

  fatal(message: string, error?: Error, metadata?: Record<string, any>): void {
    this.log(LogLevel.FATAL, message, metadata, error);
  }

  // Performance logging
  logPerformance(metric: PerformanceMetric): void {
    this.info('Performance metric recorded', {
      type: 'performance',
      metric
    });
  }

  // Business event logging
  logBusinessEvent(event: BusinessEvent): void {
    this.info('Business event occurred', {
      type: 'business_event',
      event
    });
  }

  // API request logging
  logApiRequest(method: string, path: string, statusCode: number, duration: number, metadata?: Record<string, any>): void {
    const level = statusCode >= 500 ? LogLevel.ERROR : statusCode >= 400 ? LogLevel.WARN : LogLevel.INFO;
    
    this.log(level, `API ${method} ${path} - ${statusCode}`, {
      type: 'api_request',
      method,
      path,
      statusCode,
      duration,
      ...metadata
    });
  }

  // Database query logging
  logDatabaseQuery(query: string, duration: number, error?: Error): void {
    if (error) {
      this.error('Database query failed', error, {
        type: 'database_query',
        query: query.substring(0, 200), // Truncate long queries
        duration
      });
    } else {
      this.debug('Database query executed', {
        type: 'database_query',
        query: query.substring(0, 200),
        duration
      });
    }
  }

  // Security event logging
  logSecurityEvent(event: string, severity: 'low' | 'medium' | 'high' | 'critical', metadata?: Record<string, any>): void {
    const level = severity === 'critical' ? LogLevel.FATAL : 
                  severity === 'high' ? LogLevel.ERROR :
                  severity === 'medium' ? LogLevel.WARN : LogLevel.INFO;

    this.log(level, `Security event: ${event}`, {
      type: 'security_event',
      severity,
      ...metadata
    });
  }
}

// Create default logger instance
export const logger = new Logger({
  level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
  enableFile: process.env.NODE_ENV === 'production',
  enableRemote: process.env.NODE_ENV === 'production' && !!process.env.REMOTE_LOG_ENDPOINT,
  remoteEndpoint: process.env.REMOTE_LOG_ENDPOINT,
  remoteApiKey: process.env.REMOTE_LOG_API_KEY
});

// Export utilities
export { Logger };

// Utility functions
export function createRequestLogger(requestId: string): Logger {
  return logger.setRequestId(requestId);
}

export function createUserLogger(userId: string, userRole?: string): Logger {
  return logger.setUser(userId, userRole);
}

export function measurePerformance<T>(
  name: string,
  fn: () => T | Promise<T>,
  tags?: Record<string, string>
): Promise<T> {
  const start = Date.now();
  
  const logMetric = (duration: number) => {
    logger.logPerformance({
      name,
      value: duration,
      unit: 'ms',
      timestamp: new Date().toISOString(),
      tags
    });
  };

  try {
    const result = fn();
    
    if (result instanceof Promise) {
      return result.then(
        (value) => {
          logMetric(Date.now() - start);
          return value;
        },
        (error) => {
          logMetric(Date.now() - start);
          throw error;
        }
      );
    } else {
      logMetric(Date.now() - start);
      return Promise.resolve(result);
    }
  } catch (error) {
    logMetric(Date.now() - start);
    throw error;
  }
}
