(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[570],{9341:function(e,o,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/bookings/new",function(){return r(1887)}])},1887:function(e,o,r){"use strict";r.r(o),r.d(o,{default:function(){return s}}),function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var n=r(9008),t=r.n(n),i=r(1163),a=r(1664),c=r.n(a),d=r(6026),u=r(99),l=r(2920),m=r(731),O=r.n(m);function s(){let e=(0,i.useRouter)(),{user:o,loading:r}=(0,d.a)(),[n,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[m,s]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[_,f]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[N,h]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[E,D]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[U,j]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({customer_id:"",service_id:"",tier_id:"",artist_id:"",booking_date:"",start_time:"",duration:60,notes:"",location:"Studio"});Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{e.query.customer&&j(o=>({...o,customer_id:e.query.customer}))},[e.query.customer]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{o&&v()},[o]);let v=async()=>{a(!0);try{let[e,o,r]=await Promise.all([fetch("/api/admin/customers"),fetch("/api/admin/services"),fetch("/api/admin/artists")]);if(e.ok){let o=await e.json();s(o.customers||[])}if(o.ok){let e=await o.json();f(e.services||[])}if(r.ok){let e=await r.json();h(e.artists||[])}}catch(e){console.error("Error loading initial data:",e),l.Am.error("Failed to load form data")}finally{a(!1)}};Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{U.service_id?w(U.service_id):(D([]),j(e=>({...e,tier_id:"",duration:60})))},[U.service_id]);let w=async e=>{try{let r=await fetch("/api/admin/services/".concat(e,"/tiers"));if(r.ok){var o;let e=await r.json();D(e.tiers||[]);let n=null===(o=e.tiers)||void 0===o?void 0:o.find(e=>e.is_default);n&&j(e=>({...e,tier_id:n.id,duration:n.duration||60}))}}catch(e){console.error("Error loading service tiers:",e)}},b=e=>{let{name:o,value:r}=e.target;j(e=>({...e,[o]:r}))},C=()=>{if(U.start_time&&U.duration){let[e,o]=U.start_time.split(":"),r=new Date;return r.setHours(parseInt(e),parseInt(o),0,0),new Date(r.getTime()+6e4*U.duration).toTimeString().slice(0,5)}return""},F=()=>{let e=["customer_id","service_id","tier_id","artist_id","booking_date","start_time"].filter(e=>!U[e]);return e.length>0?(l.Am.error("Please fill in all required fields: ".concat(e.join(", "))),!1):!(new Date("".concat(U.booking_date,"T").concat(U.start_time))<new Date)||(l.Am.error("Booking date and time cannot be in the past"),!1)},T=async o=>{if(o.preventDefault(),F()){a(!0);try{let o=E.find(e=>e.id===U.tier_id),r=C(),n={customer_id:U.customer_id,service_id:U.service_id,assigned_artist_id:U.artist_id,start_time:"".concat(U.booking_date,"T").concat(U.start_time,":00"),end_time:"".concat(U.booking_date,"T").concat(r,":00"),status:"confirmed",total_amount:(null==o?void 0:o.price)||0,notes:U.notes,location:U.location,tier_name:null==o?void 0:o.name,tier_price:null==o?void 0:o.price,booking_source:"admin"},t=await fetch("/api/admin/bookings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(t.ok){let o=await t.json();l.Am.success("Booking created successfully!"),e.push("/admin/bookings/".concat(o.booking.id))}else{let e=await t.json();l.Am.error(e.message||"Failed to create booking")}}catch(e){console.error("Error creating booking:",e),l.Am.error("Failed to create booking")}finally{a(!1)}}};return r?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(u.Z,{children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().loadingContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().loadingSpinner}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Loading..."})]})}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(u.Z,{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(t(),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("title",{children:"New Booking | Ocean Soul Sparkles Admin"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("meta",{name:"description",content:"Create a new customer booking"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().newBookingContainer,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("header",{className:O().header,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().headerContent,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{children:"Create New Booking"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:"Schedule a new appointment for a customer"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().headerActions,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/bookings",className:O().backButton,children:"← Back to Bookings"})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("form",{onSubmit:T,className:O().bookingForm,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formGrid,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formSection,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Customer Information"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"customer_id",children:"Customer *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"customer_id",name:"customer_id",value:U.customer_id,onChange:b,required:!0,className:O().formControl,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"",children:"Select a customer..."}),m.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.id,children:[e.first_name," ",e.last_name," - ",e.email]},e.id))]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formActions,children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c(),{href:"/admin/customers/new",className:O().linkButton,children:"+ Add New Customer"})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formSection,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Service Details"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"service_id",children:"Service *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"service_id",name:"service_id",value:U.service_id,onChange:b,required:!0,className:O().formControl,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"",children:"Select a service..."}),_.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.id,children:[e.name," - $",e.base_price]},e.id))]})]}),E.length>0&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"tier_id",children:"Service Tier *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"tier_id",name:"tier_id",value:U.tier_id,onChange:e=>{let o=e.target.value,r=E.find(e=>e.id===o);j(e=>({...e,tier_id:o,duration:(null==r?void 0:r.duration)||60}))},required:!0,className:O().formControl,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"",children:"Select a tier..."}),E.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.id,children:[e.name," - $",e.price," (",e.duration," min)"]},e.id))]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formSection,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Artist Assignment"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"artist_id",children:"Artist *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"artist_id",name:"artist_id",value:U.artist_id,onChange:b,required:!0,className:O().formControl,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"",children:"Select an artist..."}),N.map(e=>{var o;return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.id,children:[e.name||e.artist_name," - ",null===(o=e.specializations)||void 0===o?void 0:o.join(", ")]},e.id)})]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formSection,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Schedule"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formRow,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"booking_date",children:"Date *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"date",id:"booking_date",name:"booking_date",value:U.booking_date,onChange:b,min:new Date().toISOString().split("T")[0],required:!0,className:O().formControl})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"start_time",children:"Start Time *"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"time",id:"start_time",name:"start_time",value:U.start_time,onChange:b,required:!0,className:O().formControl})]})]}),U.start_time&&U.duration&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().timeInfo,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:["End Time: ",C()]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:["Duration: ",U.duration," minutes"]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formSection,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{children:"Additional Details"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"location",children:"Location"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"location",name:"location",value:U.location,onChange:b,className:O().formControl,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"Studio",children:"Studio"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"Client Location",children:"Client Location"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"Event Venue",children:"Event Venue"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"Mobile Service",children:"Mobile Service"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formGroup,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"notes",children:"Notes"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("textarea",{id:"notes",name:"notes",value:U.notes,onChange:b,rows:4,placeholder:"Any special requirements or notes for this booking...",className:O().formControl})]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:O().formActions,children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{type:"button",onClick:()=>e.push("/admin/bookings"),className:O().cancelButton,disabled:n,children:"Cancel"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{type:"submit",className:O().submitButton,disabled:n,children:n?"Creating...":"Create Booking"})]})]})]})]})}},731:function(e){e.exports={newBookingContainer:"NewBooking_newBookingContainer__D1FAU",header:"NewBooking_header__Tb9K5",headerContent:"NewBooking_headerContent__dF28c",headerActions:"NewBooking_headerActions__dMENv",backButton:"NewBooking_backButton__g2itb",bookingForm:"NewBooking_bookingForm__U3c_O",formGrid:"NewBooking_formGrid__Cak5U",formSection:"NewBooking_formSection__X0n4P",formGroup:"NewBooking_formGroup__kk_6_",formControl:"NewBooking_formControl__2ENPF",formRow:"NewBooking_formRow__4D_fo",timeInfo:"NewBooking_timeInfo__4OdA7",linkButton:"NewBooking_linkButton__fsbtM",formActions:"NewBooking_formActions__xz_I7",cancelButton:"NewBooking_cancelButton__p80yq",submitButton:"NewBooking_submitButton__g18z_",content:"NewBooking_content__Yic_y",comingSoon:"NewBooking_comingSoon__au8HZ",actions:"NewBooking_actions__iMfSi",backBtn:"NewBooking_backBtn__bXS2C",loadingContainer:"NewBooking_loadingContainer__B0B7u",loadingSpinner:"NewBooking_loadingSpinner__y0PeA",spin:"NewBooking_spin__zwaRl"}}},function(e){e.O(0,[736,592,888,179],function(){return e(e.s=9341)}),_N_E=e.O()}]);