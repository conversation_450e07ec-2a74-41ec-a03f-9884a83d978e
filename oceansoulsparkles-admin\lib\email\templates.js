/**
 * Email Templates for Ocean Soul Sparkles Admin
 * Provides HTML email templates for various notification types
 */

/**
 * Base email template wrapper
 */
function baseTemplate(content, title = 'Ocean Soul Sparkles') {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
    .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
    .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6b7280; }
    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
    .alert { padding: 15px; border-radius: 6px; margin: 15px 0; }
    .alert-info { background: #dbeafe; border-left: 4px solid #3b82f6; }
    .alert-success { background: #d1fae5; border-left: 4px solid #10b981; }
    .alert-warning { background: #fef3c7; border-left: 4px solid #f59e0b; }
    .booking-details { background: #f8fafc; padding: 20px; border-radius: 6px; margin: 15px 0; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>✨ Ocean Soul Sparkles</h1>
      <p>Face Painting • Hair Braiding • Glitter Art</p>
    </div>
    <div class="content">
      ${content}
    </div>
    <div class="footer">
      <p>Ocean Soul Sparkles Admin Dashboard</p>
      <p><EMAIL> | +61 XXX XXX XXX</p>
      <p><small>This email was sent automatically. Please do not reply to this email.</small></p>
    </div>
  </div>
</body>
</html>`;
}

/**
 * Booking confirmation email template
 */
function bookingConfirmationTemplate(booking) {
  const content = `
    <h2>Booking Confirmation</h2>
    <p>Dear ${booking.customerName},</p>
    <p>Your booking has been confirmed! Here are the details:</p>
    
    <div class="booking-details">
      <h3>Booking Details</h3>
      <p><strong>Service:</strong> ${booking.serviceName}</p>
      <p><strong>Artist:</strong> ${booking.artistName}</p>
      <p><strong>Date:</strong> ${booking.date}</p>
      <p><strong>Time:</strong> ${booking.time}</p>
      <p><strong>Duration:</strong> ${booking.duration} minutes</p>
      <p><strong>Location:</strong> ${booking.location || 'Studio'}</p>
      <p><strong>Total Amount:</strong> $${booking.totalAmount}</p>
    </div>

    <div class="alert alert-info">
      <p><strong>Important:</strong> Please arrive 10 minutes before your appointment time.</p>
    </div>

    <p>If you need to reschedule or cancel, please contact us at least 24 hours in advance.</p>
    <p>We look forward to creating something magical for you!</p>
  `;
  
  return baseTemplate(content, 'Booking Confirmation - Ocean Soul Sparkles');
}

/**
 * Booking reminder email template
 */
function bookingReminderTemplate(booking) {
  const content = `
    <h2>Booking Reminder</h2>
    <p>Dear ${booking.customerName},</p>
    <p>This is a friendly reminder about your upcoming appointment:</p>
    
    <div class="booking-details">
      <h3>Tomorrow's Appointment</h3>
      <p><strong>Service:</strong> ${booking.serviceName}</p>
      <p><strong>Artist:</strong> ${booking.artistName}</p>
      <p><strong>Date:</strong> ${booking.date}</p>
      <p><strong>Time:</strong> ${booking.time}</p>
      <p><strong>Location:</strong> ${booking.location || 'Studio'}</p>
    </div>

    <div class="alert alert-warning">
      <p><strong>Reminder:</strong> Please arrive 10 minutes early and bring any reference images if you have them.</p>
    </div>

    <p>Can't wait to see you tomorrow!</p>
  `;
  
  return baseTemplate(content, 'Appointment Reminder - Ocean Soul Sparkles');
}

/**
 * Booking cancellation email template
 */
function bookingCancellationTemplate(booking) {
  const content = `
    <h2>Booking Cancellation</h2>
    <p>Dear ${booking.customerName},</p>
    <p>We're sorry to confirm that your booking has been cancelled:</p>
    
    <div class="booking-details">
      <h3>Cancelled Booking</h3>
      <p><strong>Service:</strong> ${booking.serviceName}</p>
      <p><strong>Date:</strong> ${booking.date}</p>
      <p><strong>Time:</strong> ${booking.time}</p>
      <p><strong>Reason:</strong> ${booking.cancellationReason || 'Not specified'}</p>
    </div>

    ${booking.refundAmount ? `
    <div class="alert alert-success">
      <p><strong>Refund:</strong> $${booking.refundAmount} will be processed within 3-5 business days.</p>
    </div>
    ` : ''}

    <p>We apologize for any inconvenience. Please feel free to book another appointment when convenient.</p>
  `;
  
  return baseTemplate(content, 'Booking Cancellation - Ocean Soul Sparkles');
}

/**
 * Payment receipt email template
 */
function paymentReceiptTemplate(payment) {
  const content = `
    <h2>Payment Receipt</h2>
    <p>Dear ${payment.customerName},</p>
    <p>Thank you for your payment! Here's your receipt:</p>
    
    <div class="booking-details">
      <h3>Payment Details</h3>
      <p><strong>Receipt #:</strong> ${payment.receiptNumber}</p>
      <p><strong>Date:</strong> ${payment.date}</p>
      <p><strong>Service:</strong> ${payment.serviceName}</p>
      <p><strong>Amount Paid:</strong> $${payment.amount}</p>
      <p><strong>Payment Method:</strong> ${payment.method}</p>
      <p><strong>Transaction ID:</strong> ${payment.transactionId}</p>
    </div>

    <div class="alert alert-success">
      <p>Payment processed successfully!</p>
    </div>

    <p>Keep this receipt for your records.</p>
  `;
  
  return baseTemplate(content, 'Payment Receipt - Ocean Soul Sparkles');
}

/**
 * Staff notification email template
 */
function staffNotificationTemplate(notification) {
  const content = `
    <h2>Staff Notification</h2>
    <p>Dear ${notification.staffName},</p>
    <p>${notification.message}</p>
    
    ${notification.details ? `
    <div class="booking-details">
      <h3>Details</h3>
      ${notification.details}
    </div>
    ` : ''}

    ${notification.actionRequired ? `
    <div class="alert alert-warning">
      <p><strong>Action Required:</strong> ${notification.actionRequired}</p>
    </div>
    ` : ''}

    <p>Please check the admin dashboard for more information.</p>
    <a href="${process.env.NEXT_PUBLIC_SITE_URL}/admin" class="button">Open Admin Dashboard</a>
  `;
  
  return baseTemplate(content, 'Staff Notification - Ocean Soul Sparkles');
}

/**
 * Low inventory alert email template
 */
function lowInventoryAlertTemplate(items) {
  const itemsList = items.map(item => 
    `<li><strong>${item.name}</strong> - ${item.currentStock} remaining (minimum: ${item.minStock})</li>`
  ).join('');

  const content = `
    <h2>Low Inventory Alert</h2>
    <p>The following items are running low and need to be restocked:</p>
    
    <div class="alert alert-warning">
      <h3>Items Requiring Attention</h3>
      <ul>
        ${itemsList}
      </ul>
    </div>

    <p>Please review and reorder these items to avoid stockouts.</p>
    <a href="${process.env.NEXT_PUBLIC_SITE_URL}/admin/inventory" class="button">Manage Inventory</a>
  `;
  
  return baseTemplate(content, 'Low Inventory Alert - Ocean Soul Sparkles');
}

module.exports = {
  baseTemplate,
  bookingConfirmationTemplate,
  bookingReminderTemplate,
  bookingCancellationTemplate,
  paymentReceiptTemplate,
  staffNotificationTemplate,
  lowInventoryAlertTemplate
};
