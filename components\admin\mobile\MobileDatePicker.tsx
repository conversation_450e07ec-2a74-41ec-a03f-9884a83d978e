/**
 * Ocean Soul Sparkles - Mobile Date Picker Component
 * Touch-optimized date and time selection for mobile devices
 */

import React, { useState, useRef, useEffect } from 'react';
import styles from '../../../styles/admin/mobile/MobileDatePicker.module.css';

interface MobileDatePickerProps {
  label: string;
  value: string; // ISO date string
  onChange: (value: string) => void;
  type?: 'date' | 'time' | 'datetime-local';
  required?: boolean;
  disabled?: boolean;
  error?: string;
  hint?: string;
  icon?: string;
  min?: string;
  max?: string;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
}

export default function MobileDatePicker({
  label,
  value,
  onChange,
  type = 'date',
  required = false,
  disabled = false,
  error,
  hint,
  icon,
  min,
  max,
  onFocus,
  onBlur,
  className = ''
}: MobileDatePickerProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setHasValue(value.length > 0);
  }, [value]);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const handleLabelClick = () => {
    inputRef.current?.focus();
  };

  const formatDisplayValue = (isoValue: string) => {
    if (!isoValue) return '';

    const date = new Date(isoValue);
    
    switch (type) {
      case 'date':
        return date.toLocaleDateString('en-AU', {
          weekday: 'short',
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      case 'time':
        return date.toLocaleTimeString('en-AU', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
      case 'datetime-local':
        return date.toLocaleDateString('en-AU', {
          weekday: 'short',
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
      default:
        return isoValue;
    }
  };

  const getInputType = () => {
    // Use native HTML5 input types for better mobile experience
    return type;
  };

  const getPlaceholder = () => {
    switch (type) {
      case 'date':
        return 'Select date';
      case 'time':
        return 'Select time';
      case 'datetime-local':
        return 'Select date and time';
      default:
        return 'Select';
    }
  };

  const getIcon = () => {
    if (icon) return icon;
    
    switch (type) {
      case 'date':
        return '📅';
      case 'time':
        return '🕐';
      case 'datetime-local':
        return '📅';
      default:
        return '📅';
    }
  };

  return (
    <div className={`${styles.mobileDatePicker} ${className} ${error ? styles.error : ''} ${disabled ? styles.disabled : ''}`}>
      {/* Date Input Container */}
      <div className={styles.inputContainer}>
        {/* Hidden native input for functionality */}
        <input
          ref={inputRef}
          type={getInputType()}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          required={required}
          disabled={disabled}
          min={min}
          max={max}
          className={styles.hiddenInput}
        />

        {/* Custom display layer */}
        <div
          className={`${styles.displayLayer} ${isFocused || hasValue ? styles.hasContent : ''}`}
          onClick={handleLabelClick}
        >
          <div className={styles.displayValue}>
            <span className={styles.displayIcon}>{getIcon()}</span>
            <span className={styles.displayText}>
              {hasValue ? formatDisplayValue(value) : ''}
            </span>
            {!hasValue && (
              <span className={styles.placeholder}>{getPlaceholder()}</span>
            )}
          </div>
          
          <div className={styles.inputArrow}>
            <span className={styles.arrow}>▼</span>
          </div>
        </div>

        <label
          onClick={handleLabelClick}
          className={`${styles.label} ${isFocused || hasValue ? styles.floating : ''} ${required ? styles.required : ''}`}
        >
          {label}
        </label>

        {/* Input Border Animation */}
        <div className={`${styles.inputBorder} ${isFocused ? styles.focused : ''}`}></div>
      </div>

      {/* Quick Date Buttons */}
      {type === 'date' && !disabled && (
        <div className={styles.quickButtons}>
          <button
            type="button"
            onClick={() => onChange(new Date().toISOString().split('T')[0])}
            className={styles.quickButton}
          >
            Today
          </button>
          <button
            type="button"
            onClick={() => {
              const tomorrow = new Date();
              tomorrow.setDate(tomorrow.getDate() + 1);
              onChange(tomorrow.toISOString().split('T')[0]);
            }}
            className={styles.quickButton}
          >
            Tomorrow
          </button>
          <button
            type="button"
            onClick={() => {
              const nextWeek = new Date();
              nextWeek.setDate(nextWeek.getDate() + 7);
              onChange(nextWeek.toISOString().split('T')[0]);
            }}
            className={styles.quickButton}
          >
            Next Week
          </button>
        </div>
      )}

      {/* Quick Time Buttons */}
      {type === 'time' && !disabled && (
        <div className={styles.quickButtons}>
          <button
            type="button"
            onClick={() => onChange('09:00')}
            className={styles.quickButton}
          >
            9:00 AM
          </button>
          <button
            type="button"
            onClick={() => onChange('12:00')}
            className={styles.quickButton}
          >
            12:00 PM
          </button>
          <button
            type="button"
            onClick={() => onChange('15:00')}
            className={styles.quickButton}
          >
            3:00 PM
          </button>
          <button
            type="button"
            onClick={() => onChange('18:00')}
            className={styles.quickButton}
          >
            6:00 PM
          </button>
        </div>
      )}

      {/* Helper Text */}
      {(hint || error) && (
        <div className={styles.helperText}>
          {error ? (
            <span className={styles.errorText}>
              <span className={styles.errorIcon}>⚠️</span>
              {error}
            </span>
          ) : (
            <span className={styles.hintText}>{hint}</span>
          )}
        </div>
      )}
    </div>
  );
}
