/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/dashboard",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/KeyboardShortcuts.module.css":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/KeyboardShortcuts.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/**\\n * Ocean Soul Sparkles Admin - Keyboard Shortcuts Styles\\n * Modal interface for displaying keyboard shortcuts help\\n */\\n\\n.KeyboardShortcuts_overlay__50H6k {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.6);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  z-index: 10000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n  animation: KeyboardShortcuts_fadeIn__3V9UQ 0.2s ease-out;\\n}\\n\\n@keyframes KeyboardShortcuts_fadeIn__3V9UQ {\\n  from { opacity: 0; }\\n  to { opacity: 1; }\\n}\\n\\n.KeyboardShortcuts_modal___mIHb {\\n  background: var(--admin-bg-primary, #ffffff);\\n  border-radius: var(--admin-radius-lg, 12px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\\n  max-width: 800px;\\n  width: 100%;\\n  max-height: 90vh;\\n  overflow: hidden;\\n  animation: KeyboardShortcuts_slideUp__v_6v_ 0.3s ease-out;\\n}\\n\\n@keyframes KeyboardShortcuts_slideUp__v_6v_ {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px) scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n\\n.KeyboardShortcuts_header__qglVm {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);\\n  background: var(--admin-bg-secondary, #f8f9fa);\\n}\\n\\n.KeyboardShortcuts_title__I96H_ {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: var(--admin-text-primary, #1a1a1a);\\n}\\n\\n.KeyboardShortcuts_closeBtn__uBydE {\\n  background: none;\\n  border: none;\\n  font-size: 1.5rem;\\n  color: var(--admin-text-secondary, #666666);\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: var(--admin-radius-sm, 4px);\\n  transition: all var(--admin-transition-normal, 0.2s ease);\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.KeyboardShortcuts_closeBtn__uBydE:hover {\\n  background: var(--admin-bg-primary, #ffffff);\\n  color: var(--admin-text-primary, #1a1a1a);\\n  transform: scale(1.1);\\n}\\n\\n.KeyboardShortcuts_content__JMnWB {\\n  padding: 24px;\\n  overflow-y: auto;\\n  max-height: calc(90vh - 80px);\\n}\\n\\n.KeyboardShortcuts_intro__4pzvg {\\n  margin-bottom: 24px;\\n  padding: 16px;\\n  background: var(--admin-primary-light, #e3f2fd);\\n  border-radius: var(--admin-radius-md, 8px);\\n  border-left: 4px solid var(--admin-primary, #3788d8);\\n}\\n\\n.KeyboardShortcuts_intro__4pzvg p {\\n  margin: 0;\\n  color: var(--admin-text-primary, #1a1a1a);\\n  font-size: 0.95rem;\\n  line-height: 1.5;\\n}\\n\\n.KeyboardShortcuts_shortcutsGrid__lq1C_ {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  grid-gap: 24px;\\n  gap: 24px;\\n}\\n\\n.KeyboardShortcuts_category__atJiS {\\n  background: var(--admin-bg-primary, #ffffff);\\n  border: 1px solid var(--admin-border-light, #e0e0e0);\\n  border-radius: var(--admin-radius-md, 8px);\\n  overflow: hidden;\\n}\\n\\n.KeyboardShortcuts_categoryTitle__DcncJ {\\n  margin: 0;\\n  padding: 16px 20px;\\n  background: var(--admin-bg-secondary, #f8f9fa);\\n  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: var(--admin-text-primary, #1a1a1a);\\n}\\n\\n.KeyboardShortcuts_shortcutsList__9cq54 {\\n  padding: 0;\\n}\\n\\n.KeyboardShortcuts_shortcutItem__vBzTj {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 20px;\\n  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);\\n  transition: background var(--admin-transition-normal, 0.2s ease);\\n}\\n\\n.KeyboardShortcuts_shortcutItem__vBzTj:last-child {\\n  border-bottom: none;\\n}\\n\\n.KeyboardShortcuts_shortcutItem__vBzTj:hover {\\n  background: var(--admin-bg-secondary, #f8f9fa);\\n}\\n\\n.KeyboardShortcuts_keys__5JoYl {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  flex-shrink: 0;\\n}\\n\\n.KeyboardShortcuts_key__oyNAe {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 24px;\\n  height: 24px;\\n  padding: 0 6px;\\n  background: var(--admin-bg-primary, #ffffff);\\n  border: 1px solid var(--admin-border-medium, #cccccc);\\n  border-radius: var(--admin-radius-sm, 4px);\\n  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  color: var(--admin-text-primary, #1a1a1a);\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  text-align: center;\\n  line-height: 1;\\n}\\n\\n.KeyboardShortcuts_keySeparator__2xiWn {\\n  font-size: 0.8rem;\\n  color: var(--admin-text-secondary, #666666);\\n  margin: 0 2px;\\n}\\n\\n.KeyboardShortcuts_description__e_Npl {\\n  flex: 1 1;\\n  margin-left: 16px;\\n  font-size: 0.9rem;\\n  color: var(--admin-text-primary, #1a1a1a);\\n  line-height: 1.4;\\n}\\n\\n.KeyboardShortcuts_footer__hvpay {\\n  margin-top: 24px;\\n  padding-top: 20px;\\n  border-top: 1px solid var(--admin-border-light, #e0e0e0);\\n}\\n\\n.KeyboardShortcuts_tip__PMQsZ {\\n  padding: 16px;\\n  background: var(--admin-warning-light, #fff3cd);\\n  border: 1px solid var(--admin-warning, #ffc107);\\n  border-radius: var(--admin-radius-md, 8px);\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n  color: var(--admin-text-primary, #1a1a1a);\\n}\\n\\n.KeyboardShortcuts_tip__PMQsZ strong {\\n  color: var(--admin-warning-dark, #856404);\\n}\\n\\n.KeyboardShortcuts_tip__PMQsZ .KeyboardShortcuts_key__oyNAe {\\n  margin: 0 2px;\\n  font-size: 0.75rem;\\n  min-width: 20px;\\n  height: 20px;\\n}\\n\\n.KeyboardShortcuts_helpButton__2NzAk {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  background: var(--admin-primary, #3788d8);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n  transition: all var(--admin-transition-normal, 0.2s ease);\\n  box-shadow: 0 2px 8px rgba(55, 136, 216, 0.3);\\n  position: fixed;\\n  bottom: 20px;\\n  right: 20px;\\n  z-index: 1000;\\n}\\n\\n.KeyboardShortcuts_helpButton__2NzAk:hover {\\n  background: var(--admin-primary-dark, #2c6bb8);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(55, 136, 216, 0.4);\\n}\\n\\n.KeyboardShortcuts_helpButton__2NzAk:active {\\n  transform: translateY(0);\\n}\\n\\n/* Mobile Responsive */\\n@media (max-width: 768px) {\\n  .KeyboardShortcuts_overlay__50H6k {\\n    padding: 10px;\\n  }\\n\\n  .KeyboardShortcuts_modal___mIHb {\\n    max-height: 95vh;\\n  }\\n\\n  .KeyboardShortcuts_header__qglVm {\\n    padding: 16px 20px;\\n  }\\n\\n  .KeyboardShortcuts_title__I96H_ {\\n    font-size: 1.3rem;\\n  }\\n\\n  .KeyboardShortcuts_content__JMnWB {\\n    padding: 20px;\\n    max-height: calc(95vh - 70px);\\n  }\\n\\n  .KeyboardShortcuts_shortcutsGrid__lq1C_ {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n\\n  .KeyboardShortcuts_shortcutItem__vBzTj {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n    padding: 16px 20px;\\n  }\\n\\n  .KeyboardShortcuts_keys__5JoYl {\\n    align-self: flex-end;\\n  }\\n\\n  .KeyboardShortcuts_description__e_Npl {\\n    margin-left: 0;\\n    font-size: 1rem;\\n  }\\n\\n  .KeyboardShortcuts_helpButton__2NzAk {\\n    width: 50px;\\n    height: 50px;\\n    bottom: 16px;\\n    right: 16px;\\n    font-size: 1.4rem;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .KeyboardShortcuts_overlay__50H6k {\\n    padding: 5px;\\n  }\\n\\n  .KeyboardShortcuts_header__qglVm {\\n    padding: 12px 16px;\\n  }\\n\\n  .KeyboardShortcuts_content__JMnWB {\\n    padding: 16px;\\n  }\\n\\n  .KeyboardShortcuts_categoryTitle__DcncJ {\\n    padding: 12px 16px;\\n    font-size: 1rem;\\n  }\\n\\n  .KeyboardShortcuts_shortcutItem__vBzTj {\\n    padding: 12px 16px;\\n  }\\n\\n  .KeyboardShortcuts_key__oyNAe {\\n    min-width: 28px;\\n    height: 28px;\\n    font-size: 0.9rem;\\n  }\\n\\n  .KeyboardShortcuts_description__e_Npl {\\n    font-size: 0.95rem;\\n  }\\n}\\n\\n/* Dark mode support */\\n@media (prefers-color-scheme: dark) {\\n  .KeyboardShortcuts_modal___mIHb {\\n    background: var(--admin-bg-primary-dark, #1a1a1a);\\n  }\\n\\n  .KeyboardShortcuts_header__qglVm {\\n    background: var(--admin-bg-secondary-dark, #2a2a2a);\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .KeyboardShortcuts_category__atJiS {\\n    background: var(--admin-bg-primary-dark, #1a1a1a);\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .KeyboardShortcuts_categoryTitle__DcncJ {\\n    background: var(--admin-bg-secondary-dark, #2a2a2a);\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .KeyboardShortcuts_shortcutItem__vBzTj {\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .KeyboardShortcuts_shortcutItem__vBzTj:hover {\\n    background: var(--admin-bg-secondary-dark, #2a2a2a);\\n  }\\n\\n  .KeyboardShortcuts_key__oyNAe {\\n    background: var(--admin-bg-secondary-dark, #2a2a2a);\\n    border-color: var(--admin-border-dark, #404040);\\n    color: var(--admin-text-primary-dark, #ffffff);\\n  }\\n\\n  .KeyboardShortcuts_footer__hvpay {\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .KeyboardShortcuts_intro__4pzvg {\\n    background: rgba(55, 136, 216, 0.2);\\n  }\\n\\n  .KeyboardShortcuts_tip__PMQsZ {\\n    background: rgba(255, 193, 7, 0.2);\\n    border-color: var(--admin-warning, #ffc107);\\n  }\\n}\\n\\n/* High contrast mode */\\n@media (prefers-contrast: high) {\\n  .KeyboardShortcuts_modal___mIHb {\\n    border: 2px solid var(--admin-text-primary, #1a1a1a);\\n  }\\n\\n  .KeyboardShortcuts_key__oyNAe {\\n    border-width: 2px;\\n  }\\n\\n  .KeyboardShortcuts_category__atJiS {\\n    border-width: 2px;\\n  }\\n}\\n\\n/* Reduced motion */\\n@media (prefers-reduced-motion: reduce) {\\n  .KeyboardShortcuts_overlay__50H6k,\\n  .KeyboardShortcuts_modal___mIHb {\\n    animation: none;\\n  }\\n\\n  .KeyboardShortcuts_helpButton__2NzAk,\\n  .KeyboardShortcuts_closeBtn__uBydE,\\n  .KeyboardShortcuts_shortcutItem__vBzTj {\\n    transition: none;\\n  }\\n}\\n\\n/* Focus styles for accessibility */\\n.KeyboardShortcuts_closeBtn__uBydE:focus-visible {\\n  outline: 2px solid var(--admin-primary, #3788d8);\\n  outline-offset: 2px;\\n}\\n\\n.KeyboardShortcuts_helpButton__2NzAk:focus-visible {\\n  outline: 2px solid white;\\n  outline-offset: 2px;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/admin/KeyboardShortcuts.module.css\"],\"names\":[],\"mappings\":\"AAAA;;;EAGE;;AAEF;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,8BAA8B;EAC9B,kCAA0B;UAA1B,0BAA0B;EAC1B,cAAc;EACd,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,aAAa;EACb,wDAA+B;AACjC;;AAEA;EACE,OAAO,UAAU,EAAE;EACnB,KAAK,UAAU,EAAE;AACnB;;AAEA;EACE,4CAA4C;EAC5C,2CAA2C;EAC3C,0CAA0C;EAC1C,gBAAgB;EAChB,WAAW;EACX,gBAAgB;EAChB,gBAAgB;EAChB,yDAAgC;AAClC;;AAEA;EACE;IACE,UAAU;IACV,uCAAuC;EACzC;EACA;IACE,UAAU;IACV,iCAAiC;EACnC;AACF;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;EAC9B,kBAAkB;EAClB,2DAA2D;EAC3D,8CAA8C;AAChD;;AAEA;EACE,SAAS;EACT,iBAAiB;EACjB,gBAAgB;EAChB,yCAAyC;AAC3C;;AAEA;EACE,gBAAgB;EAChB,YAAY;EACZ,iBAAiB;EACjB,2CAA2C;EAC3C,eAAe;EACf,YAAY;EACZ,0CAA0C;EAC1C,yDAAyD;EACzD,WAAW;EACX,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AACzB;;AAEA;EACE,4CAA4C;EAC5C,yCAAyC;EACzC,qBAAqB;AACvB;;AAEA;EACE,aAAa;EACb,gBAAgB;EAChB,6BAA6B;AAC/B;;AAEA;EACE,mBAAmB;EACnB,aAAa;EACb,+CAA+C;EAC/C,0CAA0C;EAC1C,oDAAoD;AACtD;;AAEA;EACE,SAAS;EACT,yCAAyC;EACzC,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,2DAA2D;EAC3D,cAAS;EAAT,SAAS;AACX;;AAEA;EACE,4CAA4C;EAC5C,oDAAoD;EACpD,0CAA0C;EAC1C,gBAAgB;AAClB;;AAEA;EACE,SAAS;EACT,kBAAkB;EAClB,8CAA8C;EAC9C,2DAA2D;EAC3D,iBAAiB;EACjB,gBAAgB;EAChB,yCAAyC;AAC3C;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;EAC9B,kBAAkB;EAClB,2DAA2D;EAC3D,gEAAgE;AAClE;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,QAAQ;EACR,cAAc;AAChB;;AAEA;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,eAAe;EACf,YAAY;EACZ,cAAc;EACd,4CAA4C;EAC5C,qDAAqD;EACrD,0CAA0C;EAC1C,yEAAyE;EACzE,iBAAiB;EACjB,gBAAgB;EAChB,yCAAyC;EACzC,wCAAwC;EACxC,kBAAkB;EAClB,cAAc;AAChB;;AAEA;EACE,iBAAiB;EACjB,2CAA2C;EAC3C,aAAa;AACf;;AAEA;EACE,SAAO;EACP,iBAAiB;EACjB,iBAAiB;EACjB,yCAAyC;EACzC,gBAAgB;AAClB;;AAEA;EACE,gBAAgB;EAChB,iBAAiB;EACjB,wDAAwD;AAC1D;;AAEA;EACE,aAAa;EACb,+CAA+C;EAC/C,+CAA+C;EAC/C,0CAA0C;EAC1C,iBAAiB;EACjB,gBAAgB;EAChB,yCAAyC;AAC3C;;AAEA;EACE,yCAAyC;AAC3C;;AAEA;EACE,aAAa;EACb,kBAAkB;EAClB,eAAe;EACf,YAAY;AACd;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,WAAW;EACX,YAAY;EACZ,yCAAyC;EACzC,YAAY;EACZ,YAAY;EACZ,kBAAkB;EAClB,iBAAiB;EACjB,eAAe;EACf,yDAAyD;EACzD,6CAA6C;EAC7C,eAAe;EACf,YAAY;EACZ,WAAW;EACX,aAAa;AACf;;AAEA;EACE,8CAA8C;EAC9C,2BAA2B;EAC3B,8CAA8C;AAChD;;AAEA;EACE,wBAAwB;AAC1B;;AAEA,sBAAsB;AACtB;EACE;IACE,aAAa;EACf;;EAEA;IACE,gBAAgB;EAClB;;EAEA;IACE,kBAAkB;EACpB;;EAEA;IACE,iBAAiB;EACnB;;EAEA;IACE,aAAa;IACb,6BAA6B;EAC/B;;EAEA;IACE,0BAA0B;IAC1B,SAAS;EACX;;EAEA;IACE,sBAAsB;IACtB,uBAAuB;IACvB,QAAQ;IACR,kBAAkB;EACpB;;EAEA;IACE,oBAAoB;EACtB;;EAEA;IACE,cAAc;IACd,eAAe;EACjB;;EAEA;IACE,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,iBAAiB;EACnB;AACF;;AAEA;EACE;IACE,YAAY;EACd;;EAEA;IACE,kBAAkB;EACpB;;EAEA;IACE,aAAa;EACf;;EAEA;IACE,kBAAkB;IAClB,eAAe;EACjB;;EAEA;IACE,kBAAkB;EACpB;;EAEA;IACE,eAAe;IACf,YAAY;IACZ,iBAAiB;EACnB;;EAEA;IACE,kBAAkB;EACpB;AACF;;AAEA,sBAAsB;AACtB;EACE;IACE,iDAAiD;EACnD;;EAEA;IACE,mDAAmD;IACnD,+CAA+C;EACjD;;EAEA;IACE,iDAAiD;IACjD,+CAA+C;EACjD;;EAEA;IACE,mDAAmD;IACnD,+CAA+C;EACjD;;EAEA;IACE,+CAA+C;EACjD;;EAEA;IACE,mDAAmD;EACrD;;EAEA;IACE,mDAAmD;IACnD,+CAA+C;IAC/C,8CAA8C;EAChD;;EAEA;IACE,+CAA+C;EACjD;;EAEA;IACE,mCAAmC;EACrC;;EAEA;IACE,kCAAkC;IAClC,2CAA2C;EAC7C;AACF;;AAEA,uBAAuB;AACvB;EACE;IACE,oDAAoD;EACtD;;EAEA;IACE,iBAAiB;EACnB;;EAEA;IACE,iBAAiB;EACnB;AACF;;AAEA,mBAAmB;AACnB;EACE;;IAEE,eAAe;EACjB;;EAEA;;;IAGE,gBAAgB;EAClB;AACF;;AAEA,mCAAmC;AACnC;EACE,gDAAgD;EAChD,mBAAmB;AACrB;;AAEA;EACE,wBAAwB;EACxB,mBAAmB;AACrB\",\"sourcesContent\":[\"/**\\n * Ocean Soul Sparkles Admin - Keyboard Shortcuts Styles\\n * Modal interface for displaying keyboard shortcuts help\\n */\\n\\n.overlay {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.6);\\n  backdrop-filter: blur(4px);\\n  z-index: 10000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n  animation: fadeIn 0.2s ease-out;\\n}\\n\\n@keyframes fadeIn {\\n  from { opacity: 0; }\\n  to { opacity: 1; }\\n}\\n\\n.modal {\\n  background: var(--admin-bg-primary, #ffffff);\\n  border-radius: var(--admin-radius-lg, 12px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\\n  max-width: 800px;\\n  width: 100%;\\n  max-height: 90vh;\\n  overflow: hidden;\\n  animation: slideUp 0.3s ease-out;\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px) scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n\\n.header {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);\\n  background: var(--admin-bg-secondary, #f8f9fa);\\n}\\n\\n.title {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: var(--admin-text-primary, #1a1a1a);\\n}\\n\\n.closeBtn {\\n  background: none;\\n  border: none;\\n  font-size: 1.5rem;\\n  color: var(--admin-text-secondary, #666666);\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: var(--admin-radius-sm, 4px);\\n  transition: all var(--admin-transition-normal, 0.2s ease);\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.closeBtn:hover {\\n  background: var(--admin-bg-primary, #ffffff);\\n  color: var(--admin-text-primary, #1a1a1a);\\n  transform: scale(1.1);\\n}\\n\\n.content {\\n  padding: 24px;\\n  overflow-y: auto;\\n  max-height: calc(90vh - 80px);\\n}\\n\\n.intro {\\n  margin-bottom: 24px;\\n  padding: 16px;\\n  background: var(--admin-primary-light, #e3f2fd);\\n  border-radius: var(--admin-radius-md, 8px);\\n  border-left: 4px solid var(--admin-primary, #3788d8);\\n}\\n\\n.intro p {\\n  margin: 0;\\n  color: var(--admin-text-primary, #1a1a1a);\\n  font-size: 0.95rem;\\n  line-height: 1.5;\\n}\\n\\n.shortcutsGrid {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 24px;\\n}\\n\\n.category {\\n  background: var(--admin-bg-primary, #ffffff);\\n  border: 1px solid var(--admin-border-light, #e0e0e0);\\n  border-radius: var(--admin-radius-md, 8px);\\n  overflow: hidden;\\n}\\n\\n.categoryTitle {\\n  margin: 0;\\n  padding: 16px 20px;\\n  background: var(--admin-bg-secondary, #f8f9fa);\\n  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: var(--admin-text-primary, #1a1a1a);\\n}\\n\\n.shortcutsList {\\n  padding: 0;\\n}\\n\\n.shortcutItem {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 20px;\\n  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);\\n  transition: background var(--admin-transition-normal, 0.2s ease);\\n}\\n\\n.shortcutItem:last-child {\\n  border-bottom: none;\\n}\\n\\n.shortcutItem:hover {\\n  background: var(--admin-bg-secondary, #f8f9fa);\\n}\\n\\n.keys {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  flex-shrink: 0;\\n}\\n\\n.key {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 24px;\\n  height: 24px;\\n  padding: 0 6px;\\n  background: var(--admin-bg-primary, #ffffff);\\n  border: 1px solid var(--admin-border-medium, #cccccc);\\n  border-radius: var(--admin-radius-sm, 4px);\\n  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  color: var(--admin-text-primary, #1a1a1a);\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n  text-align: center;\\n  line-height: 1;\\n}\\n\\n.keySeparator {\\n  font-size: 0.8rem;\\n  color: var(--admin-text-secondary, #666666);\\n  margin: 0 2px;\\n}\\n\\n.description {\\n  flex: 1;\\n  margin-left: 16px;\\n  font-size: 0.9rem;\\n  color: var(--admin-text-primary, #1a1a1a);\\n  line-height: 1.4;\\n}\\n\\n.footer {\\n  margin-top: 24px;\\n  padding-top: 20px;\\n  border-top: 1px solid var(--admin-border-light, #e0e0e0);\\n}\\n\\n.tip {\\n  padding: 16px;\\n  background: var(--admin-warning-light, #fff3cd);\\n  border: 1px solid var(--admin-warning, #ffc107);\\n  border-radius: var(--admin-radius-md, 8px);\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n  color: var(--admin-text-primary, #1a1a1a);\\n}\\n\\n.tip strong {\\n  color: var(--admin-warning-dark, #856404);\\n}\\n\\n.tip .key {\\n  margin: 0 2px;\\n  font-size: 0.75rem;\\n  min-width: 20px;\\n  height: 20px;\\n}\\n\\n.helpButton {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  background: var(--admin-primary, #3788d8);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n  transition: all var(--admin-transition-normal, 0.2s ease);\\n  box-shadow: 0 2px 8px rgba(55, 136, 216, 0.3);\\n  position: fixed;\\n  bottom: 20px;\\n  right: 20px;\\n  z-index: 1000;\\n}\\n\\n.helpButton:hover {\\n  background: var(--admin-primary-dark, #2c6bb8);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(55, 136, 216, 0.4);\\n}\\n\\n.helpButton:active {\\n  transform: translateY(0);\\n}\\n\\n/* Mobile Responsive */\\n@media (max-width: 768px) {\\n  .overlay {\\n    padding: 10px;\\n  }\\n\\n  .modal {\\n    max-height: 95vh;\\n  }\\n\\n  .header {\\n    padding: 16px 20px;\\n  }\\n\\n  .title {\\n    font-size: 1.3rem;\\n  }\\n\\n  .content {\\n    padding: 20px;\\n    max-height: calc(95vh - 70px);\\n  }\\n\\n  .shortcutsGrid {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n\\n  .shortcutItem {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n    padding: 16px 20px;\\n  }\\n\\n  .keys {\\n    align-self: flex-end;\\n  }\\n\\n  .description {\\n    margin-left: 0;\\n    font-size: 1rem;\\n  }\\n\\n  .helpButton {\\n    width: 50px;\\n    height: 50px;\\n    bottom: 16px;\\n    right: 16px;\\n    font-size: 1.4rem;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .overlay {\\n    padding: 5px;\\n  }\\n\\n  .header {\\n    padding: 12px 16px;\\n  }\\n\\n  .content {\\n    padding: 16px;\\n  }\\n\\n  .categoryTitle {\\n    padding: 12px 16px;\\n    font-size: 1rem;\\n  }\\n\\n  .shortcutItem {\\n    padding: 12px 16px;\\n  }\\n\\n  .key {\\n    min-width: 28px;\\n    height: 28px;\\n    font-size: 0.9rem;\\n  }\\n\\n  .description {\\n    font-size: 0.95rem;\\n  }\\n}\\n\\n/* Dark mode support */\\n@media (prefers-color-scheme: dark) {\\n  .modal {\\n    background: var(--admin-bg-primary-dark, #1a1a1a);\\n  }\\n\\n  .header {\\n    background: var(--admin-bg-secondary-dark, #2a2a2a);\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .category {\\n    background: var(--admin-bg-primary-dark, #1a1a1a);\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .categoryTitle {\\n    background: var(--admin-bg-secondary-dark, #2a2a2a);\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .shortcutItem {\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .shortcutItem:hover {\\n    background: var(--admin-bg-secondary-dark, #2a2a2a);\\n  }\\n\\n  .key {\\n    background: var(--admin-bg-secondary-dark, #2a2a2a);\\n    border-color: var(--admin-border-dark, #404040);\\n    color: var(--admin-text-primary-dark, #ffffff);\\n  }\\n\\n  .footer {\\n    border-color: var(--admin-border-dark, #404040);\\n  }\\n\\n  .intro {\\n    background: rgba(55, 136, 216, 0.2);\\n  }\\n\\n  .tip {\\n    background: rgba(255, 193, 7, 0.2);\\n    border-color: var(--admin-warning, #ffc107);\\n  }\\n}\\n\\n/* High contrast mode */\\n@media (prefers-contrast: high) {\\n  .modal {\\n    border: 2px solid var(--admin-text-primary, #1a1a1a);\\n  }\\n\\n  .key {\\n    border-width: 2px;\\n  }\\n\\n  .category {\\n    border-width: 2px;\\n  }\\n}\\n\\n/* Reduced motion */\\n@media (prefers-reduced-motion: reduce) {\\n  .overlay,\\n  .modal {\\n    animation: none;\\n  }\\n\\n  .helpButton,\\n  .closeBtn,\\n  .shortcutItem {\\n    transition: none;\\n  }\\n}\\n\\n/* Focus styles for accessibility */\\n.closeBtn:focus-visible {\\n  outline: 2px solid var(--admin-primary, #3788d8);\\n  outline-offset: 2px;\\n}\\n\\n.helpButton:focus-visible {\\n  outline: 2px solid white;\\n  outline-offset: 2px;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"overlay\": \"KeyboardShortcuts_overlay__50H6k\",\n\t\"fadeIn\": \"KeyboardShortcuts_fadeIn__3V9UQ\",\n\t\"modal\": \"KeyboardShortcuts_modal___mIHb\",\n\t\"slideUp\": \"KeyboardShortcuts_slideUp__v_6v_\",\n\t\"header\": \"KeyboardShortcuts_header__qglVm\",\n\t\"title\": \"KeyboardShortcuts_title__I96H_\",\n\t\"closeBtn\": \"KeyboardShortcuts_closeBtn__uBydE\",\n\t\"content\": \"KeyboardShortcuts_content__JMnWB\",\n\t\"intro\": \"KeyboardShortcuts_intro__4pzvg\",\n\t\"shortcutsGrid\": \"KeyboardShortcuts_shortcutsGrid__lq1C_\",\n\t\"category\": \"KeyboardShortcuts_category__atJiS\",\n\t\"categoryTitle\": \"KeyboardShortcuts_categoryTitle__DcncJ\",\n\t\"shortcutsList\": \"KeyboardShortcuts_shortcutsList__9cq54\",\n\t\"shortcutItem\": \"KeyboardShortcuts_shortcutItem__vBzTj\",\n\t\"keys\": \"KeyboardShortcuts_keys__5JoYl\",\n\t\"key\": \"KeyboardShortcuts_key__oyNAe\",\n\t\"keySeparator\": \"KeyboardShortcuts_keySeparator__2xiWn\",\n\t\"description\": \"KeyboardShortcuts_description__e_Npl\",\n\t\"footer\": \"KeyboardShortcuts_footer__hvpay\",\n\t\"tip\": \"KeyboardShortcuts_tip__PMQsZ\",\n\t\"helpButton\": \"KeyboardShortcuts_helpButton__2NzAk\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/KeyboardShortcuts.module.css\n"));

/***/ }),

/***/ "./styles/admin/KeyboardShortcuts.module.css":
/*!***************************************************!*\
  !*** ./styles/admin/KeyboardShortcuts.module.css ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./KeyboardShortcuts.module.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/KeyboardShortcuts.module.css\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./KeyboardShortcuts.module.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/KeyboardShortcuts.module.css\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./KeyboardShortcuts.module.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/KeyboardShortcuts.module.css\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/admin/KeyboardShortcuts.module.css\n"));

/***/ }),

/***/ "./components/admin/AdminHeader.tsx":
/*!******************************************!*\
  !*** ./components/admin/AdminHeader.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminHeader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _GlobalSearch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./GlobalSearch */ \"./components/admin/GlobalSearch.tsx\");\n/* harmony import */ var _BreadcrumbNavigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BreadcrumbNavigation */ \"./components/admin/BreadcrumbNavigation.tsx\");\n/* harmony import */ var _KeyboardShortcuts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./KeyboardShortcuts */ \"./components/admin/KeyboardShortcuts.tsx\");\n/* harmony import */ var _styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../styles/admin/AdminHeader.module.css */ \"./styles/admin/AdminHeader.module.css\");\n/* harmony import */ var _styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdminHeader(param) {\n    let { user, onLogout, onToggleSidebar, sidebarCollapsed } = param;\n    _s();\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const notificationsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n                setShowUserMenu(false);\n            }\n            if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {\n                setShowNotifications(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    const getRoleColor = (role)=>{\n        switch(role){\n            case \"DEV\":\n                return \"#dc3545\";\n            case \"Admin\":\n                return \"#3788d8\";\n            case \"Artist\":\n                return \"#28a745\";\n            case \"Braider\":\n                return \"#fd7e14\";\n            default:\n                return \"#6c757d\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().adminHeader),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().headerLeft),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().sidebarToggle),\n                        onClick: ()=>{\n                            console.log(\"\\uD83C\\uDF54 Hamburger menu clicked!\", {\n                                sidebarCollapsed\n                            });\n                            onToggleSidebar();\n                        },\n                        title: sidebarCollapsed ? \"Expand sidebar\" : \"Collapse sidebar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().hamburger),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BreadcrumbNavigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().breadcrumb),\n                        showIcons: true,\n                        maxItems: 4\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().headerCenter),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlobalSearch__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().headerRight),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().quickActions),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/bookings/new\",\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().quickAction),\n                                title: \"New Booking\",\n                                children: \"\\uD83D\\uDCC5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/customers/new\",\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().quickAction),\n                                title: \"New Customer\",\n                                children: \"\\uD83D\\uDC64\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().quickAction),\n                                title: \"Refresh\",\n                                children: \"\\uD83D\\uDD04\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KeyboardShortcuts__WEBPACK_IMPORTED_MODULE_5__.KeyboardShortcutsButton, {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().quickAction)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notifications),\n                        ref: notificationsRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationButton),\n                                onClick: ()=>setShowNotifications(!showNotifications),\n                                title: \"Notifications\",\n                                children: [\n                                    \"\\uD83D\\uDD14\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationBadge),\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationHeader),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().markAllRead),\n                                                children: \"Mark all read\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationList),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationIcon),\n                                                        children: \"\\uD83D\\uDCC5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationTitle),\n                                                                children: \"New booking request\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationTime),\n                                                                children: \"5 minutes ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationIcon),\n                                                        children: \"\\uD83D\\uDCB0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationTitle),\n                                                                children: \"Payment received\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationTime),\n                                                                children: \"1 hour ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationIcon),\n                                                        children: \"⚠️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationTitle),\n                                                                children: \"Low inventory alert\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationTime),\n                                                                children: \"2 hours ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().notificationFooter),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/admin/notifications\",\n                                            children: \"View all notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userMenu),\n                        ref: userMenuRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userButton),\n                                onClick: ()=>{\n                                    try {\n                                        console.log(\"\\uD83D\\uDC64 User menu clicked!\", {\n                                            current: showUserMenu,\n                                            willBe: !showUserMenu\n                                        });\n                                        setShowUserMenu(!showUserMenu);\n                                    } catch (error) {\n                                        console.error(\"❌ Error toggling user menu:\", error);\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userAvatar),\n                                        children: [\n                                            user.firstName.charAt(0),\n                                            user.lastName.charAt(0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userInfo),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userName),\n                                                children: [\n                                                    user.firstName,\n                                                    \" \",\n                                                    user.lastName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userRole),\n                                                style: {\n                                                    color: getRoleColor(user.role)\n                                                },\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownArrow),\n                                        children: showUserMenu ? \"▲\" : \"▼\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userDropdownHeader),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userEmail),\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userRoleBadge),\n                                                style: {\n                                                    backgroundColor: getRoleColor(user.role)\n                                                },\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().userDropdownMenu),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/profile\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownIcon),\n                                                        children: \"\\uD83D\\uDC64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Profile Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/security\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownIcon),\n                                                        children: \"\\uD83D\\uDD12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Security & MFA\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/preferences\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownIcon),\n                                                        children: \"⚙️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Preferences\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownDivider)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/help\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownIcon),\n                                                        children: \"❓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Help & Support\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownDivider)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"\".concat((_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownItem), \" \").concat((_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().logoutItem)),\n                                                onClick: onLogout,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_6___default().dropdownIcon),\n                                                        children: \"\\uD83D\\uDEAA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sign Out\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminHeader, \"rjecgjj2i+XnaFxeShw7hKoCu3U=\");\n_c = AdminHeader;\nvar _c;\n$RefreshReg$(_c, \"AdminHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/admin/AdminHeader.tsx\n"));

/***/ }),

/***/ "./components/admin/KeyboardShortcuts.tsx":
/*!************************************************!*\
  !*** ./components/admin/KeyboardShortcuts.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeyboardShortcutsButton: function() { return /* binding */ KeyboardShortcutsButton; },\n/* harmony export */   \"default\": function() { return /* binding */ KeyboardShortcuts; },\n/* harmony export */   useKeyboardShortcuts: function() { return /* binding */ useKeyboardShortcuts; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../styles/admin/KeyboardShortcuts.module.css */ \"./styles/admin/KeyboardShortcuts.module.css\");\n/* harmony import */ var _styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/**\n * Ocean Soul Sparkles Admin - Keyboard Shortcuts Help\n * Modal component showing available keyboard shortcuts\n */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\nconst SHORTCUTS = [\n    // Navigation\n    {\n        keys: [\n            \"Ctrl\",\n            \"K\"\n        ],\n        description: \"Open global search\",\n        category: \"Navigation\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"H\"\n        ],\n        description: \"Go to dashboard\",\n        category: \"Navigation\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"U\"\n        ],\n        description: \"Go to customers\",\n        category: \"Navigation\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"B\"\n        ],\n        description: \"Go to bookings\",\n        category: \"Navigation\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"S\"\n        ],\n        description: \"Go to services\",\n        category: \"Navigation\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"P\"\n        ],\n        description: \"Go to products\",\n        category: \"Navigation\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"I\"\n        ],\n        description: \"Go to inventory\",\n        category: \"Navigation\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"T\"\n        ],\n        description: \"Go to staff management\",\n        category: \"Navigation\"\n    },\n    // Actions\n    {\n        keys: [\n            \"Ctrl\",\n            \"N\"\n        ],\n        description: \"Create new item\",\n        category: \"Actions\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"E\"\n        ],\n        description: \"Export current data\",\n        category: \"Actions\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"A\"\n        ],\n        description: \"Select all items\",\n        category: \"Actions\"\n    },\n    {\n        keys: [\n            \"Delete\"\n        ],\n        description: \"Delete selected items\",\n        category: \"Actions\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"Z\"\n        ],\n        description: \"Undo last action\",\n        category: \"Actions\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"Y\"\n        ],\n        description: \"Redo last action\",\n        category: \"Actions\"\n    },\n    // Interface\n    {\n        keys: [\n            \"Escape\"\n        ],\n        description: \"Close modal/dropdown\",\n        category: \"Interface\"\n    },\n    {\n        keys: [\n            \"Tab\"\n        ],\n        description: \"Navigate between elements\",\n        category: \"Interface\"\n    },\n    {\n        keys: [\n            \"Shift\",\n            \"Tab\"\n        ],\n        description: \"Navigate backwards\",\n        category: \"Interface\"\n    },\n    {\n        keys: [\n            \"Enter\"\n        ],\n        description: \"Confirm action\",\n        category: \"Interface\"\n    },\n    {\n        keys: [\n            \"Space\"\n        ],\n        description: \"Toggle selection\",\n        category: \"Interface\"\n    },\n    // Search & Filters\n    {\n        keys: [\n            \"Ctrl\",\n            \"F\"\n        ],\n        description: \"Focus search field\",\n        category: \"Search & Filters\"\n    },\n    {\n        keys: [\n            \"Ctrl\",\n            \"L\"\n        ],\n        description: \"Clear all filters\",\n        category: \"Search & Filters\"\n    },\n    {\n        keys: [\n            \"↑\",\n            \"↓\"\n        ],\n        description: \"Navigate search results\",\n        category: \"Search & Filters\"\n    },\n    // Help\n    {\n        keys: [\n            \"Ctrl\",\n            \"?\"\n        ],\n        description: \"Show keyboard shortcuts\",\n        category: \"Help\"\n    },\n    {\n        keys: [\n            \"F1\"\n        ],\n        description: \"Open help documentation\",\n        category: \"Help\"\n    }\n];\nfunction KeyboardShortcuts(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) return;\n        const handleKeyDown = (event)=>{\n            if (event.key === \"Escape\") {\n                onClose();\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        isOpen,\n        onClose\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            document.body.style.overflow = \"hidden\";\n        } else {\n            document.body.style.overflow = \"\";\n        }\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isOpen\n    ]);\n    if (!mounted || !isOpen) return null;\n    const categories = [\n        ...new Set(SHORTCUTS.map((s)=>s.category))\n    ];\n    const formatKey = (key)=>{\n        const keyMap = {\n            \"Ctrl\": \"⌘\",\n            \"Alt\": \"⌥\",\n            \"Shift\": \"⇧\",\n            \"Enter\": \"↵\",\n            \"Space\": \"␣\",\n            \"Tab\": \"⇥\",\n            \"Escape\": \"⎋\",\n            \"Delete\": \"⌫\",\n            \"↑\": \"↑\",\n            \"↓\": \"↓\",\n            \"←\": \"←\",\n            \"→\": \"→\"\n        };\n        return keyMap[key] || key;\n    };\n    const modal = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().overlay),\n        onClick: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n            onClick: (e)=>e.stopPropagation(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                            children: \"Keyboard Shortcuts\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeBtn),\n                            \"aria-label\": \"Close shortcuts help\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().content),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().intro),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Use these keyboard shortcuts to navigate and interact with the admin dashboard more efficiently.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().shortcutsGrid),\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().category),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryTitle),\n                                            children: category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().shortcutsList),\n                                            children: SHORTCUTS.filter((shortcut)=>shortcut.category === category).map((shortcut, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().shortcutItem),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().keys),\n                                                            children: shortcut.keys.map((key, keyIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().key),\n                                                                            children: formatKey(key)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                                                            lineNumber: 143,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        keyIndex < shortcut.keys.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().keySeparator),\n                                                                            children: \"+\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                                                            lineNumber: 145,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, keyIndex, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().description),\n                                                            children: shortcut.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, category, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().footer),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().tip),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCA1 Tip:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Most shortcuts work globally throughout the admin dashboard. Press \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                        className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().key),\n                                        children: \"Ctrl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 21\n                                    }, this),\n                                    \" + \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                        className: (_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().key),\n                                        children: \"?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 62\n                                    }, this),\n                                    \" anytime to open this help.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(modal, document.body);\n}\n_s(KeyboardShortcuts, \"1HETueMQFHnQEekrfLFLB/gw0JE=\");\n_c = KeyboardShortcuts;\n// Hook for managing keyboard shortcuts\nfunction useKeyboardShortcuts() {\n    _s1();\n    const [showShortcuts, setShowShortcuts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            var _event_target;\n            // Ignore shortcuts when typing in input fields\n            if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement || event.target instanceof HTMLSelectElement || ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.contentEditable) === \"true\") {\n                return;\n            }\n            // Handle global shortcuts\n            if (event.ctrlKey || event.metaKey) {\n                switch(event.key.toLowerCase()){\n                    case \"?\":\n                        event.preventDefault();\n                        setShowShortcuts(true);\n                        break;\n                    case \"k\":\n                        event.preventDefault();\n                        // Trigger global search\n                        const searchInput = document.querySelector(\"[data-global-search]\");\n                        if (searchInput) {\n                            searchInput.focus();\n                        }\n                        break;\n                    case \"h\":\n                        event.preventDefault();\n                        window.location.href = \"/admin\";\n                        break;\n                    case \"u\":\n                        event.preventDefault();\n                        window.location.href = \"/admin/customers\";\n                        break;\n                    case \"b\":\n                        event.preventDefault();\n                        window.location.href = \"/admin/bookings\";\n                        break;\n                    case \"s\":\n                        event.preventDefault();\n                        window.location.href = \"/admin/services\";\n                        break;\n                    case \"p\":\n                        event.preventDefault();\n                        window.location.href = \"/admin/products\";\n                        break;\n                    case \"i\":\n                        event.preventDefault();\n                        window.location.href = \"/admin/inventory\";\n                        break;\n                    case \"t\":\n                        event.preventDefault();\n                        window.location.href = \"/admin/staff\";\n                        break;\n                    case \"f\":\n                        event.preventDefault();\n                        // Focus search field\n                        const searchField = document.querySelector('input[type=\"search\"], input[placeholder*=\"search\" i]');\n                        if (searchField) {\n                            searchField.focus();\n                        }\n                        break;\n                }\n            }\n            // Handle other shortcuts\n            switch(event.key){\n                case \"F1\":\n                    event.preventDefault();\n                    setShowShortcuts(true);\n                    break;\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, []);\n    return {\n        showShortcuts,\n        setShowShortcuts,\n        openShortcuts: ()=>setShowShortcuts(true),\n        closeShortcuts: ()=>setShowShortcuts(false)\n    };\n}\n_s1(useKeyboardShortcuts, \"09t02XCz6buGMY0SdHV8ZIp9Q08=\");\n// Component to add keyboard shortcuts help button to any page\nfunction KeyboardShortcutsButton(param) {\n    let { className = \"\" } = param;\n    _s2();\n    const { showShortcuts, openShortcuts, closeShortcuts } = useKeyboardShortcuts();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: openShortcuts,\n                className: \"\".concat((_styles_admin_KeyboardShortcuts_module_css__WEBPACK_IMPORTED_MODULE_3___default().helpButton), \" \").concat(className),\n                title: \"Keyboard shortcuts (Ctrl + ?)\",\n                \"aria-label\": \"Show keyboard shortcuts help\",\n                children: \"⌨️\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KeyboardShortcuts, {\n                isOpen: showShortcuts,\n                onClose: closeShortcuts\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\KeyboardShortcuts.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(KeyboardShortcutsButton, \"WCs8ryhKm8+v/4s54UNaVHSJ4Sg=\", false, function() {\n    return [\n        useKeyboardShortcuts\n    ];\n});\n_c1 = KeyboardShortcutsButton;\nvar _c, _c1;\n$RefreshReg$(_c, \"KeyboardShortcuts\");\n$RefreshReg$(_c1, \"KeyboardShortcutsButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/admin/KeyboardShortcuts.tsx\n"));

/***/ })

});