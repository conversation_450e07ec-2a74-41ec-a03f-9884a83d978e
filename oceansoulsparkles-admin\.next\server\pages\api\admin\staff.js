"use strict";(()=>{var e={};e.id=2010,e.ids=[2010],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},241:(e,t,s)=>{s.r(t),s.d(t,{config:()=>f,default:()=>u,routeModule:()=>c});var a={};s.r(a),s.d(a,{default:()=>m});var r=s(1802),i=s(7153),o=s(8781),n=s(8456),l=s(7474);async function m(e,t){let s=Math.random().toString(36).substring(2,8);console.log(`[${s}] Staff API called - ${e.method}`);try{let{user:a,error:r}=await (0,l.ZQ)(e);if(r||!a)return t.status(401).json({error:"Authentication required",message:r?.message||"Authentication failed",requestId:s});if(!["DEV","Admin"].includes(a.role))return t.status(403).json({error:"Access denied",message:"You do not have permission to manage staff",requestId:s});if("GET"===e.method)try{let{data:e,error:a}=await n.pR.from("admin_users").select(`
            id,
            first_name,
            last_name,
            email,
            role,
            status,
            last_login,
            created_at,
            updated_at
          `).order("created_at",{ascending:!1});if(a){console.error(`[${s}] Database error:`,a);let e=[{id:"1",firstName:"Admin",lastName:"User",email:"<EMAIL>",role:"Admin",status:"active",lastLogin:new Date().toISOString(),createdAt:"2024-01-01T00:00:00Z"},{id:"2",firstName:"Sarah",lastName:"Johnson",email:"<EMAIL>",role:"Artist",status:"active",lastLogin:new Date(Date.now()-864e5).toISOString(),createdAt:"2024-02-15T00:00:00Z"},{id:"3",firstName:"Maya",lastName:"Patel",email:"<EMAIL>",role:"Braider",status:"active",lastLogin:new Date(Date.now()-1728e5).toISOString(),createdAt:"2024-03-10T00:00:00Z"}];return t.status(200).json({staff:e,source:"mock",message:"Using mock data - database connection issue",requestId:s})}let r=e.map(e=>({id:e.id,firstName:e.first_name,lastName:e.last_name,email:e.email,role:e.role,status:e.status,lastLogin:e.last_login,createdAt:e.created_at}));return t.status(200).json({staff:r,source:"database",requestId:s})}catch(e){return console.error(`[${s}] Error fetching staff:`,e),t.status(500).json({error:"Failed to fetch staff",message:e.message,requestId:s})}else if("POST"===e.method){let{firstName:a,lastName:r,email:i,role:o,password:l}=e.body;if(!a||!r||!i||!o)return t.status(400).json({error:"Missing required fields",message:"firstName, lastName, email, and role are required",requestId:s});try{let{data:e}=await n.pR.from("admin_users").select("id").eq("email",i).single();if(e)return t.status(409).json({error:"Email already exists",message:"A staff member with this email already exists",requestId:s});let{data:m,error:u}=await n.pR.from("admin_users").insert([{first_name:a,last_name:r,email:i,role:o,status:"active",password_hash:l?await d(l):null,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]).select().single();if(u)return console.error(`[${s}] Error creating staff:`,u),t.status(500).json({error:"Failed to create staff member",message:u.message,requestId:s});let f={id:m.id,firstName:m.first_name,lastName:m.last_name,email:m.email,role:m.role,status:m.status,createdAt:m.created_at};return t.status(201).json({staff:f,message:"Staff member created successfully",requestId:s})}catch(e){return console.error(`[${s}] Error creating staff:`,e),t.status(500).json({error:"Failed to create staff member",message:e.message,requestId:s})}}else if("PUT"===e.method){let{id:a,firstName:r,lastName:i,email:o,role:l,status:m}=e.body;if(!a)return t.status(400).json({error:"Missing staff ID",message:"Staff ID is required for updates",requestId:s});try{let{data:e,error:d}=await n.pR.from("admin_users").update({first_name:r,last_name:i,email:o,role:l,status:m,updated_at:new Date().toISOString()}).eq("id",a).select().single();if(d)return console.error(`[${s}] Error updating staff:`,d),t.status(500).json({error:"Failed to update staff member",message:d.message,requestId:s});let u={id:e.id,firstName:e.first_name,lastName:e.last_name,email:e.email,role:e.role,status:e.status,lastLogin:e.last_login,createdAt:e.created_at};return t.status(200).json({staff:u,message:"Staff member updated successfully",requestId:s})}catch(e){return console.error(`[${s}] Error updating staff:`,e),t.status(500).json({error:"Failed to update staff member",message:e.message,requestId:s})}}else{if("DELETE"!==e.method)return t.status(405).json({error:"Method not allowed",message:"Only GET, POST, PUT, and DELETE methods are supported",requestId:s});let{id:a}=e.query;if(!a)return t.status(400).json({error:"Missing staff ID",message:"Staff ID is required for deletion",requestId:s});try{let{error:e}=await n.pR.from("admin_users").delete().eq("id",a);if(e)return console.error(`[${s}] Error deleting staff:`,e),t.status(500).json({error:"Failed to delete staff member",message:e.message,requestId:s});return t.status(200).json({message:"Staff member deleted successfully",requestId:s})}catch(e){return console.error(`[${s}] Error deleting staff:`,e),t.status(500).json({error:"Failed to delete staff member",message:e.message,requestId:s})}}}catch(e){return console.error(`[${s}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred",requestId:s})}}async function d(e){return`hashed_${e}_${Date.now()}`}let u=(0,o.l)(a,"default"),f=(0,o.l)(a,"config"),c=new r.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/staff",pathname:"/api/admin/staff",bundlePath:"",filename:""},userland:a})},8456:(e,t,s)=>{s.d(t,{pR:()=>n});var a=s(2885);let r="https://ndlgbcsbidyhxbpqzgqp.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",o=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!r||!i)throw Error("Missing Supabase environment variables");(0,a.createClient)(r,i,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});let n=(0,a.createClient)(r,o||i,{auth:{autoRefreshToken:!1,persistSession:!1}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2805],()=>s(241));module.exports=a})();