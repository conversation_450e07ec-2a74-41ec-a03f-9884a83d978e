import styles from '../../styles/admin/DashboardStats.module.css';

interface DashboardStatsProps {
  data?: {
    totalBookings: number;
    totalRevenue: number;
    activeCustomers: number;
    pendingBookings: number;
    completedBookings: number;
    cancelledBookings: number;
    averageBookingValue: number;
    monthlyGrowth: number;
  };
}

export default function DashboardStats({ data }: DashboardStatsProps) {
  const stats = data || {
    totalBookings: 0,
    totalRevenue: 0,
    activeCustomers: 0,
    pendingBookings: 0,
    completedBookings: 0,
    cancelledBookings: 0,
    averageBookingValue: 0,
    monthlyGrowth: 0
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(1)}%`;
  };

  const getGrowthColor = (value: number) => {
    if (value > 0) return '#28a745';
    if (value < 0) return '#dc3545';
    return '#6c757d';
  };

  return (
    <div className={styles.statsContainer}>
      <h2 className={styles.sectionTitle}>Overview</h2>
      
      <div className={styles.statsGrid}>
        {/* Total Revenue */}
        <div className={styles.statCard}>
          <div className={styles.statHeader}>
            <div className={styles.statIcon}>💰</div>
            <div className={styles.statTrend} style={{ color: getGrowthColor(stats.monthlyGrowth) }}>
              {formatPercentage(stats.monthlyGrowth)}
            </div>
          </div>
          <div className={styles.statValue}>
            {formatCurrency(stats.totalRevenue)}
          </div>
          <div className={styles.statLabel}>Total Revenue</div>
          <div className={styles.statSubtext}>This month</div>
        </div>

        {/* Total Bookings */}
        <div className={styles.statCard}>
          <div className={styles.statHeader}>
            <div className={styles.statIcon}>📅</div>
            <div className={styles.statBadge}>
              {stats.pendingBookings} pending
            </div>
          </div>
          <div className={styles.statValue}>
            {stats.totalBookings.toLocaleString()}
          </div>
          <div className={styles.statLabel}>Total Bookings</div>
          <div className={styles.statSubtext}>All time</div>
        </div>

        {/* Active Customers */}
        <div className={styles.statCard}>
          <div className={styles.statHeader}>
            <div className={styles.statIcon}>👥</div>
            <div className={styles.statIndicator}>
              <div className={styles.statusDot}></div>
              Active
            </div>
          </div>
          <div className={styles.statValue}>
            {stats.activeCustomers.toLocaleString()}
          </div>
          <div className={styles.statLabel}>Active Customers</div>
          <div className={styles.statSubtext}>Last 30 days</div>
        </div>

        {/* Average Booking Value */}
        <div className={styles.statCard}>
          <div className={styles.statHeader}>
            <div className={styles.statIcon}>💎</div>
            <div className={styles.statTrend} style={{ color: getGrowthColor(5.2) }}>
              +5.2%
            </div>
          </div>
          <div className={styles.statValue}>
            {formatCurrency(stats.averageBookingValue)}
          </div>
          <div className={styles.statLabel}>Avg. Booking Value</div>
          <div className={styles.statSubtext}>This month</div>
        </div>
      </div>

      {/* Booking Status Breakdown */}
      <div className={styles.breakdownSection}>
        <h3 className={styles.breakdownTitle}>Booking Status</h3>
        <div className={styles.breakdownGrid}>
          <div className={styles.breakdownCard}>
            <div className={styles.breakdownIcon} style={{ background: '#28a745' }}>
              ✓
            </div>
            <div className={styles.breakdownContent}>
              <div className={styles.breakdownValue}>{stats.completedBookings}</div>
              <div className={styles.breakdownLabel}>Completed</div>
            </div>
          </div>

          <div className={styles.breakdownCard}>
            <div className={styles.breakdownIcon} style={{ background: '#ffc107' }}>
              ⏳
            </div>
            <div className={styles.breakdownContent}>
              <div className={styles.breakdownValue}>{stats.pendingBookings}</div>
              <div className={styles.breakdownLabel}>Pending</div>
            </div>
          </div>

          <div className={styles.breakdownCard}>
            <div className={styles.breakdownIcon} style={{ background: '#dc3545' }}>
              ✕
            </div>
            <div className={styles.breakdownContent}>
              <div className={styles.breakdownValue}>{stats.cancelledBookings}</div>
              <div className={styles.breakdownLabel}>Cancelled</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
