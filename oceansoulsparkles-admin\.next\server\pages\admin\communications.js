(()=>{var e={};e.id=2020,e.ids=[2020,660],e.modules={2915:e=>{e.exports={communicationsContainer:"Communications_communicationsContainer__aHz1C",header:"Communications_header__ejq3m",headerLeft:"Communications_headerLeft___8s_R",title:"Communications_title__jl2CO",subtitle:"Communications_subtitle__vUGOx",headerStats:"Communications_headerStats__MOftm",statCard:"Communications_statCard__KDwp_",statNumber:"Communications_statNumber__dk36K",statLabel:"Communications_statLabel__eEf9C",errorMessage:"Communications_errorMessage__qlq31",closeError:"Communications_closeError__9yIHC",filters:"Communications_filters__Ri6Fb",filterGroup:"Communications_filterGroup__nRf6K",filterSelect:"Communications_filterSelect__D_gqd",communicationsTable:"Communications_communicationsTable__pWwnJ",tableContainer:"Communications_tableContainer__7gc1n",table:"Communications_table__M1B0I",tableRow:"Communications_tableRow__7MKN2",typeCell:"Communications_typeCell__MvYPd",typeIcon:"Communications_typeIcon__ewlRy",typeText:"Communications_typeText__wLuP8",customerCell:"Communications_customerCell__LhHOI",customerName:"Communications_customerName__43M2R",bookingInfo:"Communications_bookingInfo__ai7VQ",recipientCell:"Communications_recipientCell__LQj3N",subjectCell:"Communications_subjectCell__sNdbA",subject:"Communications_subject__wN0ME",templateInfo:"Communications_templateInfo__dzMYB",statusBadge:"Communications_statusBadge__OtuNI",errorInfo:"Communications_errorInfo__ORcDp",dateCell:"Communications_dateCell__koZSm",emptyState:"Communications_emptyState__VuFX3",pagination:"Communications_pagination__bDH_p",paginationBtn:"Communications_paginationBtn__Idj1b",paginationInfo:"Communications_paginationInfo__mBfeL",loadingContainer:"Communications_loadingContainer__4e5EQ",loadingSpinner:"Communications_loadingSpinner__aikXM",spin:"Communications_spin__hzzLj"}},5277:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>x,default:()=>d,getServerSideProps:()=>h,getStaticPaths:()=>p,getStaticProps:()=>_,reportWebVitals:()=>C,routeModule:()=>S,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>g,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>j});var i=s(7093),n=s(5244),l=s(1323),r=s(2899),o=s.n(r),c=s(6814),m=s(6550),u=e([c,m]);[c,m]=u.then?(await u)():u;let d=(0,l.l)(m,"default"),_=(0,l.l)(m,"getStaticProps"),p=(0,l.l)(m,"getStaticPaths"),h=(0,l.l)(m,"getServerSideProps"),x=(0,l.l)(m,"config"),C=(0,l.l)(m,"reportWebVitals"),j=(0,l.l)(m,"unstable_getStaticProps"),b=(0,l.l)(m,"unstable_getStaticPaths"),g=(0,l.l)(m,"unstable_getStaticParams"),v=(0,l.l)(m,"unstable_getServerProps"),N=(0,l.l)(m,"unstable_getServerSideProps"),S=new i.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/communications",pathname:"/admin/communications",bundlePath:"",filename:""},components:{App:c.default,Document:o()},userland:m});a()}catch(e){a(e)}})},6550:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>_});var i=s(997),n=s(6689),l=s(968),r=s.n(l),o=s(8568),c=s(4845),m=s(2915),u=s.n(m),d=e([c]);c=(d.then?(await d)():d)[0];let p=[{value:"all",label:"All Types"},{value:"email",label:"Email"},{value:"sms",label:"SMS"}],h=[{value:"all",label:"All Statuses"},{value:"pending",label:"Pending"},{value:"sent",label:"Sent"},{value:"delivered",label:"Delivered"},{value:"opened",label:"Opened"},{value:"failed",label:"Failed"}];function _(){let{user:e}=(0,o.a)(),[t,s]=(0,n.useState)([]),[a,l]=(0,n.useState)(!0),[m,d]=(0,n.useState)(null),[_,x]=(0,n.useState)("all"),[C,j]=(0,n.useState)("all"),[b,g]=(0,n.useState)(1),[v,N]=(0,n.useState)(0),[S]=(0,n.useState)(25),f=e=>new Date(e).toLocaleString("en-AU",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),y=e=>{switch(e){case"sent":return"#10b981";case"delivered":return"#059669";case"opened":return"#047857";case"failed":return"#ef4444";case"pending":return"#f59e0b";default:return"#6b7280"}},P=e=>"email"===e?"\uD83D\uDCE7":"\uD83D\uDCF1",I=Math.ceil(v/S);return a&&0===t.length?i.jsx(c.Z,{children:(0,i.jsxs)("div",{className:u().loadingContainer,children:[i.jsx("div",{className:u().loadingSpinner}),i.jsx("p",{children:"Loading communications..."})]})}):(0,i.jsxs)(c.Z,{children:[(0,i.jsxs)(r(),{children:[i.jsx("title",{children:"Customer Communications | Ocean Soul Sparkles Admin"}),i.jsx("meta",{name:"description",content:"View and manage customer communications history"})]}),(0,i.jsxs)("div",{className:u().communicationsContainer,children:[(0,i.jsxs)("header",{className:u().header,children:[(0,i.jsxs)("div",{className:u().headerLeft,children:[i.jsx("h1",{className:u().title,children:"Customer Communications"}),i.jsx("p",{className:u().subtitle,children:"View and track all customer communications"})]}),i.jsx("div",{className:u().headerStats,children:(0,i.jsxs)("div",{className:u().statCard,children:[i.jsx("span",{className:u().statNumber,children:v}),i.jsx("span",{className:u().statLabel,children:"Total Communications"})]})})]}),m&&(0,i.jsxs)("div",{className:u().errorMessage,children:[m,i.jsx("button",{onClick:()=>d(null),className:u().closeError,children:"\xd7"})]}),(0,i.jsxs)("div",{className:u().filters,children:[(0,i.jsxs)("div",{className:u().filterGroup,children:[i.jsx("label",{htmlFor:"typeFilter",children:"Type:"}),i.jsx("select",{id:"typeFilter",value:_,onChange:e=>{x(e.target.value),g(1)},className:u().filterSelect,children:p.map(e=>i.jsx("option",{value:e.value,children:e.label},e.value))})]}),(0,i.jsxs)("div",{className:u().filterGroup,children:[i.jsx("label",{htmlFor:"statusFilter",children:"Status:"}),i.jsx("select",{id:"statusFilter",value:C,onChange:e=>{j(e.target.value),g(1)},className:u().filterSelect,children:h.map(e=>i.jsx("option",{value:e.value,children:e.label},e.value))})]})]}),i.jsx("div",{className:u().communicationsTable,children:0===t.length?(0,i.jsxs)("div",{className:u().emptyState,children:[i.jsx("h3",{children:"No communications found"}),i.jsx("p",{children:"No customer communications match your current filters."})]}):i.jsx("div",{className:u().tableContainer,children:(0,i.jsxs)("table",{className:u().table,children:[i.jsx("thead",{children:(0,i.jsxs)("tr",{children:[i.jsx("th",{children:"Type"}),i.jsx("th",{children:"Customer"}),i.jsx("th",{children:"Recipient"}),i.jsx("th",{children:"Subject/Template"}),i.jsx("th",{children:"Status"}),i.jsx("th",{children:"Sent"}),i.jsx("th",{children:"Delivered"})]})}),i.jsx("tbody",{children:t.map(e=>(0,i.jsxs)("tr",{className:u().tableRow,children:[i.jsx("td",{children:(0,i.jsxs)("div",{className:u().typeCell,children:[i.jsx("span",{className:u().typeIcon,children:P(e.communication_type)}),i.jsx("span",{className:u().typeText,children:e.communication_type.toUpperCase()})]})}),i.jsx("td",{children:(0,i.jsxs)("div",{className:u().customerCell,children:[(0,i.jsxs)("span",{className:u().customerName,children:[e.customers.first_name," ",e.customers.last_name]}),e.bookings&&(0,i.jsxs)("span",{className:u().bookingInfo,children:[e.bookings.services.name," - ",f(e.bookings.start_time)]})]})}),i.jsx("td",{className:u().recipientCell,children:e.recipient}),i.jsx("td",{children:(0,i.jsxs)("div",{className:u().subjectCell,children:[e.subject&&i.jsx("div",{className:u().subject,children:e.subject}),e.email_templates&&(0,i.jsxs)("div",{className:u().templateInfo,children:[e.email_templates.name," (",e.email_templates.type,")"]})]})}),(0,i.jsxs)("td",{children:[i.jsx("span",{className:u().statusBadge,style:{backgroundColor:y(e.status)},children:e.status.toUpperCase()}),e.error_message&&i.jsx("div",{className:u().errorInfo,title:e.error_message,children:"⚠️ Error"})]}),i.jsx("td",{className:u().dateCell,children:e.sent_at?f(e.sent_at):"-"}),i.jsx("td",{className:u().dateCell,children:e.delivered_at?f(e.delivered_at):"-"})]},e.id))})]})})}),I>1&&(0,i.jsxs)("div",{className:u().pagination,children:[i.jsx("button",{onClick:()=>g(e=>Math.max(1,e-1)),disabled:1===b,className:u().paginationBtn,children:"Previous"}),(0,i.jsxs)("span",{className:u().paginationInfo,children:["Page ",b," of ",I," (",v," total)"]}),i.jsx("button",{onClick:()=>g(e=>Math.min(I,e+1)),disabled:b===I,className:u().paginationBtn,children:"Next"})]})]})]})}a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2899,6212,1664,7441],()=>s(5277));module.exports=a})();