/**
 * Ocean Soul Sparkles Admin - Mobile Bottom Navigation Component
 * Mobile-optimized bottom navigation for quick access to main admin sections
 */

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { HapticFeedback } from '../../../lib/gestures/swipe-handler';
import styles from '../../../styles/admin/mobile/MobileBottomNav.module.css';

interface MobileBottomNavProps {
  userRole: string;
}

interface NavItem {
  id: string;
  label: string;
  icon: string;
  href: string;
  roles: string[];
}

export default function MobileBottomNav({ userRole }: MobileBottomNavProps) {
  const router = useRouter();

  const navItems: NavItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      href: '/admin/dashboard',
      roles: ['Admin', 'Manager', 'Staff']
    },
    {
      id: 'pos',
      label: 'POS',
      icon: '💳',
      href: '/admin/pos',
      roles: ['Admin', 'Manager', 'Staff']
    },
    {
      id: 'bookings',
      label: 'Bookings',
      icon: '📅',
      href: '/admin/bookings',
      roles: ['Admin', 'Manager', 'Staff']
    },
    {
      id: 'customers',
      label: 'Customers',
      icon: '👥',
      href: '/admin/customers',
      roles: ['Admin', 'Manager', 'Staff']
    },
    {
      id: 'more',
      label: 'More',
      icon: '⋯',
      href: '/admin/menu',
      roles: ['Admin', 'Manager', 'Staff']
    }
  ];

  const handleNavClick = (item: NavItem) => {
    HapticFeedback.light();
    
    // Don't navigate if already on the page
    if (router.pathname === item.href) {
      return;
    }
  };

  const isActive = (href: string): boolean => {
    if (href === '/admin/dashboard') {
      return router.pathname === '/admin/dashboard' || router.pathname === '/admin';
    }
    return router.pathname.startsWith(href);
  };

  const filteredNavItems = navItems.filter(item => 
    item.roles.includes(userRole)
  );

  return (
    <nav className={styles.mobileBottomNav}>
      <div className={styles.navContainer}>
        {filteredNavItems.map((item) => (
          <Link
            key={item.id}
            href={item.href}
            className={`${styles.navItem} ${isActive(item.href) ? styles.active : ''}`}
            onClick={() => handleNavClick(item)}
          >
            <div className={styles.navIcon}>
              {item.icon}
            </div>
            <span className={styles.navLabel}>
              {item.label}
            </span>
            {isActive(item.href) && (
              <div className={styles.activeIndicator} />
            )}
          </Link>
        ))}
      </div>
      
      {/* Safe area padding for devices with home indicators */}
      <div className={styles.safeAreaPadding} />
    </nav>
  );
}
