(()=>{var e={};e.id=1554,e.ids=[1554,660],e.modules={1242:e=>{e.exports={purchaseOrders:"PurchaseOrders_purchaseOrders__nd7hq",header:"PurchaseOrders_header__w_l_v",headerContent:"PurchaseOrders_headerContent__PVf2q",title:"PurchaseOrders_title__utnll",subtitle:"PurchaseOrders_subtitle__1wcrJ",headerActions:"PurchaseOrders_headerActions__SQeEm",addBtn:"PurchaseOrders_addBtn__KPfpC",controls:"PurchaseOrders_controls__9p8X2",searchSection:"PurchaseOrders_searchSection__I6PTA",searchInput:"PurchaseOrders_searchInput__R_JTF",filters:"PurchaseOrders_filters__b5Bor",filterSelect:"PurchaseOrders_filterSelect__R7jx2",sortSelect:"PurchaseOrders_sortSelect__L3GvC",ordersContainer:"PurchaseOrders_ordersContainer__5dBmg",loading:"PurchaseOrders_loading__Fmcxh",loadingSpinner:"PurchaseOrders_loadingSpinner__KO_Gz",spin:"PurchaseOrders_spin__HjZwU",emptyState:"PurchaseOrders_emptyState__O5SNT",emptyIcon:"PurchaseOrders_emptyIcon__JM1LS",addFirstBtn:"PurchaseOrders_addFirstBtn__vMTvw",ordersGrid:"PurchaseOrders_ordersGrid__RZktU",orderCard:"PurchaseOrders_orderCard__eJr_j",cardHeader:"PurchaseOrders_cardHeader__kKvK9",orderInfo:"PurchaseOrders_orderInfo__7lOLc",poNumber:"PurchaseOrders_poNumber___XsZw",statusBadge:"PurchaseOrders_statusBadge__40_vf",statusDraft:"PurchaseOrders_statusDraft__nhguh",statusSent:"PurchaseOrders_statusSent__Y5Ryb",statusConfirmed:"PurchaseOrders_statusConfirmed__wp_Rp",statusReceived:"PurchaseOrders_statusReceived__Z_SG3",statusCancelled:"PurchaseOrders_statusCancelled__BYw7u",cardBody:"PurchaseOrders_cardBody__JNNls",supplierInfo:"PurchaseOrders_supplierInfo__GTMI5",orderDetails:"PurchaseOrders_orderDetails__jguk6",detailItem:"PurchaseOrders_detailItem__emrhK",label:"PurchaseOrders_label__acljc",value:"PurchaseOrders_value__Trteb",notes:"PurchaseOrders_notes__klfoF",notesText:"PurchaseOrders_notesText__xxhwJ",cardFooter:"PurchaseOrders_cardFooter__iRQ7M",cardActions:"PurchaseOrders_cardActions__VMaHv",viewBtn:"PurchaseOrders_viewBtn__QBUc6",editBtn:"PurchaseOrders_editBtn__l0Pkx",receiveBtn:"PurchaseOrders_receiveBtn__qOMk6",deleteBtn:"PurchaseOrders_deleteBtn__5Sgw6",cardMeta:"PurchaseOrders_cardMeta__G_4c6",createdBy:"PurchaseOrders_createdBy__EuC7t",createdDate:"PurchaseOrders_createdDate__W_8eI",purchaseOrderForm:"PurchaseOrders_purchaseOrderForm__Jo6eJ",formSection:"PurchaseOrders_formSection__PPc2x",sectionHeader:"PurchaseOrders_sectionHeader__GOGWN",addItemBtn:"PurchaseOrders_addItemBtn__Xw7Of",formGrid:"PurchaseOrders_formGrid__6K2p_",formGroup:"PurchaseOrders_formGroup__LyNDG",fullWidth:"PurchaseOrders_fullWidth__3t1vV",formLabel:"PurchaseOrders_formLabel__k12f1",formInput:"PurchaseOrders_formInput__fCTM_",formTextarea:"PurchaseOrders_formTextarea__pvtWy",formSelect:"PurchaseOrders_formSelect__j_xYw",emptyItems:"PurchaseOrders_emptyItems__kHx1F",itemsList:"PurchaseOrders_itemsList__7hKjk",itemRow:"PurchaseOrders_itemRow__qpnoC",itemFields:"PurchaseOrders_itemFields__5hnFe",totalCost:"PurchaseOrders_totalCost__5x9Ve",removeItemBtn:"PurchaseOrders_removeItemBtn__I3BZk",orderSummary:"PurchaseOrders_orderSummary__ZpQ7a",summaryRow:"PurchaseOrders_summaryRow__bgIZv",totalRow:"PurchaseOrders_totalRow__tlYfR",formActions:"PurchaseOrders_formActions__BL5KV",saveBtn:"PurchaseOrders_saveBtn__mjzET",cancelBtn:"PurchaseOrders_cancelBtn__J1l9Y"}},2301:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{config:()=>f,default:()=>h,getServerSideProps:()=>p,getStaticPaths:()=>m,getStaticProps:()=>_,reportWebVitals:()=>x,routeModule:()=>y,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>O,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>j});var a=s(7093),d=s(5244),c=s(1323),n=s(2899),i=s.n(n),o=s(6814),l=s(2164),u=e([o,l]);[o,l]=u.then?(await u)():u;let h=(0,c.l)(l,"default"),_=(0,c.l)(l,"getStaticProps"),m=(0,c.l)(l,"getStaticPaths"),p=(0,c.l)(l,"getServerSideProps"),f=(0,c.l)(l,"config"),x=(0,c.l)(l,"reportWebVitals"),j=(0,c.l)(l,"unstable_getStaticProps"),g=(0,c.l)(l,"unstable_getStaticPaths"),P=(0,c.l)(l,"unstable_getStaticParams"),v=(0,c.l)(l,"unstable_getServerProps"),O=(0,c.l)(l,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:d.x.PAGES,page:"/admin/purchase-orders",pathname:"/admin/purchase-orders",bundlePath:"",filename:""},components:{App:o.default,Document:i()},userland:l});t()}catch(e){t(e)}})},5309:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{Z:()=>h});var a=s(997),d=s(6689),c=s(1664),n=s.n(c),i=s(3590),o=s(1242),l=s.n(o),u=e([i]);function h({initialPurchaseOrders:e=[]}){let[r,s]=(0,d.useState)(e),[t,c]=(0,d.useState)(e),[o,u]=(0,d.useState)(!1),[h,_]=(0,d.useState)(""),[m,p]=(0,d.useState)("all"),[f,x]=(0,d.useState)("order_date"),[j,g]=(0,d.useState)("desc"),P=async()=>{u(!0);try{let e=localStorage.getItem("adminToken"),r=await fetch("/api/admin/purchase-orders",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!r.ok)throw Error("Failed to fetch purchase orders");let t=await r.json();s(t.purchaseOrders||[])}catch(e){console.error("Error fetching purchase orders:",e),i.toast.error("Failed to load purchase orders")}finally{u(!1)}},v=async(e,r)=>{if(confirm(`Are you sure you want to delete purchase order "${r}"? This action cannot be undone.`))try{let r=localStorage.getItem("adminToken"),s=await fetch(`/api/admin/purchase-orders/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to delete purchase order")}i.toast.success("Purchase order deleted successfully"),P()}catch(e){console.error("Error deleting purchase order:",e),i.toast.error(e instanceof Error?e.message:"Failed to delete purchase order")}},O=e=>{switch(e){case"draft":default:return l().statusDraft;case"sent":return l().statusSent;case"confirmed":return l().statusConfirmed;case"received":return l().statusReceived;case"cancelled":return l().statusCancelled}},y=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),N=e=>new Date(e).toLocaleDateString("en-AU");return(0,a.jsxs)("div",{className:l().purchaseOrders,children:[(0,a.jsxs)("div",{className:l().header,children:[(0,a.jsxs)("div",{className:l().headerContent,children:[a.jsx("h1",{className:l().title,children:"Purchase Orders"}),a.jsx("p",{className:l().subtitle,children:"Manage purchase orders and supplier deliveries"})]}),a.jsx("div",{className:l().headerActions,children:a.jsx(n(),{href:"/admin/purchase-orders/new",className:l().addBtn,children:"+ Create Purchase Order"})})]}),(0,a.jsxs)("div",{className:l().controls,children:[a.jsx("div",{className:l().searchSection,children:a.jsx("input",{type:"text",placeholder:"Search by PO number, supplier, or notes...",value:h,onChange:e=>_(e.target.value),className:l().searchInput})}),(0,a.jsxs)("div",{className:l().filters,children:[(0,a.jsxs)("select",{value:m,onChange:e=>p(e.target.value),className:l().filterSelect,children:[a.jsx("option",{value:"all",children:"All Statuses"}),a.jsx("option",{value:"draft",children:"Draft"}),a.jsx("option",{value:"sent",children:"Sent"}),a.jsx("option",{value:"confirmed",children:"Confirmed"}),a.jsx("option",{value:"received",children:"Received"}),a.jsx("option",{value:"cancelled",children:"Cancelled"})]}),(0,a.jsxs)("select",{value:`${f}-${j}`,onChange:e=>{let[r,s]=e.target.value.split("-");x(r),g(s)},className:l().sortSelect,children:[a.jsx("option",{value:"order_date-desc",children:"Order Date (Newest)"}),a.jsx("option",{value:"order_date-asc",children:"Order Date (Oldest)"}),a.jsx("option",{value:"po_number-asc",children:"PO Number (A-Z)"}),a.jsx("option",{value:"po_number-desc",children:"PO Number (Z-A)"}),a.jsx("option",{value:"supplier_name-asc",children:"Supplier (A-Z)"}),a.jsx("option",{value:"supplier_name-desc",children:"Supplier (Z-A)"}),a.jsx("option",{value:"total_amount-desc",children:"Amount (High-Low)"}),a.jsx("option",{value:"total_amount-asc",children:"Amount (Low-High)"})]})]})]}),a.jsx("div",{className:l().ordersContainer,children:o?(0,a.jsxs)("div",{className:l().loading,children:[a.jsx("div",{className:l().loadingSpinner}),a.jsx("p",{children:"Loading purchase orders..."})]}):0===t.length?(0,a.jsxs)("div",{className:l().emptyState,children:[a.jsx("div",{className:l().emptyIcon,children:"\uD83D\uDCCB"}),a.jsx("h3",{children:"No purchase orders found"}),a.jsx("p",{children:h||"all"!==m?"Try adjusting your search or filters":"Get started by creating your first purchase order"}),!h&&"all"===m&&a.jsx(n(),{href:"/admin/purchase-orders/new",className:l().addFirstBtn,children:"Create First Purchase Order"})]}):a.jsx("div",{className:l().ordersGrid,children:t.map(e=>(0,a.jsxs)("div",{className:l().orderCard,children:[a.jsx("div",{className:l().cardHeader,children:(0,a.jsxs)("div",{className:l().orderInfo,children:[a.jsx("h3",{className:l().poNumber,children:e.po_number}),a.jsx("span",{className:`${l().statusBadge} ${O(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,a.jsxs)("div",{className:l().cardBody,children:[(0,a.jsxs)("div",{className:l().supplierInfo,children:[a.jsx("span",{className:l().label,children:"Supplier:"}),a.jsx("span",{className:l().value,children:e.suppliers.name})]}),(0,a.jsxs)("div",{className:l().orderDetails,children:[(0,a.jsxs)("div",{className:l().detailItem,children:[a.jsx("span",{className:l().label,children:"Order Date:"}),a.jsx("span",{className:l().value,children:N(e.order_date)})]}),e.expected_delivery_date&&(0,a.jsxs)("div",{className:l().detailItem,children:[a.jsx("span",{className:l().label,children:"Expected Delivery:"}),a.jsx("span",{className:l().value,children:N(e.expected_delivery_date)})]}),(0,a.jsxs)("div",{className:l().detailItem,children:[a.jsx("span",{className:l().label,children:"Total Amount:"}),a.jsx("span",{className:l().value,children:y(e.total_amount)})]})]}),e.notes&&(0,a.jsxs)("div",{className:l().notes,children:[a.jsx("span",{className:l().label,children:"Notes:"}),a.jsx("p",{className:l().notesText,children:e.notes})]})]}),(0,a.jsxs)("div",{className:l().cardFooter,children:[(0,a.jsxs)("div",{className:l().cardActions,children:[a.jsx(n(),{href:`/admin/purchase-orders/${e.id}`,className:l().viewBtn,children:"View Details"}),"draft"===e.status&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(n(),{href:`/admin/purchase-orders/${e.id}/edit`,className:l().editBtn,children:"Edit"}),a.jsx("button",{onClick:()=>v(e.id,e.po_number),className:l().deleteBtn,children:"Delete"})]}),["confirmed","sent"].includes(e.status)&&a.jsx(n(),{href:`/admin/purchase-orders/${e.id}/receive`,className:l().receiveBtn,children:"Receive Items"})]}),(0,a.jsxs)("div",{className:l().cardMeta,children:[(0,a.jsxs)("span",{className:l().createdBy,children:["Created by ",e.admin_users.first_name," ",e.admin_users.last_name]}),a.jsx("span",{className:l().createdDate,children:N(e.created_at)})]})]})]},e.id))})})]})}i=(u.then?(await u)():u)[0],t()}catch(e){t(e)}})},2164:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>m});var a=s(997),d=s(9816),c=s.n(d),n=s(6689),i=s(968),o=s.n(i),l=s(8568),u=s(4845),h=s(5309),_=e([u,h]);function m(){let{user:e,loading:r}=(0,l.a)(),[s,t]=(0,n.useState)(!0),[d,i]=(0,n.useState)([]),[_,m]=(0,n.useState)(null),p=async()=>{t(!0),m(null);try{let e=localStorage.getItem("adminToken");if(!e)throw Error("No authentication token found");let r=await fetch("/api/admin/purchase-orders",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!r.ok){if(401===r.status)throw Error("Authentication failed. Please log in again.");if(403===r.status)throw Error("You do not have permission to access purchase orders.");throw Error(`Failed to fetch purchase orders: ${r.status}`)}let s=await r.json();i(s.purchaseOrders||[])}catch(e){console.error("Error fetching purchase orders:",e),m(e.message)}finally{t(!1)}};return r?a.jsx(u.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("div",{style:{width:"32px",height:"32px",border:"3px solid #e2e8f0",borderTop:"3px solid #667eea",borderRadius:"50%",animation:"spin 1s linear infinite"}}),a.jsx("p",{style:{color:"#64748b"},children:"Authenticating..."})]})}):e?["DEV","Admin"].includes(e.role)?(0,a.jsxs)(u.Z,{children:[(0,a.jsxs)(o(),{children:[a.jsx("title",{className:"jsx-ff161281ed666c63",children:"Purchase Orders | Ocean Soul Sparkles Admin"}),a.jsx("meta",{name:"description",content:"Manage purchase orders and supplier deliveries",className:"jsx-ff161281ed666c63"})]}),_?(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},className:"jsx-ff161281ed666c63",children:[a.jsx("h2",{style:{color:"#ef4444"},className:"jsx-ff161281ed666c63",children:"Error Loading Purchase Orders"}),a.jsx("p",{style:{color:"#64748b"},className:"jsx-ff161281ed666c63",children:_}),a.jsx("button",{onClick:p,style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",border:"none",padding:"0.75rem 1.5rem",borderRadius:"8px",fontWeight:"600",cursor:"pointer"},className:"jsx-ff161281ed666c63",children:"Try Again"})]}):a.jsx(h.Z,{initialPurchaseOrders:d}),a.jsx(c(),{id:"ff161281ed666c63",children:"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]}):a.jsx(u.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("h2",{style:{color:"#ef4444"},children:"Access Denied"}),a.jsx("p",{style:{color:"#64748b"},children:"You do not have permission to access purchase order management."})]})}):a.jsx(u.Z,{children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"1rem"},children:[a.jsx("h2",{style:{color:"#ef4444"},children:"Authentication Required"}),a.jsx("p",{style:{color:"#64748b"},children:"Please log in to access the purchase orders page."})]})})}[u,h]=_.then?(await _)():_,t()}catch(e){t(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},9816:e=>{"use strict";e.exports=require("styled-jsx/style")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[2899,6212,1664,7441],()=>s(2301));module.exports=t})();