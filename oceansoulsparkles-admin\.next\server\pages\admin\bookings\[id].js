(()=>{var e={};e.id=8752,e.ids=[8752,660],e.modules={7471:e=>{e.exports={bookingDetailsContainer:"BookingDetails_bookingDetailsContainer__DEzom",header:"BookingDetails_header__Gn5XO",breadcrumb:"BookingDetails_breadcrumb__rppiO",headerActions:"BookingDetails_headerActions__UeW7q",editButton:"BookingDetails_editButton__H14Ee",deleteButton:"BookingDetails_deleteButton__isIoT",backButton:"BookingDetails_backButton__s0GR8",bookingContent:"BookingDetails_bookingContent__pW_th",mainInfo:"BookingDetails_mainInfo__ZLZfk",bookingHeader:"BookingDetails_bookingHeader__7_Xfp",statusSection:"BookingDetails_statusSection__uHyeS",statusBadge:"BookingDetails_statusBadge__T0yog",statusSelect:"BookingDetails_statusSelect__2i44o",detailsGrid:"BookingDetails_detailsGrid___Cfoi",detailCard:"BookingDetails_detailCard__Vy3Cn",customerInfo:"BookingDetails_customerInfo__JNjD7",serviceInfo:"BookingDetails_serviceInfo__0S7xh",appointmentInfo:"BookingDetails_appointmentInfo__644Ey",paymentInfo:"BookingDetails_paymentInfo__HSasv",customerName:"BookingDetails_customerName__PlI8b",serviceName:"BookingDetails_serviceName__NULY4",contactInfo:"BookingDetails_contactInfo__hFsk6",serviceDetails:"BookingDetails_serviceDetails__rcv6H",dateTime:"BookingDetails_dateTime__I9idK",artistInfo:"BookingDetails_artistInfo__jVskx",customerLink:"BookingDetails_customerLink__1UfSh",serviceLink:"BookingDetails_serviceLink__go3_x",amount:"BookingDetails_amount__3kc1e",paymentStatus:"BookingDetails_paymentStatus__4JJxP",paid:"BookingDetails_paid__9isN6",notesSection:"BookingDetails_notesSection__VmKUt",notes:"BookingDetails_notes__DmOU5",metaInfo:"BookingDetails_metaInfo__udOhZ",metaItem:"BookingDetails_metaItem__0Su8N",sidebar:"BookingDetails_sidebar__BWE2t",quickActions:"BookingDetails_quickActions__RQ6VY",timeline:"BookingDetails_timeline__v4i6j",actionButton:"BookingDetails_actionButton__JknHm",timelineItem:"BookingDetails_timelineItem__ZpnY_",timelineDate:"BookingDetails_timelineDate__MJnHp",timelineEvent:"BookingDetails_timelineEvent__nUFI2",loadingContainer:"BookingDetails_loadingContainer__MS1W9",errorContainer:"BookingDetails_errorContainer__TeeaJ",notFoundContainer:"BookingDetails_notFoundContainer__fEggV",loadingSpinner:"BookingDetails_loadingSpinner__8XAXO",spin:"BookingDetails_spin__hfaBY"}},3507:(e,i,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(i),s.d(i,{config:()=>k,default:()=>u,getServerSideProps:()=>g,getStaticPaths:()=>_,getStaticProps:()=>h,reportWebVitals:()=>x,routeModule:()=>f,unstable_getServerProps:()=>B,unstable_getServerSideProps:()=>D,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>p,unstable_getStaticProps:()=>j});var n=s(7093),a=s(5244),o=s(1323),r=s(2899),l=s.n(r),c=s(6814),d=s(1407),m=e([c,d]);[c,d]=m.then?(await m)():m;let u=(0,o.l)(d,"default"),h=(0,o.l)(d,"getStaticProps"),_=(0,o.l)(d,"getStaticPaths"),g=(0,o.l)(d,"getServerSideProps"),k=(0,o.l)(d,"config"),x=(0,o.l)(d,"reportWebVitals"),j=(0,o.l)(d,"unstable_getStaticProps"),p=(0,o.l)(d,"unstable_getStaticPaths"),v=(0,o.l)(d,"unstable_getStaticParams"),B=(0,o.l)(d,"unstable_getServerProps"),D=(0,o.l)(d,"unstable_getServerSideProps"),f=new n.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/admin/bookings/[id]",pathname:"/admin/bookings/[id]",bundlePath:"",filename:""},components:{App:c.default,Document:l()},userland:d});t()}catch(e){t(e)}})},1407:(e,i,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(i),s.d(i,{default:()=>k});var n=s(997),a=s(6689),o=s(1163),r=s(968),l=s.n(r),c=s(1664),d=s.n(c),m=s(8568),u=s(4845),h=s(7471),_=s.n(h),g=e([u]);function k(){let e=(0,o.useRouter)(),{id:i}=e.query,{user:s,loading:t}=(0,m.a)(),[r,c]=(0,a.useState)(!0),[h,g]=(0,a.useState)(null),[k,x]=(0,a.useState)(null),j=async e=>{try{let s=localStorage.getItem("admin-token"),t=await fetch(`/api/admin/bookings/${i}`,{method:"PUT",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"},body:JSON.stringify({status:e})});if(!t.ok)throw Error("Failed to update booking status");let n=await t.json();g(n.booking)}catch(e){console.error("Error updating booking status:",e),alert("Failed to update booking status: "+e.message)}},p=async()=>{if(confirm("Are you sure you want to delete this booking? This action cannot be undone."))try{let s=localStorage.getItem("admin-token");if(!(await fetch(`/api/admin/bookings/${i}`,{method:"DELETE",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}})).ok)throw Error("Failed to delete booking");e.push("/admin/bookings")}catch(e){console.error("Error deleting booking:",e),alert("Failed to delete booking: "+e.message)}},v=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e||0),B=e=>e?new Date(e).toLocaleDateString("en-AU"):"N/A",D=e=>e?new Date(e).toLocaleString("en-AU"):"N/A";return t||r?n.jsx(u.Z,{children:(0,n.jsxs)("div",{className:_().loadingContainer,children:[n.jsx("div",{className:_().loadingSpinner}),n.jsx("p",{children:"Loading booking details..."})]})}):k?n.jsx(u.Z,{children:(0,n.jsxs)("div",{className:_().errorContainer,children:[n.jsx("h2",{children:"Error Loading Booking"}),n.jsx("p",{children:k}),n.jsx(d(),{href:"/admin/bookings",className:_().backButton,children:"← Back to Bookings"})]})}):h?(0,n.jsxs)(u.Z,{children:[(0,n.jsxs)(l(),{children:[(0,n.jsxs)("title",{children:["Booking #",h.id," - Details | Ocean Soul Sparkles Admin"]}),n.jsx("meta",{name:"description",content:`Details for booking #${h.id}`})]}),(0,n.jsxs)("div",{className:_().bookingDetailsContainer,children:[(0,n.jsxs)("header",{className:_().header,children:[(0,n.jsxs)("div",{className:_().breadcrumb,children:[n.jsx(d(),{href:"/admin/bookings",children:"Bookings"}),n.jsx("span",{children:"/"}),(0,n.jsxs)("span",{children:["Booking #",h.id]})]}),(0,n.jsxs)("div",{className:_().headerActions,children:[n.jsx(d(),{href:`/admin/bookings/${h.id}/edit`,className:_().editButton,children:"✏️ Edit Booking"}),n.jsx("button",{onClick:p,className:_().deleteButton,children:"\uD83D\uDDD1️ Delete"}),n.jsx(d(),{href:"/admin/bookings",className:_().backButton,children:"← Back to Bookings"})]})]}),(0,n.jsxs)("div",{className:_().bookingContent,children:[(0,n.jsxs)("div",{className:_().mainInfo,children:[(0,n.jsxs)("div",{className:_().bookingHeader,children:[(0,n.jsxs)("h1",{children:["Booking #",h.id]}),(0,n.jsxs)("div",{className:_().statusSection,children:[n.jsx("span",{className:_().statusBadge,style:{backgroundColor:(e=>{switch(e?.toLowerCase()){case"confirmed":return"#28a745";case"pending":return"#ffc107";case"cancelled":return"#dc3545";case"completed":return"#17a2b8";default:return"#6c757d"}})(h.status)},children:h.status}),(0,n.jsxs)("select",{value:h.status,onChange:e=>j(e.target.value),className:_().statusSelect,children:[n.jsx("option",{value:"pending",children:"Pending"}),n.jsx("option",{value:"confirmed",children:"Confirmed"}),n.jsx("option",{value:"completed",children:"Completed"}),n.jsx("option",{value:"cancelled",children:"Cancelled"}),n.jsx("option",{value:"no_show",children:"No Show"})]})]})]}),(0,n.jsxs)("div",{className:_().detailsGrid,children:[(0,n.jsxs)("div",{className:_().detailCard,children:[n.jsx("h4",{children:"Customer Information"}),(0,n.jsxs)("div",{className:_().customerInfo,children:[n.jsx("div",{className:_().customerName,children:n.jsx("strong",{children:h.customer_name})}),(0,n.jsxs)("div",{className:_().contactInfo,children:[(0,n.jsxs)("div",{children:["\uD83D\uDCE7 ",h.customer_email]}),(0,n.jsxs)("div",{children:["\uD83D\uDCDE ",h.customer_phone]})]}),n.jsx(d(),{href:`/admin/customers/${h.customers?.id}`,className:_().customerLink,children:"View Customer Profile →"})]})]}),(0,n.jsxs)("div",{className:_().detailCard,children:[n.jsx("h4",{children:"Service Details"}),(0,n.jsxs)("div",{className:_().serviceInfo,children:[n.jsx("div",{className:_().serviceName,children:n.jsx("strong",{children:h.service_name})}),(0,n.jsxs)("div",{className:_().serviceDetails,children:[(0,n.jsxs)("div",{children:["Duration: ",(e=>{if(!e)return"N/A";if(e>=60){let i=Math.floor(e/60),s=e%60;return s>0?`${i}h ${s}m`:`${i}h`}return`${e}m`})(h.service_duration)]}),(0,n.jsxs)("div",{children:["Price: ",v(h.service_price)]})]}),n.jsx(d(),{href:`/admin/services/${h.services?.id}`,className:_().serviceLink,children:"View Service Details →"})]})]}),(0,n.jsxs)("div",{className:_().detailCard,children:[n.jsx("h4",{children:"Appointment Details"}),(0,n.jsxs)("div",{className:_().appointmentInfo,children:[(0,n.jsxs)("div",{className:_().dateTime,children:[(0,n.jsxs)("div",{children:[n.jsx("strong",{children:"Date:"})," ",B(h.booking_date)]}),(0,n.jsxs)("div",{children:[n.jsx("strong",{children:"Time:"})," ",h.booking_time]})]}),n.jsx("div",{className:_().artistInfo,children:(0,n.jsxs)("div",{children:[n.jsx("strong",{children:"Artist:"})," ",h.artist_name]})})]})]}),(0,n.jsxs)("div",{className:_().detailCard,children:[n.jsx("h4",{children:"Payment Information"}),(0,n.jsxs)("div",{className:_().paymentInfo,children:[n.jsx("div",{className:_().amount,children:(0,n.jsxs)("strong",{children:["Total Amount: ",v(h.total_amount)]})}),(0,n.jsxs)("div",{className:_().paymentStatus,children:["Payment Status: ",n.jsx("span",{className:_().paid,children:"Paid"})]})]})]})]}),h.notes&&(0,n.jsxs)("div",{className:_().notesSection,children:[n.jsx("h4",{children:"Notes"}),n.jsx("p",{className:_().notes,children:h.notes})]}),(0,n.jsxs)("div",{className:_().metaInfo,children:[(0,n.jsxs)("div",{className:_().metaItem,children:[n.jsx("strong",{children:"Created:"})," ",D(h.created_at)]}),(0,n.jsxs)("div",{className:_().metaItem,children:[n.jsx("strong",{children:"Booking ID:"})," ",h.id]})]})]}),(0,n.jsxs)("div",{className:_().sidebar,children:[(0,n.jsxs)("div",{className:_().quickActions,children:[n.jsx("h3",{children:"Quick Actions"}),n.jsx(d(),{href:`/admin/bookings/${h.id}/edit`,className:_().actionButton,children:"Edit Booking Details"}),n.jsx(d(),{href:`/admin/customers/${h.customers?.id}`,className:_().actionButton,children:"View Customer Profile"}),n.jsx(d(),{href:`/admin/bookings/new?customer=${h.customers?.id}`,className:_().actionButton,children:"Book Another Service"})]}),(0,n.jsxs)("div",{className:_().timeline,children:[n.jsx("h3",{children:"Booking Timeline"}),(0,n.jsxs)("div",{className:_().timelineItem,children:[n.jsx("div",{className:_().timelineDate,children:D(h.created_at)}),n.jsx("div",{className:_().timelineEvent,children:"Booking created"})]}),(0,n.jsxs)("div",{className:_().timelineItem,children:[(0,n.jsxs)("div",{className:_().timelineDate,children:[B(h.booking_date)," ",h.booking_time]}),n.jsx("div",{className:_().timelineEvent,children:"Scheduled appointment"})]})]})]})]})]})]}):n.jsx(u.Z,{children:(0,n.jsxs)("div",{className:_().notFoundContainer,children:[n.jsx("h2",{children:"Booking Not Found"}),n.jsx("p",{children:"The booking you're looking for doesn't exist."}),n.jsx(d(),{href:"/admin/bookings",className:_().backButton,children:"← Back to Bookings"})]})})}u=(g.then?(await g)():g)[0],t()}catch(e){t(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var i=require("../../../webpack-runtime.js");i.C(e);var s=e=>i(i.s=e),t=i.X(0,[2899,6212,1664,7441],()=>s(3507));module.exports=t})();