/* Ocean Soul Sparkles - Customer Portal Layout Styles */

.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.logo h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logo h1:hover {
  transform: scale(1.05);
}

.nav {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav a {
  text-decoration: none;
  color: #4a5568;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.nav a:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.nav a.active {
  color: #667eea;
  background: rgba(102, 126, 234, 0.15);
  font-weight: 600;
}

.nav a.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: #667eea;
  border-radius: 1px;
}

.userActions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notifications {
  position: relative;
}

.notificationIcon {
  font-size: 1.2rem;
  text-decoration: none;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  position: relative;
}

.notificationIcon:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.1);
}

.notificationBadge {
  position: absolute;
  top: 0;
  right: 0;
  background: #e53e3e;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1;
}

.userMenu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.userName {
  font-weight: 600;
  color: #4a5568;
}

.logoutBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logoutBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.authActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.loginBtn, .registerBtn {
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.loginBtn {
  color: #667eea;
  border: 1px solid #667eea;
}

.loginBtn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.registerBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.registerBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.mobileMenuToggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #4a5568;
}

.main {
  flex: 1;
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.pageHeader {
  margin-bottom: 2rem;
  text-align: center;
}

.pageHeader h1 {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.footer {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 3rem 0 1rem;
  margin-top: auto;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.footerSection h3, .footerSection h4 {
  margin: 0 0 1rem 0;
  color: #667eea;
}

.footerSection p {
  margin: 0.5rem 0;
  color: #cbd5e0;
}

.footerSection ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerSection ul li {
  margin: 0.5rem 0;
}

.footerSection ul li a {
  color: #cbd5e0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footerSection ul li a:hover {
  color: #667eea;
}

.socialLinks {
  display: flex;
  gap: 1rem;
}

.socialLinks a {
  color: #cbd5e0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.socialLinks a:hover {
  color: #667eea;
}

.footerBottom {
  max-width: 1200px;
  margin: 2rem auto 0;
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  color: #cbd5e0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .headerContent {
    padding: 0 1rem;
  }

  .nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    gap: 0.5rem;
  }

  .navOpen {
    display: flex;
  }

  .mobileMenuToggle {
    display: block;
  }

  .userActions {
    gap: 0.5rem;
  }

  .userName {
    display: none;
  }

  .pageHeader h1 {
    font-size: 2rem;
  }

  .footerContent {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .socialLinks {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .logo h1 {
    font-size: 1.2rem;
  }

  .pageHeader h1 {
    font-size: 1.5rem;
  }

  .main {
    padding: 1rem 0;
  }

  .footer {
    padding: 2rem 0 1rem;
  }
}
