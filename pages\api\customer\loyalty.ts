import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyCustomerToken, logCustomerActivity } from '../../../lib/auth/customer-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Get token from Authorization header or cookie
  const authHeader = req.headers.authorization;
  const token = authHeader?.startsWith('Bearer ') 
    ? authHeader.substring(7)
    : req.cookies['customer-token'];

  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // Verify customer authentication
  const authResult = await verifyCustomerToken(token);
  if (!authResult.valid || !authResult.user) {
    return res.status(401).json({ error: 'Invalid authentication token' });
  }

  const customer = authResult.user;

  try {
    if (req.method === 'GET') {
      return await handleGetLoyalty(req, res, customer);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Customer loyalty API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetLoyalty(req: NextApiRequest, res: NextApiResponse, customer: any) {
  // Get customer loyalty information
  const { data: loyaltyData, error: loyaltyError } = await supabase
    .from('customer_loyalty')
    .select(`
      points_balance,
      total_points_earned,
      total_points_redeemed,
      tier_level,
      tier_start_date,
      next_tier_points,
      lifetime_spend,
      referral_code,
      referred_by_customer_id
    `)
    .eq('customer_id', customer.customerId)
    .single();

  if (loyaltyError || !loyaltyData) {
    console.error('Customer loyalty query error:', loyaltyError);
    return res.status(500).json({ error: 'Failed to fetch loyalty information' });
  }

  // Get loyalty transactions history
  const { data: transactions, error: transactionsError } = await supabase
    .from('loyalty_transactions')
    .select(`
      id,
      transaction_type,
      points_amount,
      description,
      created_at
    `)
    .eq('customer_id', customer.customerId)
    .order('created_at', { ascending: false })
    .limit(20);

  if (transactionsError) {
    console.error('Loyalty transactions query error:', transactionsError);
    return res.status(500).json({ error: 'Failed to fetch loyalty transactions' });
  }

  // Get loyalty program rules for tier information
  const { data: tierRules, error: tierRulesError } = await supabase
    .from('loyalty_program_rules')
    .select('rule_name, rule_value')
    .eq('rule_type', 'tier_threshold')
    .eq('is_active', true)
    .order('rule_value', { ascending: true });

  if (tierRulesError) {
    console.error('Tier rules query error:', tierRulesError);
  }

  // Calculate tier progression
  const tiers = [
    { name: 'Bronze', threshold: 0, color: '#CD7F32' },
    { name: 'Silver', threshold: 100, color: '#C0C0C0' },
    { name: 'Gold', threshold: 500, color: '#FFD700' },
    { name: 'Platinum', threshold: 1000, color: '#E5E4E2' }
  ];

  const currentTier = tiers.find(tier => tier.name === loyaltyData.tier_level) || tiers[0];
  const currentTierIndex = tiers.findIndex(tier => tier.name === loyaltyData.tier_level);
  const nextTier = currentTierIndex < tiers.length - 1 ? tiers[currentTierIndex + 1] : null;
  
  const pointsToNextTier = nextTier 
    ? nextTier.threshold - loyaltyData.total_points_earned 
    : 0;

  // Get referral statistics
  const { count: referralCount } = await supabase
    .from('customer_loyalty')
    .select('*', { count: 'exact', head: true })
    .eq('referred_by_customer_id', customer.customerId);

  // Calculate points breakdown
  const pointsBreakdown = {
    earned_from_bookings: 0,
    earned_from_referrals: 0,
    earned_from_bonuses: 0,
    redeemed_total: loyaltyData.total_points_redeemed
  };

  transactions?.forEach(transaction => {
    if (transaction.transaction_type === 'earned') {
      pointsBreakdown.earned_from_bookings += transaction.points_amount;
    } else if (transaction.transaction_type === 'referral') {
      pointsBreakdown.earned_from_referrals += transaction.points_amount;
    } else if (transaction.transaction_type === 'bonus') {
      pointsBreakdown.earned_from_bonuses += transaction.points_amount;
    }
  });

  const loyaltyInfo = {
    current_status: {
      points_balance: loyaltyData.points_balance,
      total_points_earned: loyaltyData.total_points_earned,
      total_points_redeemed: loyaltyData.total_points_redeemed,
      lifetime_spend: loyaltyData.lifetime_spend,
      tier_level: loyaltyData.tier_level,
      tier_start_date: loyaltyData.tier_start_date,
      referral_code: loyaltyData.referral_code
    },
    tier_progression: {
      current_tier: {
        name: currentTier.name,
        threshold: currentTier.threshold,
        color: currentTier.color
      },
      next_tier: nextTier ? {
        name: nextTier.name,
        threshold: nextTier.threshold,
        color: nextTier.color
      } : null,
      points_to_next_tier: pointsToNextTier,
      progress_percentage: nextTier 
        ? Math.min(100, ((loyaltyData.total_points_earned - currentTier.threshold) / (nextTier.threshold - currentTier.threshold)) * 100)
        : 100
    },
    referral_program: {
      referral_code: loyaltyData.referral_code,
      successful_referrals: referralCount || 0,
      referral_bonus_points: 50, // This could be configurable
      share_url: `${process.env.NEXT_PUBLIC_CUSTOMER_PORTAL_URL}/register?ref=${loyaltyData.referral_code}`
    },
    points_breakdown: pointsBreakdown,
    recent_transactions: transactions?.map(transaction => ({
      id: transaction.id,
      type: transaction.transaction_type,
      points: transaction.points_amount,
      description: transaction.description,
      date: transaction.created_at
    })) || [],
    available_tiers: tiers
  };

  return res.status(200).json({ loyalty: loyaltyInfo });
}
