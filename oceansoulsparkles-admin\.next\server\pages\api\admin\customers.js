"use strict";(()=>{var e={};e.id=983,e.ids=[983],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},7316:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>m,routeModule:()=>_});var a={};r.r(a),r.d(a,{default:()=>l});var s=r(1802),o=r(7153),n=r(8781),i=r(7474),d=r(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,c=(0,d.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function l(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r=e.headers.authorization?.replace("Bearer ","")||e.cookies["admin-token"];if(!r)return t.status(401).json({error:"No authentication token"});let a=await (0,i.Wg)(r);if(!a.valid||!a.user)return t.status(401).json({error:"Invalid authentication"});let s=a.user;if("Admin"!==s.role&&"DEV"!==s.role)return t.status(403).json({error:"Insufficient permissions"});let{data:o,error:n}=await c.from("customers").select(`
        id,
        first_name,
        last_name,
        email,
        phone,
        date_of_birth,
        address,
        phone_secondary,
        notes,
        created_at,
        updated_at
      `).order("created_at",{ascending:!1});if(n)return console.error("Customers query error:",n),t.status(500).json({error:"Failed to fetch customers"});let d=o?.map(e=>e.id)||[],{data:u}=await c.from("bookings").select("customer_id").in("customer_id",d),l=(u||[]).reduce((e,t)=>(e[t.customer_id]=(e[t.customer_id]||0)+1,e),{}),m=(o||[]).map(e=>({id:e.id,name:`${e.first_name} ${e.last_name}`,first_name:e.first_name,last_name:e.last_name,email:e.email,phone:e.phone,date_of_birth:e.date_of_birth,address:e.address,emergency_contact_name:null,emergency_contact_phone:e.phone_secondary,notes:e.notes,total_bookings:l[e.id]||0,created_at:e.created_at,updated_at:e.updated_at}));return t.status(200).json({customers:m,total:m.length})}catch(e){return console.error("Customers API error:",e),t.status(500).json({error:"Internal server error"})}}let m=(0,n.l)(a,"default"),p=(0,n.l)(a,"config"),_=new s.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/customers",pathname:"/api/admin/customers",bundlePath:"",filename:""},userland:a})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[2805],()=>r(7316));module.exports=a})();