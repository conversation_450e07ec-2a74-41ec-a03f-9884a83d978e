"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeamMembers = void 0;
const environments = __importStar(require("../../../../environments"));
const core = __importStar(require("../../../../core"));
const serializers = __importStar(require("../../../../serialization/index"));
const url_join_1 = __importDefault(require("url-join"));
const errors = __importStar(require("../../../../errors/index"));
const Client_1 = require("../resources/wageSetting/client/Client");
class TeamMembers {
    constructor(_options = {}) {
        this._options = _options;
    }
    get wageSetting() {
        var _a;
        return ((_a = this._wageSetting) !== null && _a !== void 0 ? _a : (this._wageSetting = new Client_1.WageSetting(this._options)));
    }
    /**
     * Creates a single `TeamMember` object. The `TeamMember` object is returned on successful creates.
     * You must provide the following values in your request to this endpoint:
     * - `given_name`
     * - `family_name`
     *
     * Learn about [Troubleshooting the Team API](https://developer.squareup.com/docs/team/troubleshooting#createteammember).
     *
     * @param {Square.CreateTeamMemberRequest} request
     * @param {TeamMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @example
     *     await client.teamMembers.create({
     *         idempotencyKey: "idempotency-key-0",
     *         teamMember: {
     *             referenceId: "reference_id_1",
     *             status: "ACTIVE",
     *             givenName: "Joe",
     *             familyName: "Doe",
     *             emailAddress: "<EMAIL>",
     *             phoneNumber: "+14159283333",
     *             assignedLocations: {
     *                 assignmentType: "EXPLICIT_LOCATIONS",
     *                 locationIds: ["YSGH2WBKG94QZ", "GA2Y9HSJ8KRYT"]
     *             },
     *             wageSetting: {
     *                 jobAssignments: [{
     *                         payType: "SALARY",
     *                         annualRate: {
     *                             amount: 3000000,
     *                             currency: "USD"
     *                         },
     *                         weeklyHours: 40,
     *                         jobId: "FjS8x95cqHiMenw4f1NAUH4P"
     *                     }, {
     *                         payType: "HOURLY",
     *                         hourlyRate: {
     *                             amount: 2000,
     *                             currency: "USD"
     *                         },
     *                         jobId: "VDNpRv8da51NU8qZFC5zDWpF"
     *                     }],
     *                 isOvertimeExempt: true
     *             }
     *         }
     *     })
     */
    create(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d, _e, _f;
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_c = (_b = (yield core.Supplier.get(this._options.baseUrl))) !== null && _b !== void 0 ? _b : (yield core.Supplier.get(this._options.environment))) !== null && _c !== void 0 ? _c : environments.SquareEnvironment.Production, "v2/team-members"),
                method: "POST",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "Square-Version": (_f = (_d = requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.version) !== null && _d !== void 0 ? _d : (_e = this._options) === null || _e === void 0 ? void 0 : _e.version) !== null && _f !== void 0 ? _f : "2025-05-21", "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "square", "X-Fern-SDK-Version": "42.3.0", "User-Agent": "square/42.3.0", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.CreateTeamMemberRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "strip",
                    omitUndefined: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.CreateTeamMemberResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                throw new errors.SquareError({
                    statusCode: _response.error.statusCode,
                    body: _response.error.body,
                });
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.SquareError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.SquareTimeoutError("Timeout exceeded when calling POST /v2/team-members.");
                case "unknown":
                    throw new errors.SquareError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Creates multiple `TeamMember` objects. The created `TeamMember` objects are returned on successful creates.
     * This process is non-transactional and processes as much of the request as possible. If one of the creates in
     * the request cannot be successfully processed, the request is not marked as failed, but the body of the response
     * contains explicit error information for the failed create.
     *
     * Learn about [Troubleshooting the Team API](https://developer.squareup.com/docs/team/troubleshooting#bulk-create-team-members).
     *
     * @param {Square.BatchCreateTeamMembersRequest} request
     * @param {TeamMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @example
     *     await client.teamMembers.batchCreate({
     *         teamMembers: {
     *             "idempotency-key-1": {
     *                 teamMember: {
     *                     referenceId: "reference_id_1",
     *                     givenName: "Joe",
     *                     familyName: "Doe",
     *                     emailAddress: "<EMAIL>",
     *                     phoneNumber: "+14159283333",
     *                     assignedLocations: {
     *                         assignmentType: "EXPLICIT_LOCATIONS",
     *                         locationIds: ["YSGH2WBKG94QZ", "GA2Y9HSJ8KRYT"]
     *                     }
     *                 }
     *             },
     *             "idempotency-key-2": {
     *                 teamMember: {
     *                     referenceId: "reference_id_2",
     *                     givenName: "Jane",
     *                     familyName: "Smith",
     *                     emailAddress: "<EMAIL>",
     *                     phoneNumber: "+14159223334",
     *                     assignedLocations: {
     *                         assignmentType: "ALL_CURRENT_AND_FUTURE_LOCATIONS"
     *                     }
     *                 }
     *             }
     *         }
     *     })
     */
    batchCreate(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d, _e, _f;
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_c = (_b = (yield core.Supplier.get(this._options.baseUrl))) !== null && _b !== void 0 ? _b : (yield core.Supplier.get(this._options.environment))) !== null && _c !== void 0 ? _c : environments.SquareEnvironment.Production, "v2/team-members/bulk-create"),
                method: "POST",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "Square-Version": (_f = (_d = requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.version) !== null && _d !== void 0 ? _d : (_e = this._options) === null || _e === void 0 ? void 0 : _e.version) !== null && _f !== void 0 ? _f : "2025-05-21", "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "square", "X-Fern-SDK-Version": "42.3.0", "User-Agent": "square/42.3.0", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.BatchCreateTeamMembersRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "strip",
                    omitUndefined: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.BatchCreateTeamMembersResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                throw new errors.SquareError({
                    statusCode: _response.error.statusCode,
                    body: _response.error.body,
                });
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.SquareError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.SquareTimeoutError("Timeout exceeded when calling POST /v2/team-members/bulk-create.");
                case "unknown":
                    throw new errors.SquareError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Updates multiple `TeamMember` objects. The updated `TeamMember` objects are returned on successful updates.
     * This process is non-transactional and processes as much of the request as possible. If one of the updates in
     * the request cannot be successfully processed, the request is not marked as failed, but the body of the response
     * contains explicit error information for the failed update.
     * Learn about [Troubleshooting the Team API](https://developer.squareup.com/docs/team/troubleshooting#bulk-update-team-members).
     *
     * @param {Square.BatchUpdateTeamMembersRequest} request
     * @param {TeamMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @example
     *     await client.teamMembers.batchUpdate({
     *         teamMembers: {
     *             "AFMwA08kR-MIF-3Vs0OE": {
     *                 teamMember: {
     *                     referenceId: "reference_id_2",
     *                     isOwner: false,
     *                     status: "ACTIVE",
     *                     givenName: "Jane",
     *                     familyName: "Smith",
     *                     emailAddress: "<EMAIL>",
     *                     phoneNumber: "+14159223334",
     *                     assignedLocations: {
     *                         assignmentType: "ALL_CURRENT_AND_FUTURE_LOCATIONS"
     *                     }
     *                 }
     *             },
     *             "fpgteZNMaf0qOK-a4t6P": {
     *                 teamMember: {
     *                     referenceId: "reference_id_1",
     *                     isOwner: false,
     *                     status: "ACTIVE",
     *                     givenName: "Joe",
     *                     familyName: "Doe",
     *                     emailAddress: "<EMAIL>",
     *                     phoneNumber: "+14159283333",
     *                     assignedLocations: {
     *                         assignmentType: "EXPLICIT_LOCATIONS",
     *                         locationIds: ["YSGH2WBKG94QZ", "GA2Y9HSJ8KRYT"]
     *                     }
     *                 }
     *             }
     *         }
     *     })
     */
    batchUpdate(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d, _e, _f;
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_c = (_b = (yield core.Supplier.get(this._options.baseUrl))) !== null && _b !== void 0 ? _b : (yield core.Supplier.get(this._options.environment))) !== null && _c !== void 0 ? _c : environments.SquareEnvironment.Production, "v2/team-members/bulk-update"),
                method: "POST",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "Square-Version": (_f = (_d = requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.version) !== null && _d !== void 0 ? _d : (_e = this._options) === null || _e === void 0 ? void 0 : _e.version) !== null && _f !== void 0 ? _f : "2025-05-21", "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "square", "X-Fern-SDK-Version": "42.3.0", "User-Agent": "square/42.3.0", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.BatchUpdateTeamMembersRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "strip",
                    omitUndefined: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.BatchUpdateTeamMembersResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                throw new errors.SquareError({
                    statusCode: _response.error.statusCode,
                    body: _response.error.body,
                });
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.SquareError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.SquareTimeoutError("Timeout exceeded when calling POST /v2/team-members/bulk-update.");
                case "unknown":
                    throw new errors.SquareError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Returns a paginated list of `TeamMember` objects for a business.
     * The list can be filtered by location IDs, `ACTIVE` or `INACTIVE` status, or whether
     * the team member is the Square account owner.
     *
     * @param {Square.SearchTeamMembersRequest} request
     * @param {TeamMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @example
     *     await client.teamMembers.search({
     *         query: {
     *             filter: {
     *                 locationIds: ["0G5P3VGACMMQZ"],
     *                 status: "ACTIVE"
     *             }
     *         },
     *         limit: 10
     *     })
     */
    search() {
        return __awaiter(this, arguments, void 0, function* (request = {}, requestOptions) {
            var _a, _b, _c, _d, _e, _f;
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_c = (_b = (yield core.Supplier.get(this._options.baseUrl))) !== null && _b !== void 0 ? _b : (yield core.Supplier.get(this._options.environment))) !== null && _c !== void 0 ? _c : environments.SquareEnvironment.Production, "v2/team-members/search"),
                method: "POST",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "Square-Version": (_f = (_d = requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.version) !== null && _d !== void 0 ? _d : (_e = this._options) === null || _e === void 0 ? void 0 : _e.version) !== null && _f !== void 0 ? _f : "2025-05-21", "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "square", "X-Fern-SDK-Version": "42.3.0", "User-Agent": "square/42.3.0", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.SearchTeamMembersRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "strip",
                    omitUndefined: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.SearchTeamMembersResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                throw new errors.SquareError({
                    statusCode: _response.error.statusCode,
                    body: _response.error.body,
                });
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.SquareError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.SquareTimeoutError("Timeout exceeded when calling POST /v2/team-members/search.");
                case "unknown":
                    throw new errors.SquareError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Retrieves a `TeamMember` object for the given `TeamMember.id`.
     * Learn about [Troubleshooting the Team API](https://developer.squareup.com/docs/team/troubleshooting#retrieve-a-team-member).
     *
     * @param {Square.GetTeamMembersRequest} request
     * @param {TeamMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @example
     *     await client.teamMembers.get({
     *         teamMemberId: "team_member_id"
     *     })
     */
    get(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d, _e, _f;
            const { teamMemberId } = request;
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_c = (_b = (yield core.Supplier.get(this._options.baseUrl))) !== null && _b !== void 0 ? _b : (yield core.Supplier.get(this._options.environment))) !== null && _c !== void 0 ? _c : environments.SquareEnvironment.Production, `v2/team-members/${encodeURIComponent(teamMemberId)}`),
                method: "GET",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "Square-Version": (_f = (_d = requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.version) !== null && _d !== void 0 ? _d : (_e = this._options) === null || _e === void 0 ? void 0 : _e.version) !== null && _f !== void 0 ? _f : "2025-05-21", "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "square", "X-Fern-SDK-Version": "42.3.0", "User-Agent": "square/42.3.0", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.GetTeamMemberResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                throw new errors.SquareError({
                    statusCode: _response.error.statusCode,
                    body: _response.error.body,
                });
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.SquareError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.SquareTimeoutError("Timeout exceeded when calling GET /v2/team-members/{team_member_id}.");
                case "unknown":
                    throw new errors.SquareError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Updates a single `TeamMember` object. The `TeamMember` object is returned on successful updates.
     * Learn about [Troubleshooting the Team API](https://developer.squareup.com/docs/team/troubleshooting#update-a-team-member).
     *
     * @param {Square.UpdateTeamMembersRequest} request
     * @param {TeamMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @example
     *     await client.teamMembers.update({
     *         teamMemberId: "team_member_id",
     *         body: {
     *             teamMember: {
     *                 referenceId: "reference_id_1",
     *                 status: "ACTIVE",
     *                 givenName: "Joe",
     *                 familyName: "Doe",
     *                 emailAddress: "<EMAIL>",
     *                 phoneNumber: "+14159283333",
     *                 assignedLocations: {
     *                     assignmentType: "EXPLICIT_LOCATIONS",
     *                     locationIds: ["YSGH2WBKG94QZ", "GA2Y9HSJ8KRYT"]
     *                 },
     *                 wageSetting: {
     *                     jobAssignments: [{
     *                             payType: "SALARY",
     *                             annualRate: {
     *                                 amount: 3000000,
     *                                 currency: "USD"
     *                             },
     *                             weeklyHours: 40,
     *                             jobId: "FjS8x95cqHiMenw4f1NAUH4P"
     *                         }, {
     *                             payType: "HOURLY",
     *                             hourlyRate: {
     *                                 amount: 1200,
     *                                 currency: "USD"
     *                             },
     *                             jobId: "VDNpRv8da51NU8qZFC5zDWpF"
     *                         }],
     *                     isOvertimeExempt: true
     *                 }
     *             }
     *         }
     *     })
     */
    update(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d, _e, _f;
            const { teamMemberId, body: _body } = request;
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_c = (_b = (yield core.Supplier.get(this._options.baseUrl))) !== null && _b !== void 0 ? _b : (yield core.Supplier.get(this._options.environment))) !== null && _c !== void 0 ? _c : environments.SquareEnvironment.Production, `v2/team-members/${encodeURIComponent(teamMemberId)}`),
                method: "PUT",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "Square-Version": (_f = (_d = requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.version) !== null && _d !== void 0 ? _d : (_e = this._options) === null || _e === void 0 ? void 0 : _e.version) !== null && _f !== void 0 ? _f : "2025-05-21", "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "square", "X-Fern-SDK-Version": "42.3.0", "User-Agent": "square/42.3.0", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.UpdateTeamMemberRequest.jsonOrThrow(_body, {
                    unrecognizedObjectKeys: "strip",
                    omitUndefined: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.UpdateTeamMemberResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                throw new errors.SquareError({
                    statusCode: _response.error.statusCode,
                    body: _response.error.body,
                });
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.SquareError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.SquareTimeoutError("Timeout exceeded when calling PUT /v2/team-members/{team_member_id}.");
                case "unknown":
                    throw new errors.SquareError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    _getAuthorizationHeader() {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            const bearer = (_a = (yield core.Supplier.get(this._options.token))) !== null && _a !== void 0 ? _a : process === null || process === void 0 ? void 0 : process.env["SQUARE_TOKEN"];
            if (bearer != null) {
                return `Bearer ${bearer}`;
            }
            return undefined;
        });
    }
}
exports.TeamMembers = TeamMembers;
