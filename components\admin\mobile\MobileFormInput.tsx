/**
 * Ocean Soul Sparkles - Mobile Form Input Component
 * Touch-optimized input fields with mobile keyboard handling
 */

import React, { useState, useRef, useEffect } from 'react';
import styles from '../../../styles/admin/mobile/MobileFormInput.module.css';

interface MobileFormInputProps {
  label: string;
  type?: 'text' | 'email' | 'tel' | 'password' | 'number' | 'url' | 'search';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  hint?: string;
  icon?: string;
  autoComplete?: string;
  inputMode?: 'text' | 'numeric' | 'decimal' | 'tel' | 'email' | 'url' | 'search';
  pattern?: string;
  maxLength?: number;
  minLength?: number;
  min?: number;
  max?: number;
  step?: number;
  onFocus?: () => void;
  onBlur?: () => void;
  onEnter?: () => void;
  className?: string;
}

export default function MobileFormInput({
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  error,
  hint,
  icon,
  autoComplete,
  inputMode,
  pattern,
  maxLength,
  minLength,
  min,
  max,
  step,
  onFocus,
  onBlur,
  onEnter,
  className = ''
}: MobileFormInputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setHasValue(value.length > 0);
  }, [value]);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && onEnter) {
      e.preventDefault();
      onEnter();
    }
  };

  const handleLabelClick = () => {
    inputRef.current?.focus();
  };

  return (
    <div className={`${styles.mobileInput} ${className} ${error ? styles.error : ''} ${disabled ? styles.disabled : ''}`}>
      {/* Floating Label */}
      <div className={styles.inputContainer}>
        <input
          ref={inputRef}
          type={type}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyPress={handleKeyPress}
          placeholder={isFocused ? placeholder : ''}
          required={required}
          disabled={disabled}
          autoComplete={autoComplete}
          inputMode={inputMode}
          pattern={pattern}
          maxLength={maxLength}
          minLength={minLength}
          min={min}
          max={max}
          step={step}
          className={`${styles.input} ${isFocused || hasValue ? styles.hasContent : ''}`}
        />
        
        <label
          onClick={handleLabelClick}
          className={`${styles.label} ${isFocused || hasValue ? styles.floating : ''} ${required ? styles.required : ''}`}
        >
          {icon && <span className={styles.labelIcon}>{icon}</span>}
          {label}
        </label>

        {/* Input Border Animation */}
        <div className={`${styles.inputBorder} ${isFocused ? styles.focused : ''}`}></div>
      </div>

      {/* Helper Text */}
      {(hint || error) && (
        <div className={styles.helperText}>
          {error ? (
            <span className={styles.errorText}>
              <span className={styles.errorIcon}>⚠️</span>
              {error}
            </span>
          ) : (
            <span className={styles.hintText}>{hint}</span>
          )}
        </div>
      )}
    </div>
  );
}
