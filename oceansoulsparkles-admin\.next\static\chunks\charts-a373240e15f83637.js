"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[482],{7577:function(t,e,i){let s;i.d(e,{qi:function(){return sn},vn:function(){return eQ},ZL:function(){return sv},uw:function(){return sG},kL:function(){return se},jI:function(){return e0},De:function(){return sT},ST:function(){return e1},jn:function(){return sg},f$:function(){return sX},tt:function(){return e5},od:function(){return sp},Dx:function(){return sA},u:function(){return s$}});var a=i(6193);function n(){}let r=(s=0,()=>s++);function o(t){return null==t}function l(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function h(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function d(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function c(t,e){return d(t)?t:e}function u(t,e){return void 0===t?e:t}let g=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:+t/e,f=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function p(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function m(t,e,i,s){let a,n,r;if(l(t)){if(n=t.length,s)for(a=n-1;a>=0;a--)e.call(i,t[a],a);else for(a=0;a<n;a++)e.call(i,t[a],a)}else if(h(t))for(a=0,n=(r=Object.keys(t)).length;a<n;a++)e.call(i,t[r[a]],r[a])}function x(t,e){let i,s,a,n;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(a=t[i],n=e[i],a.datasetIndex!==n.datasetIndex||a.index!==n.index)return!1;return!0}function b(t){if(l(t))return t.map(b);if(h(t)){let e=Object.create(null),i=Object.keys(t),s=i.length,a=0;for(;a<s;++a)e[i[a]]=b(t[i[a]]);return e}return t}function _(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function y(t,e,i,s){if(!_(t))return;let a=e[t],n=i[t];h(a)&&h(n)?v(a,n,s):e[t]=b(n)}function v(t,e,i){let s;let a=l(e)?e:[e],n=a.length;if(!h(t))return t;let r=(i=i||{}).merger||y;for(let e=0;e<n;++e){if(!h(s=a[e]))continue;let n=Object.keys(s);for(let e=0,a=n.length;e<a;++e)r(n[e],t,s,i)}return t}function M(t,e){return v(t,e,{merger:w})}function w(t,e,i){if(!_(t))return;let s=e[t],a=i[t];h(s)&&h(a)?M(s,a):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=b(a))}let k={"":t=>t,x:t=>t.x,y:t=>t.y};function D(t,e){return(k[e]||(k[e]=function(t){let e=function(t){let e=t.split("."),i=[],s="";for(let t of e)(s+=t).endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(let i of e){if(""===i)break;t=t&&t[i]}return t}}(e)))(t)}function O(t){return t.charAt(0).toUpperCase()+t.slice(1)}let S=t=>void 0!==t,P=t=>"function"==typeof t,C=(t,e)=>{if(t.size!==e.size)return!1;for(let i of t)if(!e.has(i))return!1;return!0},T=Math.PI,L=2*T,A=L+T,E=Number.POSITIVE_INFINITY,I=T/180,R=T/2,F=T/4,z=2*T/3,N=Math.log10,V=Math.sign;function B(t,e,i){return Math.abs(t-e)<i}function j(t){let e=Math.round(t),i=Math.pow(10,Math.floor(N(t=B(t,e,t/1e3)?e:t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function W(t){return!("symbol"==typeof t||"object"==typeof t&&null!==t&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function H(t,e,i){let s,a,n;for(s=0,a=t.length;s<a;s++)isNaN(n=t[s][i])||(e.min=Math.min(e.min,n),e.max=Math.max(e.max,n))}function $(t){return T/180*t}function U(t){if(!d(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function Y(t,e){let i=e.x-t.x,s=e.y-t.y,a=Math.atan2(s,i);return a<-.5*T&&(a+=L),{angle:a,distance:Math.sqrt(i*i+s*s)}}function q(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function G(t,e){return(t-e+A)%L-T}function K(t){return(t%L+L)%L}function J(t,e,i,s){let a=K(t),n=K(e),r=K(i),o=K(n-a),l=K(r-a),h=K(a-n),d=K(a-r);return a===n||a===r||s&&n===r||o>l&&h<d}function X(t,e,i){return Math.max(e,Math.min(i,t))}function Q(t,e,i,s=1e-6){return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function Z(t,e,i){let s;i=i||(i=>t[i]<e);let a=t.length-1,n=0;for(;a-n>1;)i(s=n+a>>1)?n=s:a=s;return{lo:n,hi:a}}let tt=(t,e,i,s)=>Z(t,i,s?s=>{let a=t[s][e];return a<i||a===i&&t[s+1][e]===i}:s=>t[s][e]<i),te=(t,e,i)=>Z(t,i,s=>t[s][e]>=i),ti=["push","pop","shift","splice","unshift"];function ts(t,e){let i=t._chartjs;if(!i)return;let s=i.listeners,a=s.indexOf(e);-1!==a&&s.splice(a,1),s.length>0||(ti.forEach(e=>{delete t[e]}),delete t._chartjs)}function ta(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tn="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tr(t,e){let i=[],s=!1;return function(...a){i=a,s||(s=!0,tn.call(window,()=>{s=!1,t.apply(e,i)}))}}let to=t=>"start"===t?"left":"end"===t?"right":"center",tl=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,th=(t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e;function td(t,e,i){let s=e.length,a=0,n=s;if(t._sorted){let{iScale:r,vScale:l,_parsed:h}=t,d=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,c=r.axis,{min:u,max:g,minDefined:f,maxDefined:p}=r.getUserBounds();if(f){if(a=Math.min(tt(h,c,u).lo,i?s:tt(e,c,r.getPixelForValue(u)).lo),d){let t=h.slice(0,a+1).reverse().findIndex(t=>!o(t[l.axis]));a-=Math.max(0,t)}a=X(a,0,s-1)}if(p){let t=Math.max(tt(h,r.axis,g,!0).hi+1,i?0:tt(e,c,r.getPixelForValue(g),!0).hi+1);if(d){let e=h.slice(t-1).findIndex(t=>!o(t[l.axis]));t+=Math.max(0,e)}n=X(t,a,s)-a}else n=s-a}return{start:a,count:n}}function tc(t){let{xScale:e,yScale:i,_scaleRanges:s}=t,a={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=a,!0;let n=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,a),n}let tu=t=>0===t||1===t,tg=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*L/i)),tf=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*L/i)+1,tp={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*R)+1,easeOutSine:t=>Math.sin(t*R),easeInOutSine:t=>-.5*(Math.cos(T*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>tu(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>tu(t)?t:tg(t,.075,.3),easeOutElastic:t=>tu(t)?t:tf(t,.075,.3),easeInOutElastic:t=>tu(t)?t:t<.5?.5*tg(2*t,.1125,.45):.5+.5*tf(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?t*t*(((e*=1.525)+1)*t-e)*.5:.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-tp.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*tp.easeInBounce(2*t):.5*tp.easeOutBounce(2*t-1)+.5};function tm(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function tx(t){return tm(t)?t:new a.Il(t)}function tb(t){return tm(t)?t:new a.Il(t).saturate(.5).darken(.1).hexString()}let t_=["x","y","borderWidth","radius","tension"],ty=["color","borderColor","backgroundColor"],tv=new Map;function tM(t,e,i){return(function(t,e){let i=t+JSON.stringify(e=e||{}),s=tv.get(i);return s||(s=new Intl.NumberFormat(t,e),tv.set(i,s)),s})(e,i).format(t)}let tw={values:t=>l(t)?t:""+t,numeric(t,e,i){let s;if(0===t)return"0";let a=this.chart.options.locale,n=t;if(i.length>1){let e;let a=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(a<1e-4||a>1e15)&&(s="scientific"),Math.abs(e=i.length>3?i[2].value-i[1].value:i[1].value-i[0].value)>=1&&t!==Math.floor(t)&&(e=t-Math.floor(t)),n=e}let r=N(Math.abs(n)),o=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:s,minimumFractionDigits:o,maximumFractionDigits:o};return Object.assign(l,this.options.ticks.format),tM(t,a,l)},logarithmic(t,e,i){return 0===t?"0":[1,2,3,5,10,15].includes(i[e].significand||t/Math.pow(10,Math.floor(N(t))))||e>.8*i.length?tw.numeric.call(this,t,e,i):""}},tk=Object.create(null),tD=Object.create(null);function tO(t,e){if(!e)return t;let i=e.split(".");for(let e=0,s=i.length;e<s;++e){let s=i[e];t=t[s]||(t[s]=Object.create(null))}return t}function tS(t,e,i){return"string"==typeof e?v(tO(t,e),i):v(tO(t,""),e)}class tP{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>tb(e.backgroundColor),this.hoverBorderColor=(t,e)=>tb(e.borderColor),this.hoverColor=(t,e)=>tb(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return tS(this,t,e)}get(t){return tO(this,t)}describe(t,e){return tS(tD,t,e)}override(t,e){return tS(tk,t,e)}route(t,e,i,s){let a=tO(this,t),n=tO(this,i),r="_"+e;Object.defineProperties(a,{[r]:{value:a[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[r],e=n[s];return h(t)?Object.assign({},e,t):u(t,e)},set(t){this[r]=t}}})}apply(t){t.forEach(t=>t(this))}}var tC=new tP({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:ty},numbers:{type:"number",properties:t_}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:tw.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function tT(t,e,i,s,a){let n=e[a];return n||(n=e[a]=t.measureText(a).width,i.push(a)),n>s&&(s=n),s}function tL(t,e,i){let s=t.currentDevicePixelRatio,a=0!==i?Math.max(i/2,.5):0;return Math.round((e-a)*s)/s+a}function tA(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function tE(t,e,i,s){tI(t,e,i,s,null)}function tI(t,e,i,s,a){let n,r,o,l,h,d,c,u;let g=e.pointStyle,f=e.rotation,p=e.radius,m=(f||0)*I;if(g&&"object"==typeof g&&("[object HTMLImageElement]"===(n=g.toString())||"[object HTMLCanvasElement]"===n)){t.save(),t.translate(i,s),t.rotate(m),t.drawImage(g,-g.width/2,-g.height/2,g.width,g.height),t.restore();return}if(!isNaN(p)&&!(p<=0)){switch(t.beginPath(),g){default:a?t.ellipse(i,s,a/2,p,0,0,L):t.arc(i,s,p,0,L),t.closePath();break;case"triangle":d=a?a/2:p,t.moveTo(i+Math.sin(m)*d,s-Math.cos(m)*p),m+=z,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*p),m+=z,t.lineTo(i+Math.sin(m)*d,s-Math.cos(m)*p),t.closePath();break;case"rectRounded":h=.516*p,r=Math.cos(m+F)*(l=p-h),c=Math.cos(m+F)*(a?a/2-h:l),o=Math.sin(m+F)*l,u=Math.sin(m+F)*(a?a/2-h:l),t.arc(i-c,s-o,h,m-T,m-R),t.arc(i+u,s-r,h,m-R,m),t.arc(i+c,s+o,h,m,m+R),t.arc(i-u,s+r,h,m+R,m+T),t.closePath();break;case"rect":if(!f){l=Math.SQRT1_2*p,d=a?a/2:l,t.rect(i-d,s-l,2*d,2*l);break}m+=F;case"rectRot":c=Math.cos(m)*(a?a/2:p),r=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+u,s-r),t.lineTo(i+c,s+o),t.lineTo(i-u,s+r),t.closePath();break;case"crossRot":m+=F;case"cross":c=Math.cos(m)*(a?a/2:p),r=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"star":c=Math.cos(m)*(a?a/2:p),r=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r),m+=F,c=Math.cos(m)*(a?a/2:p),r=Math.cos(m)*p,o=Math.sin(m)*p,u=Math.sin(m)*(a?a/2:p),t.moveTo(i-c,s-o),t.lineTo(i+c,s+o),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"line":r=a?a/2:Math.cos(m)*p,o=Math.sin(m)*p,t.moveTo(i-r,s-o),t.lineTo(i+r,s+o);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(m)*(a?a/2:p),s+Math.sin(m)*p);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function tR(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function tF(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function tz(t){t.restore()}function tN(t,e,i,s,a){if(!e)return t.lineTo(i.x,i.y);if("middle"===a){let s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===a!=!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function tV(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function tB(t,e,i,s,a,n={}){let r,h;let d=l(e)?e:[e],c=n.strokeWidth>0&&""!==n.strokeColor;for(t.save(),t.font=a.string,n.translation&&t.translate(n.translation[0],n.translation[1]),o(n.rotation)||t.rotate(n.rotation),n.color&&(t.fillStyle=n.color),n.textAlign&&(t.textAlign=n.textAlign),n.textBaseline&&(t.textBaseline=n.textBaseline),r=0;r<d.length;++r)h=d[r],n.backdrop&&function(t,e){let i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}(t,n.backdrop),c&&(n.strokeColor&&(t.strokeStyle=n.strokeColor),o(n.strokeWidth)||(t.lineWidth=n.strokeWidth),t.strokeText(h,i,s,n.maxWidth)),t.fillText(h,i,s,n.maxWidth),function(t,e,i,s,a){if(a.strikethrough||a.underline){let n=t.measureText(s),r=e-n.actualBoundingBoxLeft,o=e+n.actualBoundingBoxRight,l=i-n.actualBoundingBoxAscent,h=i+n.actualBoundingBoxDescent,d=a.strikethrough?(l+h)/2:h;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=a.decorationWidth||2,t.moveTo(r,d),t.lineTo(o,d),t.stroke()}}(t,i,s,h,n),s+=Number(a.lineHeight);t.restore()}function tj(t,e){let{x:i,y:s,w:a,h:n,radius:r}=e;t.arc(i+r.topLeft,s+r.topLeft,r.topLeft,1.5*T,T,!0),t.lineTo(i,s+n-r.bottomLeft),t.arc(i+r.bottomLeft,s+n-r.bottomLeft,r.bottomLeft,T,R,!0),t.lineTo(i+a-r.bottomRight,s+n),t.arc(i+a-r.bottomRight,s+n-r.bottomRight,r.bottomRight,R,0,!0),t.lineTo(i+a,s+r.topRight),t.arc(i+a-r.topRight,s+r.topRight,r.topRight,0,-R,!0),t.lineTo(i+r.topLeft,s)}let tW=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,tH=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,t$=t=>+t||0;function tU(t,e){let i={},s=h(e),a=s?Object.keys(e):e,n=h(t)?s?i=>u(t[i],t[e[i]]):e=>t[e]:()=>t;for(let t of a)i[t]=t$(n(t));return i}function tY(t){return tU(t,{top:"y",right:"x",bottom:"y",left:"x"})}function tq(t){return tU(t,["topLeft","topRight","bottomLeft","bottomRight"])}function tG(t){let e=tY(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function tK(t,e){t=t||{},e=e||tC.font;let i=u(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let s=u(t.style,e.style);s&&!(""+s).match(tH)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let a={family:u(t.family,e.family),lineHeight:function(t,e){let i=(""+t).match(tW);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}(u(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:u(t.weight,e.weight),string:""};return a.string=!a||o(a.size)||o(a.family)?null:(a.style?a.style+" ":"")+(a.weight?a.weight+" ":"")+a.size+"px "+a.family,a}function tJ(t,e,i,s){let a,n,r,o=!0;for(a=0,n=t.length;a<n;++a)if(void 0!==(r=t[a])&&(void 0!==e&&"function"==typeof r&&(r=r(e),o=!1),void 0!==i&&l(r)&&(r=r[i%r.length],o=!1),void 0!==r))return s&&!o&&(s.cacheable=!1),r}function tX(t,e){return Object.assign(Object.create(t),e)}function tQ(t,e=[""],i,s,a=()=>t[0]){let n=i||t;return void 0===s&&(s=t7("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:n,_fallback:s,_getTarget:a,override:i=>tQ([i,...t],e,n,s)},{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>t5(i,s,()=>(function(t,e,i,s){let a;for(let n of e)if(void 0!==(a=t7(t1(n,t),i)))return t2(t,a)?t6(i,s,t,a):a})(s,e,t,i)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>t8(t).includes(e),ownKeys:t=>t8(t),set(t,e,i){let s=t._storage||(t._storage=a());return t[e]=s[e]=i,delete t._keys,!0}})}function tZ(t,e,i,s){return new Proxy({_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:t0(t,s),setContext:e=>tZ(t,e,i,s),override:a=>tZ(t.override(a),e,i,s)},{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>t5(t,e,()=>(function(t,e,i){let{_proxy:s,_context:a,_subProxy:n,_descriptors:r}=t,o=s[e];return P(o)&&r.isScriptable(e)&&(o=function(t,e,i,s){let{_proxy:a,_context:n,_subProxy:r,_stack:o}=i;if(o.has(t))throw Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(n,r||s);return o.delete(t),t2(t,l)&&(l=t6(a._scopes,a,t,l)),l}(e,o,t,i)),l(o)&&o.length&&(o=function(t,e,i,s){let{_proxy:a,_context:n,_subProxy:r,_descriptors:o}=i;if(void 0!==n.index&&s(t))return e[n.index%e.length];if(h(e[0])){let i=e,s=a._scopes.filter(t=>t!==i);for(let l of(e=[],i)){let i=t6(s,a,t,l);e.push(tZ(i,n,r&&r[t],o))}}return e}(e,o,t,r.isIndexable)),t2(e,o)&&(o=tZ(o,a,n&&n[e],r)),o})(t,e,i)),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,s)=>(t[i]=s,delete e[i],!0)})}function t0(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:a=e.allKeys}=t;return{allKeys:a,scriptable:i,indexable:s,isScriptable:P(i)?i:()=>i,isIndexable:P(s)?s:()=>s}}let t1=(t,e)=>t?t+O(e):e,t2=(t,e)=>h(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function t5(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let s=i();return t[e]=s,s}let t3=(t,e)=>!0===t?e:"string"==typeof t?D(e,t):void 0;function t6(t,e,i,s){var a;let n=e._rootScopes,r=P(a=e._fallback)?a(i,s):a,o=[...t,...n],d=new Set;d.add(s);let c=t4(d,o,i,r||i,s);return null!==c&&(void 0===r||r===i||null!==(c=t4(d,o,r,c,s)))&&tQ(Array.from(d),[""],n,r,()=>(function(t,e,i){let s=t._getTarget();e in s||(s[e]={});let a=s[e];return l(a)&&h(i)?i:a||{}})(e,i,s))}function t4(t,e,i,s,a){for(;i;)i=function(t,e,i,s,a){for(let r of e){let e=t3(i,r);if(e){var n;t.add(e);let r=P(n=e._fallback)?n(i,a):n;if(void 0!==r&&r!==i&&r!==s)return r}else if(!1===e&&void 0!==s&&i!==s)return null}return!1}(t,e,i,s,a);return i}function t7(t,e){for(let i of e){if(!i)continue;let e=i[t];if(void 0!==e)return e}}function t8(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let i of t)for(let t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function t9(t,e,i,s){let a,n,r;let{iScale:o}=t,{key:l="r"}=this._parsing,h=Array(s);for(a=0;a<s;++a)r=e[n=a+i],h[a]={r:o.parse(D(r,l),n)};return h}let et=Number.EPSILON||1e-14,ee=(t,e)=>e<t.length&&!t[e].skip&&t[e],ei=t=>"x"===t?"y":"x";function es(t,e,i){return Math.max(Math.min(t,i),e)}function ea(){return"undefined"!=typeof window&&"undefined"!=typeof document}function en(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function er(t,e,i){let s;return"string"==typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}let eo=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),el=["top","right","bottom","left"];function eh(t,e,i){let s={};i=i?"-"+i:"";for(let a=0;a<4;a++){let n=el[a];s[n]=parseFloat(t[e+"-"+n+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}let ed=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function ec(t,e){if("native"in t)return t;let{canvas:i,currentDevicePixelRatio:s}=e,a=eo(i),n="border-box"===a.boxSizing,r=eh(a,"padding"),o=eh(a,"border","width"),{x:l,y:h,box:d}=function(t,e){let i,s;let a=t.touches,n=a&&a.length?a[0]:t,{offsetX:r,offsetY:o}=n,l=!1;if(ed(r,o,t.target))i=r,s=o;else{let t=e.getBoundingClientRect();i=n.clientX-t.left,s=n.clientY-t.top,l=!0}return{x:i,y:s,box:l}}(t,i),c=r.left+(d&&o.left),u=r.top+(d&&o.top),{width:g,height:f}=e;return n&&(g-=r.width+o.width,f-=r.height+o.height),{x:Math.round((l-c)/g*i.width/s),y:Math.round((h-u)/f*i.height/s)}}let eu=t=>Math.round(10*t)/10;function eg(t,e,i){let s=e||1,a=Math.floor(t.height*s),n=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let r=t.canvas;return r.style&&(i||!r.style.height&&!r.style.width)&&(r.style.height=`${t.height}px`,r.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||r.height!==a||r.width!==n)&&(t.currentDevicePixelRatio=s,r.height=a,r.width=n,t.ctx.setTransform(s,0,0,s,0,0),!0)}let ef=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};ea()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function ep(t,e){let i=eo(t).getPropertyValue(e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function em(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function ex(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function eb(t,e,i,s){let a={x:t.cp2x,y:t.cp2y},n={x:e.cp1x,y:e.cp1y},r=em(t,a,i),o=em(a,n,i),l=em(n,e,i),h=em(r,o,i),d=em(o,l,i);return em(h,d,i)}function e_(t,e,i){var s;return t?(s=i,{x:t=>e+e+s-t,setWidth(t){s=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function ey(t,e){let i,s;("ltr"===e||"rtl"===e)&&(s=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function ev(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function eM(t){return"angle"===t?{between:J,compare:G,normalize:K}:{between:Q,compare:(t,e)=>t-e,normalize:t=>t}}function ew({start:t,end:e,count:i,loop:s,style:a}){return{start:t%i,end:e%i,loop:s&&(e-t+1)%i==0,style:a}}function ek(t,e,i){let s,a,n;if(!i)return[t];let{property:r,start:o,end:l}=i,h=e.length,{compare:d,between:c,normalize:u}=eM(r),{start:g,end:f,loop:p,style:m}=function(t,e,i){let s;let{property:a,start:n,end:r}=i,{between:o,normalize:l}=eM(a),h=e.length,{start:d,end:c,loop:u}=t;if(u){for(d+=h,c+=h,s=0;s<h&&o(l(e[d%h][a]),n,r);++s)d--,c--;d%=h,c%=h}return c<d&&(c+=h),{start:d,end:c,loop:u,style:t.style}}(t,e,i),x=[],b=!1,_=null,y=()=>c(o,n,s)&&0!==d(o,n),v=()=>0===d(l,s)||c(l,n,s),M=()=>b||y(),w=()=>!b||v();for(let t=g,i=g;t<=f;++t)(a=e[t%h]).skip||(s=u(a[r]))===n||(b=c(s,o,l),null===_&&M()&&(_=0===d(s,o)?t:i),null!==_&&w()&&(x.push(ew({start:_,end:t,loop:p,count:h,style:m})),_=null),i=t,n=s);return null!==_&&x.push(ew({start:_,end:f,loop:p,count:h,style:m})),x}function eD(t,e){let i=[],s=t.segments;for(let a=0;a<s.length;a++){let n=ek(s[a],t.points,e);n.length&&i.push(...n)}return i}function eO(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){let a=t._chart.getContext(),n=eS(t.options),{_datasetIndex:r,options:{spanGaps:o}}=t,l=i.length,h=[],d=n,c=e[0].start,u=c;function g(t,e,s,a){let n=o?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=n;for(;i[e%l].skip;)e+=n;t%l!=e%l&&(h.push({start:t%l,end:e%l,loop:s,style:a}),d=a,c=e%l)}}for(let t of e){let e;let n=i[(c=o?c:t.start)%l];for(u=c+1;u<=t.end;u++){let o=i[u%l];(function(t,e){if(!e)return!1;let i=[],s=function(t,e){return tm(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)})(e=eS(s.setContext(tX(a,{type:"segment",p0:n,p1:o,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:r}))),d)&&g(c,u-1,t.loop,d),n=o,d=e}c<u-1&&g(c,u-1,t.loop,d)}return h}(t,e,i,s):e}function eS(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function eP(t,e,i){return t.options.clip?t[i]:e[i]}class eC{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let a=e.listeners[s],n=e.duration;a.forEach(s=>s({chart:t,initial:e.initial,numSteps:n,currentStep:Math.min(i-e.start,n)}))}_refresh(){this._request||(this._running=!0,this._request=tn.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{let a;if(!i.running||!i.items.length)return;let n=i.items,r=n.length-1,o=!1;for(;r>=0;--r)(a=n[r])._active?(a._total>i.duration&&(i.duration=a._total),a.tick(t),o=!0):(n[r]=n[n.length-1],n.pop());o&&(s.draw(),this._notify(s,i,t,"progress")),n.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=n.length}),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!!e&&!!e.running&&!!e.items.length}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var eT=new eC;let eL="transparent",eA={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){let s=tx(t||eL),a=s.valid&&tx(e||eL);return a&&a.valid?a.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class eE{constructor(t,e,i,s){let a=e[i];s=tJ([t.to,s,a,t.from]);let n=tJ([t.from,a,s]);this._active=!0,this._fn=t.fn||eA[t.type||typeof n],this._easing=tp[t.easing]||tp.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=n,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let s=this._target[this._prop],a=i-this._start,n=this._duration-a;this._start=i,this._duration=Math.floor(Math.max(n,t.duration)),this._total+=a,this._loop=!!t.loop,this._to=tJ([t.to,e,s,t.from]),this._from=tJ([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e;let i=t-this._start,s=this._duration,a=this._prop,n=this._from,r=this._loop,o=this._to;if(this._active=n!==o&&(r||i<s),!this._active){this._target[a]=o,this._notify(!0);return}if(i<0){this._target[a]=n;return}e=i/s%2,e=r&&e>1?2-e:e,e=this._easing(Math.min(1,Math.max(0,e))),this._target[a]=this._fn(n,o,e)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class eI{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!h(t))return;let e=Object.keys(tC.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{let a=t[s];if(!h(a))return;let n={};for(let t of e)n[t]=a[t];(l(a.properties)&&a.properties||[s]).forEach(t=>{t!==s&&i.has(t)||i.set(t,n)})})}_animateOptions(t,e){let i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i){t.options=e;return}return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}(t,i);if(!s)return[];let a=this._createAnimations(s,i);return i.$shared&&(function(t,e){let i=[],s=Object.keys(e);for(let e=0;e<s.length;e++){let a=t[s[e]];a&&a.active()&&i.push(a.wait())}return Promise.all(i)})(t.options.$animations,i).then(()=>{t.options=i},()=>{}),a}_createAnimations(t,e){let i;let s=this._properties,a=[],n=t.$animations||(t.$animations={}),r=Object.keys(e),o=Date.now();for(i=r.length-1;i>=0;--i){let l=r[i];if("$"===l.charAt(0))continue;if("options"===l){a.push(...this._animateOptions(t,e));continue}let h=e[l],d=n[l],c=s.get(l);if(d){if(c&&d.active()){d.update(c,h,o);continue}d.cancel()}if(!c||!c.duration){t[l]=h;continue}n[l]=d=new eE(c,t,l,h),a.push(d)}return a}update(t,e){if(0===this._properties.size){Object.assign(t,e);return}let i=this._createAnimations(t,e);if(i.length)return eT.add(this._chart,i),!0}}function eR(t,e){let i=t&&t.options||{},s=i.reverse,a=void 0===i.min?e:0,n=void 0===i.max?e:0;return{start:s?n:a,end:s?a:n}}function eF(t,e){let i,s;let a=[],n=t._getSortedDatasetMetas(e);for(i=0,s=n.length;i<s;++i)a.push(n[i].index);return a}function ez(t,e,i,s={}){let a,n,r,o;let l=t.keys,h="single"===s.mode;if(null===e)return;let c=!1;for(a=0,n=l.length;a<n;++a){if((r=+l[a])===i){if(c=!0,s.all)continue;break}d(o=t.values[r])&&(h||0===e||V(e)===V(o))&&(e+=o)}return c||s.all?e:0}function eN(t,e){let i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function eV(t,e,i,s){for(let a of e.getMatchingVisibleMetas(s).reverse()){let e=t[a.index];if(i&&e>0||!i&&e<0)return a.index}return null}function eB(t,e){let i;let{chart:s,_cachedMeta:a}=t,n=s._stacks||(s._stacks={}),{iScale:r,vScale:o,index:l}=a,h=r.axis,d=o.axis,c=`${r.id}.${o.id}.${a.stack||a.type}`,u=e.length;for(let t=0;t<u;++t){let s=e[t],{[h]:r,[d]:u}=s;(i=(s._stacks||(s._stacks={}))[d]=function(t,e,i){let s=t[e]||(t[e]={});return s[i]||(s[i]={})}(n,c,r))[l]=u,i._top=eV(i,o,!0,a.type),i._bottom=eV(i,o,!1,a.type),(i._visualValues||(i._visualValues={}))[l]=u}}function ej(t,e){let i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function eW(t,e){let i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s)for(let a of e=e||t._parsed){let t=a._stacks;if(!t||void 0===t[s]||void 0===t[s][i])return;delete t[s][i],void 0!==t[s]._visualValues&&void 0!==t[s]._visualValues[i]&&delete t[s]._visualValues[i]}}let eH=t=>"reset"===t||"none"===t,e$=(t,e)=>e?t:Object.assign({},t),eU=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:eF(i,!0),values:null};class eY{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=eN(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&eW(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,a=e.xAxisID=u(i.xAxisID,ej(t,"x")),n=e.yAxisID=u(i.yAxisID,ej(t,"y")),r=e.rAxisID=u(i.rAxisID,ej(t,"r")),o=e.indexAxis,l=e.iAxisID=s(o,a,n,r),h=e.vAxisID=s(o,n,a,r);e.xScale=this.getScaleForId(a),e.yScale=this.getScaleForId(n),e.rScale=this.getScaleForId(r),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&ts(this._data,this),t._stacked&&eW(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(h(e)){let t=this._cachedMeta;this._data=function(t,e){let i,s,a;let{iScale:n,vScale:r}=e,o="x"===n.axis?"x":"y",l="x"===r.axis?"x":"y",h=Object.keys(t),d=Array(h.length);for(i=0,s=h.length;i<s;++i)a=h[i],d[i]={[o]:a,[l]:t[a]};return d}(e,t)}else if(i!==e){if(i){ts(i,this);let t=this._cachedMeta;eW(t),t._parsed=[]}e&&Object.isExtensible(e)&&function(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),ti.forEach(e=>{let i="_onData"+O(e),s=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let a=s.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),a}})})}(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let a=e._stacked;e._stacked=eN(e.vScale,e),e.stack!==i.stack&&(s=!0,eW(e),e.stack=i.stack),this._resyncElements(t),(s||a!==e._stacked)&&(eB(this,e._parsed),e._stacked=eN(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let i,s,a;let{_cachedMeta:n,_data:r}=this,{iScale:o,_stacked:d}=n,c=o.axis,u=0===t&&e===r.length||n._sorted,g=t>0&&n._parsed[t-1];if(!1===this._parsing)n._parsed=r,n._sorted=!0,a=r;else{a=l(r[t])?this.parseArrayData(n,r,t,e):h(r[t])?this.parseObjectData(n,r,t,e):this.parsePrimitiveData(n,r,t,e);let o=()=>null===s[c]||g&&s[c]<g[c];for(i=0;i<e;++i)n._parsed[i+t]=s=a[i],u&&(o()&&(u=!1),g=s);n._sorted=u}d&&eB(this,a)}parsePrimitiveData(t,e,i,s){let a,n;let{iScale:r,vScale:o}=t,l=r.axis,h=o.axis,d=r.getLabels(),c=r===o,u=Array(s);for(a=0;a<s;++a)n=a+i,u[a]={[l]:c||r.parse(d[n],n),[h]:o.parse(e[n],n)};return u}parseArrayData(t,e,i,s){let a,n,r;let{xScale:o,yScale:l}=t,h=Array(s);for(a=0;a<s;++a)r=e[n=a+i],h[a]={x:o.parse(r[0],n),y:l.parse(r[1],n)};return h}parseObjectData(t,e,i,s){let a,n,r;let{xScale:o,yScale:l}=t,{xAxisKey:h="x",yAxisKey:d="y"}=this._parsing,c=Array(s);for(a=0;a<s;++a)r=e[n=a+i],c[a]={x:o.parse(D(r,h),n),y:l.parse(D(r,d),n)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,a=this._cachedMeta,n=e[t.axis];return ez({keys:eF(s,!0),values:e._stacks[t.axis]._visualValues},n,a.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let a=i[e.axis],n=null===a?NaN:a,r=s&&i._stacks[e.axis];s&&r&&(s.values=r,n=ez(s,a,this._cachedMeta.index)),t.min=Math.min(t.min,n),t.max=Math.max(t.max,n)}getMinMax(t,e){let i,s;let a=this._cachedMeta,n=a._parsed,r=a._sorted&&t===a.iScale,o=n.length,l=this._getOtherScale(t),h=eU(e,a,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:u,max:g}=function(t){let{min:e,max:i,minDefined:s,maxDefined:a}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:a?i:Number.POSITIVE_INFINITY}}(l);function f(){let e=(s=n[i])[l.axis];return!d(s[t.axis])||u>e||g<e}for(i=0;i<o&&(f()||(this.updateRangeFromParsed(c,t,s,h),!r));++i);if(r){for(i=o-1;i>=0;--i)if(!f()){this.updateRangeFromParsed(c,t,s,h);break}}return c}getAllParsedValues(t){let e,i,s;let a=this._cachedMeta._parsed,n=[];for(e=0,i=a.length;e<i;++e)d(s=a[e][t.axis])&&n.push(s);return n}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,a=this.getParsed(t);return{label:i?""+i.getLabelForValue(a[i.axis]):"",value:s?""+s.getLabelForValue(a[s.axis]):""}}_update(t){var e;let i,s,a,n;let r=this._cachedMeta;this.update(t||"default"),r._clip=(h(e=u(this.options.clip,function(t,e,i){if(!1===i)return!1;let s=eR(t,i),a=eR(e,i);return{top:a.end,right:s.end,bottom:a.start,left:s.start}}(r.xScale,r.yScale,this.getMaxOverflow())))?(i=e.top,s=e.right,a=e.bottom,n=e.left):i=s=a=n=e,{top:i,right:s,bottom:a,left:n,disabled:!1===e})}update(t){}draw(){let t;let e=this._ctx,i=this.chart,s=this._cachedMeta,a=s.data||[],n=i.chartArea,r=[],o=this._drawStart||0,l=this._drawCount||a.length-o,h=this.options.drawActiveElementsOnTop;for(s.dataset&&s.dataset.draw(e,n,o,l),t=o;t<o+l;++t){let i=a[t];i.hidden||(i.active&&h?r.push(i):i.draw(e,n))}for(t=0;t<r.length;++t)r[t].draw(e,n)}getStyle(t,e){let i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){var s;let a;let n=this.getDataset();if(t>=0&&t<this._cachedMeta.data.length){let e=this._cachedMeta.data[t];(a=e.$context||(e.$context=tX(this.getContext(),{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"}))).parsed=this.getParsed(t),a.raw=n.data[t],a.index=a.dataIndex=t}else(a=this.$context||(this.$context=tX(this.chart.getContext(),{active:!1,dataset:void 0,datasetIndex:s=this.index,index:s,mode:"default",type:"dataset"}))).dataset=n,a.index=a.datasetIndex=this.index;return a.active=!!e,a.mode=i,a}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let s="active"===e,a=this._cachedDataOpts,n=t+"-"+e,r=a[n],o=this.enableOptionSharing&&S(i);if(r)return e$(r,o);let l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),d=s?[`${t}Hover`,"hover",t,""]:[t,""],c=l.getOptionScopes(this.getDataset(),h),u=Object.keys(tC.elements[t]),g=l.resolveNamedOptions(c,u,()=>this.getContext(i,s,e),d);return g.$shared&&(g.$shared=o,a[n]=Object.freeze(e$(g,o))),g}_resolveAnimations(t,e,i){let s;let a=this.chart,n=this._cachedDataOpts,r=`animation-${e}`,o=n[r];if(o)return o;if(!1!==a.options.animation){let a=this.chart.config,n=a.datasetAnimationScopeKeys(this._type,e),r=a.getOptionScopes(this.getDataset(),n);s=a.createResolver(r,this.getContext(t,i,e))}let l=new eI(a,s&&s.animations);return s&&s._cacheable&&(n[r]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||eH(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,a=this.getSharedOptions(i),n=this.includeOptions(e,a)||a!==s;return this.updateSharedOptions(a,e,i),{sharedOptions:a,includeOptions:n}}updateElement(t,e,i,s){eH(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!eH(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let a=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(a)||a})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];let s=i.length,a=e.length,n=Math.min(a,s);n&&this.parse(0,n),a>s?this._insertElements(s,a-s,t):a<s&&this._removeElements(a,s-a)}_insertElements(t,e,i=!0){let s;let a=this._cachedMeta,n=a.data,r=t+e,o=t=>{for(t.length+=e,s=t.length-1;s>=r;s--)t[s]=t[s-e]};for(o(n),s=t;s<r;++s)n[s]=new this.dataElementType;this._parsing&&o(a._parsed),this.parse(t,e),i&&this.updateElements(n,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&eW(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function eq(t,e,i,s){return l(t)?function(t,e,i,s){let a=i.parse(t[0],s),n=i.parse(t[1],s),r=Math.min(a,n),o=Math.max(a,n),l=r,h=o;Math.abs(r)>Math.abs(o)&&(l=o,h=r),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:a,end:n,min:r,max:o}}(t,e,i,s):e[i.axis]=i.parse(t,s),e}function eG(t,e,i,s){let a,n,r,o;let l=t.iScale,h=t.vScale,d=l.getLabels(),c=l===h,u=[];for(a=i,n=i+s;a<n;++a)o=e[a],(r={})[l.axis]=c||l.parse(d[a],a),u.push(eq(o,r,h,a));return u}function eK(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function eJ(t,e,i,s){var a;return t=s?eX(t=(a=t)===e?i:a===i?e:a,i,e):eX(t,e,i)}function eX(t,e,i){return"start"===t?e:"end"===t?i:t}class eQ extends eY{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,s){return eG(t,e,i,s)}parseArrayData(t,e,i,s){return eG(t,e,i,s)}parseObjectData(t,e,i,s){let a,n,r,o;let{iScale:l,vScale:h}=t,{xAxisKey:d="x",yAxisKey:c="y"}=this._parsing,u="x"===l.axis?d:c,g="x"===h.axis?d:c,f=[];for(a=i,n=i+s;a<n;++a)o=e[a],(r={})[l.axis]=l.parse(D(o,u),a),f.push(eq(D(o,g),r,h,a));return f}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let a=i._custom;a&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let{iScale:e,vScale:i}=this._cachedMeta,s=this.getParsed(t),a=s._custom,n=eK(a)?"["+a.start+", "+a.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+e.getLabelForValue(s[e.axis]),value:n}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){let a="reset"===s,{index:n,_cachedMeta:{vScale:r}}=this,l=r.getBasePixel(),h=r.isHorizontal(),d=this._getRuler(),{sharedOptions:c,includeOptions:u}=this._getSharedOptions(e,s);for(let g=e;g<e+i;g++){let e=this.getParsed(g),i=a||o(e[r.axis])?{base:l,head:l}:this._calculateBarValuePixels(g),f=this._calculateBarIndexPixels(g,d),p=(e._stacks||{})[r.axis],m={horizontal:h,base:i.base,enableBorderRadius:!p||eK(e._custom)||n===p._top||n===p._bottom,x:h?i.head:f.center,y:h?f.center:i.head,height:h?f.size:Math.abs(i.size),width:h?Math.abs(i.size):f.size};u&&(m.options=c||this.resolveDataElementOptions(g,t[g].active?"active":s));let x=m.options||t[g].options;!function(t,e,i,s){let a,n,r,o,l,h=e.borderSkipped,d={};if(!h){t.borderSkipped=d;return}if(!0===h){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:c,end:u,reverse:g,top:f,bottom:p}=(t.horizontal?(a=t.base>t.x,n="left",r="right"):(a=t.base<t.y,n="bottom",r="top"),a?(o="end",l="start"):(o="start",l="end"),{start:n,end:r,reverse:a,top:o,bottom:l});"middle"===h&&i&&(t.enableBorderRadius=!0,(i._top||0)===s?h=f:(i._bottom||0)===s?h=p:(d[eJ(p,c,u,g)]=!0,h=f)),d[eJ(h,c,u,g)]=!0,t.borderSkipped=d}(m,x,p,n),function(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?1===i?.33:0:e}(m,x,d.ratio),this.updateElement(t[g],g,m,s)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),a=i.options.stacked,n=[],r=this._cachedMeta.controller.getParsed(e),l=r&&r[i.axis],h=t=>{let e=t._parsed.find(t=>t[i.axis]===l),s=e&&e[t.vScale.axis];if(o(s)||isNaN(s))return!0};for(let i of s)if(!(void 0!==e&&h(i))&&((!1===a||-1===n.indexOf(i.stack)||void 0===a&&void 0===i.stack)&&n.push(i.stack),i.index===t))break;return n.length||n.push(void 0),n}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){let t=this.chart.scales,e=this.chart.options.indexAxis;return Object.keys(t).filter(i=>t[i].axis===e).shift()}_getAxis(){let t={},e=this.getFirstScaleIdForIndexAxis();for(let i of this.chart.data.datasets)t[u("x"===this.chart.options.indexAxis?i.xAxisID:i.yAxisID,e)]=!0;return Object.keys(t)}_getStackIndex(t,e,i){let s=this._getStacks(t,i),a=void 0!==e?s.indexOf(e):-1;return -1===a?s.length-1:a}_getRuler(){let t,e;let i=this.options,s=this._cachedMeta,a=s.iScale,n=[];for(t=0,e=s.data.length;t<e;++t)n.push(a.getPixelForValue(this.getParsed(t)[a.axis],t));let r=i.barThickness;return{min:r||function(t){let e,i,s,a;let n=t.iScale,r=function(t,e){if(!t._cache.$bar){let i=t.getMatchingVisibleMetas(e),s=[];for(let e=0,a=i.length;e<a;e++)s=s.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=ta(s.sort((t,e)=>t-e))}return t._cache.$bar}(n,t.type),o=n._length,l=()=>{32767!==s&&-32768!==s&&(S(a)&&(o=Math.min(o,Math.abs(s-a)||o)),a=s)};for(e=0,i=r.length;e<i;++e)s=n.getPixelForValue(r[e]),l();for(e=0,a=void 0,i=n.ticks.length;e<i;++e)s=n.getPixelForTick(e),l();return o}(s),pixels:n,start:a._startPixel,end:a._endPixel,stackCount:this._getStackCount(),scale:a,grouped:i.grouped,ratio:r?1:i.categoryPercentage*i.barPercentage}}_calculateBarValuePixels(t){let e,i;let{_cachedMeta:{vScale:s,_stacked:a,index:n},options:{base:r,minBarLength:l}}=this,h=r||0,d=this.getParsed(t),c=d._custom,u=eK(c),g=d[s.axis],f=0,p=a?this.applyStack(s,d,a):g;p!==g&&(f=p-g,p=g),u&&(g=c.barStart,p=c.barEnd-c.barStart,0!==g&&V(g)!==V(c.barEnd)&&(f=0),f+=g);let m=o(r)||u?f:r,x=s.getPixelForValue(m);if(Math.abs(i=(e=this.chart.getDataVisibility(t)?s.getPixelForValue(f+p):x)-x)<l){var b;i=(0!==(b=i)?V(b):(s.isHorizontal()?1:-1)*(s.min>=h?1:-1))*l,g===h&&(x-=i/2);let t=s.getPixelForDecimal(0),r=s.getPixelForDecimal(1);e=(x=Math.max(Math.min(x,Math.max(t,r)),Math.min(t,r)))+i,a&&!u&&(d._stacks[s.axis]._visualValues[n]=s.getValueForPixel(e)-s.getValueForPixel(x))}if(x===s.getPixelForValue(h)){let t=V(i)*s.getLineWidthForValue(h)/2;x+=t,i-=t}return{size:i,base:x,head:e,center:e+i/2}}_calculateBarIndexPixels(t,e){let i,s;let a=e.scale,n=this.options,r=n.skipNull,l=u(n.maxBarThickness,1/0),h=this._getAxisCount();if(e.grouped){let a=r?this._getStackCount(t):e.stackCount,d="flex"===n.barThickness?function(t,e,i,s){let a=e.pixels,n=a[t],r=t>0?a[t-1]:null,o=t<a.length-1?a[t+1]:null,l=i.categoryPercentage;null===r&&(r=n-(null===o?e.end-e.start:o-n)),null===o&&(o=n+n-r);let h=n-(n-Math.min(r,o))/2*l;return{chunk:Math.abs(o-r)/2*l/s,ratio:i.barPercentage,start:h}}(t,e,n,a*h):function(t,e,i,s){let a,n;let r=i.barThickness;return o(r)?(a=e.min*i.categoryPercentage,n=i.barPercentage):(a=r*s,n=1),{chunk:a/s,ratio:n,start:e.pixels[t]-a/2}}(t,e,n,a*h),c="x"===this.chart.options.indexAxis?this.getDataset().xAxisID:this.getDataset().yAxisID,g=this._getAxis().indexOf(u(c,this.getFirstScaleIdForIndexAxis())),f=this._getStackIndex(this.index,this._cachedMeta.stack,r?t:void 0)+g;i=d.start+d.chunk*f+d.chunk/2,s=Math.min(l,d.chunk*d.ratio)}else i=a.getPixelForValue(this.getParsed(t)[a.axis],t),s=Math.min(l,e.min*e.ratio);return{base:i-s/2,head:i+s/2,center:i,size:s}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,a=0;for(;a<s;++a)null===this.getParsed(a)[e.axis]||i[a].hidden||i[a].draw(this._ctx)}}class eZ extends eY{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let a=super.parsePrimitiveData(t,e,i,s);for(let t=0;t<a.length;t++)a[t]._custom=this.resolveDataElementOptions(t+i).radius;return a}parseArrayData(t,e,i,s){let a=super.parseArrayData(t,e,i,s);for(let t=0;t<a.length;t++){let s=e[i+t];a[t]._custom=u(s[2],this.resolveDataElementOptions(t+i).radius)}return a}parseObjectData(t,e,i,s){let a=super.parseObjectData(t,e,i,s);for(let t=0;t<a.length;t++){let s=e[i+t];a[t]._custom=u(s&&s.r&&+s.r,this.resolveDataElementOptions(t+i).radius)}return a}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,n=this.getParsed(t),r=s.getLabelForValue(n.x),o=a.getLabelForValue(n.y),l=n._custom;return{label:i[t]||"",value:"("+r+", "+o+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let a="reset"===s,{iScale:n,vScale:r}=this._cachedMeta,{sharedOptions:o,includeOptions:l}=this._getSharedOptions(e,s),h=n.axis,d=r.axis;for(let c=e;c<e+i;c++){let e=t[c],i=!a&&this.getParsed(c),u={},g=u[h]=a?n.getPixelForDecimal(.5):n.getPixelForValue(i[h]),f=u[d]=a?r.getBasePixel():r.getPixelForValue(i[d]);u.skip=isNaN(g)||isNaN(f),l&&(u.options=o||this.resolveDataElementOptions(c,e.active?"active":s),a&&(u.options.radius=0)),this.updateElement(e,c,u,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));let a=s.radius;return"active"!==e&&(s.radius=0),s.radius+=u(i&&i._custom,a),s}}class e0 extends eY{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let n=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=i;else{let a,n,r=t=>+i[t];if(h(i[t])){let{key:t="value"}=this._parsing;r=e=>+D(i[e],t)}for(a=t,n=t+e;a<n;++a)s._parsed[a]=r(a)}}_getRotation(){return $(this.options.rotation-90)}_getCircumference(){return $(this.options.circumference)}_getRotationExtents(){let t=L,e=-L;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){let s=this.chart.getDatasetMeta(i).controller,a=s._getRotation(),n=s._getCircumference();t=Math.min(t,a),e=Math.max(e,a+n)}return{rotation:t,circumference:e-t}}update(t){let{chartArea:e}=this.chart,i=this._cachedMeta,s=i.data,a=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,n=Math.max((Math.min(e.width,e.height)-a)/2,0),r=Math.min(g(this.options.cutout,n),1),o=this._getRingWeight(this.index),{circumference:l,rotation:h}=this._getRotationExtents(),{ratioX:d,ratioY:c,offsetX:u,offsetY:p}=function(t,e,i){let s=1,a=1,n=0,r=0;if(e<L){let o=t+e,l=Math.cos(t),h=Math.sin(t),d=Math.cos(o),c=Math.sin(o),u=(e,s,a)=>J(e,t,o,!0)?1:Math.max(s,s*i,a,a*i),g=(e,s,a)=>J(e,t,o,!0)?-1:Math.min(s,s*i,a,a*i),f=u(0,l,d),p=u(R,h,c),m=g(T,l,d),x=g(T+R,h,c);s=(f-m)/2,a=(p-x)/2,n=-(f+m)/2,r=-(p+x)/2}return{ratioX:s,ratioY:a,offsetX:n,offsetY:r}}(h,l,r),m=(e.width-a)/d,x=(e.height-a)/c,b=f(this.options.radius,Math.max(Math.min(m,x)/2,0)),_=Math.max(b*r,0),y=(b-_)/this._getVisibleDatasetWeightTotal();this.offsetX=u*b,this.offsetY=p*b,i.total=this.calculateTotal(),this.outerRadius=b-y*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-y*o,0),this.updateElements(s,0,s.length,t)}_circumference(t,e){let i=this.options,s=this._cachedMeta,a=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*a/L)}updateElements(t,e,i,s){let a;let n="reset"===s,r=this.chart,o=r.chartArea,l=r.options.animation,h=(o.left+o.right)/2,d=(o.top+o.bottom)/2,c=n&&l.animateScale,u=c?0:this.innerRadius,g=c?0:this.outerRadius,{sharedOptions:f,includeOptions:p}=this._getSharedOptions(e,s),m=this._getRotation();for(a=0;a<e;++a)m+=this._circumference(a,n);for(a=e;a<e+i;++a){let e=this._circumference(a,n),i=t[a],r={x:h+this.offsetX,y:d+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:g,innerRadius:u};p&&(r.options=f||this.resolveDataElementOptions(a,i.active?"active":s)),m+=e,this.updateElement(i,a,r,s)}}calculateTotal(){let t;let e=this._cachedMeta,i=e.data,s=0;for(t=0;t<i.length;t++){let a=e._parsed[t];null!==a&&!isNaN(a)&&this.chart.getDataVisibility(t)&&!i[t].hidden&&(s+=Math.abs(a))}return s}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?Math.abs(t)/e*L:0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=tM(e._parsed[t],i.options.locale);return{label:s[t]||"",value:a}}getMaxBorderWidth(t){let e,i,s,a,n,r=0,o=this.chart;if(!t){for(e=0,i=o.data.datasets.length;e<i;++e)if(o.isDatasetVisible(e)){t=(s=o.getDatasetMeta(e)).data,a=s.controller;break}}if(!t)return 0;for(e=0,i=t.length;e<i;++e)"inner"!==(n=a.resolveDataElementOptions(e)).borderAlign&&(r=Math.max(r,n.borderWidth||0,n.hoverBorderWidth||0));return r}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(u(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class e1 extends eY{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:s=[],_dataset:a}=e,n=this.chart._animationsDisabled,{start:r,count:o}=td(e,s,n);this._drawStart=r,this._drawCount=o,tc(e)&&(r=0,o=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!a._decimated,i.points=s;let l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!n,options:l},t),this.updateElements(s,r,o,t)}updateElements(t,e,i,s){let a="reset"===s,{iScale:n,vScale:r,_stacked:l,_dataset:h}=this._cachedMeta,{sharedOptions:d,includeOptions:c}=this._getSharedOptions(e,s),u=n.axis,g=r.axis,{spanGaps:f,segment:p}=this.options,m=W(f)?f:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||a||"none"===s,b=e+i,_=t.length,y=e>0&&this.getParsed(e-1);for(let i=0;i<_;++i){let f=t[i],_=x?f:{};if(i<e||i>=b){_.skip=!0;continue}let v=this.getParsed(i),M=o(v[g]),w=_[u]=n.getPixelForValue(v[u],i),k=_[g]=a||M?r.getBasePixel():r.getPixelForValue(l?this.applyStack(r,v,l):v[g],i);_.skip=isNaN(w)||isNaN(k)||M,_.stop=i>0&&Math.abs(v[u]-y[u])>m,p&&(_.parsed=v,_.raw=h.data[i]),c&&(_.options=d||this.resolveDataElementOptions(i,f.active?"active":s)),x||this.updateElement(f,i,_,s),y=v}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];return s.length?Math.max(i,s[0].size(this.resolveDataElementOptions(0)),s[s.length-1].size(this.resolveDataElementOptions(s.length-1)))/2:i}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class e2 extends eY{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let n=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=tM(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:a}}parseObjectData(t,e,i,s){return t9.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,i)=>{let s=this.getParsed(i).r;!isNaN(s)&&this.chart.getDataVisibility(i)&&(s<e.min&&(e.min=s),s>e.max&&(e.max=s))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.max(Math.min(e.right-e.left,e.bottom-e.top)/2,0),a=Math.max(i.cutoutPercentage?s/100*i.cutoutPercentage:1,0),n=(s-a)/t.getVisibleDatasetCount();this.outerRadius=s-n*this.index,this.innerRadius=this.outerRadius-n}updateElements(t,e,i,s){let a;let n="reset"===s,r=this.chart,o=r.options.animation,l=this._cachedMeta.rScale,h=l.xCenter,d=l.yCenter,c=l.getIndexAngle(0)-.5*T,u=c,g=360/this.countVisibleElements();for(a=0;a<e;++a)u+=this._computeAngle(a,s,g);for(a=e;a<e+i;a++){let e=t[a],i=u,f=u+this._computeAngle(a,s,g),p=r.getDataVisibility(a)?l.getDistanceFromCenterForValue(this.getParsed(a).r):0;u=f,n&&(o.animateScale&&(p=0),o.animateRotate&&(i=f=c));let m={x:h,y:d,innerRadius:0,outerRadius:p,startAngle:i,endAngle:f,options:this.resolveDataElementOptions(a,e.active?"active":s)};this.updateElement(e,a,m,s)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?$(this.resolveDataElementOptions(t,e).angle||i):0}}class e5 extends e0{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class e3 extends eY{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return t9.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],a=e.iScale.getLabels();if(i.points=s,"resize"!==t){let e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);let n={_loop:!0,_fullLoop:a.length===s.length,options:e};this.updateElement(i,void 0,n,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let a=this._cachedMeta.rScale,n="reset"===s;for(let r=e;r<e+i;r++){let e=t[r],i=this.resolveDataElementOptions(r,e.active?"active":s),o=a.getPointPositionForValue(r,this.getParsed(r).r),l=n?a.xCenter:o.x,h=n?a.yCenter:o.y,d={x:l,y:h,angle:o.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,r,d,s)}}}class e6 extends eY{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,n=this.getParsed(t),r=s.getLabelForValue(n.x),o=a.getLabelForValue(n.y);return{label:i[t]||"",value:"("+r+", "+o+")"}}update(t){let e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled,{start:a,count:n}=td(e,i,s);if(this._drawStart=a,this._drawCount=n,tc(e)&&(a=0,n=i.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:a,_dataset:n}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!n._decimated,a.points=i;let r=this.resolveDatasetElementOptions(t);r.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:r},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,a,n,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,s){let a="reset"===s,{iScale:n,vScale:r,_stacked:l,_dataset:h}=this._cachedMeta,d=this.resolveDataElementOptions(e,s),c=this.getSharedOptions(d),u=this.includeOptions(s,c),g=n.axis,f=r.axis,{spanGaps:p,segment:m}=this.options,x=W(p)?p:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||a||"none"===s,_=e>0&&this.getParsed(e-1);for(let d=e;d<e+i;++d){let e=t[d],i=this.getParsed(d),p=b?e:{},y=o(i[f]),v=p[g]=n.getPixelForValue(i[g],d),M=p[f]=a||y?r.getBasePixel():r.getPixelForValue(l?this.applyStack(r,i,l):i[f],d);p.skip=isNaN(v)||isNaN(M)||y,p.stop=d>0&&Math.abs(i[g]-_[g])>x,m&&(p.parsed=i,p.raw=h.data[d]),u&&(p.options=c||this.resolveDataElementOptions(d,e.active?"active":s)),b||this.updateElement(e,d,p,s),_=i}this.updateSharedOptions(c,s,d)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}let i=t.dataset,s=i.options&&i.options.borderWidth||0;return e.length?Math.max(s,e[0].size(this.resolveDataElementOptions(0)),e[e.length-1].size(this.resolveDataElementOptions(e.length-1)))/2:s}}function e4(){throw Error("This method is not implemented: Check that a complete date adapter is provided.")}class e7{static override(t){Object.assign(e7.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return e4()}parse(){return e4()}format(){return e4()}add(){return e4()}diff(){return e4()}startOf(){return e4()}endOf(){return e4()}}function e8(t,e,i,s,a){let n=t.getSortedVisibleDatasetMetas(),r=i[e];for(let t=0,i=n.length;t<i;++t){let{index:i,data:l}=n[t],{lo:h,hi:d}=function(t,e,i,s){let{controller:a,data:n,_sorted:r}=t,l=a._cachedMeta.iScale,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(l&&e===l.axis&&"r"!==e&&r&&n.length){let r=l._reversePixels?te:tt;if(s){if(a._sharedOptions){let t=n[0],s="function"==typeof t.getRange&&t.getRange(e);if(s){let t=r(n,e,i-s),a=r(n,e,i+s);return{lo:t.lo,hi:a.hi}}}}else{let s=r(n,e,i);if(h){let{vScale:e}=a._cachedMeta,{_parsed:i}=t,n=i.slice(0,s.lo+1).reverse().findIndex(t=>!o(t[e.axis]));s.lo-=Math.max(0,n);let r=i.slice(s.hi).findIndex(t=>!o(t[e.axis]));s.hi+=Math.max(0,r)}return s}}return{lo:0,hi:n.length-1}}(n[t],e,r,a);for(let t=h;t<=d;++t){let e=l[t];e.skip||s(e,i,t)}}}function e9(t,e,i,s,a){let n=[];return(a||t.isPointInArea(e))&&e8(t,i,e,function(i,r,o){(a||tR(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&n.push({element:i,datasetIndex:r,index:o})},!0),n}function it(t,e,i,s,a,n){let r;return n||t.isPointInArea(e)?"r"!==i||s?function(t,e,i,s,a,n){let r=[],o=function(t){let e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){return Math.sqrt(Math.pow(e?Math.abs(t.x-s.x):0,2)+Math.pow(i?Math.abs(t.y-s.y):0,2))}}(i),l=Number.POSITIVE_INFINITY;return e8(t,i,e,function(i,h,d){let c=i.inRange(e.x,e.y,a);if(s&&!c)return;let u=i.getCenterPoint(a);if(!(n||t.isPointInArea(u))&&!c)return;let g=o(e,u);g<l?(r=[{element:i,datasetIndex:h,index:d}],l=g):g===l&&r.push({element:i,datasetIndex:h,index:d})}),r}(t,e,i,s,a,n):(r=[],e8(t,i,e,function(t,i,s){let{startAngle:n,endAngle:o}=t.getProps(["startAngle","endAngle"],a),{angle:l}=Y(t,{x:e.x,y:e.y});J(l,n,o)&&r.push({element:t,datasetIndex:i,index:s})}),r):[]}function ie(t,e,i,s,a){let n=[],r="x"===i?"inXRange":"inYRange",o=!1;return(e8(t,i,e,(t,s,l)=>{t[r]&&t[r](e[i],a)&&(n.push({element:t,datasetIndex:s,index:l}),o=o||t.inRange(e.x,e.y,a))}),s&&!o)?[]:n}var ii={modes:{index(t,e,i,s){let a=ec(e,t),n=i.axis||"x",r=i.includeInvisible||!1,o=i.intersect?e9(t,a,n,s,r):it(t,a,n,!1,s,r),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{let e=o[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})}),l):[]},dataset(t,e,i,s){let a=ec(e,t),n=i.axis||"xy",r=i.includeInvisible||!1,o=i.intersect?e9(t,a,n,s,r):it(t,a,n,!1,s,r);if(o.length>0){let e=o[0].datasetIndex,i=t.getDatasetMeta(e).data;o=[];for(let t=0;t<i.length;++t)o.push({element:i[t],datasetIndex:e,index:t})}return o},point(t,e,i,s){let a=ec(e,t);return e9(t,a,i.axis||"xy",s,i.includeInvisible||!1)},nearest(t,e,i,s){let a=ec(e,t),n=i.axis||"xy",r=i.includeInvisible||!1;return it(t,a,n,i.intersect,s,r)},x(t,e,i,s){let a=ec(e,t);return ie(t,a,"x",i.intersect,s)},y(t,e,i,s){let a=ec(e,t);return ie(t,a,"y",i.intersect,s)}}};let is=["left","top","right","bottom"];function ia(t,e){return t.filter(t=>t.pos===e)}function ir(t,e){return t.filter(t=>-1===is.indexOf(t.pos)&&t.box.axis===e)}function io(t,e){return t.sort((t,i)=>{let s=e?i:t,a=e?t:i;return s.weight===a.weight?s.index-a.index:s.weight-a.weight})}function il(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function ih(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function id(t,e,i,s){let a,n,r,o,l,d;let c=[];for(a=0,n=t.length,l=0;a<n;++a){(o=(r=t[a]).box).update(r.width||e.w,r.height||e.h,function(t,e){let i=e.maxPadding;return function(t){let s={left:0,top:0,right:0,bottom:0};return t.forEach(t=>{s[t]=Math.max(e[t],i[t])}),s}(t?["left","right"]:["top","bottom"])}(r.horizontal,e));let{same:n,other:u}=function(t,e,i,s){let{pos:a,box:n}=i,r=t.maxPadding;if(!h(a)){i.size&&(t[a]-=i.size);let e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?n.height:n.width),i.size=e.size/e.count,t[a]+=i.size}n.getPadding&&ih(r,n.getPadding());let o=Math.max(0,e.outerWidth-il(r,t,"left","right")),l=Math.max(0,e.outerHeight-il(r,t,"top","bottom")),d=o!==t.w,c=l!==t.h;return t.w=o,t.h=l,i.horizontal?{same:d,other:c}:{same:c,other:d}}(e,i,r,s);l|=n&&c.length,d=d||u,o.fullSize||c.push(r)}return l&&id(c,e,i,s)||d}function ic(t,e,i,s,a){t.top=i,t.left=e,t.right=e+s,t.bottom=i+a,t.width=s,t.height=a}function iu(t,e,i,s){let a=i.padding,{x:n,y:r}=e;for(let o of t){let t=o.box,l=s[o.stack]||{count:1,placed:0,weight:1},h=o.stackWeight/l.weight||1;if(o.horizontal){let s=e.w*h,n=l.size||t.height;S(l.start)&&(r=l.start),t.fullSize?ic(t,a.left,r,i.outerWidth-a.right-a.left,n):ic(t,e.left+l.placed,r,s,n),l.start=r,l.placed+=s,r=t.bottom}else{let s=e.h*h,r=l.size||t.width;S(l.start)&&(n=l.start),t.fullSize?ic(t,n,a.top,r,i.outerHeight-a.bottom-a.top):ic(t,n,e.top+l.placed,r,s),l.start=n,l.placed+=s,n=t.right}}e.x=n,e.y=r}var ig={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){let i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;let a=tG(t.options.layout.padding),n=Math.max(e-a.width,0),r=Math.max(i-a.height,0),o=function(t){let e=function(t){let e,i,s,a,n,r;let o=[];for(e=0,i=(t||[]).length;e<i;++e)s=t[e],({position:a,options:{stack:n,stackWeight:r=1}}=s),o.push({index:e,box:s,pos:a,horizontal:s.isHorizontal(),weight:s.weight,stack:n&&a+n,stackWeight:r});return o}(t),i=io(e.filter(t=>t.box.fullSize),!0),s=io(ia(e,"left"),!0),a=io(ia(e,"right")),n=io(ia(e,"top"),!0),r=io(ia(e,"bottom")),o=ir(e,"x"),l=ir(e,"y");return{fullSize:i,leftAndTop:s.concat(n),rightAndBottom:a.concat(l).concat(r).concat(o),chartArea:ia(e,"chartArea"),vertical:s.concat(a).concat(l),horizontal:n.concat(r).concat(o)}}(t.boxes),l=o.vertical,h=o.horizontal;m(t.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()});let d=Object.freeze({outerWidth:e,outerHeight:i,padding:a,availableWidth:n,availableHeight:r,vBoxMaxWidth:n/2/(l.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1),hBoxMaxHeight:r/2}),c=Object.assign({},a);ih(c,tG(s));let u=Object.assign({maxPadding:c,w:n,h:r,x:a.left,y:a.top},a),g=function(t,e){let i,s,a;let n=function(t){let e={};for(let i of t){let{stack:t,pos:s,stackWeight:a}=i;if(!t||!is.includes(s))continue;let n=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});n.count++,n.weight+=a}return e}(t),{vBoxMaxWidth:r,hBoxMaxHeight:o}=e;for(i=0,s=t.length;i<s;++i){let{fullSize:s}=(a=t[i]).box,l=n[a.stack],h=l&&a.stackWeight/l.weight;a.horizontal?(a.width=h?h*r:s&&e.availableWidth,a.height=o):(a.width=r,a.height=h?h*o:s&&e.availableHeight)}return n}(l.concat(h),d);id(o.fullSize,u,d,g),id(l,u,d,g),id(h,u,d,g)&&id(l,u,d,g),function(t){let e=t.maxPadding;function i(i){let s=Math.max(e[i]-t[i],0);return t[i]+=s,s}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}(u),iu(o.leftAndTop,u,d,g),u.x+=u.w,u.y+=u.h,iu(o.rightAndBottom,u,d,g),t.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},m(o.chartArea,e=>{let i=e.box;Object.assign(i,t.chartArea),i.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class ip{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class im extends ip{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}let ix="$chartjs",ib={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},i_=t=>null===t||""===t,iy=!!ef&&{passive:!0};function iv(t,e){for(let i of t)if(i===e||i.contains(e))return!0}function iM(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||iv(i.addedNodes,s))&&!iv(i.removedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}function iw(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||iv(i.removedNodes,s))&&!iv(i.addedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}let ik=new Map,iD=0;function iO(){let t=window.devicePixelRatio;t!==iD&&(iD=t,ik.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function iS(t,e,i){let s=t.canvas,a=s&&en(s);if(!a)return;let n=tr((t,e)=>{let s=a.clientWidth;i(t,e),s<a.clientWidth&&i()},window),r=new ResizeObserver(t=>{let e=t[0],i=e.contentRect.width,s=e.contentRect.height;(0!==i||0!==s)&&n(i,s)});return r.observe(a),ik.size||window.addEventListener("resize",iO),ik.set(t,n),r}function iP(t,e,i){i&&i.disconnect(),"resize"===e&&(ik.delete(t),ik.size||window.removeEventListener("resize",iO))}function iC(t,e,i){let s=t.canvas,a=tr(e=>{null!==t.ctx&&i(function(t,e){let i=ib[t.type]||t.type,{x:s,y:a}=ec(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==a?a:null}}(e,t))},t);return s&&s.addEventListener(e,a,iy),a}class iT extends ip{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(!function(t,e){let i=t.style,s=t.getAttribute("height"),a=t.getAttribute("width");if(t[ix]={initial:{height:s,width:a,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",i_(a)){let e=ep(t,"width");void 0!==e&&(t.width=e)}if(i_(s)){if(""===t.style.height)t.height=t.width/(e||2);else{let e=ep(t,"height");void 0!==e&&(t.height=e)}}}(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[ix])return!1;let i=e[ix].initial;["height","width"].forEach(t=>{let s=i[t];o(s)?e.removeAttribute(t):e.setAttribute(t,s)});let s=i.style||{};return Object.keys(s).forEach(t=>{e.style[t]=s[t]}),e.width=e.width,delete e[ix],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),a={attach:iM,detach:iw,resize:iS}[e]||iC;s[e]=a(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];s&&((({attach:iP,detach:iP,resize:iP})[e]||function(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,iy)})(t,e,s),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return function(t,e,i,s){let a=eo(t),n=eh(a,"margin"),r=er(a.maxWidth,t,"clientWidth")||E,o=er(a.maxHeight,t,"clientHeight")||E,l=function(t,e,i){let s,a;if(void 0===e||void 0===i){let n=t&&en(t);if(n){let t=n.getBoundingClientRect(),r=eo(n),o=eh(r,"border","width"),l=eh(r,"padding");e=t.width-l.width-o.width,i=t.height-l.height-o.height,s=er(r.maxWidth,n,"clientWidth"),a=er(r.maxHeight,n,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||E,maxHeight:a||E}}(t,e,i),{width:h,height:d}=l;if("content-box"===a.boxSizing){let t=eh(a,"border","width"),e=eh(a,"padding");h-=e.width+t.width,d-=e.height+t.height}return h=Math.max(0,h-n.width),d=Math.max(0,s?h/s:d-n.height),h=eu(Math.min(h,r,l.maxWidth)),d=eu(Math.min(d,o,l.maxHeight)),h&&!d&&(d=eu(h/2)),(void 0!==e||void 0!==i)&&s&&l.height&&d>l.height&&(h=eu(Math.floor((d=l.height)*s))),{width:h,height:d}}(t,e,i,s)}isAttached(t){let e=t&&en(t);return!!(e&&e.isConnected)}}class iL{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return W(this.x)&&W(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),s}}function iA(t,e,i,s,a){let n,r,o;let l=u(s,0),h=Math.min(u(a,t.length),t.length),d=0;for(i=Math.ceil(i),a&&(i=(n=a-s)/Math.floor(n/i)),o=l;o<0;)o=Math.round(l+ ++d*i);for(r=Math.max(l,0);r<h;r++)r===o&&(e.push(t[r]),o=Math.round(l+ ++d*i))}let iE=t=>"left"===t?"right":"right"===t?"left":t,iI=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,iR=(t,e)=>Math.min(e||t,t);function iF(t,e){let i=[],s=t.length/e,a=t.length,n=0;for(;n<a;n+=s)i.push(t[Math.floor(n)]);return i}function iz(t){return t.drawTicks?t.tickLength:0}function iN(t,e){if(!t.display)return 0;let i=tK(t.font,e),s=tG(t.padding);return(l(t.text)?t.text.length:1)*i.lineHeight+s.height}class iV extends iL{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=c(t,Number.POSITIVE_INFINITY),e=c(e,Number.NEGATIVE_INFINITY),i=c(i,Number.POSITIVE_INFINITY),s=c(s,Number.NEGATIVE_INFINITY),{min:c(t,i),max:c(e,s),minDefined:d(t),maxDefined:d(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:a,maxDefined:n}=this.getUserBounds();if(a&&n)return{min:i,max:s};let r=this.getMatchingVisibleMetas();for(let o=0,l=r.length;o<l;++o)e=r[o].controller.getMinMax(this,t),a||(i=Math.min(i,e.min)),n||(s=Math.max(s,e.max));return i=n&&i>s?s:i,s=a&&i>s?i:s,{min:c(i,c(s,i)),max:c(s,c(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){p(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:s,grace:a,ticks:n}=this.options,r=n.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){let{min:s,max:a}=t,n=f(e,(a-s)/2),r=(t,e)=>i&&0===t?0:t+e;return{min:r(s,-Math.abs(n)),max:r(a,n)}}(this,a,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let l=r<this.ticks.length;this._convertTicksToLabels(l?iF(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),n.display&&(n.autoSkip||"auto"===n.source)&&(this.ticks=function(t,e){let i=t.options.ticks,s=function(t){let e=t.options.offset,i=t._tickSize();return Math.floor(Math.min(t._length/i+(e?0:1),t._maxLength/i))}(t),a=Math.min(i.maxTicksLimit||s,s),n=i.major.enabled?function(t){let e,i;let s=[];for(e=0,i=t.length;e<i;e++)t[e].major&&s.push(e);return s}(e):[],r=n.length,l=n[0],h=n[r-1],d=[];if(r>a)return function(t,e,i,s){let a,n=0,r=i[0];for(a=0,s=Math.ceil(s);a<t.length;a++)a===r&&(e.push(t[a]),r=i[++n*s])}(e,d,n,r/a),d;let c=function(t,e,i){let s=function(t){let e,i;let s=t.length;if(s<2)return!1;for(i=t[0],e=1;e<s;++e)if(t[e]-t[e-1]!==i)return!1;return i}(t),a=e.length/i;if(!s)return Math.max(a,1);let n=function(t){let e;let i=[],s=Math.sqrt(t);for(e=1;e<s;e++)t%e==0&&(i.push(e),i.push(t/e));return s===(0|s)&&i.push(s),i.sort((t,e)=>t-e).pop(),i}(s);for(let t=0,e=n.length-1;t<e;t++){let e=n[t];if(e>a)return e}return Math.max(a,1)}(n,e,a);if(r>0){let t,i;let s=r>1?Math.round((h-l)/(r-1)):null;for(iA(e,d,c,o(s)?0:l-s,l),t=0,i=r-1;t<i;t++)iA(e,d,c,n[t],n[t+1]);return iA(e,d,c,h,o(s)?e.length:h+s),d}return iA(e,d,c),d}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){p(this.options.afterUpdate,[this])}beforeSetDimensions(){p(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){p(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),p(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){p(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e,i,s;let a=this.options.ticks;for(e=0,i=t.length;e<i;e++)(s=t[e]).label=p(a.callback,[s.value,e,t],this)}afterTickToLabelConversion(){p(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){p(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t,e,i;let s=this.options,a=s.ticks,n=iR(this.ticks.length,s.ticks.maxTicksLimit),r=a.minRotation||0,o=a.maxRotation,l=r;if(!this._isVisible()||!a.display||r>=o||n<=1||!this.isHorizontal()){this.labelRotation=r;return}let h=this._getLabelSizes(),d=h.widest.width,c=h.highest.height,u=X(this.chart.width-d,0,this.maxWidth);d+6>(t=s.offset?this.maxWidth/n:u/(n-1))&&(t=u/(n-(s.offset?.5:1)),e=this.maxHeight-iz(s.grid)-a.padding-iN(s.title,this.chart.options.font),i=Math.sqrt(d*d+c*c),l=Math.max(r,Math.min(o,l=180/T*Math.min(Math.asin(X((h.highest.height+6)/t,-1,1)),Math.asin(X(e/i,-1,1))-Math.asin(X(c/i,-1,1)))))),this.labelRotation=l}afterCalculateLabelRotation(){p(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){p(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:a}}=this,n=this._isVisible(),r=this.isHorizontal();if(n){let n=iN(s,e.options.font);if(r?(t.width=this.maxWidth,t.height=iz(a)+n):(t.height=this.maxHeight,t.width=iz(a)+n),i.display&&this.ticks.length){let{first:e,last:s,widest:a,highest:n}=this._getLabelSizes(),o=2*i.padding,l=$(this.labelRotation),h=Math.cos(l),d=Math.sin(l);if(r){let e=i.mirror?0:d*a.width+h*n.height;t.height=Math.min(this.maxHeight,t.height+e+o)}else{let e=i.mirror?0:h*a.width+d*n.height;t.width=Math.min(this.maxWidth,t.width+e+o)}this._calculatePadding(e,s,d,h)}}this._handleMargins(),r?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:a,padding:n},position:r}=this.options,o=0!==this.labelRotation,l="top"!==r&&"x"===this.axis;if(this.isHorizontal()){let r=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1),d=0,c=0;o?l?(d=s*t.width,c=i*e.height):(d=i*t.height,c=s*e.width):"start"===a?c=e.width:"end"===a?d=t.width:"inner"!==a&&(d=t.width/2,c=e.width/2),this.paddingLeft=Math.max((d-r+n)*this.width/(this.width-r),0),this.paddingRight=Math.max((c-h+n)*this.width/(this.width-h),0)}else{let i=e.height/2,s=t.height/2;"start"===a?(i=0,s=t.height):"end"===a&&(i=e.height,s=0),this.paddingTop=i+n,this.paddingBottom=s+n}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){p(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)o(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=iF(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){let s,a,n,r,h,d,c,u,g,f,p;let{ctx:x,_longestTextCache:b}=this,_=[],y=[],v=Math.floor(e/iR(e,i)),M=0,w=0;for(s=0;s<e;s+=v){if(r=t[s].label,h=this._resolveTickFontOptions(s),x.font=d=h.string,c=b[d]=b[d]||{data:{},gc:[]},u=h.lineHeight,g=f=0,o(r)||l(r)){if(l(r))for(a=0,n=r.length;a<n;++a)o(p=r[a])||l(p)||(g=tT(x,c.data,c.gc,g,p),f+=u)}else g=tT(x,c.data,c.gc,g,r),f=u;_.push(g),y.push(f),M=Math.max(g,M),w=Math.max(f,w)}m(b,t=>{let i;let s=t.gc,a=s.length/2;if(a>e){for(i=0;i<a;++i)delete t.data[s[i]];s.splice(0,a)}});let k=_.indexOf(M),D=y.indexOf(w),O=t=>({width:_[t]||0,height:y[t]||0});return{first:O(0),last:O(e-1),widest:O(k),highest:O(D),widths:_,heights:y}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return X(this._alignToPixels?tL(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){let e=this.ticks||[];if(t>=0&&t<e.length){let i=e[t];return i.$context||(i.$context=tX(this.getContext(),{tick:i,index:t,type:"tick"}))}return this.$context||(this.$context=tX(this.chart.getContext(),{scale:this,type:"scale"}))}_tickSize(){let t=this.options.ticks,e=$(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),a=this._getLabelSizes(),n=t.autoSkipPadding||0,r=a?a.widest.width+n:0,o=a?a.highest.height+n:0;return this.isHorizontal()?o*i>r*s?r/i:o/s:o*s<r*i?o/i:r/s}_isVisible(){let t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e,i,s,a,n,r,o,l,d,c,g,f;let p=this.axis,m=this.chart,x=this.options,{grid:b,position:_,border:y}=x,v=b.offset,M=this.isHorizontal(),w=this.ticks.length+(v?1:0),k=iz(b),D=[],O=y.setContext(this.getContext()),S=O.display?O.width:0,P=S/2,C=function(t){return tL(m,t,S)};if("top"===_)e=C(this.bottom),r=this.bottom-k,l=e-P,c=C(t.top)+P,f=t.bottom;else if("bottom"===_)e=C(this.top),c=t.top,f=C(t.bottom)-P,r=e+P,l=this.top+k;else if("left"===_)e=C(this.right),n=this.right-k,o=e-P,d=C(t.left)+P,g=t.right;else if("right"===_)e=C(this.left),d=t.left,g=C(t.right)-P,n=e+P,o=this.left+k;else if("x"===p){if("center"===_)e=C((t.top+t.bottom)/2+.5);else if(h(_)){let t=Object.keys(_)[0],i=_[t];e=C(this.chart.scales[t].getPixelForValue(i))}c=t.top,f=t.bottom,l=(r=e+P)+k}else if("y"===p){if("center"===_)e=C((t.left+t.right)/2);else if(h(_)){let t=Object.keys(_)[0],i=_[t];e=C(this.chart.scales[t].getPixelForValue(i))}o=(n=e-P)-k,d=t.left,g=t.right}let T=u(x.ticks.maxTicksLimit,w),L=Math.max(1,Math.ceil(w/T));for(i=0;i<w;i+=L){let t=this.getContext(i),e=b.setContext(t),h=y.setContext(t),u=e.lineWidth,p=e.color,x=h.dash||[],_=h.dashOffset,w=e.tickWidth,k=e.tickColor,O=e.tickBorderDash||[],S=e.tickBorderDashOffset;void 0!==(s=function(t,e,i){let s;let a=t.ticks.length,n=Math.min(e,a-1),r=t._startPixel,o=t._endPixel,l=t.getPixelForTick(n);if(!i||(s=1===a?Math.max(l-r,o-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(n-1))/2,!((l+=n<e?s:-s)<r-1e-6)&&!(l>o+1e-6)))return l}(this,i,v))&&(a=tL(m,s,u),M?n=o=d=g=a:r=l=c=f=a,D.push({tx1:n,ty1:r,tx2:o,ty2:l,x1:d,y1:c,x2:g,y2:f,width:u,color:p,borderDash:x,borderDashOffset:_,tickWidth:w,tickColor:k,tickBorderDash:O,tickBorderDashOffset:S}))}return this._ticksLength=w,this._borderValue=e,D}_computeLabelItems(t){let e,i,s,a,n,r,o,d,c,u,g;let f=this.axis,p=this.options,{position:m,ticks:x}=p,b=this.isHorizontal(),_=this.ticks,{align:y,crossAlign:v,padding:M,mirror:w}=x,k=iz(p.grid),D=k+M,O=w?-M:D,S=-$(this.labelRotation),P=[],C="middle";if("top"===m)n=this.bottom-O,r=this._getXAxisLabelAlignment();else if("bottom"===m)n=this.top+O,r=this._getXAxisLabelAlignment();else if("left"===m){let t=this._getYAxisLabelAlignment(k);r=t.textAlign,a=t.x}else if("right"===m){let t=this._getYAxisLabelAlignment(k);r=t.textAlign,a=t.x}else if("x"===f){if("center"===m)n=(t.top+t.bottom)/2+D;else if(h(m)){let t=Object.keys(m)[0],e=m[t];n=this.chart.scales[t].getPixelForValue(e)+D}r=this._getXAxisLabelAlignment()}else if("y"===f){if("center"===m)a=(t.left+t.right)/2-D;else if(h(m)){let t=Object.keys(m)[0],e=m[t];a=this.chart.scales[t].getPixelForValue(e)}r=this._getYAxisLabelAlignment(k).textAlign}"y"===f&&("start"===y?C="top":"end"===y&&(C="bottom"));let T=this._getLabelSizes();for(e=0,i=_.length;e<i;++e){let t;s=_[e].label;let h=x.setContext(this.getContext(e));o=this.getPixelForTick(e)+x.labelOffset,c=(d=this._resolveTickFontOptions(e)).lineHeight;let f=(u=l(s)?s.length:1)/2,p=h.color,y=h.textStrokeColor,M=h.textStrokeWidth,k=r;if(b?(a=o,"inner"===r&&(k=e===i-1?this.options.reverse?"left":"right":0===e?this.options.reverse?"right":"left":"center"),g="top"===m?"near"===v||0!==S?-u*c+c/2:"center"===v?-T.highest.height/2-f*c+c:-T.highest.height+c/2:"near"===v||0!==S?c/2:"center"===v?T.highest.height/2-f*c:T.highest.height-u*c,w&&(g*=-1),0===S||h.showLabelBackdrop||(a+=c/2*Math.sin(S))):(n=o,g=(1-u)*c/2),h.showLabelBackdrop){let s=tG(h.backdropPadding),a=T.heights[e],n=T.widths[e],o=g-s.top,l=0-s.left;switch(C){case"middle":o-=a/2;break;case"bottom":o-=a}switch(r){case"center":l-=n/2;break;case"right":l-=n;break;case"inner":e===i-1?l-=n:e>0&&(l-=n/2)}t={left:l,top:o,width:n+s.width,height:a+s.height,color:h.backdropColor}}P.push({label:s,font:d,textOffset:g,options:{rotation:S,color:p,strokeColor:y,strokeWidth:M,textAlign:k,textBaseline:C,translation:[a,n],backdrop:t}})}return P}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-$(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){let e,i;let{position:s,ticks:{crossAlign:a,mirror:n,padding:r}}=this.options,o=this._getLabelSizes(),l=t+r,h=o.widest.width;return"left"===s?n?(i=this.right+r,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i+=h)):(i=this.right-l,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i=this.left)):"right"===s?n?(i=this.left+r,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i-=h)):(i=this.left+l,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i=this.right)):e="right",{textAlign:e,x:i}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:a,height:n}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,a,n),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let i=this.ticks.findIndex(e=>e.value===t);return i>=0?e.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){let e,i;let s=this.options.grid,a=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),r=(t,e,i)=>{i.width&&i.color&&(a.save(),a.lineWidth=i.width,a.strokeStyle=i.color,a.setLineDash(i.borderDash||[]),a.lineDashOffset=i.borderDashOffset,a.beginPath(),a.moveTo(t.x,t.y),a.lineTo(e.x,e.y),a.stroke(),a.restore())};if(s.display)for(e=0,i=n.length;e<i;++e){let t=n[e];s.drawOnChartArea&&r({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),s.drawTicks&&r({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){let t,e,i,s;let{chart:a,ctx:n,options:{border:r,grid:o}}=this,l=r.setContext(this.getContext()),h=r.display?l.width:0;if(!h)return;let d=o.setContext(this.getContext(0)).lineWidth,c=this._borderValue;this.isHorizontal()?(t=tL(a,this.left,h)-h/2,e=tL(a,this.right,d)+d/2,i=s=c):(i=tL(a,this.top,h)-h/2,s=tL(a,this.bottom,d)+d/2,t=e=c),n.save(),n.lineWidth=l.width,n.strokeStyle=l.color,n.beginPath(),n.moveTo(t,i),n.lineTo(e,s),n.stroke(),n.restore()}drawLabels(t){if(!this.options.ticks.display)return;let e=this.ctx,i=this._computeLabelArea();for(let s of(i&&tF(e,i),this.getLabelItems(t))){let t=s.options,i=s.font;tB(e,s.label,0,s.textOffset,i,t)}i&&tz(e)}drawTitle(){let t;let{ctx:e,options:{position:i,title:s,reverse:a}}=this;if(!s.display)return;let n=tK(s.font),r=tG(s.padding),o=s.align,d=n.lineHeight/2;"bottom"===i||"center"===i||h(i)?(d+=r.bottom,l(s.text)&&(d+=n.lineHeight*(s.text.length-1))):d+=r.top;let{titleX:c,titleY:u,maxWidth:g,rotation:f}=function(t,e,i,s){let a,n,r;let{top:o,left:l,bottom:d,right:c,chart:u}=t,{chartArea:g,scales:f}=u,p=0,m=d-o,x=c-l;if(t.isHorizontal()){if(n=tl(s,l,c),h(i)){let t=Object.keys(i)[0],s=i[t];r=f[t].getPixelForValue(s)+m-e}else r="center"===i?(g.bottom+g.top)/2+m-e:iI(t,i,e);a=c-l}else{if(h(i)){let t=Object.keys(i)[0],s=i[t];n=f[t].getPixelForValue(s)-x+e}else n="center"===i?(g.left+g.right)/2-x+e:iI(t,i,e);r=tl(s,d,o),p="left"===i?-R:R}return{titleX:n,titleY:r,maxWidth:a,rotation:p}}(this,d,i,o);tB(e,s.text,0,0,n,{color:s.color,maxWidth:g,rotation:f,textAlign:(t=to(o),(a&&"right"!==i||!a&&"right"===i)&&(t=iE(t)),t),textBaseline:"middle",translation:[c,u]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=u(t.grid&&t.grid.z,-1),s=u(t.border&&t.border.z,0);return this._isVisible()&&this.draw===iV.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){let e,i;let s=this.chart.getSortedVisibleDatasetMetas(),a=this.axis+"AxisID",n=[];for(e=0,i=s.length;e<i;++e){let i=s[e];i[a]!==this.id||t&&i.type!==t||n.push(i)}return n}_resolveTickFontOptions(t){return tK(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class iB{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){let e;let i=Object.getPrototypeOf(t);"id"in i&&"defaults"in i&&(e=this.register(i));let s=this.items,a=t.id,n=this.scope+"."+a;if(!a)throw Error("class does not have id: "+t);return a in s||(s[a]=t,function(t,e,i){let s=v(Object.create(null),[i?tC.get(i):{},tC.get(e),t.defaults]);tC.set(e,s),t.defaultRoutes&&function(t,e){Object.keys(e).forEach(i=>{let s=i.split("."),a=s.pop(),n=[t].concat(s).join("."),r=e[i].split("."),o=r.pop(),l=r.join(".");tC.route(n,a,l,o)})}(e,t.defaultRoutes),t.descriptors&&tC.describe(e,t.descriptors)}(t,n,e),this.override&&tC.override(t.id,t.overrides)),n}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in tC[s]&&(delete tC[s][i],this.override&&delete tk[i])}}class ij{constructor(){this.controllers=new iB(eY,"datasets",!0),this.elements=new iB(iL,"elements"),this.plugins=new iB(Object,"plugins"),this.scales=new iB(iV,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(e=>{let s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):m(e,e=>{let s=i||this._getRegistryForType(e);this._exec(t,s,e)})})}_exec(t,e,i){let s=O(t);p(i["before"+s],[],i),e[t](i),p(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(void 0===s)throw Error('"'+t+'" is not a registered '+i+".");return s}}var iW=new ij;class iH{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let a=s?this._descriptors(t).filter(s):this._descriptors(t),n=this._notify(a,t,e,i);return"afterDestroy"===e&&(this._notify(a,t,"stop"),this._notify(this._init,t,"uninstall")),n}_notify(t,e,i,s){for(let a of(s=s||{},t)){let t=a.plugin;if(!1===p(t[i],[e,s,a.options],t)&&s.cancelable)return!1}return!0}invalidate(){o(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,s=u(i.options&&i.options.plugins,{}),a=function(t){let e={},i=[],s=Object.keys(iW.plugins.items);for(let t=0;t<s.length;t++)i.push(iW.getPlugin(s[t]));let a=t.plugins||[];for(let t=0;t<a.length;t++){let s=a[t];-1===i.indexOf(s)&&(i.push(s),e[s.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==s||e?function(t,{plugins:e,localIds:i},s,a){let n=[],r=t.getContext();for(let l of e){var o;let e=l.id,h=(o=s[e],a||!1!==o?!0===o?{}:o:null);null!==h&&n.push({plugin:l,options:function(t,{plugin:e,local:i},s,a){let n=t.pluginScopeKeys(e),r=t.getOptionScopes(s,n);return i&&e.defaults&&r.push(e.defaults),t.createResolver(r,a,[""],{scriptable:!1,indexable:!1,allKeys:!0})}(t.config,{plugin:l,local:i[e]},h,r)})}return n}(t,a,s,e):[]}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter(t=>!e.some(e=>t.plugin.id===e.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function i$(t,e){let i=tC.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function iU(t){if("x"===t||"y"===t||"r"===t)return t}function iY(t,...e){if(iU(t))return t;for(let s of e){var i;let e=s.axis||("top"===(i=s.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.length>1&&iU(t[0].toLowerCase());if(e)return e}throw Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function iq(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function iG(t){let e=t.options||(t.options={});e.plugins=u(e.plugins,{}),e.scales=function(t,e){let i=tk[t.type]||{scales:{}},s=e.scales||{},a=i$(t.type,e),n=Object.create(null);return Object.keys(s).forEach(e=>{let r=s[e];if(!h(r))return console.error(`Invalid scale configuration for scale: ${e}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);let o=iY(e,r,function(t,e){if(e.data&&e.data.datasets){let i=e.data.datasets.filter(e=>e.xAxisID===t||e.yAxisID===t);if(i.length)return iq(t,"x",i[0])||iq(t,"y",i[0])}return{}}(e,t),tC.scales[r.type]),l=o===a?"_index_":"_value_",d=i.scales||{};n[e]=M(Object.create(null),[{axis:o},r,d[o],d[l]])}),t.data.datasets.forEach(i=>{let a=i.type||t.type,r=i.indexAxis||i$(a,e),o=(tk[a]||{}).scales||{};Object.keys(o).forEach(t=>{let e;let a=(e=t,"_index_"===t?e=r:"_value_"===t&&(e="x"===r?"y":"x"),e),l=i[a+"AxisID"]||a;n[l]=n[l]||Object.create(null),M(n[l],[{axis:a},s[l],o[t]])})}),Object.keys(n).forEach(t=>{let e=n[t];M(e,[tC.scales[e.type],tC.scale])}),n}(t,e)}function iK(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}let iJ=new Map,iX=new Set;function iQ(t,e){let i=iJ.get(t);return i||(i=e(),iJ.set(t,i),iX.add(i)),i}let iZ=(t,e,i)=>{let s=D(e,i);void 0!==s&&t.add(s)};class i0{constructor(t){var e;this._config=((e=(e=t)||{}).data=iK(e.data),iG(e),e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=iK(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),iG(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return iQ(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return iQ(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return iQ(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return iQ(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:s,type:a}=this,n=this._cachedScopes(t,i),r=n.get(e);if(r)return r;let o=new Set;e.forEach(e=>{t&&(o.add(t),e.forEach(e=>iZ(o,t,e))),e.forEach(t=>iZ(o,s,t)),e.forEach(t=>iZ(o,tk[a]||{},t)),e.forEach(t=>iZ(o,tC,t)),e.forEach(t=>iZ(o,tD,t))});let l=Array.from(o);return 0===l.length&&l.push(Object.create(null)),iX.has(e)&&n.set(e,l),l}chartOptionScopes(){let{options:t,type:e}=this;return[t,tk[e]||{},tC.datasets[e]||{},{type:e},tC,tD]}resolveNamedOptions(t,e,i,s=[""]){let a={$shared:!0},{resolver:n,subPrefixes:r}=i1(this._resolverCache,t,s),o=n;if(function(t,e){let{isScriptable:i,isIndexable:s}=t0(t);for(let a of e){let e=i(a),n=s(a),r=(n||e)&&t[a];if(e&&(P(r)||i2(r))||n&&l(r))return!0}return!1}(n,e)){a.$shared=!1,i=P(i)?i():i;let e=this.createResolver(t,i,r);o=tZ(n,i,e)}for(let t of e)a[t]=o[t];return a}createResolver(t,e,i=[""],s){let{resolver:a}=i1(this._resolverCache,t,i);return h(e)?tZ(a,e,void 0,s):a}}function i1(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));let a=i.join(),n=s.get(a);return n||(n={resolver:tQ(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},s.set(a,n)),n}let i2=t=>h(t)&&Object.getOwnPropertyNames(t).some(e=>P(t[e])),i5=["top","bottom","left","right","chartArea"];function i3(t,e){return"top"===t||"bottom"===t||-1===i5.indexOf(t)&&"x"===e}function i6(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function i4(t){let e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),p(i&&i.onComplete,[t],e)}function i7(t){let e=t.chart,i=e.options.animation;p(i&&i.onProgress,[t],e)}function i8(t){return ea()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}let i9={},st=t=>{let e=i8(t);return Object.values(i9).filter(t=>t.canvas===e).pop()};class se{static defaults=tC;static instances=i9;static overrides=tk;static registry=iW;static version="4.5.0";static getChart=st;static register(...t){iW.add(...t),si()}static unregister(...t){iW.remove(...t),si()}constructor(t,e){var i,s;let a;let n=this.config=new i0(e),o=i8(t),l=st(o);if(l)throw Error("Canvas is already in use. Chart with ID '"+l.id+"' must be destroyed before the canvas with ID '"+l.canvas.id+"' can be reused.");let h=n.createResolver(n.chartOptionScopes(),this.getContext());this.platform=new(n.platform||(!ea()||"undefined"!=typeof OffscreenCanvas&&o instanceof OffscreenCanvas?im:iT)),this.platform.updateConfig(n);let d=this.platform.acquireContext(o,h.aspectRatio),c=d&&d.canvas,u=c&&c.height,g=c&&c.width;if(this.id=r(),this.ctx=d,this.canvas=c,this.width=g,this.height=u,this._options=h,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new iH,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=(i=t=>this.update(t),s=h.resizeDelay||0,function(...t){return s?(clearTimeout(a),a=setTimeout(i,s,t)):i.apply(this,t),s}),this._dataChanges=[],i9[this.id]=this,!d||!c){console.error("Failed to create chart: can't acquire context from the given item");return}eT.listen(this,"complete",i4),eT.listen(this,"progress",i7),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:a}=this;return o(t)?e&&a?a:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return iW}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():eg(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return tA(this.canvas,this.ctx),this}stop(){return eT.stop(this),this}resize(t,e){eT.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,s=this.canvas,a=i.maintainAspectRatio&&this.aspectRatio,n=this.platform.getMaximumSize(s,t,e,a),r=i.devicePixelRatio||this.platform.getDevicePixelRatio(),o=this.width?"resize":"attach";this.width=n.width,this.height=n.height,this._aspectRatio=this.aspectRatio,eg(this,r,!0)&&(this.notifyPlugins("resize",{size:n}),p(i.onResize,[this,n],this),this.attached&&this._doResize(o)&&this.render())}ensureScalesHaveIDs(){m(this.options.scales||{},(t,e)=>{t.id=e})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((t,e)=>(t[e]=!1,t),{}),a=[];e&&(a=a.concat(Object.keys(e).map(t=>{let i=e[t],s=iY(t,i),a="r"===s,n="x"===s;return{options:i,dposition:a?"chartArea":n?"bottom":"left",dtype:a?"radialLinear":n?"category":"linear"}}))),m(a,e=>{let a=e.options,n=a.id,r=iY(n,a),o=u(a.type,e.dtype);(void 0===a.position||i3(a.position,r)!==i3(e.dposition))&&(a.position=e.dposition),s[n]=!0;let l=null;n in i&&i[n].type===o?l=i[n]:i[(l=new(iW.getScale(o))({id:n,type:o,ctx:this.ctx,chart:this})).id]=l,l.init(a,t)}),m(s,(t,e)=>{t||delete i[e]}),m(i,t=>{ig.configure(this,t,t.options),ig.addBox(this,t)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(i6("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((t,i)=>{0===e.filter(e=>e===t._dataset).length&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){let t,e;let i=[],s=this.data.datasets;for(this._removeUnreferencedMetasets(),t=0,e=s.length;t<e;t++){let e=s[t],a=this.getDatasetMeta(t),n=e.type||this.config.type;if(a.type&&a.type!==n&&(this._destroyDatasetMeta(t),a=this.getDatasetMeta(t)),a.type=n,a.indexAxis=e.indexAxis||i$(n,this.options),a.order=e.order||0,a.index=t,a.label=""+e.label,a.visible=this.isDatasetVisible(t),a.controller)a.controller.updateIndex(t),a.controller.linkScales();else{let e=iW.getController(n),{datasetElementType:s,dataElementType:r}=tC.datasets[n];Object.assign(e,{dataElementType:iW.getElement(r),datasetElementType:s&&iW.getElement(s)}),a.controller=new e(this,t),i.push(a.controller)}}return this._updateMetasets(),i}_resetElements(){m(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;let a=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let n=0;for(let t=0,e=this.data.datasets.length;t<e;t++){let{controller:e}=this.getDatasetMeta(t),i=!s&&-1===a.indexOf(e);e.buildOrUpdateElements(i),n=Math.max(+e.getMaxOverflow(),n)}n=this._minPadding=i.layout.autoPadding?n:0,this._updateLayout(n),s||m(a,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(i6("z","_idx"));let{_active:r,_lastEvent:o}=this;o?this._eventHandler(o,!0):r.length&&this._updateHoverStyles(r,r,!0),this.render()}_updateScales(){m(this.scales,t=>{ig.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options;C(new Set(Object.keys(this._listeners)),new Set(t.events))&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this;for(let{method:e,start:i,count:s}of this._getUniformDataChanges()||[])!function(t,e,i){for(let s of Object.keys(t)){let a=+s;if(a>=e){let n=t[s];delete t[s],(i>0||a>e)&&(t[a+i]=n)}}}(t,i,"_removeElements"===e?-s:s)}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),s=i(0);for(let t=1;t<e;t++)if(!C(s,i(t)))return;return Array.from(s).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;ig.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],m(this.boxes,t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,P(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(eT.has(this)?this.attached&&!eT.running(this)&&eT.start(this):(this.draw(),i4({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e,i;let s=this._sortedMetasets,a=[];for(e=0,i=s.length;e<i;++e){let i=s[e];(!t||i.visible)&&a.push(i)}return a}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=function(t,e){let i=e._clip;if(i.disabled)return!1;let s=function(t,e){let{xScale:i,yScale:s}=t;return i&&s?{left:eP(i,e,"left"),right:eP(i,e,"right"),top:eP(s,e,"top"),bottom:eP(s,e,"bottom")}:e}(e,t.chartArea);return{left:!1===i.left?0:s.left-(!0===i.left?0:i.left),right:!1===i.right?t.width:s.right+(!0===i.right?0:i.right),top:!1===i.top?0:s.top-(!0===i.top?0:i.top),bottom:!1===i.bottom?t.height:s.bottom+(!0===i.bottom?0:i.bottom)}}(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",i)&&(s&&tF(e,s),t.controller.draw(),s&&tz(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return tR(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let a=ii.modes[e];return"function"==typeof a?a(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(t=>t&&t._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=tX(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let s=i?"show":"hide",a=this.getDatasetMeta(t),n=a.controller._resolveAnimations(void 0,s);S(e)?(a.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),n.update(a,{visible:i}),this.update(e=>e.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),eT.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),tA(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete i9[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};m(this.options.events,t=>i(t,s))}bindResponsiveEvents(){let t;this._responsiveListeners||(this._responsiveListeners={});let e=this._responsiveListeners,i=this.platform,s=(t,s)=>{i.addEventListener(this,t,s),e[t]=s},a=(t,s)=>{e[t]&&(i.removeEventListener(this,t,s),delete e[t])},n=(t,e)=>{this.canvas&&this.resize(t,e)},r=()=>{a("attach",r),this.attached=!0,this.resize(),s("resize",n),s("detach",t)};t=()=>{this.attached=!1,a("resize",n),this._stop(),this._resize(0,0),s("attach",r)},i.isAttached(this.canvas)?r():t()}unbindEvents(){m(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},m(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s,a,n;let r=i?"set":"remove";for("dataset"===e&&this.getDatasetMeta(t[0].datasetIndex).controller["_"+r+"DatasetHoverStyle"](),a=0,n=t.length;a<n;++a){let e=(s=t[a])&&this.getDatasetMeta(s.datasetIndex).controller;e&&e[r+"HoverStyle"](s.element,s.datasetIndex,s.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:t,index:e})=>{let i=this.getDatasetMeta(t);if(!i)throw Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}});x(i,e)||(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,i){let s=this.options.hover,a=(t,e)=>t.filter(t=>!e.some(e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)),n=a(e,t),r=i?t:a(t,e);n.length&&this.updateHoverStyle(n,s.mode,!1),r.length&&s.mode&&this.updateHoverStyle(r,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;let a=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(a||i.changed)&&this.render(),this}_handleEvent(t,e,i){var s;let{_active:a=[],options:n}=this,r=this._getActiveElements(t,a,i,e),o="mouseup"===t.type||"click"===t.type||"contextmenu"===t.type,l=(s=this._lastEvent,i&&"mouseout"!==t.type?o?s:t:null);i&&(this._lastEvent=null,p(n.onHover,[t,r,this],this),o&&p(n.onClick,[t,r,this],this));let h=!x(r,a);return(h||e)&&(this._active=r,this._updateHoverStyles(r,a,e)),this._lastEvent=l,h}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;let a=this.options.hover;return this.getElementsAtEventForMode(t,a.mode,a,s)}}function si(){return m(se.instances,t=>t._plugins.invalidate())}function ss(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function sa(t,e,i,s,a,n){let{x:r,y:o,startAngle:l,pixelMargin:h,innerRadius:d}=e,c=Math.max(e.outerRadius+s+i-h,0),u=d>0?d+s+i+h:0,g=0,f=a-l;if(s){let t=c>0?c-s:0,e=((d>0?d-s:0)+t)/2;g=(f-(0!==e?f*e/(e+s):f))/2}let p=Math.max(.001,f*c-i/T)/c,m=(f-p)/2,x=l+m+g,b=a-m-g,{outerStart:_,outerEnd:y,innerStart:v,innerEnd:M}=function(t,e,i,s){let a=tU(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]),n=(i-e)/2,r=Math.min(n,s*e/2),o=t=>{let e=(i-Math.min(n,t))*s/2;return X(t,0,Math.min(n,e))};return{outerStart:o(a.outerStart),outerEnd:o(a.outerEnd),innerStart:X(a.innerStart,0,r),innerEnd:X(a.innerEnd,0,r)}}(e,u,c,b-x),w=c-_,k=c-y,D=x+_/w,O=b-y/k,S=u+v,P=u+M,C=x+v/S,L=b-M/P;if(t.beginPath(),n){let e=(D+O)/2;if(t.arc(r,o,c,D,e),t.arc(r,o,c,e,O),y>0){let e=ss(k,O,r,o);t.arc(e.x,e.y,y,O,b+R)}let i=ss(P,b,r,o);if(t.lineTo(i.x,i.y),M>0){let e=ss(P,L,r,o);t.arc(e.x,e.y,M,b+R,L+Math.PI)}let s=(b-M/u+(x+v/u))/2;if(t.arc(r,o,u,b-M/u,s,!0),t.arc(r,o,u,s,x+v/u,!0),v>0){let e=ss(S,C,r,o);t.arc(e.x,e.y,v,C+Math.PI,x-R)}let a=ss(w,x,r,o);if(t.lineTo(a.x,a.y),_>0){let e=ss(w,D,r,o);t.arc(e.x,e.y,_,x-R,D)}}else{t.moveTo(r,o);let e=Math.cos(D)*c+r,i=Math.sin(D)*c+o;t.lineTo(e,i);let s=Math.cos(O)*c+r,a=Math.sin(O)*c+o;t.lineTo(s,a)}t.closePath()}class sn extends iL{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let{angle:s,distance:a}=Y(this.getProps(["x","y"],i),{x:t,y:e}),{startAngle:n,endAngle:r,innerRadius:o,outerRadius:l,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,c=u(h,r-n),g=J(s,n,r)&&n!==r,f=c>=L||g,p=Q(a,o+d,l+d);return f&&p}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:a,innerRadius:n,outerRadius:r}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:o,spacing:l}=this.options,h=(s+a)/2,d=(n+r+l+o)/2;return{x:e+Math.cos(h)*d,y:i+Math.sin(h)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,s=(e.offset||0)/4,a=(e.spacing||0)/2,n=e.circular;if(this.pixelMargin="inner"===e.borderAlign?.33:0,this.fullCircles=i>L?Math.floor(i/L):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();let r=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(r)*s,Math.sin(r)*s);let o=s*(1-Math.sin(Math.min(T,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,s,a){let{fullCircles:n,startAngle:r,circumference:o}=e,l=e.endAngle;if(n){sa(t,e,i,s,l,a);for(let e=0;e<n;++e)t.fill();isNaN(o)||(l=r+(o%L||L))}sa(t,e,i,s,l,a),t.fill()}(t,this,o,a,n),function(t,e,i,s,a){let{fullCircles:n,startAngle:r,circumference:o,options:l}=e,{borderWidth:h,borderJoinStyle:d,borderDash:c,borderDashOffset:u,borderRadius:g}=l,f="inner"===l.borderAlign;if(!h)return;t.setLineDash(c||[]),t.lineDashOffset=u,f?(t.lineWidth=2*h,t.lineJoin=d||"round"):(t.lineWidth=h,t.lineJoin=d||"bevel");let p=e.endAngle;if(n){sa(t,e,i,s,p,a);for(let e=0;e<n;++e)t.stroke();isNaN(o)||(p=r+(o%L||L))}f&&function(t,e,i){let{startAngle:s,pixelMargin:a,x:n,y:r,outerRadius:o,innerRadius:l}=e,h=a/o;t.beginPath(),t.arc(n,r,o,s-h,i+h),l>a?(h=a/l,t.arc(n,r,l,i+h,s-h,!0)):t.arc(n,r,a,i+R,s-R),t.closePath(),t.clip()}(t,e,p),l.selfJoin&&p-r>=T&&0===g&&"miter"!==d&&function(t,e,i){let{startAngle:s,x:a,y:n,outerRadius:r,innerRadius:o,options:l}=e,{borderWidth:h,borderJoinStyle:d}=l,c=Math.min(h/r,K(s-i));if(t.beginPath(),t.arc(a,n,r-h/2,s+c/2,i-c/2),o>0){let e=Math.min(h/o,K(s-i));t.arc(a,n,o+h/2,i-e/2,s+e/2,!0)}else{let e=Math.min(h/2,r*K(s-i));if("round"===d)t.arc(a,n,e,i-T/2,s+T/2,!0);else if("bevel"===d){let r=2*e*e,o=-r*Math.cos(i+T/2)+a,l=-r*Math.sin(i+T/2)+n,h=r*Math.cos(s+T/2)+a,d=r*Math.sin(s+T/2)+n;t.lineTo(o,l),t.lineTo(h,d)}}t.closePath(),t.moveTo(0,0),t.rect(0,0,t.canvas.width,t.canvas.height),t.clip("evenodd")}(t,e,p),n||(sa(t,e,i,s,p,a),t.stroke())}(t,this,o,a,n),t.restore()}}function sr(t,e,i=e){t.lineCap=u(i.borderCapStyle,e.borderCapStyle),t.setLineDash(u(i.borderDash,e.borderDash)),t.lineDashOffset=u(i.borderDashOffset,e.borderDashOffset),t.lineJoin=u(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=u(i.borderWidth,e.borderWidth),t.strokeStyle=u(i.borderColor,e.borderColor)}function so(t,e,i){t.lineTo(i.x,i.y)}function sl(t,e,i={}){let s=t.length,{start:a=0,end:n=s-1}=i,{start:r,end:o}=e,l=Math.max(a,r),h=Math.min(n,o);return{count:s,start:l,loop:e.loop,ilen:h<l&&!(a<r&&n<r||a>o&&n>o)?s+h-l:h-l}}function sh(t,e,i,s){let a,n,r;let{points:o,options:l}=e,{count:h,start:d,loop:c,ilen:u}=sl(o,i,s),g=l.stepped?tN:l.tension||"monotone"===l.cubicInterpolationMode?tV:so,{move:f=!0,reverse:p}=s||{};for(a=0;a<=u;++a)(n=o[(d+(p?u-a:a))%h]).skip||(f?(t.moveTo(n.x,n.y),f=!1):g(t,r,n,p,l.stepped),r=n);return c&&g(t,r,n=o[(d+(p?u:0))%h],p,l.stepped),!!c}function sd(t,e,i,s){let a,n,r,o,l,h;let d=e.points,{count:c,start:u,ilen:g}=sl(d,i,s),{move:f=!0,reverse:p}=s||{},m=0,x=0,b=t=>(u+(p?g-t:t))%c,_=()=>{o!==l&&(t.lineTo(m,l),t.lineTo(m,o),t.lineTo(m,h))};for(f&&(n=d[b(0)],t.moveTo(n.x,n.y)),a=0;a<=g;++a){if((n=d[b(a)]).skip)continue;let e=n.x,i=n.y,s=0|e;s===r?(i<o?o=i:i>l&&(l=i),m=(x*m+e)/++x):(_(),t.lineTo(e,i),r=s,x=0,o=l=i),h=i}_()}function sc(t){let e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?sh:sd}let su="function"==typeof Path2D;class sg extends iL{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){let s=i.spanGaps?this._loop:this._fullLoop;!function(t,e,i,s,a){let n,r,o,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let i,s,a;let n=ei(e),r=t.length,o=Array(r).fill(0),l=Array(r),h=ee(t,0);for(i=0;i<r;++i)if(s=a,a=h,h=ee(t,i+1),a){if(h){let t=h[e]-a[e];o[i]=0!==t?(h[n]-a[n])/t:0}l[i]=s?h?V(o[i-1])!==V(o[i])?0:(o[i-1]+o[i])/2:o[i-1]:o[i]}(function(t,e,i){let s,a,n,r,o;let l=t.length,h=ee(t,0);for(let d=0;d<l-1;++d)if(o=h,h=ee(t,d+1),o&&h){if(B(e[d],0,et)){i[d]=i[d+1]=0;continue}(r=Math.pow(s=i[d]/e[d],2)+Math.pow(a=i[d+1]/e[d],2))<=9||(n=3/Math.sqrt(r),i[d]=s*n*e[d],i[d+1]=a*n*e[d])}})(t,o,l),function(t,e,i="x"){let s,a,n;let r=ei(i),o=t.length,l=ee(t,0);for(let h=0;h<o;++h){if(a=n,n=l,l=ee(t,h+1),!n)continue;let o=n[i],d=n[r];a&&(s=(o-a[i])/3,n[`cp1${i}`]=o-s,n[`cp1${r}`]=d-s*e[h]),l&&(s=(l[i]-o)/3,n[`cp2${i}`]=o+s,n[`cp2${r}`]=d+s*e[h])}}(t,l,e)}(t,a);else{let i=s?t[t.length-1]:t[0];for(n=0,r=t.length;n<r;++n)l=function(t,e,i,s){let a=t.skip?e:t,n=i.skip?e:i,r=q(e,a),o=q(n,e),l=r/(r+o),h=o/(r+o);l=isNaN(l)?0:l,h=isNaN(h)?0:h;let d=s*l,c=s*h;return{previous:{x:e.x-d*(n.x-a.x),y:e.y-d*(n.y-a.y)},next:{x:e.x+c*(n.x-a.x),y:e.y+c*(n.y-a.y)}}}(i,o=t[n],t[Math.min(n+1,r-(s?0:1))%r],e.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,i=o}e.capBezierPoints&&function(t,e){let i,s,a,n,r;let o=tR(t[0],e);for(i=0,s=t.length;i<s;++i)r=n,n=o,o=i<s-1&&tR(t[i+1],e),n&&(a=t[i],r&&(a.cp1x=es(a.cp1x,e.left,e.right),a.cp1y=es(a.cp1y,e.top,e.bottom)),o&&(a.cp2x=es(a.cp2x,e.left,e.right),a.cp2y=es(a.cp2y,e.top,e.bottom)))}(t,i)}(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){let i=t.points,s=t.options.spanGaps,a=i.length;if(!a)return[];let n=!!t._loop,{start:r,end:o}=function(t,e,i,s){let a=0,n=e-1;if(i&&!s)for(;a<e&&!t[a].skip;)a++;for(;a<e&&t[a].skip;)a++;for(a%=e,i&&(n+=a);n>a&&t[n%e].skip;)n--;return{start:a,end:n%=e}}(i,a,n,s);if(!0===s)return eO(t,[{start:r,end:o,loop:n}],i,e);let l=o<r?o+a:o,h=!!t._fullLoop&&0===r&&o===a-1;return eO(t,function(t,e,i,s){let a;let n=t.length,r=[],o=e,l=t[e];for(a=e+1;a<=i;++a){let i=t[a%n];i.skip||i.stop?l.skip||(s=!1,r.push({start:e%n,end:(a-1)%n,loop:s}),e=o=i.stop?a:null):(o=a,l.skip&&(e=a)),l=i}return null!==o&&r.push({start:e%n,end:o%n,loop:s}),r}(i,r,l,h),i,e)}(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i,s;let a=this.options,n=t[e],r=this.points,o=eD(this,{property:e,start:n,end:n});if(!o.length)return;let l=[],h=a.stepped?ex:a.tension||"monotone"===a.cubicInterpolationMode?eb:em;for(i=0,s=o.length;i<s;++i){let{start:s,end:d}=o[i],c=r[s],u=r[d];if(c===u){l.push(c);continue}let g=Math.abs((n-c[e])/(u[e]-c[e])),f=h(c,u,g,a.stepped);f[e]=t[e],l.push(f)}return 1===l.length?l[0]:l}pathSegment(t,e,i){return sc(this)(t,this,e,i)}path(t,e,i){let s=this.segments,a=sc(this),n=this._loop;for(let r of(e=e||0,i=i||this.points.length-e,s))n&=a(t,this,r,{start:e,end:e+i-1});return!!n}draw(t,e,i,s){let a=this.options||{};(this.points||[]).length&&a.borderWidth&&(t.save(),function(t,e,i,s){if(su&&!e.options.segment){let a;(a=e._path)||(a=e._path=new Path2D,e.path(a,i,s)&&a.closePath()),sr(t,e.options),t.stroke(a)}else!function(t,e,i,s){let{segments:a,options:n}=e,r=sc(e);for(let o of a)sr(t,n,o.style),t.beginPath(),r(t,e,o,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}(t,e,i,s)}(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function sf(t,e,i,s){let a=t.options,{[i]:n}=t.getProps([i],s);return Math.abs(e-n)<a.radius+a.hitRadius}class sp extends iL{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:a,y:n}=this.getProps(["x","y"],i);return Math.pow(t-a,2)+Math.pow(e-n,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return sf(this,t,"x",e)}inYRange(t,e){return sf(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0,i=(e=Math.max(e,e&&t.hoverRadius||0))&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;!this.skip&&!(i.radius<.1)&&tR(this,e,this.size(i)/2)&&(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,tE(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}function sm(t,e){let i,s,a,n,r;let{x:o,y:l,base:h,width:d,height:c}=t.getProps(["x","y","base","width","height"],e);return t.horizontal?(r=c/2,i=Math.min(o,h),s=Math.max(o,h),a=l-r,n=l+r):(i=o-(r=d/2),s=o+r,a=Math.min(l,h),n=Math.max(l,h)),{left:i,top:a,right:s,bottom:n}}function sx(t,e,i,s){return t?0:X(e,i,s)}function sb(t,e,i,s){let a=null===e,n=null===i,r=t&&!(a&&n)&&sm(t,s);return r&&(a||Q(e,r.left,r.right))&&(n||Q(i,r.top,r.bottom))}function s_(t,e){t.rect(e.x,e.y,e.w,e.h)}function sy(t,e,i={}){let s=t.x!==i.x?-e:0,a=t.y!==i.y?-e:0,n=(t.x+t.w!==i.x+i.w?e:0)-s,r=(t.y+t.h!==i.y+i.h?e:0)-a;return{x:t.x+s,y:t.y+a,w:t.w+n,h:t.h+r,radius:t.radius}}class sv extends iL{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){var e;let{inflateAmount:i,options:{borderColor:s,backgroundColor:a}}=this,{inner:n,outer:r}=function(t){let e=sm(t),i=e.right-e.left,s=e.bottom-e.top,a=function(t,e,i){let s=t.options.borderWidth,a=t.borderSkipped,n=tY(s);return{t:sx(a.top,n.top,0,i),r:sx(a.right,n.right,0,e),b:sx(a.bottom,n.bottom,0,i),l:sx(a.left,n.left,0,e)}}(t,i/2,s/2),n=function(t,e,i){let{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),a=t.options.borderRadius,n=tq(a),r=Math.min(e,i),o=t.borderSkipped,l=s||h(a);return{topLeft:sx(!l||o.top||o.left,n.topLeft,0,r),topRight:sx(!l||o.top||o.right,n.topRight,0,r),bottomLeft:sx(!l||o.bottom||o.left,n.bottomLeft,0,r),bottomRight:sx(!l||o.bottom||o.right,n.bottomRight,0,r)}}(t,i/2,s/2);return{outer:{x:e.left,y:e.top,w:i,h:s,radius:n},inner:{x:e.left+a.l,y:e.top+a.t,w:i-a.l-a.r,h:s-a.t-a.b,radius:{topLeft:Math.max(0,n.topLeft-Math.max(a.t,a.l)),topRight:Math.max(0,n.topRight-Math.max(a.t,a.r)),bottomLeft:Math.max(0,n.bottomLeft-Math.max(a.b,a.l)),bottomRight:Math.max(0,n.bottomRight-Math.max(a.b,a.r))}}}}(this),o=(e=r.radius).topLeft||e.topRight||e.bottomLeft||e.bottomRight?tj:s_;t.save(),(r.w!==n.w||r.h!==n.h)&&(t.beginPath(),o(t,sy(r,i,n)),t.clip(),o(t,sy(n,-i,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),o(t,sy(n,i)),t.fillStyle=a,t.fill(),t.restore()}inRange(t,e,i){return sb(this,t,e,i)}inXRange(t,e){return sb(this,t,null,e)}inYRange(t,e){return sb(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:a}=this.getProps(["x","y","base","horizontal"],t);return{x:a?(e+s)/2:e,y:a?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}function sM(t,e,i,s){if(s)return;let a=e[t],n=i[t];return"angle"===t&&(a=K(a),n=K(n)),{property:t,start:a,end:n}}function sw(t,e,i){for(;e>t;e--){let t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function sk(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}t=>t.replace("rgb(","rgba(").replace(")",", 0.5)");function sD(t,e,i,s){let a=e.interpolate(i,s);a&&t.lineTo(a.x,a.y)}let sO=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}},sS=(t,e)=>null!==t&&null!==e&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class sP extends iL{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=p(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(e=>t.filter(e,this.chart.data))),t.sort&&(e=e.sort((e,i)=>t.sort(e,i,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let t,e;let{options:i,ctx:s}=this;if(!i.display){this.width=this.height=0;return}let a=i.labels,n=tK(a.font),r=n.size,o=this._computeTitleHeight(),{boxWidth:l,itemHeight:h}=sO(a,r);s.font=n.string,this.isHorizontal()?(t=this.maxWidth,e=this._fitRows(o,r,l,h)+10):(e=this.maxHeight,t=this._fitCols(o,n,l,h)+10),this.width=Math.min(t,i.maxWidth||this.maxWidth),this.height=Math.min(e,i.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:a,maxWidth:n,options:{labels:{padding:r}}}=this,o=this.legendHitBoxes=[],l=this.lineWidths=[0],h=s+r,d=t;a.textAlign="left",a.textBaseline="middle";let c=-1,u=-h;return this.legendItems.forEach((t,g)=>{let f=i+e/2+a.measureText(t.text).width;(0===g||l[l.length-1]+f+2*r>n)&&(d+=h,l[l.length-(g>0?0:1)]=0,u+=h,c++),o[g]={left:0,top:u,row:c,width:f,height:s},l[l.length-1]+=f+r}),d}_fitCols(t,e,i,s){let{ctx:a,maxHeight:n,options:{labels:{padding:r}}}=this,o=this.legendHitBoxes=[],l=this.columnSizes=[],h=n-t,d=r,c=0,u=0,g=0,f=0;return this.legendItems.forEach((t,n)=>{var p;let m,x;let{itemWidth:b,itemHeight:_}={itemWidth:((m=t.text)&&"string"!=typeof m&&(m=m.reduce((t,e)=>t.length>e.length?t:e)),i+e.size/2+a.measureText(m).width),itemHeight:(p=e.lineHeight,x=s,"string"!=typeof t.text&&(x=sC(t,p)),x)};n>0&&u+_+2*r>h&&(d+=c+r,l.push({width:c,height:u}),g+=c+r,f++,c=u=0),o[n]={left:g,top:u,col:f,width:b,height:_},c=Math.max(c,b),u+=_+r}),d+=c,l.push({width:c,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:a}}=this,n=e_(a,this.left,this.width);if(this.isHorizontal()){let a=0,r=tl(i,this.left+s,this.right-this.lineWidths[a]);for(let o of e)a!==o.row&&(a=o.row,r=tl(i,this.left+s,this.right-this.lineWidths[a])),o.top+=this.top+t+s,o.left=n.leftForLtr(n.x(r),o.width),r+=o.width+s}else{let a=0,r=tl(i,this.top+t+s,this.bottom-this.columnSizes[a].height);for(let o of e)o.col!==a&&(a=o.col,r=tl(i,this.top+t+s,this.bottom-this.columnSizes[a].height)),o.top=r,o.left+=this.left+s,o.left=n.leftForLtr(n.x(o.left),o.width),r+=o.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){let t=this.ctx;tF(t,this),this._draw(),tz(t)}}_draw(){let t;let{options:e,columnSizes:i,lineWidths:s,ctx:a}=this,{align:n,labels:r}=e,o=tC.color,l=e_(e.rtl,this.left,this.width),h=tK(r.font),{padding:d}=r,c=h.size,g=c/2;this.drawTitle(),a.textAlign=l.textAlign("left"),a.textBaseline="middle",a.lineWidth=.5,a.font=h.string;let{boxWidth:f,boxHeight:p,itemHeight:m}=sO(r,c),x=function(t,e,i){if(isNaN(f)||f<=0||isNaN(p)||p<0)return;a.save();let s=u(i.lineWidth,1);if(a.fillStyle=u(i.fillStyle,o),a.lineCap=u(i.lineCap,"butt"),a.lineDashOffset=u(i.lineDashOffset,0),a.lineJoin=u(i.lineJoin,"miter"),a.lineWidth=s,a.strokeStyle=u(i.strokeStyle,o),a.setLineDash(u(i.lineDash,[])),r.usePointStyle)tI(a,{radius:p*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:s},l.xPlus(t,f/2),e+g,r.pointStyleWidth&&f);else{let n=e+Math.max((c-p)/2,0),r=l.leftForLtr(t,f),o=tq(i.borderRadius);a.beginPath(),Object.values(o).some(t=>0!==t)?tj(a,{x:r,y:n,w:f,h:p,radius:o}):a.rect(r,n,f,p),a.fill(),0!==s&&a.stroke()}a.restore()},b=function(t,e,i){tB(a,i.text,t,e+m/2,h,{strikethrough:i.hidden,textAlign:l.textAlign(i.textAlign)})},_=this.isHorizontal(),y=this._computeTitleHeight();t=_?{x:tl(n,this.left+d,this.right-s[0]),y:this.top+d+y,line:0}:{x:this.left+d,y:tl(n,this.top+y+d,this.bottom-i[0].height),line:0},ey(this.ctx,e.textDirection);let v=m+d;this.legendItems.forEach((o,c)=>{a.strokeStyle=o.fontColor,a.fillStyle=o.fontColor;let u=a.measureText(o.text).width,p=l.textAlign(o.textAlign||(o.textAlign=r.textAlign)),m=f+g+u,M=t.x,w=t.y;if(l.setWidth(this.width),_?c>0&&M+m+d>this.right&&(w=t.y+=v,t.line++,M=t.x=tl(n,this.left+d,this.right-s[t.line])):c>0&&w+v>this.bottom&&(M=t.x=M+i[t.line].width+d,t.line++,w=t.y=tl(n,this.top+y+d,this.bottom-i[t.line].height)),x(l.x(M),w,o),M=th(p,M+f+g,_?M+m:this.right,e.rtl),b(l.x(M),w,o),_)t.x+=m+d;else if("string"!=typeof o.text){let e=h.lineHeight;t.y+=sC(o,e)+d}else t.y+=v}),ev(this.ctx,e.textDirection)}drawTitle(){let t;let e=this.options,i=e.title,s=tK(i.font),a=tG(i.padding);if(!i.display)return;let n=e_(e.rtl,this.left,this.width),r=this.ctx,o=i.position,l=s.size/2,h=a.top+l,d=this.left,c=this.width;if(this.isHorizontal())c=Math.max(...this.lineWidths),t=this.top+h,d=tl(e.align,d,this.right-c);else{let i=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);t=h+tl(e.align,this.top,this.bottom-i-e.labels.padding-this._computeTitleHeight())}let u=tl(o,d,d+c);r.textAlign=n.textAlign(to(o)),r.textBaseline="middle",r.strokeStyle=i.color,r.fillStyle=i.color,r.font=s.string,tB(r,i.text,u,t,s)}_computeTitleHeight(){let t=this.options.title,e=tK(t.font),i=tG(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,a;if(Q(t,this.left,this.right)&&Q(e,this.top,this.bottom)){for(i=0,a=this.legendHitBoxes;i<a.length;++i)if(Q(t,(s=a[i]).left,s.left+s.width)&&Q(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){var e;let i=this.options;if(("mousemove"!==(e=t.type)&&"mouseout"!==e||!i.onHover&&!i.onLeave)&&(!i.onClick||"click"!==e&&"mouseup"!==e))return;let s=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){let e=this._hoveredItem,a=sS(e,s);e&&!a&&p(i.onLeave,[t,e,this],this),this._hoveredItem=s,s&&!a&&p(i.onHover,[t,s,this],this)}else s&&p(i.onClick,[t,s,this],this)}}function sC(t,e){return e*(t.text?t.text.length:0)}var sT={id:"legend",_element:sP,start(t,e,i){let s=t.legend=new sP({ctx:t.ctx,options:i,chart:t});ig.configure(t,s,i),ig.addBox(t,s)},stop(t){ig.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){let s=t.legend;ig.configure(t,s,i),s.options=i},afterUpdate(t){let e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){let s=e.datasetIndex,a=i.chart;a.isDatasetVisible(s)?(a.hide(s),e.hidden=!0):(a.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){let e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:a,color:n,useBorderRadius:r,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(t=>{let l=t.controller.getStyle(i?0:void 0),h=tG(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:n,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:l.borderColor,pointStyle:s||l.pointStyle,rotation:l.rotation,textAlign:a||l.textAlign,borderRadius:r&&(o||l.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class sL extends iL{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let s=l(i.text)?i.text.length:1;this._padding=tG(i.padding);let a=s*tK(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=a:this.width=a}isHorizontal(){let t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){let e,i,s;let{top:a,left:n,bottom:r,right:o,options:l}=this,h=l.align,d=0;return this.isHorizontal()?(i=tl(h,n,o),s=a+t,e=o-n):("left"===l.position?(i=n+t,s=tl(h,r,a),d=-.5*T):(i=o-t,s=tl(h,a,r),d=.5*T),e=r-a),{titleX:i,titleY:s,maxWidth:e,rotation:d}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let i=tK(e.font),s=i.lineHeight/2+this._padding.top,{titleX:a,titleY:n,maxWidth:r,rotation:o}=this._drawArgs(s);tB(t,e.text,0,0,i,{color:e.color,maxWidth:r,rotation:o,textAlign:to(e.align),textBaseline:"middle",translation:[a,n]})}}var sA={id:"title",_element:sL,start(t,e,i){!function(t,e){let i=new sL({ctx:t.ctx,options:e,chart:t});ig.configure(t,i,e),ig.addBox(t,i),t.titleBlock=i}(t,i)},stop(t){let e=t.titleBlock;ig.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){let s=t.titleBlock;ig.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};new WeakMap;let sE={average(t){let e,i;if(!t.length)return!1;let s=new Set,a=0,n=0;for(e=0,i=t.length;e<i;++e){let i=t[e].element;if(i&&i.hasValue()){let t=i.tooltipPosition();s.add(t.x),a+=t.y,++n}}return 0!==n&&0!==s.size&&{x:[...s].reduce((t,e)=>t+e)/s.size,y:a/n}},nearest(t,e){let i,s,a;if(!t.length)return!1;let n=e.x,r=e.y,o=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){let s=t[i].element;if(s&&s.hasValue()){let t=q(e,s.getCenterPoint());t<o&&(o=t,a=s)}}if(a){let t=a.tooltipPosition();n=t.x,r=t.y}return{x:n,y:r}}};function sI(t,e){return e&&(l(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function sR(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function sF(t,e){let i=t.chart.ctx,{body:s,footer:a,title:n}=t,{boxWidth:r,boxHeight:o}=e,l=tK(e.bodyFont),h=tK(e.titleFont),d=tK(e.footerFont),c=n.length,u=a.length,g=s.length,f=tG(e.padding),p=f.height,x=0,b=s.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0);b+=t.beforeBody.length+t.afterBody.length,c&&(p+=c*h.lineHeight+(c-1)*e.titleSpacing+e.titleMarginBottom),b&&(p+=g*(e.displayColors?Math.max(o,l.lineHeight):l.lineHeight)+(b-g)*l.lineHeight+(b-1)*e.bodySpacing),u&&(p+=e.footerMarginTop+u*d.lineHeight+(u-1)*e.footerSpacing);let _=0,y=function(t){x=Math.max(x,i.measureText(t).width+_)};return i.save(),i.font=h.string,m(t.title,y),i.font=l.string,m(t.beforeBody.concat(t.afterBody),y),_=e.displayColors?r+2+e.boxPadding:0,m(s,t=>{m(t.before,y),m(t.lines,y),m(t.after,y)}),_=0,i.font=d.string,m(t.footer,y),i.restore(),{width:x+=f.width,height:p}}function sz(t,e,i){let s=i.yAlign||e.yAlign||function(t,e){let{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||function(t,e,i,s){let{x:a,width:n}=i,{width:r,chartArea:{left:o,right:l}}=t,h="center";return"center"===s?h=a<=(o+l)/2?"left":"right":a<=n/2?h="left":a>=r-n/2&&(h="right"),function(t,e,i,s){let{x:a,width:n}=s,r=i.caretSize+i.caretPadding;if("left"===t&&a+n+r>e.width||"right"===t&&a-n-r<0)return!0}(h,t,e,i)&&(h="center"),h}(t,e,i,s),yAlign:s}}function sN(t,e,i,s){let{caretSize:a,caretPadding:n,cornerRadius:r}=t,{xAlign:o,yAlign:l}=i,h=a+n,{topLeft:d,topRight:c,bottomLeft:u,bottomRight:g}=tq(r),f=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,o),p=function(t,e,i){let{y:s,height:a}=t;return"top"===e?s+=i:"bottom"===e?s-=a+i:s-=a/2,s}(e,l,h);return"center"===l?"left"===o?f+=h:"right"===o&&(f-=h):"left"===o?f-=Math.max(d,u)+a:"right"===o&&(f+=Math.max(c,g)+a),{x:X(f,0,s.width-e.width),y:X(p,0,s.height-e.height)}}function sV(t,e,i){let s=tG(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function sB(t,e){let i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}let sj={beforeTitle:n,title(t){if(t.length>0){let e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:n,beforeBody:n,beforeLabel:n,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");let i=t.formattedValue;return o(i)||(e+=i),e},labelColor(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:n,afterBody:n,beforeFooter:n,footer:n,afterFooter:n};function sW(t,e,i,s){let a=t[e].call(i,s);return void 0===a?sj[e].call(i,s):a}class sH extends iL{static positioners=sE;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,a=new eI(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(a)),a}getContext(){return this.$context||(this.$context=tX(this.chart.getContext(),{tooltip:this,tooltipItems:this._tooltipItems,type:"tooltip"}))}getTitle(t,e){let{callbacks:i}=e,s=sW(i,"beforeTitle",this,t),a=sW(i,"title",this,t),n=sW(i,"afterTitle",this,t),r=[];return r=sI(r,sR(s)),r=sI(r,sR(a)),r=sI(r,sR(n))}getBeforeBody(t,e){return sI([],sR(sW(e.callbacks,"beforeBody",this,t)))}getBody(t,e){let{callbacks:i}=e,s=[];return m(t,t=>{let e={before:[],lines:[],after:[]},a=sB(i,t);sI(e.before,sR(sW(a,"beforeLabel",this,t))),sI(e.lines,sW(a,"label",this,t)),sI(e.after,sR(sW(a,"afterLabel",this,t))),s.push(e)}),s}getAfterBody(t,e){return sI([],sR(sW(e.callbacks,"afterBody",this,t)))}getFooter(t,e){let{callbacks:i}=e,s=sW(i,"beforeFooter",this,t),a=sW(i,"footer",this,t),n=sW(i,"afterFooter",this,t),r=[];return r=sI(r,sR(s)),r=sI(r,sR(a)),r=sI(r,sR(n))}_createItems(t){let e,i;let s=this._active,a=this.chart.data,n=[],r=[],o=[],l=[];for(e=0,i=s.length;e<i;++e)l.push(function(t,e){let{element:i,datasetIndex:s,index:a}=e,n=t.getDatasetMeta(s).controller,{label:r,value:o}=n.getLabelAndValue(a);return{chart:t,label:r,parsed:n.getParsed(a),raw:t.data.datasets[s].data[a],formattedValue:o,dataset:n.getDataset(),dataIndex:a,datasetIndex:s,element:i}}(this.chart,s[e]));return t.filter&&(l=l.filter((e,i,s)=>t.filter(e,i,s,a))),t.itemSort&&(l=l.sort((e,i)=>t.itemSort(e,i,a))),m(l,e=>{let i=sB(t.callbacks,e);n.push(sW(i,"labelColor",this,e)),r.push(sW(i,"labelPointStyle",this,e)),o.push(sW(i,"labelTextColor",this,e))}),this.labelColors=n,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){let i;let s=this.options.setContext(this.getContext()),a=this._active,n=[];if(a.length){let t=sE[s.position].call(this,a,this._eventPosition);n=this._createItems(s),this.title=this.getTitle(n,s),this.beforeBody=this.getBeforeBody(n,s),this.body=this.getBody(n,s),this.afterBody=this.getAfterBody(n,s),this.footer=this.getFooter(n,s);let e=this._size=sF(this,s),r=Object.assign({},t,e),o=sz(this.chart,s,r),l=sN(s,r,o,this.chart);this.xAlign=o.xAlign,this.yAlign=o.yAlign,i={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(i={opacity:0});this._tooltipItems=n,this.$context=void 0,i&&this._resolveAnimations().update(this,i),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let a=this.getCaretPosition(t,i,s);e.lineTo(a.x1,a.y1),e.lineTo(a.x2,a.y2),e.lineTo(a.x3,a.y3)}getCaretPosition(t,e,i){let s,a,n,r,o,l;let{xAlign:h,yAlign:d}=this,{caretSize:c,cornerRadius:u}=i,{topLeft:g,topRight:f,bottomLeft:p,bottomRight:m}=tq(u),{x:x,y:b}=t,{width:_,height:y}=e;return"center"===d?(o=b+y/2,"left"===h?(a=(s=x)-c,r=o+c,l=o-c):(a=(s=x+_)+c,r=o-c,l=o+c),n=s):(a="left"===h?x+Math.max(g,p)+c:"right"===h?x+_-Math.max(f,m)-c:this.caretX,"top"===d?(o=(r=b)-c,s=a-c,n=a+c):(o=(r=b+y)+c,s=a+c,n=a-c),l=r),{x1:s,x2:a,x3:n,y1:r,y2:o,y3:l}}drawTitle(t,e,i){let s,a,n;let r=this.title,o=r.length;if(o){let l=e_(i.rtl,this.x,this.width);for(n=0,t.x=sV(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",s=tK(i.titleFont),a=i.titleSpacing,e.fillStyle=i.titleColor,e.font=s.string;n<o;++n)e.fillText(r[n],l.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+a,n+1===o&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,e,i,s,a){let n=this.labelColors[i],r=this.labelPointStyles[i],{boxHeight:o,boxWidth:l}=a,d=tK(a.bodyFont),c=sV(this,"left",a),u=s.x(c),g=o<d.lineHeight?(d.lineHeight-o)/2:0,f=e.y+g;if(a.usePointStyle){let e={radius:Math.min(l,o)/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:1},i=s.leftForLtr(u,l)+l/2,h=f+o/2;t.strokeStyle=a.multiKeyBackground,t.fillStyle=a.multiKeyBackground,tE(t,e,i,h),t.strokeStyle=n.borderColor,t.fillStyle=n.backgroundColor,tE(t,e,i,h)}else{t.lineWidth=h(n.borderWidth)?Math.max(...Object.values(n.borderWidth)):n.borderWidth||1,t.strokeStyle=n.borderColor,t.setLineDash(n.borderDash||[]),t.lineDashOffset=n.borderDashOffset||0;let e=s.leftForLtr(u,l),i=s.leftForLtr(s.xPlus(u,1),l-2),r=tq(n.borderRadius);Object.values(r).some(t=>0!==t)?(t.beginPath(),t.fillStyle=a.multiKeyBackground,tj(t,{x:e,y:f,w:l,h:o,radius:r}),t.fill(),t.stroke(),t.fillStyle=n.backgroundColor,t.beginPath(),tj(t,{x:i,y:f+1,w:l-2,h:o-2,radius:r}),t.fill()):(t.fillStyle=a.multiKeyBackground,t.fillRect(e,f,l,o),t.strokeRect(e,f,l,o),t.fillStyle=n.backgroundColor,t.fillRect(i,f+1,l-2,o-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let s,a,n,r,o,l,h;let{body:d}=this,{bodySpacing:c,bodyAlign:u,displayColors:g,boxHeight:f,boxWidth:p,boxPadding:x}=i,b=tK(i.bodyFont),_=b.lineHeight,y=0,v=e_(i.rtl,this.x,this.width),M=function(i){e.fillText(i,v.x(t.x+y),t.y+_/2),t.y+=_+c},w=v.textAlign(u);for(e.textAlign=u,e.textBaseline="middle",e.font=b.string,t.x=sV(this,w,i),e.fillStyle=i.bodyColor,m(this.beforeBody,M),y=g&&"right"!==w?"center"===u?p/2+x:p+2+x:0,r=0,l=d.length;r<l;++r){for(s=d[r],a=this.labelTextColors[r],e.fillStyle=a,m(s.before,M),n=s.lines,g&&n.length&&(this._drawColorBox(e,t,r,v,i),_=Math.max(b.lineHeight,f)),o=0,h=n.length;o<h;++o)M(n[o]),_=b.lineHeight;m(s.after,M)}y=0,_=b.lineHeight,m(this.afterBody,M),t.y-=c}drawFooter(t,e,i){let s,a;let n=this.footer,r=n.length;if(r){let o=e_(i.rtl,this.x,this.width);for(t.x=sV(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=o.textAlign(i.footerAlign),e.textBaseline="middle",s=tK(i.footerFont),e.fillStyle=i.footerColor,e.font=s.string,a=0;a<r;++a)e.fillText(n[a],o.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){let{xAlign:a,yAlign:n}=this,{x:r,y:o}=t,{width:l,height:h}=i,{topLeft:d,topRight:c,bottomLeft:u,bottomRight:g}=tq(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(r+d,o),"top"===n&&this.drawCaret(t,e,i,s),e.lineTo(r+l-c,o),e.quadraticCurveTo(r+l,o,r+l,o+c),"center"===n&&"right"===a&&this.drawCaret(t,e,i,s),e.lineTo(r+l,o+h-g),e.quadraticCurveTo(r+l,o+h,r+l-g,o+h),"bottom"===n&&this.drawCaret(t,e,i,s),e.lineTo(r+u,o+h),e.quadraticCurveTo(r,o+h,r,o+h-u),"center"===n&&"left"===a&&this.drawCaret(t,e,i,s),e.lineTo(r,o+d),e.quadraticCurveTo(r,o,r+d,o),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,a=i&&i.y;if(s||a){let i=sE[t.position].call(this,this._active,this._eventPosition);if(!i)return;let n=this._size=sF(this,t),r=Object.assign({},i,this._size),o=sz(e,t,r),l=sN(t,r,o,e);(s._to!==l.x||a._to!==l.y)&&(this.xAlign=o.xAlign,this.yAlign=o.yAlign,this.width=n.width,this.height=n.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let s={width:this.width,height:this.height},a={x:this.x,y:this.y};i=.001>Math.abs(i)?0:i;let n=tG(e.padding),r=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&r&&(t.save(),t.globalAlpha=i,this.drawBackground(a,t,s,e),ey(t,e.textDirection),a.y+=n.top,this.drawTitle(a,t,e),this.drawBody(a,t,e),this.drawFooter(a,t,e),ev(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,s=t.map(({datasetIndex:t,index:e})=>{let i=this.chart.getDatasetMeta(t);if(!i)throw Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}),a=!x(i,s),n=this._positionChanged(s,e);(a||n)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let s=this.options,a=this._active||[],n=this._getActiveElements(t,a,e,i),r=this._positionChanged(n,t),o=e||!x(n,a)||r;return o&&(this._active=n,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),o}_getActiveElements(t,e,i,s){let a=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index));let n=this.chart.getElementsAtEventForMode(t,a.mode,a,i);return a.reverse&&n.reverse(),n}_positionChanged(t,e){let{caretX:i,caretY:s,options:a}=this,n=sE[a.position].call(this,t,e);return!1!==n&&(i!==n.x||s!==n.y)}}var s$={id:"tooltip",_element:sH,positioners:sE,afterInit(t,e,i){i&&(t.tooltip=new sH({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){let e=t.tooltip;if(e&&e._willRender()){let i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){let i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:sj},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};let sU=(t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i),sY=(t,e)=>null===t?null:X(Math.round(t),0,e);function sq(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class sG extends iV{static id="category";static defaults={ticks:{callback:sq}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let t=this.getLabels();for(let{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(o(t))return null;let i=this.getLabels();return sY(e=isFinite(e)&&i[e]===t?e:function(t,e,i,s){let a=t.indexOf(e);return -1===a?sU(t,e,i,s):a!==t.lastIndexOf(e)?i:a}(i,t,u(e,t),this._addedLabels),i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);"ticks"!==this.options.bounds||(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],a=this.getLabels();a=0===t&&e===a.length-1?a:a.slice(t,e+1),this._valueRange=Math.max(a.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let i=t;i<=e;i++)s.push({value:i});return s}getLabelForValue(t){return sq.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function sK(t,e,{horizontal:i,minRotation:s}){let a=$(s),n=.75*e*(""+t).length;return Math.min(e/((i?Math.sin(a):Math.cos(a))||.001),n)}class sJ extends iV{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return o(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:s,max:a}=this,n=t=>s=e?s:t,r=t=>a=i?a:t;if(t){let t=V(s),e=V(a);t<0&&e<0?r(0):t>0&&e>0&&n(0)}if(s===a){let e=0===a?1:Math.abs(.05*a);r(a+e),t||n(s-e)}this.min=s,this.max=a}getTickLimit(){let t;let{maxTicksLimit:e,stepSize:i}=this.options.ticks;return i?(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1)>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${t} ticks. Limiting to 1000.`),t=1e3):(t=this.computeTickLimit(),e=e||11),e&&(t=Math.min(e,t)),t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit(),s=function(t,e){let i,s,a,n;let r=[],{bounds:l,step:h,min:d,max:c,precision:u,count:g,maxTicks:f,maxDigits:p,includeBounds:m}=t,x=h||1,b=f-1,{min:_,max:y}=e,v=!o(d),M=!o(c),w=!o(g),k=(y-_)/(p+1),D=j((y-_)/b/x)*x;if(D<1e-14&&!v&&!M)return[{value:_},{value:y}];(n=Math.ceil(y/D)-Math.floor(_/D))>b&&(D=j(n*D/b/x)*x),o(u)||(D=Math.ceil(D*(i=Math.pow(10,u)))/i),"ticks"===l?(s=Math.floor(_/D)*D,a=Math.ceil(y/D)*D):(s=_,a=y),v&&M&&h&&function(t,e){let i=Math.round(t);return i-e<=t&&i+e>=t}((c-d)/h,D/1e3)?(n=Math.round(Math.min((c-d)/D,f)),D=(c-d)/n,s=d,a=c):w?(s=v?d:s,D=((a=M?c:a)-s)/(n=g-1)):n=B(n=(a-s)/D,Math.round(n),D/1e3)?Math.round(n):Math.ceil(n);let O=Math.max(U(D),U(s));s=Math.round(s*(i=Math.pow(10,o(u)?O:u)))/i,a=Math.round(a*i)/i;let S=0;for(v&&(m&&s!==d?(r.push({value:d}),s<d&&S++,B(Math.round((s+S*D)*i)/i,d,sK(d,k,t))&&S++):s<d&&S++);S<n;++S){let t=Math.round((s+S*D)*i)/i;if(M&&t>c)break;r.push({value:t})}return M&&m&&a!==c?r.length&&B(r[r.length-1].value,c,sK(c,k,t))?r[r.length-1].value=c:r.push({value:c}):M&&a!==c||r.push({value:a}),r}({maxTicks:i=Math.max(2,i),bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&H(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return tM(t,this.chart.options.locale,this.options.ticks.format)}}class sX extends sJ{static id="linear";static defaults={ticks:{callback:tw.numeric}};determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=d(t)?t:0,this.max=d(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=$(this.options.ticks.minRotation);return Math.ceil(e/Math.min(40,this._resolveTickFontOptions(0).lineHeight/((t?Math.sin(i):Math.cos(i))||.001)))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}let sQ=t=>Math.floor(N(t)),sZ=(t,e)=>Math.pow(10,sQ(t)+e);function s0(t){return 1==t/Math.pow(10,sQ(t))}function s1(t,e,i){let s=Math.pow(10,i);return Math.ceil(e/s)-Math.floor(t/s)}class s2 extends iV{static id="logarithmic";static defaults={ticks:{callback:tw.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=sJ.prototype.parse.apply(this,[t,e]);if(0===i){this._zero=!0;return}return d(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=d(t)?Math.max(0,t):null,this.max=d(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!d(this._userMin)&&(this.min=t===sZ(this.min,0)?sZ(this.min,-1):sZ(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,a=e=>i=t?i:e,n=t=>s=e?s:t;i===s&&(i<=0?(a(1),n(10)):(a(sZ(i,-1)),n(sZ(s,1)))),i<=0&&a(sZ(s,-1)),s<=0&&n(sZ(i,1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e=function(t,{min:e,max:i}){e=c(t.min,e);let s=[],a=sQ(e),n=function(t,e){let i=sQ(e-t);for(;s1(t,e,i)>10;)i++;for(;10>s1(t,e,i);)i--;return Math.min(i,sQ(t))}(e,i),r=n<0?Math.pow(10,Math.abs(n)):1,o=Math.pow(10,n),l=a>n?Math.pow(10,a):0,h=Math.round((e-l)*r)/r,d=Math.floor((e-l)/o/10)*o*10,u=Math.floor((h-d)/Math.pow(10,n)),g=c(t.min,Math.round((l+d+u*Math.pow(10,n))*r)/r);for(;g<i;)s.push({value:g,major:s0(g),significand:u}),u>=10?u=u<15?15:20:u++,u>=20&&(u=2,r=++n>=0?1:r),g=Math.round((l+d+u*Math.pow(10,n))*r)/r;let f=c(t.max,g);return s.push({value:f,major:s0(f),significand:u}),s}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&H(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":tM(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=N(t),this._valueRange=N(this.max)-N(t)}getPixelForValue(t){return((void 0===t||0===t)&&(t=this.min),null===t||isNaN(t))?NaN:this.getPixelForDecimal(t===this.min?0:(N(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function s5(t){let e=t.ticks;if(e.display&&t.display){let t=tG(e.backdropPadding);return u(e.font&&e.font.size,tC.font.size)+t.height}return 0}function s3(t,e,i,s,a){return t===s||t===a?{start:e-i/2,end:e+i/2}:t<s||t>a?{start:e-i,end:e}:{start:e,end:e+i}}function s6(t,e,i,s){let{ctx:a}=t;if(i)a.arc(t.xCenter,t.yCenter,e,0,L);else{let i=t.getPointPosition(0,e);a.moveTo(i.x,i.y);for(let n=1;n<s;n++)i=t.getPointPosition(n,e),a.lineTo(i.x,i.y)}}class s4 extends sJ{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:tw.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=tG(s5(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=d(t)&&!isNaN(t)?t:0,this.max=d(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/s5(this.options))}generateTickLabels(t){sJ.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{let i=p(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){let t=this.options;t.display&&t.pointLabels.display?function(t){let e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),s=[],a=[],n=t._pointLabels.length,r=t.options.pointLabels,o=r.centerPointLabels?T/n:0;for(let c=0;c<n;c++){var h,d;let n=r.setContext(t.getPointLabelContext(c));a[c]=n.padding;let u=t.getPointPosition(c,t.drawingArea+a[c],o),g=tK(n.font),f=(h=t.ctx,d=l(d=t._pointLabels[c])?d:[d],{w:function(t,e,i,s){let a,n,r,o,h;let d=(s=s||{}).data=s.data||{},c=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(d=s.data={},c=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let u=0,g=i.length;for(a=0;a<g;a++)if(null==(o=i[a])||l(o)){if(l(o))for(n=0,r=o.length;n<r;n++)null==(h=o[n])||l(h)||(u=tT(t,d,c,u,h))}else u=tT(t,d,c,u,o);t.restore();let f=c.length/2;if(f>i.length){for(a=0;a<f;a++)delete d[c[a]];c.splice(0,f)}return u}(h,g.string,d),h:d.length*g.lineHeight});s[c]=f;let p=K(t.getIndexAngle(c)+o),m=Math.round(180/T*p);!function(t,e,i,s,a){let n=Math.abs(Math.sin(i)),r=Math.abs(Math.cos(i)),o=0,l=0;s.start<e.l?(o=(e.l-s.start)/n,t.l=Math.min(t.l,e.l-o)):s.end>e.r&&(o=(s.end-e.r)/n,t.r=Math.max(t.r,e.r+o)),a.start<e.t?(l=(e.t-a.start)/r,t.t=Math.min(t.t,e.t-l)):a.end>e.b&&(l=(a.end-e.b)/r,t.b=Math.max(t.b,e.b+l))}(i,e,p,s3(m,u.x,f.w,0,180),s3(m,u.y,f.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){let s;let a=[],n=t._pointLabels.length,r=t.options,{centerPointLabels:o,display:l}=r.pointLabels,h={extra:s5(r)/2,additionalAngle:o?T/n:0};for(let r=0;r<n;r++){h.padding=i[r],h.size=e[r];let n=function(t,e,i){var s,a,n,r;let o=t.drawingArea,{extra:l,additionalAngle:h,padding:d,size:c}=i,u=t.getPointPosition(e,o+l+d,h),g=Math.round(180/T*K(u.angle+R)),f=(s=u.y,a=c.h,90===g||270===g?s-=a/2:(g>270||g<90)&&(s-=a),s),p=0===g||180===g?"center":g<180?"left":"right",m=(n=u.x,r=c.w,"right"===p?n-=r:"center"===p&&(n-=r/2),n);return{visible:!0,x:u.x,y:f,textAlign:p,left:m,top:f,right:m+c.w,bottom:f+c.h}}(t,r,h);a.push(n),"auto"===l&&(n.visible=function(t,e){if(!e)return!0;let{left:i,top:s,right:a,bottom:n}=t;return!(tR({x:i,y:s},e)||tR({x:i,y:n},e)||tR({x:a,y:s},e)||tR({x:a,y:n},e))}(n,s),n.visible&&(s=n))}return a}(t,s,a)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){return K(L/(this._pointLabels.length||1)*t+$(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(o(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(o(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){let i=e[t];return tX(this.getContext(),{label:i,index:t,type:"pointLabel"})}}getPointPosition(t,e,i=0){let s=this.getIndexAngle(t)-R+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:a}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:a}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),s6(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t,e,i;let s=this.ctx,a=this.options,{angleLines:n,grid:r,border:l}=a,h=this._pointLabels.length;if(a.pointLabels.display&&function(t,e){let{ctx:i,options:{pointLabels:s}}=t;for(let a=e-1;a>=0;a--){let e=t._pointLabelItems[a];if(!e.visible)continue;let n=s.setContext(t.getPointLabelContext(a));!function(t,e,i){let{left:s,top:a,right:n,bottom:r}=i,{backdropColor:l}=e;if(!o(l)){let i=tq(e.borderRadius),o=tG(e.backdropPadding);t.fillStyle=l;let h=s-o.left,d=a-o.top,c=n-s+o.width,u=r-a+o.height;Object.values(i).some(t=>0!==t)?(t.beginPath(),tj(t,{x:h,y:d,w:c,h:u,radius:i}),t.fill()):t.fillRect(h,d,c,u)}}(i,n,e);let r=tK(n.font),{x:l,y:h,textAlign:d}=e;tB(i,t._pointLabels[a],l,h+r.lineHeight/2,r,{color:n.color,textAlign:d,textBaseline:"middle"})}}(this,h),r.display&&this.ticks.forEach((t,i)=>{if(0!==i||0===i&&this.min<0){e=this.getDistanceFromCenterForValue(t.value);let s=this.getContext(i),a=r.setContext(s),n=l.setContext(s);!function(t,e,i,s,a){let n=t.ctx,r=e.circular,{color:o,lineWidth:l}=e;(r||s)&&o&&l&&!(i<0)&&(n.save(),n.strokeStyle=o,n.lineWidth=l,n.setLineDash(a.dash||[]),n.lineDashOffset=a.dashOffset,n.beginPath(),s6(t,i,r,s),n.closePath(),n.stroke(),n.restore())}(this,a,e,h,n)}}),n.display){for(s.save(),t=h-1;t>=0;t--){let r=n.setContext(this.getPointLabelContext(t)),{color:o,lineWidth:l}=r;l&&o&&(s.lineWidth=l,s.strokeStyle=o,s.setLineDash(r.borderDash),s.lineDashOffset=r.borderDashOffset,e=this.getDistanceFromCenterForValue(a.reverse?this.min:this.max),i=this.getPointPosition(t,e),s.beginPath(),s.moveTo(this.xCenter,this.yCenter),s.lineTo(i.x,i.y),s.stroke())}s.restore()}}drawBorder(){}drawLabels(){let t,e;let i=this.ctx,s=this.options,a=s.ticks;if(!a.display)return;let n=this.getIndexAngle(0);i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(n),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((n,r)=>{if(0===r&&this.min>=0&&!s.reverse)return;let o=a.setContext(this.getContext(r)),l=tK(o.font);if(t=this.getDistanceFromCenterForValue(this.ticks[r].value),o.showLabelBackdrop){i.font=l.string,e=i.measureText(n.label).width,i.fillStyle=o.backdropColor;let s=tG(o.backdropPadding);i.fillRect(-e/2-s.left,-t-l.size/2-s.top,e+s.width,l.size+s.height)}tB(i,n.label,0,-t,l,{color:o.color,strokeColor:o.textStrokeColor,strokeWidth:o.textStrokeWidth})}),i.restore()}drawTitle(){}}let s7={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},s8=Object.keys(s7);function s9(t,e){return t-e}function at(t,e){if(o(e))return null;let i=t._adapter,{parser:s,round:a,isoWeekday:n}=t._parseOpts,r=e;return("function"==typeof s&&(r=s(r)),d(r)||(r="string"==typeof s?i.parse(r,s):i.parse(r)),null===r)?null:(a&&(r="week"===a&&(W(n)||!0===n)?i.startOf(r,"isoWeek",n):i.startOf(r,a)),+r)}function ae(t,e,i,s){let a=s8.length;for(let n=s8.indexOf(t);n<a-1;++n){let t=s7[s8[n]],a=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(a*t.size))<=s)return s8[n]}return s8[a-1]}function ai(t,e,i){if(i){if(i.length){let{lo:s,hi:a}=Z(i,e);t[i[s]>=e?i[s]:i[a]]=!0}}else t[e]=!0}function as(t,e,i){let s,a;let n=[],r={},o=e.length;for(s=0;s<o;++s)r[a=e[s]]=s,n.push({value:a,major:!1});return 0!==o&&i?function(t,e,i,s){let a,n;let r=t._adapter,o=+r.startOf(e[0].value,s),l=e[e.length-1].value;for(a=o;a<=l;a=+r.add(a,1,s))(n=i[a])>=0&&(e[n].major=!0);return e}(t,n,r,i):n}class aa extends iV{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let i=t.time||(t.time={}),s=this._adapter=new e7(t.adapters.date);s.init(e),M(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:at(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:s,max:a,minDefined:n,maxDefined:r}=this.getUserBounds();function o(t){n||isNaN(t.min)||(s=Math.min(s,t.min)),r||isNaN(t.max)||(a=Math.max(a,t.max))}n&&r||(o(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&o(this.getMinMax(!1))),s=d(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),a=d(a)&&!isNaN(a)?a:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,a-1),this.max=Math.max(s+1,a)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);let a=this.min,n=function(t,e,i){let s=0,a=t.length;for(;s<a&&t[s]<e;)s++;for(;a>s&&t[a-1]>i;)a--;return s>0||a<t.length?t.slice(s,a):t}(s,a,this.max);return this._unit=e.unit||(i.autoSkip?ae(e.minUnit,this.min,this.max,this._getLabelCapacity(a)):function(t,e,i,s,a){for(let n=s8.length-1;n>=s8.indexOf(i);n--){let i=s8[n];if(s7[i].common&&t._adapter.diff(a,s,i)>=e-1)return i}return s8[i?s8.indexOf(i):0]}(this,n.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=s8.indexOf(t)+1,i=s8.length;e<i;++e)if(s7[s8[e]].common)return s8[e]}(this._unit):void 0,this.initOffsets(s),t.reverse&&n.reverse(),as(this,n,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,s=0,a=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),s=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),a=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);let n=t.length<3?.5:.25;s=X(s,0,n),a=X(a,0,n),this._offsets={start:s,end:a,factor:1/(s+1+a)}}_generate(){let t,e;let i=this._adapter,s=this.min,a=this.max,n=this.options,r=n.time,o=r.unit||ae(r.minUnit,s,a,this._getLabelCapacity(s)),l=u(n.ticks.stepSize,1),h="week"===o&&r.isoWeekday,d=W(h)||!0===h,c={},g=s;if(d&&(g=+i.startOf(g,"isoWeek",h)),g=+i.startOf(g,d?"day":o),i.diff(a,s,o)>1e5*l)throw Error(s+" and "+a+" are too far apart with stepSize of "+l+" "+o);let f="data"===n.ticks.source&&this.getDataTimestamps();for(t=g,e=0;t<a;t=+i.add(t,l,o),e++)ai(c,t,f);return(t===a||"ticks"===n.bounds||1===e)&&ai(c,t,f),Object.keys(c).sort(s9).map(t=>+t)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){let i=this.options.time.displayFormats,s=this._unit,a=e||i[s];return this._adapter.format(t,a)}_tickFormatFunction(t,e,i,s){let a=this.options,n=a.ticks.callback;if(n)return p(n,[t,e,i],this);let r=a.time.displayFormats,o=this._unit,l=this._majorUnit,h=o&&r[o],d=l&&r[l],c=i[e],u=l&&d&&c&&c.major;return this._adapter.format(t,s||(u?d:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)(s=t[e]).label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,s=$(this.isHorizontal()?e.maxRotation:e.minRotation),a=Math.cos(s),n=Math.sin(s),r=this._resolveTickFontOptions(0).size;return{w:i*a+r*n,h:i*n+r*a}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,a=this._tickFormatFunction(t,0,as(this,[t],this._majorUnit),s),n=this._getLabelSize(a),r=Math.floor(this.isHorizontal()?this.width/n.w:this.height/n.h)-1;return r>0?r:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){let t,e;let i=this._cache.labels||[];if(i.length)return i;let s=this.getLabels();for(t=0,e=s.length;t<e;++t)i.push(at(this,s[t]));return this._cache.labels=this._normalized?i:this.normalize(i)}normalize(t){return ta(t.sort(s9))}}function an(t,e,i){let s,a,n,r,o=0,l=t.length-1;i?(e>=t[o].pos&&e<=t[l].pos&&({lo:o,hi:l}=tt(t,"pos",e)),{pos:s,time:n}=t[o],{pos:a,time:r}=t[l]):(e>=t[o].time&&e<=t[l].time&&({lo:o,hi:l}=tt(t,"time",e)),{time:s,pos:n}=t[o],{time:a,pos:r}=t[l]);let h=a-s;return h?n+(r-n)*(e-s)/h:n}class ar extends aa{static id="timeseries";static defaults=aa.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=an(e,this.min),this._tableRange=an(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let e,i,s;let{min:a,max:n}=this,r=[],o=[];for(e=0,i=t.length;e<i;++e)(s=t[e])>=a&&s<=n&&r.push(s);if(r.length<2)return[{time:a,pos:0},{time:n,pos:1}];for(e=0,i=r.length;e<i;++e)Math.round((r[e+1]+r[e-1])/2)!==(s=r[e])&&o.push({time:s,pos:e/(i-1)});return o}_generate(){let t=this.min,e=this.max,i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t}getDecimalForValue(t){return(an(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return an(this._table,i*this._tableRange+this._minPos,!0)}}},6495:function(t,e,i){i.d(e,{$I:function(){return c},$Q:function(){return d},by:function(){return u},x1:function(){return h}}),function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();var s=i(7577);let a="label";function n(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function r(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a,s=[];t.datasets=e.map(e=>{let a=t.datasets.find(t=>t[i]===e[i]);return!a||!e.data||s.includes(a)?{...e}:(s.push(a),Object.assign(a,e),a)})}let o=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(function(t,e){let{height:i=150,width:o=300,redraw:l=!1,datasetIdKey:h,type:d,data:c,options:u,plugins:g=[],fallbackContent:f,updateMode:p,...m}=t,x=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(null),b=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(null),_=()=>{x.current&&(b.current=new s.kL(x.current,{type:d,data:function(t){var e;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,s={labels:[],datasets:[]};return e=t.labels,s.labels=e,r(s,t.datasets,i),s}(c,h),options:u&&{...u},plugins:g}),n(e,b.current))},y=()=>{n(e,null),b.current&&(b.current.destroy(),b.current=null)};return Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{!l&&b.current&&u&&function(t,e){let i=t.options;i&&e&&Object.assign(i,e)}(b.current,u)},[l,u]),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{if(!l&&b.current){var t,e;t=b.current.config.data,e=c.labels,t.labels=e}},[l,c.labels]),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{!l&&b.current&&c.datasets&&r(b.current.config.data,c.datasets,h)},[l,c.datasets]),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{b.current&&(l?(y(),setTimeout(_)):b.current.update(p))},[l,u,c.labels,c.datasets,p]),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{b.current&&(y(),setTimeout(_))},[d]),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>(_(),()=>y()),[]),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())("canvas",{ref:x,role:"img",height:i,width:o,...m},f)});function l(t,e){return s.kL.register(e),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())((e,i)=>Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(o,{...e,ref:i,type:t}))}let h=l("line",s.ST),d=l("bar",s.vn),c=l("doughnut",s.jI),u=l("pie",s.tt)}}]);