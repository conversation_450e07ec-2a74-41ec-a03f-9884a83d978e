"use strict";exports.id=5815,exports.ids=[5815],exports.modules={5815:(t,e,r)=>{r.d(e,{endPOSPaymentOperation:()=>a,startPOSPaymentOperation:()=>n});let o=null;function n(){try{return o=setTimeout(()=>{a(),console.warn("POS payment operation timed out for security")},3e5),console.log("POS payment operation started with security protection"),!0}catch(t){return console.error("Error starting POS payment operation:",t),!1}}function a(){try{return o&&(clearTimeout(o),o=null),console.log("POS payment operation ended"),!0}catch(t){return console.error("Error ending POS payment operation:",t),!1}}}};