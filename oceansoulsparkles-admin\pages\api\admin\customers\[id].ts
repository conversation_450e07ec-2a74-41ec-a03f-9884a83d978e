import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminToken } from '../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Customer ID is required' });
  }

  try {
    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '') || 
                 req.cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const authResult = await verifyAdminToken(token);
    if (!authResult.valid || !authResult.user) {
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const user = authResult.user;

    if (req.method === 'GET') {
      // Get customer details
      const { data: customer, error } = await supabase
        .from('customers')
        .select(`
          id,
          first_name,
          last_name,
          email,
          phone,
          phone_secondary,
          date_of_birth,
          address,
          notes,
          created_at,
          updated_at
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Customer query error:', error);
        return res.status(500).json({ error: 'Failed to fetch customer' });
      }

      if (!customer) {
        return res.status(404).json({ error: 'Customer not found' });
      }

      // Get booking count for this customer
      const { count: bookingCount } = await supabase
        .from('bookings')
        .select('*', { count: 'exact', head: true })
        .eq('customer_id', id);

      // Transform data
      const transformedCustomer = {
        ...customer,
        total_bookings: bookingCount || 0
      };

      return res.status(200).json({
        customer: transformedCustomer
      });

    } else if (req.method === 'PUT') {
      // Update customer
      const {
        first_name,
        last_name,
        email,
        phone,
        phone_secondary,
        date_of_birth,
        address,
        notes
      } = req.body;

      // Validate required fields
      if (!first_name || !last_name || !email) {
        return res.status(400).json({ error: 'First name, last name, and email are required' });
      }

      const { data: customer, error } = await supabase
        .from('customers')
        .update({
          first_name,
          last_name,
          email,
          phone,
          phone_secondary,
          date_of_birth,
          address,
          notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Customer update error:', error);
        return res.status(500).json({ error: 'Failed to update customer' });
      }

      return res.status(200).json({
        customer: customer
      });

    } else if (req.method === 'DELETE') {
      // Delete customer (only admin can delete)
      if (user.role !== 'Admin' && user.role !== 'DEV') {
        return res.status(403).json({ error: 'Only admins can delete customers' });
      }

      // Check if customer has any bookings
      const { count: bookingCount } = await supabase
        .from('bookings')
        .select('*', { count: 'exact', head: true })
        .eq('customer_id', id);

      if (bookingCount && bookingCount > 0) {
        return res.status(400).json({ 
          error: 'Cannot delete customer with existing bookings',
          message: `This customer has ${bookingCount} booking(s). Please cancel or delete all bookings first.`
        });
      }

      const { error } = await supabase
        .from('customers')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Customer delete error:', error);
        return res.status(500).json({ error: 'Failed to delete customer' });
      }

      return res.status(200).json({ message: 'Customer deleted successfully' });

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Customer API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
