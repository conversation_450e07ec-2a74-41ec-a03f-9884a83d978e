/**
 * Ocean Soul Sparkles Admin - Mobile POS Styles
 * Complete mobile point-of-sale interface optimized for touch devices
 */

.mobilePOS {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--admin-background, #f8f9fa);
  overflow: hidden;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: var(--admin-card-background, #ffffff);
  border-bottom: 1px solid var(--admin-border, #e0e0e0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  margin: 0;
}

.cartBadge {
  background: var(--admin-primary, #16213e);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  background: var(--admin-card-background, #ffffff);
  border-bottom: 1px solid var(--admin-border, #e0e0e0);
}

.tab {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  color: var(--admin-text-secondary, #666666);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
  min-height: 48px;
}

.tab:hover {
  background: var(--admin-hover-background, #f5f5f5);
  color: var(--admin-text-primary, #1a1a1a);
}

.tab.active {
  color: var(--admin-primary, #16213e);
  border-bottom-color: var(--admin-primary, #16213e);
  background: rgba(22, 33, 62, 0.05);
}

/* Content */
.content {
  flex: 1;
  overflow: hidden;
}

/* Search */
.searchContainer {
  padding: 16px 20px;
  background: var(--admin-card-background, #ffffff);
  border-bottom: 1px solid var(--admin-border, #e0e0e0);
}

.searchInput {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--admin-border, #e0e0e0);
  border-radius: 8px;
  font-size: 1rem;
  background: var(--admin-background, #f8f9fa);
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--admin-primary, #16213e);
  background: var(--admin-card-background, #ffffff);
}

/* Items Grid */
.itemsGrid {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.itemCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--admin-card-background, #ffffff);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.itemCard:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.itemInfo {
  flex: 1;
}

.itemName {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  margin: 0 0 4px 0;
}

.itemCategory {
  font-size: 0.875rem;
  color: var(--admin-text-secondary, #666666);
  margin: 0 0 8px 0;
}

.itemDetails {
  display: flex;
  align-items: center;
  gap: 12px;
}

.price {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-primary, #16213e);
}

.duration,
.stock {
  font-size: 0.8rem;
  color: var(--admin-text-secondary, #666666);
  background: var(--admin-background, #f8f9fa);
  padding: 2px 8px;
  border-radius: 12px;
}

.addButton {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  background: var(--admin-primary, #16213e);
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.addButton:hover {
  background: var(--admin-primary-dark, #0f1419);
  transform: scale(1.05);
}

.addButton:active {
  transform: scale(0.95);
}

.addButton:disabled {
  background: var(--admin-text-disabled, #cccccc);
  cursor: not-allowed;
  transform: none;
}

/* Cart View */
.cartView {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.emptyCart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.emptyCart h3 {
  font-size: 1.25rem;
  color: var(--admin-text-primary, #1a1a1a);
  margin: 0 0 8px 0;
}

.emptyCart p {
  color: var(--admin-text-secondary, #666666);
  margin: 0;
}

.cartItems {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.cartItem {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--admin-card-background, #ffffff);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cartItem .itemInfo {
  flex: 1;
  margin-right: 12px;
}

.cartItem .itemName {
  font-size: 1rem;
  margin-bottom: 4px;
}

.itemType {
  font-size: 0.8rem;
  color: var(--admin-text-secondary, #666666);
  margin: 0 0 4px 0;
}

.cartItem .itemPrice {
  font-size: 0.875rem;
  color: var(--admin-primary, #16213e);
  font-weight: 500;
}

.quantityControls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
}

.quantityBtn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid var(--admin-border, #e0e0e0);
  background: var(--admin-card-background, #ffffff);
  color: var(--admin-text-primary, #1a1a1a);
  font-size: 1.125rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantityBtn:hover {
  border-color: var(--admin-primary, #16213e);
  background: var(--admin-primary, #16213e);
  color: white;
}

.quantityBtn:active {
  transform: scale(0.9);
}

.quantity {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  min-width: 24px;
  text-align: center;
}

.itemTotal {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-primary, #16213e);
  min-width: 80px;
  text-align: right;
}

/* Cart Summary */
.cartSummary {
  background: var(--admin-card-background, #ffffff);
  border-top: 1px solid var(--admin-border, #e0e0e0);
  padding: 20px;
}

.totalRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--admin-border, #e0e0e0);
}

.totalLabel {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
}

.totalAmount {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--admin-primary, #16213e);
}

.customerSection {
  margin-bottom: 20px;
}

.customerSection h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
  margin: 0 0 8px 0;
}

.selectedCustomer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--admin-background, #f8f9fa);
  border-radius: 8px;
}

.changeCustomerBtn {
  background: none;
  border: none;
  color: var(--admin-primary, #16213e);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

.customerSelect {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--admin-border, #e0e0e0);
  border-radius: 8px;
  font-size: 1rem;
  background: var(--admin-card-background, #ffffff);
  color: var(--admin-text-primary, #1a1a1a);
}

.customerSelect:focus {
  outline: none;
  border-color: var(--admin-primary, #16213e);
}

.checkoutBtn {
  width: 100%;
  padding: 16px;
  background: var(--admin-primary, #16213e);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 56px;
}

.checkoutBtn:hover {
  background: var(--admin-primary-dark, #0f1419);
}

.checkoutBtn:active {
  transform: scale(0.98);
}

.checkoutBtn:disabled {
  background: var(--admin-text-disabled, #cccccc);
  cursor: not-allowed;
  transform: none;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
  }

  .title {
    font-size: 1.125rem;
  }

  .tab {
    padding: 10px 6px;
    font-size: 0.8rem;
  }

  .searchContainer {
    padding: 12px 16px;
  }

  .itemsGrid {
    padding: 12px;
    gap: 8px;
  }

  .itemCard {
    padding: 12px;
  }

  .cartItems {
    padding: 12px;
  }

  .cartSummary {
    padding: 16px;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .header {
    padding: 10px 12px;
  }

  .title {
    font-size: 1rem;
  }

  .cartBadge {
    padding: 6px 10px;
    font-size: 0.8rem;
  }

  .tab {
    padding: 8px 4px;
    font-size: 0.75rem;
  }

  .itemCard {
    padding: 10px;
  }

  .itemName {
    font-size: 0.9rem;
  }

  .addButton {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .cartItem {
    padding: 12px;
  }

  .quantityBtn {
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .mobilePOS {
    background: var(--admin-background-dark, #1a1a1a);
  }

  .header,
  .tabNavigation,
  .searchContainer,
  .itemCard,
  .cartItem,
  .cartSummary {
    background: var(--admin-card-background-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
  }

  .title,
  .itemName,
  .totalLabel,
  .quantity {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .tab {
    color: var(--admin-text-secondary-dark, #cccccc);
  }

  .tab:hover {
    background: var(--admin-hover-background-dark, #3a3a3a);
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .searchInput,
  .customerSelect {
    background: var(--admin-card-background-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .quantityBtn {
    background: var(--admin-card-background-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
    color: var(--admin-text-primary-dark, #ffffff);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .itemCard,
  .cartItem {
    border: 2px solid var(--admin-border, #000000);
  }

  .addButton,
  .checkoutBtn {
    border: 2px solid var(--admin-primary, #16213e);
  }

  .quantityBtn {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tab,
  .itemCard,
  .addButton,
  .quantityBtn,
  .checkoutBtn {
    transition: none;
  }

  .itemCard:active,
  .addButton:active,
  .quantityBtn:active,
  .checkoutBtn:active {
    transform: none;
  }
}
