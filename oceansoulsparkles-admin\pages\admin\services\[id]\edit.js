import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import styles from '@/styles/admin/ServiceForm.module.css';

export default function EditService() {
  const router = useRouter();
  const { id } = router.query;
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [service, setService] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    duration: '',
    price: '',
    category: 'Hair Braiding',
    status: 'active',
    visible_on_public: true,
    visible_on_pos: true,
    visible_on_events: true
  });
  const [errors, setErrors] = useState({});

  const categories = [
    'Hair Braiding',
    'Protective Styles',
    'Hair Care',
    'Styling',
    'Consultation',
    'Special Events',
    'Maintenance'
  ];

  useEffect(() => {
    if (id && !authLoading && user) {
      loadService();
    }
  }, [id, authLoading, user]);

  const loadService = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/services/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch service details');
      }

      const data = await response.json();
      const serviceData = data.service;
      
      setService(serviceData);
      setFormData({
        name: serviceData.name || '',
        description: serviceData.description || '',
        duration: serviceData.duration || '',
        price: serviceData.price || '',
        category: serviceData.category || 'Hair Braiding',
        status: serviceData.status || 'active',
        visible_on_public: serviceData.visible_on_public !== false,
        visible_on_pos: serviceData.visible_on_pos !== false,
        visible_on_events: serviceData.visible_on_events !== false
      });
    } catch (error) {
      console.error('Error loading service:', error);
      setErrors({ load: error.message });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (formData.duration && (isNaN(formData.duration) || parseInt(formData.duration) <= 0)) {
      newErrors.duration = 'Duration must be a positive number';
    }

    if (formData.price && (isNaN(formData.price) || parseFloat(formData.price) < 0)) {
      newErrors.price = 'Price must be a valid number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);
      const token = localStorage.getItem('admin-token');
      
      const response = await fetch(`/api/admin/services/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update service');
      }

      router.push(`/admin/services/${id}`);
    } catch (error) {
      console.error('Error updating service:', error);
      setErrors({ submit: error.message });
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading service details...</p>
        </div>
      </AdminLayout>
    );
  }

  if (errors.load) {
    return (
      <AdminLayout>
        <div className={styles.errorContainer}>
          <h2>Error Loading Service</h2>
          <p>{errors.load}</p>
          <Link href="/admin/services" className={styles.backButton}>
            ← Back to Services
          </Link>
        </div>
      </AdminLayout>
    );
  }

  if (!service) {
    return (
      <AdminLayout>
        <div className={styles.notFoundContainer}>
          <h2>Service Not Found</h2>
          <Link href="/admin/services" className={styles.backButton}>
            ← Back to Services
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Edit {service.name} | Ocean Soul Sparkles Admin</title>
        <meta name="description" content={`Edit ${service.name} service`} />
      </Head>

      <div className={styles.serviceFormContainer}>
        <header className={styles.header}>
          <div className={styles.breadcrumb}>
            <Link href="/admin/services">Services</Link>
            <span>/</span>
            <Link href={`/admin/services/${service.id}`}>{service.name}</Link>
            <span>/</span>
            <span>Edit</span>
          </div>
          
          <Link href={`/admin/services/${service.id}`} className={styles.backButton}>
            ← Back to Service
          </Link>
        </header>

        <div className={styles.formContent}>
          <h1>Edit Service: {service.name}</h1>
          
          <form onSubmit={handleSubmit} className={styles.form}>
            {errors.submit && (
              <div className={styles.errorAlert}>
                {errors.submit}
              </div>
            )}

            <div className={styles.formGrid}>
              <div className={styles.formGroup}>
                <label htmlFor="name">Service Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={errors.name ? styles.inputError : ''}
                  placeholder="Enter service name"
                  required
                />
                {errors.name && <span className={styles.errorText}>{errors.name}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="category">Category *</label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className={errors.category ? styles.inputError : ''}
                  required
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
                {errors.category && <span className={styles.errorText}>{errors.category}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="duration">Duration (minutes)</label>
                <input
                  type="number"
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className={errors.duration ? styles.inputError : ''}
                  placeholder="e.g., 120"
                  min="1"
                />
                {errors.duration && <span className={styles.errorText}>{errors.duration}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="price">Price (AUD)</label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className={errors.price ? styles.inputError : ''}
                  placeholder="e.g., 150.00"
                  min="0"
                  step="0.01"
                />
                {errors.price && <span className={styles.errorText}>{errors.price}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="status">Status</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="draft">Draft</option>
                </select>
              </div>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows="4"
                placeholder="Describe the service, what's included, any special requirements..."
              />
            </div>

            <div className={styles.visibilitySection}>
              <h3>Visibility Options</h3>
              <div className={styles.checkboxGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="visible_on_public"
                    checked={formData.visible_on_public}
                    onChange={handleInputChange}
                  />
                  <span>Show on public website</span>
                </label>

                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="visible_on_pos"
                    checked={formData.visible_on_pos}
                    onChange={handleInputChange}
                  />
                  <span>Available in POS system</span>
                </label>

                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="visible_on_events"
                    checked={formData.visible_on_events}
                    onChange={handleInputChange}
                  />
                  <span>Available for events</span>
                </label>
              </div>
            </div>

            <div className={styles.formActions}>
              <button
                type="submit"
                className={styles.submitButton}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
              
              <Link href={`/admin/services/${service.id}`} className={styles.cancelButton}>
                Cancel
              </Link>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
