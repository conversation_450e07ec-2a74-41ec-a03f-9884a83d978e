/**
 * Ocean Soul Sparkles Admin - Mobile Debug Page
 * Minimal page to debug mobile layout issues
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function MobileDebugPage() {
  const [screenInfo, setScreenInfo] = useState({
    width: 0,
    height: 0,
    isMobile: false,
    userAgent: ''
  });

  useEffect(() => {
    const updateScreenInfo = () => {
      setScreenInfo({
        width: window.innerWidth,
        height: window.innerHeight,
        isMobile: window.innerWidth <= 768,
        userAgent: window.navigator.userAgent
      });
    };

    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);
    return () => window.removeEventListener('resize', updateScreenInfo);
  }, []);

  return (
    <>
      <Head>
        <title>Mobile Debug - Ocean Soul Sparkles Admin</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <div style={{
        padding: '20px',
        fontFamily: 'system-ui, sans-serif',
        backgroundColor: '#f8f9fa',
        minHeight: '100vh'
      }}>
        <h1 style={{ 
          color: '#16213e',
          marginBottom: '20px',
          fontSize: screenInfo.isMobile ? '1.5rem' : '2rem'
        }}>
          Mobile Debug Page
        </h1>

        {/* Screen Information */}
        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ marginTop: 0, color: '#16213e' }}>Screen Information</h2>
          <div style={{ display: 'grid', gap: '10px' }}>
            <div><strong>Width:</strong> {screenInfo.width}px</div>
            <div><strong>Height:</strong> {screenInfo.height}px</div>
            <div><strong>Is Mobile:</strong> {screenInfo.isMobile ? 'YES' : 'NO'}</div>
            <div><strong>Breakpoint:</strong> {screenInfo.width <= 480 ? 'Small Mobile' : screenInfo.width <= 768 ? 'Mobile' : screenInfo.width <= 1024 ? 'Tablet' : 'Desktop'}</div>
          </div>
        </div>

        {/* CSS Media Query Test */}
        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ marginTop: 0, color: '#16213e' }}>CSS Media Query Test</h2>
          
          <div 
            style={{
              padding: '10px',
              borderRadius: '4px',
              marginBottom: '10px',
              backgroundColor: '#e3f2fd'
            }}
            className="desktop-only"
          >
            <strong>Desktop Only:</strong> This should only be visible on desktop (width &gt; 768px)
          </div>

          <div 
            style={{
              padding: '10px',
              borderRadius: '4px',
              marginBottom: '10px',
              backgroundColor: '#f3e5f5'
            }}
            className="mobile-only"
          >
            <strong>Mobile Only:</strong> This should only be visible on mobile (width ≤ 768px)
          </div>

          <div 
            style={{
              padding: '10px',
              borderRadius: '4px',
              backgroundColor: '#e8f5e8'
            }}
            className="always-visible"
          >
            <strong>Always Visible:</strong> This should always be visible
          </div>
        </div>

        {/* Touch Test */}
        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ marginTop: 0, color: '#16213e' }}>Touch-Friendly Elements</h2>
          
          <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '15px' }}>
            <button style={{
              padding: '12px 24px',
              fontSize: '16px',
              minHeight: '44px',
              backgroundColor: '#16213e',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}>
              Touch Button 1
            </button>
            <button style={{
              padding: '12px 24px',
              fontSize: '16px',
              minHeight: '44px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}>
              Touch Button 2
            </button>
          </div>

          <input 
            type="text" 
            placeholder="Touch-friendly input (44px min height)"
            style={{
              width: '100%',
              padding: '12px',
              fontSize: '16px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              minHeight: '44px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        {/* Layout Test */}
        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ marginTop: 0, color: '#16213e' }}>Responsive Layout Test</h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: screenInfo.isMobile ? '1fr' : 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '15px'
          }}>
            <div style={{
              backgroundColor: '#f0f0f0',
              padding: '15px',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              Card 1
            </div>
            <div style={{
              backgroundColor: '#f0f0f0',
              padding: '15px',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              Card 2
            </div>
            <div style={{
              backgroundColor: '#f0f0f0',
              padding: '15px',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              Card 3
            </div>
          </div>
        </div>

        {/* Navigation Test */}
        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '80px', // Extra space for potential mobile nav
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ marginTop: 0, color: '#16213e' }}>Navigation Test</h2>
          <p>
            <strong>Expected behavior:</strong>
          </p>
          <ul>
            <li>On mobile (≤768px): Bottom navigation should be visible</li>
            <li>On desktop (&gt;768px): Sidebar should be visible</li>
            <li>Mobile hamburger menu should work on mobile</li>
          </ul>
          
          <div style={{ marginTop: '20px' }}>
            <a 
              href="/admin/dashboard" 
              style={{
                display: 'inline-block',
                padding: '12px 24px',
                backgroundColor: '#16213e',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '8px',
                fontSize: '16px'
              }}
            >
              Back to Dashboard
            </a>
          </div>
        </div>
      </div>

      {/* Inline CSS for media query testing */}
      <style jsx>{`
        .desktop-only {
          display: block;
        }
        .mobile-only {
          display: none;
        }
        
        @media (max-width: 768px) {
          .desktop-only {
            display: none;
          }
          .mobile-only {
            display: block;
          }
        }
      `}</style>
    </>
  );
}
