const { default: fetch } = await import('node-fetch');

async function testLoginFlow() {
  console.log('🧪 Testing complete login flow...\n');
  
  try {
    // Step 1: Test login
    console.log('1. Testing login...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    });

    const loginData = await loginResponse.json();
    console.log(`   Status: ${loginResponse.status}`);
    
    if (!loginResponse.ok) {
      console.log('   ❌ Login failed:', loginData.error);
      return;
    }
    
    console.log(`   ✅ Login successful for: ${loginData.user.email}`);
    console.log(`   📋 User role: ${loginData.user.role}`);
    console.log(`   🔑 Token received: ${loginData.token ? 'Yes' : 'No'}`);    // Step 2: Test dashboard data access
    console.log('\n2. Testing dashboard data access...');
    const dashboardResponse = await fetch('http://localhost:3001/api/admin/dashboard', {
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Cookie': `admin-token=${loginData.token}`
      }
    });

    console.log(`   Status: ${dashboardResponse.status}`);
    console.log(`   Content-Type: ${dashboardResponse.headers.get('content-type')}`);
    
    const responseText = await dashboardResponse.text();
    
    if (!dashboardResponse.ok) {
      console.log('   ❌ Dashboard access failed');
      console.log('   Response:', responseText.substring(0, 200) + '...');
      return;
    }
    
    let dashboardData;
    try {
      dashboardData = JSON.parse(responseText);
    } catch (error) {
      console.log('   ❌ Failed to parse dashboard response as JSON');
      console.log('   Response preview:', responseText.substring(0, 200) + '...');
      return;
    }
    
    console.log('   ✅ Dashboard data loaded successfully');
    console.log(`   📊 Stats available: ${Object.keys(dashboardData.stats || {}).length} metrics`);
    console.log(`   📝 Recent bookings: ${dashboardData.recentBookings?.length || 0} items`);
    console.log(`   📈 Recent activities: ${dashboardData.recentActivity?.length || 0} items`);

    // Step 3: Test token verification
    console.log('\n3. Testing token verification...');
    const verifyResponse = await fetch('http://localhost:3001/api/auth/verify', {
      headers: {
        'Authorization': `Bearer ${loginData.token}`
      }
    });

    const verifyData = await verifyResponse.json();
    console.log(`   Status: ${verifyResponse.status}`);
    
    if (!verifyResponse.ok) {
      console.log('   ❌ Token verification failed:', verifyData.error);
      return;
    }
    
    console.log('   ✅ Token verification successful');
    console.log(`   👤 User: ${verifyData.user.firstName} ${verifyData.user.lastName}`);

    console.log('\n🎉 All tests passed! The admin dashboard is working correctly.');
    console.log('\n📝 Summary:');
    console.log('   • Login API: Working ✅');
    console.log('   • Dashboard API: Working ✅');
    console.log('   • Authentication: Working ✅');
    console.log('   • User role permissions: Working ✅');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

testLoginFlow();
