"use strict";(()=>{var e={};e.id=3270,e.ids=[3270],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},5658:(e,t,a)=>{a.r(t),a.d(t,{config:()=>m,default:()=>g,routeModule:()=>p});var r={};a.r(r),a.d(r,{default:()=>d});var o=a(1802),s=a(7153),n=a(8781),i=a(7474),l=a(2885);let u=process.env.SUPABASE_SERVICE_ROLE_KEY,c=(0,l.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",u);async function d(e,t){let a=Math.random().toString(36).substring(2,8);console.log(`[${a}] Staff performance API called - ${e.method}`);try{let r=await (0,i.SA)(e);if(!r.success)return t.status(401).json({error:"Authentication required",message:r.message||"Authentication failed",requestId:a});let{user:o}=r;if("GET"===e.method){let{staff_id:r,start_date:o,end_date:s,period:n="month"}=e.query;if(r){let e=c.from("staff_performance_metrics").select(`
            id,
            metric_date,
            total_bookings,
            completed_bookings,
            cancelled_bookings,
            total_revenue,
            total_tips,
            average_rating,
            customer_feedback_count,
            hours_worked,
            punctuality_score,
            created_at
          `).eq("staff_id",r).order("metric_date",{ascending:!1});o&&(e=e.gte("metric_date",o)),s&&(e=e.lte("metric_date",s));let{data:n,error:i}=await e;if(i)return console.error(`[${a}] Database error:`,i),t.status(500).json({error:"Failed to fetch performance metrics",message:i.message,requestId:a});let l=n?.length||0,u=n?.reduce((e,t)=>({totalBookings:e.totalBookings+(t.total_bookings||0),completedBookings:e.completedBookings+(t.completed_bookings||0),cancelledBookings:e.cancelledBookings+(t.cancelled_bookings||0),totalRevenue:e.totalRevenue+parseFloat(t.total_revenue||"0"),totalTips:e.totalTips+parseFloat(t.total_tips||"0"),totalHours:e.totalHours+parseFloat(t.hours_worked||"0"),ratingSum:e.ratingSum+(t.average_rating||0),ratingCount:e.ratingCount+(t.average_rating?1:0),punctualitySum:e.punctualitySum+(t.punctuality_score||0),punctualityCount:e.punctualityCount+(t.punctuality_score?1:0)}),{totalBookings:0,completedBookings:0,cancelledBookings:0,totalRevenue:0,totalTips:0,totalHours:0,ratingSum:0,ratingCount:0,punctualitySum:0,punctualityCount:0})||{totalBookings:0,completedBookings:0,cancelledBookings:0,totalRevenue:0,totalTips:0,totalHours:0,ratingSum:0,ratingCount:0,punctualitySum:0,punctualityCount:0},d=u.ratingCount>0?u.ratingSum/u.ratingCount:0,g=u.punctualityCount>0?u.punctualitySum/u.punctualityCount:0,m=u.totalBookings>0?u.completedBookings/u.totalBookings*100:0,p=u.totalBookings>0?u.cancelledBookings/u.totalBookings*100:0;return t.status(200).json({metrics:n||[],summary:{...u,averageRating:Math.round(100*d)/100,averagePunctuality:Math.round(100*g)/100,completionRate:Math.round(100*m)/100,cancellationRate:Math.round(100*p)/100,totalPeriods:l},requestId:a})}{let{data:e,error:r}=await c.from("staff_performance_metrics").select(`
            staff_id,
            metric_date,
            total_bookings,
            completed_bookings,
            total_revenue,
            total_tips,
            average_rating,
            admin_users!inner(
              id,
              first_name,
              last_name,
              role
            )
          `).order("metric_date",{ascending:!1});if(r)return console.error(`[${a}] Database error:`,r),t.status(500).json({error:"Failed to fetch staff performance overview",message:r.message,requestId:a});let o={};e?.forEach(e=>{let t=e.staff_id;o[t]||(o[t]={staff:e.admin_users,totalBookings:0,completedBookings:0,totalRevenue:0,totalTips:0,ratingSum:0,ratingCount:0,periods:0});let a=o[t];a.totalBookings+=e.total_bookings||0,a.completedBookings+=e.completed_bookings||0,a.totalRevenue+=parseFloat(e.total_revenue||"0"),a.totalTips+=parseFloat(e.total_tips||"0"),e.average_rating&&(a.ratingSum+=e.average_rating,a.ratingCount+=1),a.periods+=1});let s=Object.values(o).map(e=>({...e,averageRating:e.ratingCount>0?Math.round(e.ratingSum/e.ratingCount*100)/100:0,completionRate:e.totalBookings>0?Math.round(e.completedBookings/e.totalBookings*100):0}));return t.status(200).json({staffPerformance:s,requestId:a})}}if("POST"===e.method){let{action:r,staff_id:o,metric_date:s,metrics:n}=e.body;if(!r||!o)return t.status(400).json({error:"Missing required fields",message:"Action and staff ID are required",requestId:a});if("update_metrics"===r){if(!s||!n)return t.status(400).json({error:"Missing required fields",message:"Metric date and metrics data are required",requestId:a});let{data:e,error:r}=await c.from("staff_performance_metrics").upsert([{staff_id:o,metric_date:s,...n,updated_at:new Date().toISOString()}]).select().single();if(r)return console.error(`[${a}] Error updating metrics:`,r),t.status(500).json({error:"Failed to update performance metrics",message:r.message,requestId:a});return t.status(200).json({metrics:e,message:"Performance metrics updated successfully",requestId:a})}if("calculate_daily_metrics"===r){let e=s||new Date().toISOString().split("T")[0],{data:r,error:n}=await c.rpc("calculate_staff_daily_metrics",{p_staff_id:o,p_date:e});if(n)return console.error(`[${a}] Error calculating metrics:`,n),t.status(500).json({error:"Failed to calculate daily metrics",message:n.message,requestId:a});return t.status(200).json({calculatedMetrics:r,message:"Daily metrics calculated successfully",requestId:a})}return t.status(400).json({error:"Invalid action",message:"Action must be update_metrics or calculate_daily_metrics",requestId:a})}return t.status(405).json({error:"Method not allowed",requestId:a})}catch(e){return console.error(`[${a}] Unexpected error:`,e),t.status(500).json({error:"Internal server error",message:e instanceof Error?e.message:"Unknown error",requestId:a})}}let g=(0,n.l)(r,"default"),m=(0,n.l)(r,"config"),p=new o.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/admin/staff/performance",pathname:"/api/admin/staff/performance",bundlePath:"",filename:""},userland:r})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[2805],()=>a(5658));module.exports=r})();