/* Recent Bookings Component Styles */
.recentBookingsContainer {
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-xl);
  border: 1px solid var(--admin-border-light);
  box-shadow: 0 2px 4px var(--admin-shadow-light);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-spacing-lg);
}

.headerLeft {
  flex: 1;
}

.sectionTitle {
  color: var(--admin-darker);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 var(--admin-spacing-xs) 0;
}

.sectionSubtitle {
  color: var(--admin-gray);
  font-size: 1rem;
  margin: 0;
}

.headerRight {
  display: flex;
  gap: var(--admin-spacing-md);
}

.viewAllButton {
  background: var(--admin-primary);
  color: white;
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  border-radius: var(--admin-radius-md);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--admin-transition-normal);
}

.viewAllButton:hover {
  background: var(--admin-primary-dark);
  transform: translateY(-1px);
}

.bookingsContainer {
  margin-bottom: var(--admin-spacing-lg);
}

.emptyState {
  text-align: center;
  padding: var(--admin-spacing-xxl);
  color: var(--admin-gray);
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: var(--admin-spacing-md);
  opacity: 0.5;
}

.emptyState h3 {
  color: var(--admin-darker);
  margin-bottom: var(--admin-spacing-sm);
}

.emptyState p {
  margin-bottom: var(--admin-spacing-lg);
}

.createButton {
  background: var(--admin-primary);
  color: white;
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  border-radius: var(--admin-radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: all var(--admin-transition-normal);
}

.createButton:hover {
  background: var(--admin-primary-dark);
  transform: translateY(-1px);
}

.bookingsList {
  display: flex;
  flex-direction: column;
  gap: var(--admin-spacing-md);
}

.bookingCard {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-spacing-lg);
  transition: all var(--admin-transition-normal);
}

.bookingCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px var(--admin-shadow-medium);
  border-color: var(--admin-primary);
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-spacing-md);
}

.customerInfo {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
}

.customerAvatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.customerDetails {
  flex: 1;
}

.customerName {
  font-weight: 600;
  color: var(--admin-darker);
  font-size: 1.1rem;
  margin-bottom: 2px;
}

.bookingDate {
  color: var(--admin-gray);
  font-size: 0.9rem;
}

.statusBadge {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-xs);
  padding: 6px 12px;
  border-radius: var(--admin-radius-md);
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: capitalize;
}

.statusIcon {
  font-size: 0.9rem;
}

.statusText {
  font-size: 0.8rem;
}

.bookingContent {
  margin-bottom: var(--admin-spacing-md);
}

.serviceInfo {
  margin-bottom: var(--admin-spacing-sm);
}

.serviceName {
  font-weight: 600;
  color: var(--admin-darker);
  font-size: 1rem;
  margin-bottom: 2px;
}

.artistName {
  color: var(--admin-gray);
  font-size: 0.9rem;
}

.bookingMeta {
  display: flex;
  gap: var(--admin-spacing-lg);
}

.metaItem {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-xs);
}

.metaIcon {
  font-size: 1rem;
  color: var(--admin-gray);
}

.metaText {
  font-size: 0.9rem;
  color: var(--admin-gray);
  font-weight: 500;
}

.bookingActions {
  display: flex;
  gap: var(--admin-spacing-sm);
  flex-wrap: wrap;
}

.viewButton {
  background: var(--admin-bg-primary);
  color: var(--admin-primary);
  border: 1px solid var(--admin-primary);
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  border-radius: var(--admin-radius-md);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all var(--admin-transition-normal);
}

.viewButton:hover {
  background: var(--admin-primary);
  color: white;
}

.confirmButton {
  background: var(--admin-success);
  color: white;
  border: none;
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  border-radius: var(--admin-radius-md);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--admin-transition-normal);
}

.confirmButton:hover {
  background: #218838;
  transform: translateY(-1px);
}

.editButton {
  background: var(--admin-bg-primary);
  color: var(--admin-gray);
  border: 1px solid var(--admin-border-medium);
  padding: var(--admin-spacing-sm) var(--admin-spacing-md);
  border-radius: var(--admin-radius-md);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--admin-transition-normal);
}

.editButton:hover {
  background: var(--admin-gray);
  color: white;
}

.quickStats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--admin-spacing-md);
  padding-top: var(--admin-spacing-lg);
  border-top: 1px solid var(--admin-border-light);
}

.statItem {
  text-align: center;
  padding: var(--admin-spacing-md);
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-md);
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--admin-primary);
  margin-bottom: var(--admin-spacing-xs);
}

.statLabel {
  font-size: 0.85rem;
  color: var(--admin-gray);
  font-weight: 500;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .recentBookingsContainer {
    padding: var(--admin-spacing-lg);
  }

  .header {
    flex-direction: column;
    gap: var(--admin-spacing-md);
  }

  .bookingHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-spacing-sm);
  }

  .customerInfo {
    width: 100%;
  }

  .bookingMeta {
    flex-direction: column;
    gap: var(--admin-spacing-sm);
  }

  .quickStats {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .recentBookingsContainer {
    padding: var(--admin-spacing-md);
  }

  .sectionTitle {
    font-size: 1.25rem;
  }

  .bookingCard {
    padding: var(--admin-spacing-md);
  }

  .customerAvatar {
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }

  .bookingActions {
    flex-direction: column;
  }

  .viewButton,
  .confirmButton,
  .editButton {
    text-align: center;
  }
}
